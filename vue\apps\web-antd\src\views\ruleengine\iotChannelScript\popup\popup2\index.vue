<script lang="ts" setup>
import { useVbenModal, VbenButton } from '@vben/common-ui';

import { h, reactive, ref, computed, onMounted } from 'vue';
import { Button, message, Tag, Popconfirm, Switch } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import {
  List,
  Export,
  Delete,
  Status,
  TestConnect,
} from '#/api/ruleengine/iotChannel';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';

const type = ref<number>();
const direction = ref<number>();
const props = defineProps(['getBridge']);

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'channelId',
  },
  rowConfig: {
    keyField: 'channelId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
          type: type.value,
          direction: direction.value,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

async function handleRefresh() {
  await gridApi.query();
}

async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '资源通道', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success('导出成功');
}
async function handleStatusChange(row: RowType) {
  await Status({ channelId: row.channelId, status: row.status });
  await message.success('操作成功');
  await handleRefresh();
}

//确认按钮回调函数
const onConfirm = () => {
  const channelId = gridApi.grid.getRadioRecord().channelId;
  const channelName = gridApi.grid.getRadioRecord().channelName;

  props.getBridge(channelId, channelName, direction.value);
  modalApi.close();
};

const [Modal, modalApi] = useVbenModal({ onConfirm: onConfirm });

function openModal(paramType: number, paramDirection: number) {
  type.value = paramType;
  direction.value = paramDirection;
  modalApi.open();
}

defineExpose({
  openModal,
});
</script>
<template>
  <div>
    <Modal class="h-[600px] w-[1000px]" title="基础示例">
      <div class="table-content">
        <Grid>
          <template #status="{ row }">
            <Switch
              v-model:checked="row.status"
              :checkedValue="'0'"
              :unCheckedValue="'1'"
              @change="handleStatusChange(row)"
              :disabled="
                !hasAccessByCodes(['cpm:ruleengine:iotChannel:status'])
              "
            />
          </template>
        </Grid>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
.table-content {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
