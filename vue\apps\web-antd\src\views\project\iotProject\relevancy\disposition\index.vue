<template>
  <Modal v-model:open="visible" :width="'100vw'" :style="{ top: 0, paddingBottom: 0 }"
    :bodyStyle="{ height: 'calc(100vh - 55px)', padding: '5px', overflow: 'auto' }" :mask-closable="false"
    :closable="false" :footer="null" class="disposition-modal" wrapClassName="fullscreen-modal">
    <!-- 自定义标题栏 -->
    <template #title>
      <div class="custom-title-bar">
        <span class="title-text">{{ title }}</span>
        <div class="title-actions">
          <!-- 项目选择器 -->
          <div class="title-selector">
            <label class="selector-label">项目({{ projectList.length }})：</label>
            <Select v-model:value="selectedProjectId" placeholder="请选择项目" style="width: 150px" size="small"
              @change="handleProjectChange" :loading="projectLoading"
              :options="projectList.map(p => ({ label: p.projectName, value: p.projectId }))" />
          </div>

          <!-- 设备选择器 -->
          <div class="title-selector">
            <label class="selector-label">产品({{ deviceList.length }})：</label>
            <Select v-model:value="selectedDeviceId" placeholder="请选择产品" style="width: 150px" size="small"
              @change="handleDeviceChange" :loading="deviceLoading" :disabled="!selectedProjectId"
              :options="deviceList.map(d => ({ label: d.productAlias, value: d.ppId }))" />
          </div>

          <Button type="text" size="small" @click="handleRefresh" title="重置面板配置" :loading="isRefreshing"
            class="refresh-button" :class="{ loading: isRefreshing }">
            <IconifyIcon icon="ant-design:reload-outlined" class="refresh-icon" :class="{ spinning: isRefreshing }" />
          </Button>
          <Button type="primary" size="small" @click="handleConfirm" :loading="isSaving" :disabled="isSaving">
            {{ isSaving ? '保存中...' : '确定' }}
          </Button>
          <Button size="small" @click="handleCancel">
            取消
          </Button>
          <Button type="text" size="small" @click="handleCancel" class="close-btn">
            <IconifyIcon icon="ant-design:close-outlined" />
          </Button>
        </div>
      </div>
    </template>



    <!-- 面板配置内容区域 -->
    <div class="panel-container" v-loading="isInitialLoading" :loading-text="'正在加载面板配置...'">
      <!-- 左侧控制台 -->
      <div class="control-panel left-panel" @click="handleGlobalSettingsClick">

        <div class="panel-content">
          <!-- 面板组件列表 -->
          <div class="component-list" v-loading="componentLoading">
            <div v-for="component in componentList" :key="component.componentId" class="component-card">

              <img :src="getImageUrl(component.imgUrl)" :alt="component.componentName"
                @error="handleComponentImageError" @load="handleComponentImageLoad" />

              <div class="component-actions">
                <Button type="primary" size="small" @click.stop="handleAddComponent(component)"
                  class="add-component-btn">
                  添加组件
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间被控区域 -->
      <div :class="['controlled-area', { dragging: isDragging }]" @wheel="handleWheel" @mousedown="handleMouseDown"
        @mousemove="handleMouseMove" @mouseup="handleMouseUp" @mouseleave="handleMouseUp"
        @click="handleControlledAreaClick" ref="controlledAreaRef">
        <div class="controlled-content" :style="{
          transform: `translate(${translateX}px, ${translateY + scrollY}px)`
        }">



          <!-- 中间的两个卡片 -->
          <div class="card-container">
            <Card class="left-card clickable-card" :bordered="true" :bodyStyle="{ padding: '0', minHeight: '200px' }"
              @click="handlePreviewPanelClick">
              <div class="card-image-container">
                <img :src="imageUrl" alt="左侧卡片图片" class="card-image" @error="handleImageError" />
              </div>
            </Card>

            <Card class="right-card" :bordered="true" :bodyStyle="{ padding: '16px', minHeight: '200px' }"
              @mouseenter="handleRightCardMouseEnter" @mouseleave="handleRightCardMouseLeave">
              <div class="added-components-container">


                <!-- 加载状态 -->
                <div v-if="deviceDetailLoading" class="loading-placeholder">
                  <div class="loading-spinner"></div>
                  <p>正在加载已配置组件...</p>
                </div>

                <!-- 组件列表 -->
                <template v-else-if="addedComponents.length > 0">
                  <div v-for="(component, index) in addedComponents" :key="`added-${component.componentId}-${index}`"
                    class="component-image-card"
                    @click="handleComponentClick(component)">
                    <!-- 图片完全铺满 -->
                    <img :src="getImageUrl(component.imgUrl)" :alt="component.componentName"
                      class="component-image"
                      @error="handleAddedComponentImageError" />

                    <!-- 删除按钮 - 悬停时显示 -->
                    <Button
                      type="text"
                      danger
                      size="small"
                      class="delete-btn-overlay"
                      @click.stop="handleRemoveComponent(component, index)"
                      title="删除组件">
                      <IconifyIcon icon="ant-design:close-outlined" />
                    </Button>
                  </div>
                </template>

                <!-- 空状态 -->
                <div v-else class="empty-placeholder">
                  <p>暂无配置的组件</p>
                  <p>请从左侧控制台选择组件添加</p>
                </div>
              </div>
            </Card>
          </div>
          <!-- 被控台的内容 -->
        </div>
      </div>

      <!-- 右侧控制台 -->
      <div class="control-panel right-panel">
        <div class="panel-title">{{ rightPanelTitle }}</div>

        <div class="panel-content">
          <!-- 全局设置内容 -->
          <div v-if="rightPanelTitle === '全局设置'" class="global-settings">
            <div class="setting-item">
              <label class="setting-label">背景色</label>
              <Select v-model:value="globalSettings.page_bg_mode" placeholder="请选择背景色模式" class="w-full"
                @change="handleBackgroundModeChange">
                <Select.Option value="color">纯色</Select.Option>
              </Select>
            </div>

            <div class="setting-item">
              <label class="setting-label">纯色配置</label>
              <div class="color-picker-container">
                <div class="color-preview-wrapper">
                  <input ref="colorInputRef" type="color" :value="globalSettings.bgcolor"
                    @input="handleColorPickerChange" class="color-picker-input" />
                  <div class="color-display" :style="{ backgroundColor: globalSettings.bgcolor }"
                    @click="openColorPicker">
                  </div>
                </div>
                <Input v-model:value="globalSettings.bgcolor" placeholder="#FFFFFF" class="color-input"
                  @blur="handleColorChange" />
              </div>
            </div>
          </div>

          <!-- 预览面板内容 -->
          <div v-else-if="rightPanelTitle === '预览面板'" class="preview-panel">
            <!-- 选择属性 -->
            <div class="preview-section">
              <div class="section-header">
                <span class="section-title">选择属性</span>
                <IconifyIcon icon="mdi-help-circle-outline" class="help-icon" />
              </div>
              <Button type="primary" @click="handlePreviewSelectProperty" :disabled="!selectedDeviceId"
                class="select-property-btn">
                选择属性
              </Button>
              <div v-if="!selectedDeviceId" class="select-property-warning">
                ⚠️ 请先在顶部选择项目和设备
              </div>

              <!-- 显示已选择的属性列表 -->
              <div v-if="previewSettings.card_attributes && previewSettings.card_attributes.length > 0"
                class="preview-setting-item">
                <label class="preview-setting-label">设置属性</label>
                <div class="property-list">
                  <Card v-for="(property, index) in previewSettings.card_attributes" :key="index"
                    size="small"
                    class="property-card"
                    :title="property.name || property.modelName">
                    <template #extra>
                      <IconifyIcon v-if="property.icon" :icon="property.icon" />
                    </template>

                    <div class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium mb-1">显示名称</label>
                        <Input :value="property.name || property.modelName" placeholder="请输入显示名称"
                          @change="(e) => updatePreviewPropertyField(index, 'name', e.target?.value || '')"
                          @blur="savePreviewPropertyChanges(index)" />
                      </div>

                      <div>
                        <label class="block text-sm font-medium mb-1">图标</label>
                        <IconPicker :model-value="property.icon || ''" placeholder="请选择图标" class="w-full"
                          @change="(value) => updatePreviewPropertyField(index, 'icon', value)"
                          @blur="savePreviewPropertyChanges(index)" />
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
            </div>

            <!-- 宽卡片属性列数 -->
            <div class="preview-section">
              <div class="section-header">
                <span class="section-title">宽卡片属性列数</span>
                <IconifyIcon icon="mdi-help-circle-outline" class="help-icon" />
              </div>
              <div class="radio-group">
                <label class="radio-item">
                  <input type="radio" name="card_items_cols" value="1" v-model="previewSettings.card_items_cols" />
                  <span class="radio-label">1个</span>
                </label>
                <label class="radio-item">
                  <input type="radio" name="card_items_cols" value="2" v-model="previewSettings.card_items_cols" />
                  <span class="radio-label">2个</span>
                </label>
                <label class="radio-item">
                  <input type="radio" name="card_items_cols" value="3" v-model="previewSettings.card_items_cols" />
                  <span class="radio-label">3个</span>
                </label>
                <label class="radio-item">
                  <input type="radio" name="card_items_cols" value="4" v-model="previewSettings.card_items_cols" />
                  <span class="radio-label">4个</span>
                </label>
              </div>
            </div>

            <!-- 窄卡片属性列数 -->
            <div class="preview-section">
              <div class="section-header">
                <span class="section-title">窄卡片属性列数</span>
                <IconifyIcon icon="mdi-help-circle-outline" class="help-icon" />
              </div>
              <div class="radio-group">
                <label class="radio-item">
                  <input type="radio" name="card_items_thin_cols" value="1"
                    v-model="previewSettings.card_items_thin_cols" />
                  <span class="radio-label">1个</span>
                </label>
                <label class="radio-item">
                  <input type="radio" name="card_items_thin_cols" value="2"
                    v-model="previewSettings.card_items_thin_cols" />
                  <span class="radio-label">2个</span>
                </label>
              </div>
            </div>

            <!-- 对齐方式 -->
            <div class="preview-section">
              <div class="section-header">
                <span class="section-title">对齐方式</span>
              </div>
              <div class="radio-group">
                <label class="radio-item">
                  <input type="radio" name="card_items_align" value="left" v-model="previewSettings.card_items_align" />
                  <span class="radio-label">居左</span>
                </label>
                <label class="radio-item">
                  <input type="radio" name="card_items_align" value="center"
                    v-model="previewSettings.card_items_align" />
                  <span class="radio-label">居中</span>
                </label>
                <label class="radio-item">
                  <input type="radio" name="card_items_align" value="right"
                    v-model="previewSettings.card_items_align" />
                  <span class="radio-label">居右</span>
                </label>
              </div>
            </div>

            <!-- 布局样式 -->
            <div class="preview-section">
              <div class="section-header">
                <span class="section-title">布局样式</span>
              </div>
              <div class="radio-group">
                <label class="radio-item">
                  <input type="radio" name="card_items_style" value="1" v-model="previewSettings.card_items_style" />
                  <span class="radio-label">属性名称在上</span>
                </label>
                <label class="radio-item">
                  <input type="radio" name="card_items_style" value="2" v-model="previewSettings.card_items_style" />
                  <span class="radio-label">属性名称在下</span>
                </label>
              </div>
            </div>

            <!-- 属性水平间距 -->
            <div class="preview-section">
              <div class="section-header">
                <span class="section-title">属性水平间距</span>
              </div>
              <div class="slider-container">
                <div class="slider-wrapper">
                  <input type="range" min="0" max="50" v-model="previewSettings.card_items_gap_x" class="slider" />
                </div>
                <div class="input-number-wrapper">
                  <input type="number" v-model="previewSettings.card_items_gap_x" class="number-input" min="0"
                    max="50" />
                  <div class="input-controls">
                    <button @click="incrementValue('horizontalSpacing')" class="control-btn">
                      <IconifyIcon icon="mdi-chevron-up" />
                    </button>
                    <button @click="decrementValue('horizontalSpacing')" class="control-btn">
                      <IconifyIcon icon="mdi-chevron-down" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 属性垂直间距 -->
            <div class="preview-section">
              <div class="section-header">
                <span class="section-title">属性垂直间距</span>
              </div>
              <div class="slider-container">
                <div class="slider-wrapper">
                  <input type="range" min="0" max="50" v-model="previewSettings.card_items_gap_y" class="slider" />
                </div>
                <div class="input-number-wrapper">
                  <input type="number" v-model="previewSettings.card_items_gap_y" class="number-input" min="0"
                    max="50" />
                  <div class="input-controls">
                    <button @click="incrementValue('verticalSpacing')" class="control-btn">
                      <IconifyIcon icon="mdi-chevron-up" />
                    </button>
                    <button @click="decrementValue('verticalSpacing')" class="control-btn">
                      <IconifyIcon icon="mdi-chevron-down" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 总体内间距 -->
            <div class="preview-section">
              <div class="section-header">
                <span class="section-title">总体内间距</span>
              </div>
              <div class="slider-container">
                <div class="slider-wrapper">
                  <input type="range" min="0" max="50" v-model="previewSettings.card_items_padding" class="slider" />
                </div>
                <div class="input-number-wrapper">
                  <input type="number" v-model="previewSettings.card_items_padding" class="number-input" min="0"
                    max="50" />
                  <div class="input-controls">
                    <button @click="incrementValue('totalPadding')" class="control-btn">
                      <IconifyIcon icon="mdi-chevron-up" />
                    </button>
                    <button @click="decrementValue('totalPadding')" class="control-btn">
                      <IconifyIcon icon="mdi-chevron-down" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 组件设置内容 -->
          <div v-else-if="rightPanelTitle === '组件设置'" class="component-settings">
            <div v-if="selectedComponent">

              <div v-if="!isComponentAdded" class="setting-item">
                <label class="setting-label">选择属性：</label>
                <div class="select-property-container">
                  <Button type="primary" @click="handleSelectProperty" :disabled="!selectedDeviceId"
                    class="select-property-btn">
                    选择属性
                  </Button>
                  <div class="select-property-tip">
                    请选择属性后，组件将自动添加到右侧卡片
                  </div>
                  <div v-if="!selectedDeviceId" class="select-property-warning">
                    ⚠️ 请先在顶部选择项目和设备，然后才能选择属性
                  </div>
                </div>
              </div>

              <!-- 如果组件已添加，显示重新选择属性按钮 -->
              <div v-else class="setting-item">
                <label class="setting-label">属性配置：</label>
                <div class="select-property-container">
                  <Button type="default" @click="handleSelectProperty" :disabled="!selectedDeviceId"
                    class="select-property-btn">
                    重新选择属性
                  </Button>
                  <div class="select-property-tip">
                    可以重新选择属性来更新组件配置
                  </div>
                </div>
              </div>

              <!-- 显示已选择的属性列表 -->
              <div v-if="selectedComponent.selectedProperties && selectedComponent.selectedProperties.length > 0"
                class="setting-item">
                <label class="setting-label">设置属性</label>
                <div class="property-list">
                  <Card v-for="(property, index) in selectedComponent.selectedProperties" :key="index"
                    size="small"
                    class="property-card mb-3"
                    :title="property.name || property.label">
                    <template #extra>
                      <IconifyIcon v-if="property.icon" :icon="property.icon" />
                    </template>

                    <div class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium mb-1">显示名称</label>
                        <Input :value="property.name || property.label" placeholder="请输入显示名称"
                          @change="(e) => updatePropertyField(index, 'name', e.target?.value || '')"
                          @blur="savePropertyChanges(index)" />
                      </div>

                      <div>
                        <label class="block text-sm font-medium mb-1">图标</label>
                        <IconPicker :model-value="property.icon || ''" placeholder="请选择图标" class="w-full"
                          @change="(value) => updatePropertyField(index, 'icon', value)"
                          @blur="savePropertyChanges(index)" />
                      </div>
                    </div>
                  </Card>
                </div>
              </div>




            </div>
            <div v-else>
              <p class="no-component">请先添加组件</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 属性选择弹窗 -->
    <Modal v-model:open="propertySelectVisible" title="选择属性" :width="600" @ok="handlePropertyConfirm"
      @cancel="handlePropertyCancel">
      <template #extra>
        <Button type="text" size="small" @click="refreshComponentPropertyModal" :loading="propertyLoading"
          title="刷新模态框内容" class="refresh-button" :class="{ loading: propertyLoading }">
          <IconifyIcon icon="ant-design:reload-outlined" class="refresh-icon" :class="{ spinning: propertyLoading }" />
        </Button>
      </template>

      <div style="padding: 16px 0;">
        <div style="margin-bottom: 8px; font-weight: 500;">选择属性</div>
        <Select v-model:value="selectedProperties" mode="multiple" placeholder="请选择属性（可多选）" style="width: 100%"
          :loading="propertyLoading" :options="propertyOptions" show-search :filter-option="(input: string, option: any) =>
            option.label.toLowerCase().includes(input.toLowerCase())
            " :max-tag-count="3" :max-tag-placeholder="(omittedValues: any[]) => `+${omittedValues.length}...`" />
        <div v-if="propertyLoading" style="text-align: center; color: #666; margin-top: 8px;">
          正在加载属性列表...
        </div>
        <div v-else-if="propertyOptions.length === 0"
          style="text-align: center; color: #999; margin-top: 8px; padding: 12px; border: 1px dashed #d9d9d9; border-radius: 4px;">
          暂无可选属性，请检查设备配置或点击刷新重试
        </div>
      </div>

      <template #footer>
        <div style="text-align: right;">
          <Button @click="handlePropertyCancel" style="margin-right: 8px;">取消</Button>
          <Button type="primary" @click="handlePropertyConfirm"
            :disabled="!selectedProperties || selectedProperties.length === 0">
            提交
          </Button>
        </div>
      </template>
    </Modal>

    <!-- 预览面板属性选择弹窗 -->
    <Modal v-model:open="previewPropertySelectVisible" title="选择属性" :width="500" @ok="handlePreviewPropertyConfirm"
      @cancel="handlePreviewPropertyCancel">
      <template #extra>
        <Button type="text" size="small" @click="refreshPreviewPropertyModal" :loading="propertyLoading" title="刷新模态框内容"
          class="refresh-button" :class="{ loading: propertyLoading }">
          <IconifyIcon icon="ant-design:reload-outlined" class="refresh-icon" :class="{ spinning: propertyLoading }" />
        </Button>
      </template>

      <div style="padding: 16px 0;">
        <div style="margin-bottom: 8px; font-weight: 500;">请选择属性：</div>
        <Select v-model:value="previewSelectedProperties" mode="multiple" placeholder="请选择属性" style="width: 100%"
          :loading="propertyLoading" :options="previewPropertyOptions" :max-tag-count="3" show-search
          :filter-option="filterPropertyOption" />
        <div v-if="propertyLoading" style="text-align: center; color: #666; margin-top: 8px;">
          正在加载属性列表...
        </div>
        <div v-else-if="previewPropertyOptions.length === 0"
          style="text-align: center; color: #999; margin-top: 8px; padding: 12px; border: 1px dashed #d9d9d9; border-radius: 4px;">
          暂无可选属性，请检查设备配置或点击刷新重试
        </div>
      </div>
      <template #footer>
        <div style="text-align: right;">
          <Button @click="handlePreviewPropertyCancel" style="margin-right: 8px;">取消</Button>
          <Button type="primary" @click="handlePreviewPropertyConfirm"
            :disabled="previewSelectedProperties.length === 0">
            确定
          </Button>
        </div>
      </template>
    </Modal>

  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue';
import { Modal, Button, Card, Select, Input, message } from 'ant-design-vue';
import { IconPicker } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { List as ProjectList } from '#/api/project/iotProject'; // 项目列表API
import { List as ProductList, SetPanelConfig, ViewDevice } from '#/api/project/iotProjectProduct'; // 项目产品关联列表API
import { List as ComponentList } from '#/api/project/iotPanelComponents'; // 面板组件列表API
import { requestClient } from '#/api/request'; // 请求客户端

interface ModalProps {
  ppId?: number;
  productKey?: string;
  productAlias?: string;
  projectId?: number; // 项目ID
  projectName?: string; // 项目名称
}

const visible = ref(false);
const modalData = ref<ModalProps>({});
const controlledAreaRef = ref();

// 加载状态
const isInitialLoading = ref(false);
const isRefreshing = ref(false); // 主刷新按钮的加载状态
const isSaving = ref(false); // 保存按钮的加载状态

// 拖拽相关状态
const translateX = ref(0);
const translateY = ref(0);
const isDragging = ref(false);
const lastMouseX = ref(0);
const lastMouseY = ref(0);

// 滚动相关状态
const scrollY = ref(0);

// 右侧卡片滚动相关状态
const isMouseOverRightCard = ref(false);

// 图片路径 - 使用动态导入
const imageUrl = ref('');

// 控制台2标题状态
const rightPanelTitle = ref('全局设置'); // 默认显示全局设置

// 下拉选择器相关状态
const selectedProjectId = ref<number | undefined>();
const selectedDeviceId = ref<number | undefined>();
const projectList = ref<any[]>([]);
const deviceList = ref<any[]>([]);
const projectLoading = ref(false);
const deviceLoading = ref(false);

// 面板组件相关状态
const componentList = ref<any[]>([]);
const componentLoading = ref(false);

// 被控区域右侧卡片中的组件列表（从接口获取的已配置组件）
const addedComponents = ref<any[]>([]);

// 设备详情数据
const deviceDetail = ref<any>(null);
const deviceDetailLoading = ref(false);

// 计算属性：用于调试组件显示状态
const componentDisplayInfo = computed(() => {
  const info = {
    loading: deviceDetailLoading.value,
    componentCount: addedComponents.value.length,
    hasComponents: addedComponents.value.length > 0,
    components: addedComponents.value.map(comp => ({
      id: comp.componentId,
      name: comp.componentName,
      widget_id: comp.widget_id
    }))
  };

  return info;
});

// 监听 addedComponents 的变化
watch(addedComponents, (newVal: any[], oldVal: any[]) => {
  // 组件变化监听
}, { deep: true });

// 当前选中的组件（用于组件设置）
const selectedComponent = ref<any>(null);

// 标记当前组件是否已添加到右侧卡片
const isComponentAdded = ref(false);

// 属性选择弹窗相关状态
const propertySelectVisible = ref(false);
const propertyOptions = ref<any[]>([]);
const propertyLoading = ref(false);
const selectedProperties = ref<string[]>([]); // 改为数组，支持多选

// 预览面板属性选择相关状态
const previewPropertySelectVisible = ref(false);
const previewSelectedProperties = ref<string[]>([]); // 预览面板选择的属性
const previewPropertyOptions = ref<any[]>([]); // 预览面板下拉选项

// 预览面板属性折叠面板相关状态
const previewActivePropertyKeys = ref<(string | number)[]>([]); // 预览面板当前展开的面板keys

// 属性折叠面板相关状态
const activePropertyKeys = ref<(string | number)[]>([]); // 当前展开的面板keys

// 全局设置相关状态
const globalSettings = ref({
  page_bg_mode: 'color', // 背景色模式，默认为纯色
  bgcolor: '#FFFFFF', // 纯色配置，默认为白色
  bgcolor_gradient: '[]', // 渐变色配置，默认为空数组字符串
  tabs: [] // 标签页配置，默认为空数组
});

// 颜色选择器引用
const colorInputRef = ref<HTMLInputElement>();

// 预览面板设置
const previewSettings = ref({
  card_items_cols: 3, // 宽卡片属性列数，对应接口字段
  card_items_thin_cols: 2, // 窄卡片属性列数，对应接口字段
  card_items_align: 'center', // 对齐方式，对应接口字段
  card_items_style: 1, // 布局样式，对应接口字段（1：属性名称在上，2：属性名称在下）
  card_items_gap_x: 10, // 属性水平间距，对应接口字段
  card_items_gap_y: 1, // 属性垂直间距，对应接口字段
  card_items_padding: 0, // 总体内间距，对应接口字段
  card_attributes: [] as any[], // 预览面板选择的属性，对应接口字段
  // 在线状态相关默认值
  card_show_online_text: true, // 显示在线文本
  card_online_text_color: '#2196F3', // 在线文本颜色
  card_show_online_icon: true, // 显示在线图标
  card_online_icon: 'mdi-wifi', // 在线图标
  card_use_online_icon_attr: false, // 使用在线图标属性
  card_online_icon_attr: '', // 在线图标属性
  card_online_icon_color: '', // 在线图标颜色
  card_show_online_signal_icon: false, // 显示在线信号图标
  card_online_signal_icon: 'mdi-wifi', // 在线信号图标
  card_use_online_signal_icon_attr: true, // 使用在线信号图标属性
  card_online_signal_icon_attr: '', // 在线信号图标属性
  card_online_signal_icon_color: '', // 在线信号图标颜色
  // 电池相关默认值
  card_show_battery_icon: true, // 显示电池图标
  card_show_battery_number: false, // 显示电池数字
  card_battery_number_attr: '', // 电池数字属性
  card_battery_charger_attr: '', // 电池充电器属性
  // 报警和活动时间相关默认值
  card_show_alarm_icon: true, // 显示报警图标
  card_show_active_time: true // 显示活动时间
});

// 动态加载图片
async function loadImage() {
  try {
    // 尝试动态导入图片
    const imageModule = await import('./image.png');
    imageUrl.value = imageModule.default;
  } catch (error) {
    // 使用base64编码的占位图
    imageUrl.value = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lm77niYfliqDovb3lpLHotKU8L3RleHQ+PC9zdmc+';
  }
}

const title = computed(() => {
  const { productAlias, productKey } = modalData.value;
  const displayName = productAlias || productKey;
  return displayName ? `面板配置 - ${displayName}` : '面板配置';
});

// 添加计算属性来监控项目列表变化
const projectOptions = computed(() => {
  return projectList.value;
});

async function open(data: ModalProps) {
  // 重置所有状态
  resetAllStates();

  modalData.value = data;
  visible.value = true;
  isInitialLoading.value = true; // 开始加载

  try {
    // 并行执行加载图片和等待1秒
    const [,] = await Promise.all([
      loadImage(), // 加载图片
      new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒
    ]);

    // 每次打开都重新加载项目列表，确保数据最新
    await fetchProjectList();

    // 加载面板组件列表
    await fetchComponentList();

    // 设置默认选择
    await setDefaultSelection(data);

    // 获取设备详情（包含已配置的组件）
    if (data.ppId && data.ppId !== 0) {
      await fetchDeviceDetail(data.ppId);
    }
  } finally {
    // 无论成功还是失败，都要关闭加载状态
    isInitialLoading.value = false;
  }
}

// 重置所有状态
function resetAllStates() {
  selectedProjectId.value = undefined;
  selectedDeviceId.value = undefined;
  projectList.value = [];
  deviceList.value = [];
  projectLoading.value = false;
  deviceLoading.value = false;
  isInitialLoading.value = false;

  // 重置面板组件状态
  componentList.value = [];
  componentLoading.value = false;

  // 重置添加的组件
  addedComponents.value = [];

  // 重置设备详情
  deviceDetail.value = null;
  deviceDetailLoading.value = false;

  // 重置组件设置相关状态
  selectedComponent.value = null;
  isComponentAdded.value = false;
  propertySelectVisible.value = false;
  propertyOptions.value = [];
  propertyLoading.value = false;
  selectedProperties.value = [];

  // 重置属性折叠面板状态
  activePropertyKeys.value = [];

  // 重置拖拽和滚动状态
  translateX.value = 0;
  translateY.value = 0;
  scrollY.value = 0;
  isMouseOverRightCard.value = false;
  isDragging.value = false;

  // 重置控制台标题
  rightPanelTitle.value = '全局设置';
}

// 设置默认选择
async function setDefaultSelection(data: ModalProps) {
  // 如果有ppId，通过ppId查找对应的项目并设置默认选择
  if (data.ppId && data.ppId > 0) {
    // 先设置设备ID
    selectedDeviceId.value = data.ppId;

    // 通过ppId查找对应的项目ID
    // 遍历所有项目，加载每个项目的设备列表，找到包含该ppId的项目
    for (const project of projectList.value) {
      try {
        const response = await ProductList({
          page: 1,
          pageSize: 1000,
          projectId: project.projectId
        });

        const devices = response?.items || [];
        const targetDevice = devices.find((device: any) => device.ppId === data.ppId);

        if (targetDevice) {
          selectedProjectId.value = project.projectId;
          // 加载该项目的设备列表到下拉框
          await fetchDeviceList(project.projectId);
          break;
        }
      } catch (error) {
        console.error('查找设备所属项目时出错:', error);
      }
    }
  }
  // 如果没有ppId但有projectId，直接设置项目默认选择
  else if (data.projectId && data.projectId > 0) {
    selectedProjectId.value = data.projectId;
    // 加载该项目的设备列表
    await fetchDeviceList(data.projectId);
  }
}

function close() {
  isInitialLoading.value = false; // 确保关闭加载状态
  visible.value = false;
  modalData.value = {};
  // 关闭时不重置状态，保持用户的选择，下次打开时会重新加载
}

async function handleRefresh() {
  try {
    // 设置刷新状态
    isRefreshing.value = true;

    // 检查是否有选择的项目和设备
    if (!selectedProjectId.value || !selectedDeviceId.value) {
      message.warning('请先选择项目和设备');
      return;
    }

    // 保存当前选择
    const currentProjectId = selectedProjectId.value;
    const currentDeviceId = selectedDeviceId.value;

    // 重置界面状态
    translateX.value = 0;
    translateY.value = 0;
    scrollY.value = 0;
    isMouseOverRightCard.value = false;
    isDragging.value = false;
    selectedComponent.value = null;
    addedComponents.value = [];
    rightPanelTitle.value = '全局设置';

    // 重新调用所有接口，就像重新打开设备配置面板一样
    // 1. 重新加载项目列表
    await fetchProjectList();

    // 2. 重新加载设备列表
    await fetchDeviceList(currentProjectId);

    // 3. 重新加载组件列表
    await fetchComponentList();

    // 4. 重新获取设备详情（这会触发面板配置的加载）
    await fetchDeviceDetail(currentDeviceId);

    // 恢复选择状态
    selectedProjectId.value = currentProjectId;
    selectedDeviceId.value = currentDeviceId;

    message.success('刷新完成');
  } catch (error) {
    console.error('刷新失败:', error);
    message.error('刷新失败: ' + ((error as Error).message || '未知错误'));
  } finally {
    isRefreshing.value = false;
  }
}



async function handleConfirm() {
  try {
    // 设置保存状态
    isSaving.value = true;

    // 检查必要的数据
    if (modalData.value.ppId === undefined || modalData.value.ppId === null) {
      message.error('缺少项目产品关联ID，无法保存配置');
      return;
    }

    if (modalData.value.ppId === 0) {
      message.warning('该产品尚未与项目建立关联关系，ppId为0可能导致保存失败');
      // 继续执行，让服务器返回具体的错误信息
    }

    // 构建widgets数据 - 将componentConfigObj转换为widgets格式
    const widgets = addedComponents.value.map(component => {
      // 获取组件的配置对象
      const componentConfigObj = component.componentConfigObj || {};
      const attrConfigObj = component.attrConfigObj || {};

      // 将attrConfigObj转换为attributes格式
      const attributes = component.selectedProperties?.map(property => {
        // 根据属性的实际数据类型设置相应的配置
        const dataType = property.dataType || property.type || 'boolean';
        const propertyName = property.name || property.modelName || '';

        // 根据数据类型生成相应的配置
        let dataOptions = {};
        let attrType = 'push';
        let onText = '开启';
        let offText = '关闭';
        let onIcon = 'mdi-power';
        let offIcon = 'mdi-power-off';
        let confirmTitle = `操作${propertyName}`;
        let confirmTips = `确认要操作${propertyName}吗？`;

        // 根据不同的数据类型设置不同的配置
        switch (dataType.toLowerCase()) {
          case 'boolean':
          case 'bool':
            dataOptions = {
              value_type: 'boolean',
              on_text: onText,
              off_text: offText
            };
            break;
          case 'int':
          case 'integer':
          case 'number':
            dataOptions = {
              value_type: 'number',
              min: property.min || 0,
              max: property.max || 100,
              step: property.step || 1,
              unit: property.unit || ''
            };
            attrType = 'input';
            break;
          case 'string':
          case 'text':
            dataOptions = {
              value_type: 'string',
              max_length: property.maxLength || 255
            };
            attrType = 'input';
            break;
          case 'enum':
            dataOptions = {
              value_type: 'enum',
              options: property.options || property.enumValues || []
            };
            attrType = 'select';
            break;
          default:
            dataOptions = {
              value_type: 'boolean',
              on_text: onText,
              off_text: offText
            };
        }

        // 如果属性有自定义的图标，使用自定义图标
        if (property.icon) {
          onIcon = property.icon;
          offIcon = property.icon;
        }

        return {
          identifier: property.modelKey || '',
          name: propertyName,
          attr_type: attrType,
          data_type: dataType,
          data_options: dataOptions,
          on_icon: onIcon,
          off_icon: offIcon,
          on_icon_animate_speed: 1,
          off_icon_animate_speed: 1,
          confirm_title: confirmTitle,
          confirm_tips: confirmTips,
          modelKey: property.modelKey || '',
          modelName: property.modelName || '',
          icon: property.icon || '',
          allAttributes: component.selectedProperties?.map(attr => ({
            modelKey: attr.modelKey || '',
            modelName: attr.modelName || '',
            name: attr.name || attr.modelName || '',
            icon: attr.icon || '',
            dataType: attr.dataType || attr.type || 'boolean'
          })) || []
        };
      }) || [];

      // 根据组件类型动态确定widget_id
      const widgetId = component.widget_id || component.componentKey || component.componentId || 'default_widget';

      // 根据组件类型和配置动态生成样式
      const defaultStyle: any = {
        label_color: '#333333',
        need_confirm: false,
        panel_bgcolor: '#ffffff',
        panel_bgcolor_mode: 'color',
        show_panel: true,
        show_shadow: true,
        state_color: '#999999',
        style_type: 'normal'
      };

      // 根据组件类型添加特定的样式配置
      if (component.componentKey && component.componentKey.includes('switch')) {
        // 开关类组件的样式
        Object.assign(defaultStyle, {
          off_icon_bgcolor: '#eee',
          off_icon_color: '#999',
          on_icon_bgcolor: '#EBF4FF',
          on_icon_color: '#4286DE',
          switch_color: '#4286DE'
        });
      } else if (component.componentKey && component.componentKey.includes('slider')) {
        // 滑块类组件的样式
        Object.assign(defaultStyle, {
          slider_color: '#4286DE',
          track_color: '#f0f0f0',
          thumb_color: '#4286DE'
        });
      } else if (component.componentKey && component.componentKey.includes('button')) {
        // 按钮类组件的样式
        Object.assign(defaultStyle, {
          button_color: '#4286DE',
          button_text_color: '#ffffff',
          button_border_radius: '4px'
        });
      }

      // 如果有渐变背景配置，添加默认渐变
      if (defaultStyle.panel_bgcolor_mode === 'gradient') {
        defaultStyle.panel_bgcolor_gradient = [
          {
            color: '#a8ff78',
            position: 0
          },
          {
            color: '#78ffd6',
            position: 1
          }
        ];
      }

      return {
        id: component.id || '',
        widget_id: widgetId,
        visible_type: component.visible_type || 0,
        visible_roles: component.visible_roles || [],
        visible_attrs_conds: component.visible_attrs_conds || [],
        tab_id: component.tab_id || '',
        options: {
          config: {
            attributes: attributes
          },
          style: componentConfigObj.style || defaultStyle
        }
      };
    });

    // 构建完整的请求数据
    const requestData = {
      ppId: modalData.value.ppId,
      panelConfigObj: {
        global_config: {
          page_bg_mode: globalSettings.value.page_bg_mode,
          bgcolor: globalSettings.value.bgcolor,
          bgcolor_gradient: globalSettings.value.bgcolor_gradient,
          tabs: globalSettings.value.tabs
        },
        preview: {
          card_attributes: previewSettings.value.card_attributes,
          card_items_cols: Number(previewSettings.value.card_items_cols),
          card_items_thin_cols: Number(previewSettings.value.card_items_thin_cols),
          card_items_align: previewSettings.value.card_items_align,
          card_items_style: previewSettings.value.card_items_style,
          card_items_gap_x: previewSettings.value.card_items_gap_x,
          card_items_gap_y: previewSettings.value.card_items_gap_y,
          card_items_padding: previewSettings.value.card_items_padding,
          card_show_online_text: previewSettings.value.card_show_online_text,
          card_online_text_color: previewSettings.value.card_online_text_color,
          card_show_online_icon: previewSettings.value.card_show_online_icon,
          card_online_icon: previewSettings.value.card_online_icon,
          card_use_online_icon_attr: previewSettings.value.card_use_online_icon_attr,
          card_online_icon_attr: previewSettings.value.card_online_icon_attr,
          card_online_icon_color: previewSettings.value.card_online_icon_color,
          card_show_online_signal_icon: previewSettings.value.card_show_online_signal_icon,
          card_online_signal_icon: previewSettings.value.card_online_signal_icon,
          card_use_online_signal_icon_attr: previewSettings.value.card_use_online_signal_icon_attr,
          card_online_signal_icon_attr: previewSettings.value.card_online_signal_icon_attr,
          card_online_signal_icon_color: previewSettings.value.card_online_signal_icon_color,
          card_show_battery_icon: previewSettings.value.card_show_battery_icon,
          card_show_battery_number: previewSettings.value.card_show_battery_number,
          card_battery_number_attr: previewSettings.value.card_battery_number_attr,
          card_battery_charger_attr: previewSettings.value.card_battery_charger_attr,
          card_show_alarm_icon: previewSettings.value.card_show_alarm_icon,
          card_show_active_time: previewSettings.value.card_show_active_time
        },
        widgets: widgets
      }
    };



    try {
      // 确保数据结构正确
      if (!requestData.ppId && requestData.ppId !== 0) {
        throw new Error('requestData 缺少 ppId 字段');
      }

      if (!requestData.panelConfigObj) {
        throw new Error('requestData 缺少 panelConfigObj 字段');
      }

      const response = await SetPanelConfig(requestData);
      message.success('面板配置保存成功');

      // 关闭模态框
      close();
    } catch (apiError) {
      console.error('API调用失败:', apiError);
      throw apiError; // 重新抛出错误，让外层catch处理
    }

  } catch (error) {
    console.error('❌ [保存] 面板配置保存失败:', error);
    message.error('面板配置保存失败，请重试');
  } finally {
    // 重置保存状态
    isSaving.value = false;
  }
}

function handleCancel() {
  close();
}

// 处理滚轮上下滚动
function handleWheel(event: WheelEvent) {
  // 检查鼠标是否在右侧卡片区域
  if (isMouseOverRightCard.value) {
    // 如果在右侧卡片区域，不阻止默认行为，让右侧卡片自然滚动
    return;
  }

  // 只有在非右侧卡片区域才阻止默认行为并控制控制台滚动
  event.preventDefault();

  const delta = event.deltaY;
  const scrollStep = 50; // 每次滚动的像素距离

  // 控制控制台的滚动
  if (delta < 0) {
    // 向上滚动滚轮，页面向上滚动（内容向下移动）
    scrollY.value += scrollStep;
  } else {
    // 向下滚动滚轮，页面向下滚动（内容向上移动）
    scrollY.value -= scrollStep;
  }
}

// 处理鼠标按下开始拖拽
function handleMouseDown(event: MouseEvent) {
  isDragging.value = true;
  lastMouseX.value = event.clientX;
  lastMouseY.value = event.clientY;
}

// 处理鼠标移动拖拽
function handleMouseMove(event: MouseEvent) {
  if (!isDragging.value) return;

  const deltaX = event.clientX - lastMouseX.value;
  const deltaY = event.clientY - lastMouseY.value;

  translateX.value += deltaX;
  translateY.value += deltaY;

  lastMouseX.value = event.clientX;
  lastMouseY.value = event.clientY;
}

// 处理鼠标松开结束拖拽
function handleMouseUp() {
  if (isDragging.value) {
    isDragging.value = false;
  }
}

// 处理图片加载错误
function handleImageError(event: Event) {
  const img = event.target as HTMLImageElement;
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lm77niYfliqDovb3lpLHotKU8L3RleHQ+PC9zdmc+';
}

// 获取面板组件列表
async function fetchComponentList() {
  try {
    componentLoading.value = true;

    const response = await ComponentList({
      page: 1,
      pageSize: 1000 // 获取所有组件
    });

    // 根据API返回结构获取数据 - 数据直接在response.items中
    const rawComponents = response?.items || [];

    // 处理组件数据，确保componentConfigObj和attrConfigObj正确解析
    componentList.value = rawComponents.map(component => {
      let componentConfigObj = null;
      let attrConfigObj = null;

      // 1. 尝试解析componentConfig字符串
      if (component.componentConfig) {
        try {
          componentConfigObj = JSON.parse(component.componentConfig);
        } catch (error) {
          console.warn(`组件 ${component.componentName} 的componentConfig解析失败:`, error);
          // 提供默认结构
          componentConfigObj = {
            options: {
              config: {
                attributes: []
              }
            }
          };
        }
      } else {
        // 如果没有componentConfig，提供默认结构
        componentConfigObj = {
          options: {
            config: {
              attributes: []
            }
          }
        };
      }

      // 2. 尝试解析attrConfig字符串
      if (component.attrConfig) {
        try {
          attrConfigObj = JSON.parse(component.attrConfig);
        } catch (error) {
          console.warn(`组件 ${component.componentName} 的attrConfig解析失败:`, error);
          // 提供默认结构
          attrConfigObj = {};
        }
      } else {
        // 如果没有attrConfig，提供默认结构
        attrConfigObj = {};
      }

      return {
        ...component,
        componentConfigObj: componentConfigObj,
        attrConfigObj: attrConfigObj
      };
    });


  } catch (error) {
    console.error('获取面板组件列表失败:', error);
    componentList.value = [];
  } finally {
    componentLoading.value = false;
  }
}

// 点击控制台1或空白处 - 显示全局设置
function handleGlobalSettingsClick() {
  rightPanelTitle.value = '全局设置';
}

// 处理添加组件按钮点击
function handleAddComponent(component: any, event?: Event) {
  // 确保阻止事件冒泡
  if (event) {
    event.stopPropagation();
  }

  // 创建待配置的组件（不立即添加到右侧卡片）
  const componentToConfig = {
    ...component,
    addedAt: new Date().toISOString() // 添加时间戳，用于区分相同组件的多次添加
  };

  // 设置当前选中的组件并切换到组件设置面板
  selectedComponent.value = componentToConfig;
  isComponentAdded.value = false; // 标记组件尚未添加到右侧卡片
  rightPanelTitle.value = '组件设置';
}



// 处理添加的组件图片加载错误
function handleAddedComponentImageError(event: Event) {
  const img = event.target as HTMLImageElement;
  const originalSrc = img.src;

  // 尝试不同的路径格式
  if (originalSrc.includes('/components/') && !originalSrc.includes('localhost')) {
    // 尝试使用绝对路径
    const fileName = originalSrc.split('/components/')[1];
    const newSrc = `http://localhost:5666/components/${fileName}`;
    img.src = newSrc;

    // 添加再次失败的处理
    img.onerror = () => {
      img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7nu4Tku7blm77niYc8L3RleHQ+PC9zdmc+';
      img.onerror = null; // 防止无限循环
    };
  } else {
    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7nu4Tku7blm77niYc8L3RleHQ+PC9zdmc+';
  }
}

// 获取完整的图片URL
function getImageUrl(imgUrl: string) {
  if (!imgUrl) return '';

  // 如果是完整URL，直接返回
  if (imgUrl.startsWith('http://') || imgUrl.startsWith('https://')) {
    return imgUrl;
  }

  // 如果是相对路径，拼接基础URL
  const baseUrl = window.location.origin;
  const fullUrl = imgUrl.startsWith('/') ? `${baseUrl}${imgUrl}` : `${baseUrl}/${imgUrl}`;
  return fullUrl;
}

// 处理组件图片加载成功
function handleComponentImageLoad(event: Event) {
  // 图片加载成功
}

// 移除已添加的组件
function handleRemoveComponent(component: any, index: number) {
  // 从已添加组件列表中移除
  addedComponents.value.splice(index, 1);

  // 如果删除的是当前选中的组件，重置选中状态
  if (selectedComponent.value && selectedComponent.value.addedAt === component.addedAt) {
    selectedComponent.value = null;
    isComponentAdded.value = false;
    rightPanelTitle.value = '全局设置';
  }

  message.success('组件删除成功');
}

// 处理组件图片加载错误
function handleComponentImageError(event: Event) {
  const img = event.target as HTMLImageElement;
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7nu4Tku7blm77niYc8L3RleHQ+PC9zdmc+';
}

// 点击左侧卡片（整个卡片区域）- 显示预览面板
function handlePreviewPanelClick(event: MouseEvent) {
  // 阻止事件冒泡，避免触发被控区域的点击事件
  event.stopPropagation();
  rightPanelTitle.value = '预览面板';
}

// 点击被控区域空白处 - 显示全局设置
function handleControlledAreaClick(event: MouseEvent) {
  // 检查点击的是否是空白区域（不是卡片）
  const target = event.target as HTMLElement;
  if (target.closest('.ant-card') || target.closest('.clickable-card')) {
    // 如果点击的是卡片内部，不处理（卡片有自己的点击事件）
    return;
  }
  rightPanelTitle.value = '全局设置';
}

// 获取项目列表
async function fetchProjectList() {
  try {
    console.log('开始调用项目列表API...');
    projectLoading.value = true;
    const response = await ProjectList({
      page: 1,
      pageSize: 1000 // 获取所有项目
    });
    console.log('项目列表API响应:', response);

    // 直接使用response.items，因为API返回的就是这个结构
    const list = response?.items || [];
    console.log('原始项目列表数据:', list);

    // 检查第一个项目的数据结构
    if (list.length > 0) {
      console.log('第一个项目的数据结构:', list[0]);
      console.log('第一个项目的所有字段:', Object.keys(list[0]));
    }

    projectList.value = list;
    console.log('获取项目列表成功，数量:', projectList.value.length);

    // 强制触发响应式更新
    await nextTick();
    console.log('nextTick后，projectList.value:', projectList.value);
    console.log('nextTick后，projectOptions计算属性:', projectOptions.value);
  } catch (error) {
    console.error('获取项目列表失败:', error);
    projectList.value = [];
  } finally {
    projectLoading.value = false;
    console.log('项目列表加载状态结束');
  }
}

// 获取关联设备列表
async function fetchDeviceList(projectId: number) {
  try {
    deviceLoading.value = true;
    console.log('开始获取项目设备列表，projectId:', projectId);
    const response = await ProductList({
      page: 1,
      pageSize: 1000, // 获取所有设备
      projectId: projectId
    });
    console.log('设备列表API响应:', response);

    // 根据关联设备tab的逻辑，数据直接在response.items中
    deviceList.value = response?.items || [];
    console.log('获取设备列表成功，数量:', deviceList.value.length);
    console.log('设备列表内容:', deviceList.value);

    // 检查第一个设备的数据结构
    if (deviceList.value.length > 0) {
      console.log('第一个设备的数据结构:', deviceList.value[0]);
      console.log('第一个设备的所有字段:', Object.keys(deviceList.value[0]));
    }
  } catch (error) {
    console.error('获取设备列表失败:', error);
    deviceList.value = [];
  } finally {
    deviceLoading.value = false;
  }
}

// 项目选择变化处理
function handleProjectChange(value: any) {
  const projectId = Number(value);
  console.log('选择项目:', projectId);
  selectedDeviceId.value = undefined; // 重置设备选择
  deviceList.value = []; // 清空设备列表
  if (projectId) {
    fetchDeviceList(projectId);
  }
}

// 设备选择变化处理
function handleDeviceChange(value: any) {
  const deviceId = Number(value);
  console.log('选择设备:', deviceId);
  // 这里可以根据选择的设备更新面板配置
}

// 打开属性选择弹窗
function handleSelectProperty() {
  console.log('打开属性选择弹窗');

  // 检查是否已选择设备
  if (!selectedDeviceId.value) {
    console.error('未选择设备，无法获取属性列表');
    return;
  }

  // 获取当前选择的设备信息
  const currentDevice = deviceList.value.find(d => d.ppId === selectedDeviceId.value);
  if (!currentDevice || !currentDevice.productKey) {
    console.error('未找到当前设备或productKey');
    return;
  }

  console.log('使用当前选择设备的productKey:', currentDevice.productKey);

  // 重置选择状态
  selectedProperties.value = [];
  propertyOptions.value = [];

  // 打开弹窗
  propertySelectVisible.value = true;

  // 获取产品模型列表
  fetchProductModelList(currentDevice.productKey);
}

// 确认属性选择
function handlePropertyConfirm() {
  console.log('确认选择属性:', selectedProperties.value);

  if (!selectedProperties.value || selectedProperties.value.length === 0) {
    console.warn('未选择属性');
    return;
  }

  // 找到选中的属性详细信息
  const selectedPropertiesInfo = propertyOptions.value.filter(p =>
    selectedProperties.value.includes(p.value)
  );
  console.log('选中的属性信息:', selectedPropertiesInfo);

  // 将选中的属性信息保存到当前组件配置中
  if (selectedComponent.value) {
    selectedComponent.value.selectedProperties = selectedPropertiesInfo;

    // 将属性信息存储到组件的attributes配置中
    updateComponentAttributes(selectedComponent.value, selectedPropertiesInfo);

    console.log('属性已保存到组件配置');

    // 如果组件还未添加到右侧卡片，则添加
    if (!isComponentAdded.value) {
      addedComponents.value.push({
        ...selectedComponent.value
      });

      // 标记组件已添加
      isComponentAdded.value = true;

      console.log('组件已添加到右侧卡片，当前组件数量:', addedComponents.value.length);
    } else {
      // 如果组件已存在，更新右侧卡片中对应组件的属性
      const existingComponentIndex = addedComponents.value.findIndex(
        comp => comp.addedAt === selectedComponent.value.addedAt
      );

      if (existingComponentIndex !== -1) {
        addedComponents.value[existingComponentIndex].selectedProperties = selectedPropertiesInfo;
        // 同时更新右侧卡片中组件的attributes配置
        updateComponentAttributes(addedComponents.value[existingComponentIndex], selectedPropertiesInfo);
        console.log('已更新右侧卡片中组件的属性配置');
      }
    }

    console.log('组件配置完成:', selectedComponent.value);
  }

  // 关闭弹窗
  propertySelectVisible.value = false;
}

// 取消属性选择
function handlePropertyCancel() {
  console.log('取消属性选择');
  propertySelectVisible.value = false;
  selectedProperties.value = [];
}

// 刷新组件设置模态框 - 重置到初始状态
async function refreshComponentPropertyModal() {
  console.log('🔄 [模态框重置] 重置组件设置模态框到初始状态');

  try {
    // 设置加载状态，触发刷新动画
    propertyLoading.value = true;

    // 添加短暂延迟，让用户看到刷新动画
    await new Promise(resolve => setTimeout(resolve, 300));

    // 重置所有相关状态到初始状态
    console.log('🔄 [模态框重置] 重置所有状态...');

    // 1. 重置模态框选择状态
    selectedProperties.value = [];
    propertyOptions.value = [];

    // 2. 重置组件设置相关状态（参考父组件刷新逻辑）
    selectedComponent.value = null;
    addedComponents.value = [];
    rightPanelTitle.value = '全局设置';

    // 3. 重置全局设置
    globalSettings.value = {
      page_bg_mode: 'color', // 背景色模式，默认为纯色
      bgcolor: '#FFFFFF' // 纯色配置，默认为白色
    };

    // 4. 重置预览设置
    previewSettings.value = {
      card_items_cols: 3, // 宽卡片属性列数，对应接口字段
      card_items_thin_cols: 2, // 窄卡片属性列数，对应接口字段
      card_items_align: 'center', // 对齐方式，对应接口字段
      card_items_style: 1, // 布局样式，对应接口字段（1：属性名称在上，2：属性名称在下）
      card_items_gap_x: 10, // 属性水平间距，对应接口字段
      card_items_gap_y: 1, // 属性垂直间距，对应接口字段
      card_items_padding: 0, // 总体内间距，对应接口字段
      card_attributes: [] as any[] // 预览面板选择的属性，对应接口字段
    };
    previewActivePropertyKeys.value = [];

    // 5. 如果有选择的设备，重新获取属性数据
    if (selectedDeviceId.value) {
      const currentDevice = deviceList.value.find(d => d.ppId === selectedDeviceId.value);
      if (currentDevice && currentDevice.productKey) {
        console.log('🔄 [模态框重置] 重新获取属性数据，productKey:', currentDevice.productKey);
        await fetchProductModelList(currentDevice.productKey);
      }
    }

    console.log('✅ [模态框重置] 组件设置模态框已完全重置到初始状态');

  } catch (error) {
    console.error('❌ [模态框重置] 重置失败:', error);
  } finally {
    // 确保加载状态至少持续一段时间，让动画完整播放
    setTimeout(() => {
      propertyLoading.value = false;
    }, 200);
  }
}

// 处理右侧卡片中组件的点击事件
function handleComponentClick(component: any) {
  console.log('🔍 [组件点击] 点击已配置的组件:', component.componentName, component);

  // 设置当前选中的组件
  selectedComponent.value = component;
  isComponentAdded.value = true; // 标记组件已添加
  rightPanelTitle.value = '组件设置';

  // 恢复已配置的属性到选中状态
  if (component.selectedProperties && component.selectedProperties.length > 0) {
    console.log('🔍 [组件点击] 恢复allAttributes到选中状态:', component.selectedProperties);

    // 直接将allAttributes中的modelKey设置为选中状态
    selectedProperties.value = component.selectedProperties.map((attr: any) => attr.modelKey);

    console.log('🔍 [组件点击] 设置选中的属性keys:', selectedProperties.value);
  } else {
    console.log('🔍 [组件点击] 组件暂无配置的属性');
    selectedProperties.value = [];
  }

  console.log('✅ [组件点击] 切换到组件设置面板，显示已配置的组件信息');
}

// 删除组件
function handleDeleteComponent() {
  if (!selectedComponent.value) {
    console.warn('没有选中的组件');
    return;
  }

  console.log('删除组件:', selectedComponent.value.componentName);

  // 从右侧卡片中移除组件
  const componentIndex = addedComponents.value.findIndex(
    comp => comp.addedAt === selectedComponent.value.addedAt
  );

  if (componentIndex !== -1) {
    addedComponents.value.splice(componentIndex, 1);
    console.log('组件已从右侧卡片中移除，剩余组件数量:', addedComponents.value.length);
  }

  // 重置选中状态
  selectedComponent.value = null;
  isComponentAdded.value = false;
  rightPanelTitle.value = '全局设置';

  console.log('组件删除完成，返回全局设置面板');
}

// 更新属性字段
function updatePropertyField(index: number, field: string, value: string) {
  if (!selectedComponent.value || !selectedComponent.value.selectedProperties) {
    console.warn('没有选中的组件或属性');
    return;
  }

  console.log('更新属性字段:', { index, field, value });

  // 直接更新属性字段
  selectedComponent.value.selectedProperties[index][field] = value;

  // 如果是图标字段，立即保存变更
  if (field === 'icon') {
    savePropertyChanges(index);
  }
}

// 保存属性变更
function savePropertyChanges(index: number) {
  console.log('保存属性变更:', index);

  if (!selectedComponent.value || !selectedComponent.value.selectedProperties) {
    console.warn('没有选中的组件或属性');
    return;
  }

  // 重新更新组件配置
  updateComponentAttributes(selectedComponent.value, selectedComponent.value.selectedProperties);

  // 如果组件已添加到右侧卡片，也要更新右侧卡片中的组件
  if (isComponentAdded.value) {
    const existingComponentIndex = addedComponents.value.findIndex(
      comp => comp.addedAt === selectedComponent.value.addedAt
    );

    if (existingComponentIndex !== -1) {
      addedComponents.value[existingComponentIndex].selectedProperties = selectedComponent.value.selectedProperties;
      updateComponentAttributes(addedComponents.value[existingComponentIndex], selectedComponent.value.selectedProperties);
    }
  }

  console.log('属性变更保存完成');
}

// 处理背景色模式变化
function handleBackgroundModeChange(value: any) {
  console.log('背景色模式变化:', value);
  globalSettings.value.page_bg_mode = value as string;

  // 这里可以添加后续的处理逻辑
  // 比如保存到后端、更新页面样式等
  console.log('当前全局设置:', globalSettings.value);
}

// 打开颜色选择器
function openColorPicker() {
  console.log('打开颜色选择器');

  // 由于我们现在使用了覆盖层的方式，点击事件会自动传递到input元素
  // 这个函数主要用于调试
  if (colorInputRef.value) {
    console.log('颜色选择器引用存在');
    colorInputRef.value.click();
  } else {
    console.error('颜色选择器引用未找到');
  }
}

// 处理颜色选择器变化
function handleColorPickerChange(event: Event) {
  const target = event.target as HTMLInputElement;
  const color = target.value;
  console.log('颜色选择器变化:', color);

  globalSettings.value.bgcolor = color;
  console.log('当前全局设置:', globalSettings.value);
}

// 处理颜色输入框变化
function handleColorChange() {
  console.log('颜色输入框变化:', globalSettings.value.bgcolor);

  // 验证颜色格式
  if (isValidHexColor(globalSettings.value.bgcolor)) {
    console.log('颜色格式有效:', globalSettings.value.bgcolor);
    console.log('当前全局设置:', globalSettings.value);
  } else {
    console.warn('颜色格式无效:', globalSettings.value.bgcolor);
  }
}

// 验证十六进制颜色格式
function isValidHexColor(color: string): boolean {
  const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return hexColorRegex.test(color);
}

// 增加数值
function incrementValue(field: string) {
  const currentValue = previewSettings.value[field as keyof typeof previewSettings.value] as number;
  const maxValue = 50;
  if (currentValue < maxValue) {
    (previewSettings.value[field as keyof typeof previewSettings.value] as number) = currentValue + 1;
  }
  console.log(`${field} 增加到:`, previewSettings.value[field as keyof typeof previewSettings.value]);
}

// 减少数值
function decrementValue(field: string) {
  const currentValue = previewSettings.value[field as keyof typeof previewSettings.value] as number;
  const minValue = 0;
  if (currentValue > minValue) {
    (previewSettings.value[field as keyof typeof previewSettings.value] as number) = currentValue - 1;
  }
  console.log(`${field} 减少到:`, previewSettings.value[field as keyof typeof previewSettings.value]);
}

// 打开预览面板属性选择弹窗
function handlePreviewSelectProperty() {
  console.log('打开预览面板属性选择弹窗');

  // 检查是否已选择设备
  if (!selectedDeviceId.value) {
    console.error('未选择设备，无法获取属性列表');
    return;
  }

  // 获取当前选择的设备信息
  const currentDevice = deviceList.value.find(d => d.ppId === selectedDeviceId.value);
  if (!currentDevice || !currentDevice.productKey) {
    console.error('未找到当前设备或productKey');
    return;
  }

  console.log('预览面板使用当前选择设备的productKey:', currentDevice.productKey);

  // 重置选择状态
  previewSelectedProperties.value = [];
  previewPropertyOptions.value = [];

  // 打开弹窗
  previewPropertySelectVisible.value = true;

  // 获取产品模型列表并转换为下拉选项
  fetchPreviewPropertyOptions(currentDevice.productKey);
}

// 获取预览面板属性选项
async function fetchPreviewPropertyOptions(productKey: string) {
  try {
    propertyLoading.value = true;
    console.log('获取预览面板属性选项:', productKey);

    const response = await requestClient.get('/device/iotProductModel/list', {
      params: {
        productKey: productKey,
        page: 1,
        pageSize: 1000
      }
    });

    console.log('预览面板属性选项响应:', response);

    // 根据API返回结构获取数据
    const items = response?.items || [];

    if (items.length > 0) {
      // 转换为下拉选项格式
      previewPropertyOptions.value = items.map((item: any) => ({
        label: item.modelName,
        value: item.modelKey,
        modelName: item.modelName,
        icon: item.icon || '',
        // 保存完整的模型信息
        ...item
      }));

      console.log('预览面板下拉选项:', previewPropertyOptions.value);
    } else {
      previewPropertyOptions.value = [];
      console.warn('预览面板未获取到属性数据');
    }
  } catch (error) {
    console.error('预览面板获取属性选项失败:', error);
    previewPropertyOptions.value = [];
  } finally {
    propertyLoading.value = false;
  }
}

// 属性选项过滤函数
function filterPropertyOption(input: string, option: any) {
  const searchText = input.toLowerCase();
  const label = (option.label || '').toLowerCase();
  const value = (option.value || '').toLowerCase();
  return label.includes(searchText) || value.includes(searchText);
}

// 确认预览面板属性选择
function handlePreviewPropertyConfirm() {
  console.log('确认预览面板属性选择:', previewSelectedProperties.value);

  // 根据选择的属性value，从previewPropertyOptions中获取完整的属性信息
  const selectedAttributes = previewPropertyOptions.value.filter(option =>
    previewSelectedProperties.value.includes(option.value)
  ).map(option => ({
    modelKey: option.value,           // 属性键值
    modelName: option.modelName,      // 模型名称
    name: option.label,               // 显示名称
    icon: option.icon || ''           // 图标
  }));

  // 存储到预览面板设置的card_attributes字段
  previewSettings.value.card_attributes = selectedAttributes;

  console.log('预览面板card_attributes更新:', previewSettings.value.card_attributes);
  console.log('存储的字段包含:', selectedAttributes.map(attr => ({
    modelKey: attr.modelKey,
    modelName: attr.modelName,
    name: attr.name,
    icon: attr.icon
  })));

  // 验证每个属性是否包含所有必需字段
  selectedAttributes.forEach((attr, index) => {
    console.log(`属性${index + 1}:`, {
      modelKey: attr.modelKey,
      modelName: attr.modelName,
      name: attr.name,
      icon: attr.icon,
      '字段完整性': {
        hasModelKey: !!attr.modelKey,
        hasModelName: !!attr.modelName,
        hasName: !!attr.name,
        hasIcon: attr.icon !== undefined
      }
    });
  });

  // 关闭弹窗
  previewPropertySelectVisible.value = false;
}

// 取消预览面板属性选择
function handlePreviewPropertyCancel() {
  console.log('取消预览面板属性选择');
  previewPropertySelectVisible.value = false;
  // 重置选择状态
  previewSelectedProperties.value = [];
}

// 刷新预览面板模态框 - 重置到初始状态
async function refreshPreviewPropertyModal() {
  console.log('🔄 [模态框重置] 重置预览面板模态框到初始状态');

  try {
    // 设置加载状态，触发刷新动画
    propertyLoading.value = true;

    // 添加短暂延迟，让用户看到刷新动画
    await new Promise(resolve => setTimeout(resolve, 300));

    // 重置所有相关状态到初始状态
    console.log('🔄 [模态框重置] 重置预览面板所有状态...');

    // 1. 重置预览模态框选择状态
    previewSelectedProperties.value = [];
    previewPropertyOptions.value = [];

    // 2. 重置预览面板相关状态（参考父组件刷新逻辑）
    previewSettings.value = {
      card_items_cols: 3, // 宽卡片属性列数，对应接口字段
      card_items_thin_cols: 2, // 窄卡片属性列数，对应接口字段
      card_items_align: 'center', // 对齐方式，对应接口字段
      card_items_style: 1, // 布局样式，对应接口字段（1：属性名称在上，2：属性名称在下）
      card_items_gap_x: 10, // 属性水平间距，对应接口字段
      card_items_gap_y: 1, // 属性垂直间距，对应接口字段
      card_items_padding: 0, // 总体内间距，对应接口字段
      card_attributes: [] as any[] // 预览面板选择的属性，对应接口字段
    };
    previewActivePropertyKeys.value = [];

    // 3. 如果有选择的设备，重新获取预览属性数据
    if (selectedDeviceId.value) {
      const currentDevice = deviceList.value.find(d => d.ppId === selectedDeviceId.value);
      if (currentDevice && currentDevice.productKey) {
        console.log('🔄 [模态框重置] 重新获取预览属性数据，productKey:', currentDevice.productKey);
        await fetchPreviewPropertyOptions(currentDevice.productKey);
      }
    }

    console.log('✅ [模态框重置] 预览面板模态框已完全重置到初始状态');

  } catch (error) {
    console.error('❌ [模态框重置] 预览面板重置失败:', error);
  } finally {
    // 确保加载状态至少持续一段时间，让动画完整播放
    setTimeout(() => {
      propertyLoading.value = false;
    }, 200);
  }
}

// 清空预览面板所有属性
function clearPreviewProperties() {
  previewSettings.value.card_attributes = [];
}

// 删除预览面板单个属性
function removePreviewProperty(index: number) {
  console.log('删除预览面板属性，索引:', index);
  if (index >= 0 && index < previewSettings.value.card_attributes.length) {
    const removedProperty = previewSettings.value.card_attributes[index];
    previewSettings.value.card_attributes.splice(index, 1);
    console.log('已删除属性:', removedProperty);
    console.log('剩余属性:', previewSettings.value.card_attributes);
  }
}

// 更新预览面板属性字段
function updatePreviewPropertyField(index: number, field: string, value: string) {
  if (!previewSettings.value.card_attributes || index < 0 || index >= previewSettings.value.card_attributes.length) {
    console.warn('预览面板属性索引无效或属性列表为空');
    return;
  }

  console.log('更新预览面板属性字段:', { index, field, value });

  // 直接更新属性字段
  previewSettings.value.card_attributes[index][field] = value;

  // 如果是图标字段，立即保存变更
  if (field === 'icon') {
    savePreviewPropertyChanges(index);
  }
}

// 保存预览面板属性变更
function savePreviewPropertyChanges(index: number) {
  console.log('保存预览面板属性变更:', index);

  if (!previewSettings.value.card_attributes || index < 0 || index >= previewSettings.value.card_attributes.length) {
    console.warn('预览面板属性索引无效或属性列表为空');
    return;
  }

  console.log('预览面板属性变更保存完成:', previewSettings.value.card_attributes[index]);
}

// 调试函数：显示当前预览面板的 card_attributes
function debugPreviewCardAttributes() {
  console.log('=== 预览面板 card_attributes 调试信息 ===');
  console.log('当前 card_attributes 数量:', previewSettings.value.card_attributes.length);

  previewSettings.value.card_attributes.forEach((attr, index) => {
    console.log(`属性 ${index + 1}:`, {
      modelKey: attr.modelKey,
      modelName: attr.modelName,
      name: attr.name,
      icon: attr.icon,
      '完整对象': attr
    });
  });

  console.log('完整 previewSettings:', previewSettings.value);
  console.log('=== 调试信息结束 ===');
}

// 暴露调试函数到全局（开发时使用）
if (typeof window !== 'undefined') {
  (window as any).debugPreviewCardAttributes = debugPreviewCardAttributes;
}

// 更新组件的attributes配置
function updateComponentAttributes(component: any, selectedProperties: any[]) {
  console.log('更新组件attributes配置:', component.componentName, selectedProperties);

  // 确保组件有componentConfigObj
  if (!component.componentConfigObj) {
    console.warn('组件缺少componentConfigObj，无法更新attributes');
    return;
  }

  // 确保有options.config结构
  if (!component.componentConfigObj.options) {
    component.componentConfigObj.options = {};
  }
  if (!component.componentConfigObj.options.config) {
    component.componentConfigObj.options.config = {};
  }

  // 将选择的属性转换为attributes格式
  const attributes = selectedProperties.map(property => ({
    modelKey: property.modelKey,
    modelName: property.modelName,
    name: property.name || property.label, // 使用name字段而不是displayName
    icon: property.icon || ''
  }));

  // 1. 更新componentConfigObj中的attributes
  component.componentConfigObj.options.config.attributes = attributes;

  // 2. 更新attrConfigObj中的属性信息
  // 确保attrConfigObj存在
  if (!component.attrConfigObj) {
    component.attrConfigObj = {};
  }

  // 将属性信息存储到attrConfigObj中
  if (attributes.length > 0) {
    // 存储第一个属性的信息到attrConfigObj
    component.attrConfigObj!.modelKey = attributes[0].modelKey;
    component.attrConfigObj!.modelName = attributes[0].modelName;
    component.attrConfigObj!.name = attributes[0].name; // 使用name字段
    component.attrConfigObj!.icon = attributes[0].icon;

    // 如果有多个属性，也可以存储所有属性
    component.attrConfigObj!.allAttributes = attributes;
  }

  // 3. 同时更新componentConfig字符串（如果需要保持同步）
  try {
    component.componentConfig = JSON.stringify(component.componentConfigObj);
  } catch (error) {
    console.error('更新componentConfig字符串失败:', error);
  }

  // 4. 同时更新attrConfig字符串（如果需要保持同步）
  try {
    component.attrConfig = JSON.stringify(component.attrConfigObj);
  } catch (error) {
    console.error('更新attrConfig字符串失败:', error);
  }

  console.log('组件attributes配置已更新:', {
    componentName: component.componentName,
    attributes: attributes,
    componentConfigObj: component.componentConfigObj,
    attrConfigObj: component.attrConfigObj
  });
}

// 鼠标进入右侧卡片
function handleRightCardMouseEnter() {
  isMouseOverRightCard.value = true;
  console.log('鼠标进入右侧卡片区域');
}

// 鼠标离开右侧卡片
function handleRightCardMouseLeave() {
  isMouseOverRightCard.value = false;
  console.log('鼠标离开右侧卡片区域');
}

// 获取设备详情API
async function fetchDeviceDetail(ppId: number) {
  if (!ppId || ppId === 0) {
    console.log('⚠️ [设备详情] ppId 无效，跳过获取设备详情');
    return;
  }

  try {
    deviceDetailLoading.value = true;
    console.log('🔍 [设备详情] 开始获取设备详情，ppId:', ppId);

    const response = await ViewDevice(ppId);
    console.log('✅ [设备详情] 设备详情获取成功:', response);

    // 检查响应数据结构
    console.log('🔍 [设备详情] 检查响应结构:');
    console.log('🔍 [设备详情] response:', response);
    console.log('🔍 [设备详情] response.data:', response.data);
    console.log('🔍 [设备详情] response直接包含数据:', !!response.ppId);

    // 判断数据在response还是response.data中
    const deviceData = response.data || response;
    console.log('🔍 [设备详情] 使用的数据源:', deviceData);

    if (deviceData) {
      deviceDetail.value = deviceData;

      console.log('🔍 [设备详情] 检查数据结构:');
      console.log('🔍 [设备详情] deviceData.panelConfig (字符串):', deviceData.panelConfig);
      console.log('🔍 [设备详情] deviceData.panelConfigObj (对象):', deviceData.panelConfigObj);
      console.log('🔍 [设备详情] panelConfigObj存在:', !!deviceData.panelConfigObj);
      console.log('🔍 [设备详情] widgets存在:', !!deviceData.panelConfigObj?.widgets);
      console.log('🔍 [设备详情] widgets数量:', deviceData.panelConfigObj?.widgets?.length || 0);

      // 从设备详情中提取已配置的组件
      console.log('🔍 [设备详情] 检查widgets条件:');
      console.log('🔍 [设备详情] deviceData.panelConfigObj 存在:', !!deviceData.panelConfigObj);
      console.log('🔍 [设备详情] deviceData.panelConfigObj.widgets 存在:', !!deviceData.panelConfigObj?.widgets);
      console.log('🔍 [设备详情] deviceData.panelConfigObj.widgets 长度:', deviceData.panelConfigObj?.widgets?.length || 0);

      if (deviceData.panelConfigObj && deviceData.panelConfigObj.widgets && deviceData.panelConfigObj.widgets.length > 0) {
        const widgets = deviceData.panelConfigObj.widgets;
        console.log('📋 [设备详情] 提取到的组件列表:', widgets);
        console.log('📋 [设备详情] widgets数组长度:', widgets.length);

        // 简化：直接将widgets转换为addedComponents格式
        addedComponents.value = widgets.map((widget: any, index: number) => {
          console.log(`📋 [设备详情] 处理组件 ${index + 1}:`, widget.widget_id);

          // 1. 用widget_id匹配左侧组件列表的componentKey
          const matchedComponent = componentList.value.find(comp => comp.componentKey === widget.widget_id);

          if (matchedComponent) {
            console.log(`✅ [设备详情] 找到匹配组件:`, matchedComponent.componentName);

            // 2. 提取allAttributes作为selectedProperties
            const allAttributes = widget.options?.config?.attributes?.[0]?.allAttributes || [];
            console.log(`📋 [设备详情] 提取allAttributes:`, allAttributes);

            // 3. 创建组件对象，使用匹配组件的信息
            return {
              ...matchedComponent, // 使用左侧组件的完整信息（包括图片）
              widget_id: widget.widget_id,
              selectedProperties: allAttributes, // 使用allAttributes作为已选属性
              originalWidget: widget, // 保存原始widget数据
              addedAt: Date.now() + index
            };
          } else {
            console.warn(`⚠️ [设备详情] 未找到匹配的组件:`, widget.widget_id);
            // 如果没找到匹配的组件，创建一个基本的组件对象
            return {
              componentId: widget.widget_id,
              componentKey: widget.widget_id,
              componentName: widget.widget_id,
              imgUrl: `/components/${widget.widget_id}.png`,
              widget_id: widget.widget_id,
              selectedProperties: widget.options?.config?.attributes?.[0]?.allAttributes || [],
              originalWidget: widget,
              addedAt: Date.now() + index
            };
          }
        });

        console.log('✅ [设备详情] 组件列表转换完成，组件数量:', addedComponents.value.length);
        console.log('✅ [设备详情] 最终的addedComponents:', addedComponents.value);

        // 立即检查组件是否正确设置
        console.log('🔍 [设备详情] 立即检查addedComponents状态:');
        console.log('🔍 [设备详情] addedComponents.value.length:', addedComponents.value.length);
        addedComponents.value.forEach((comp, index) => {
          console.log(`🔍 [设备详情] 组件${index + 1}:`, {
            componentName: comp.componentName,
            imgUrl: comp.imgUrl,
            widget_id: comp.widget_id
          });
        });

        // 不需要从组件中提取属性到card_attributes
        // card_attributes 和 widgets 是独立的数据源

        // 强制触发Vue的响应式更新
        addedComponents.value = [...addedComponents.value];
        console.log('📋 [设备详情] 最终的 addedComponents 数据:', addedComponents.value);

        // 详细检查每个组件的数据
        addedComponents.value.forEach((comp, index) => {
          console.log(`📋 [设备详情] 组件 ${index + 1} 详情:`, {
            componentId: comp.componentId,
            componentName: comp.componentName,
            widget_id: comp.widget_id,
            imgUrl: comp.imgUrl,
            hasOptions: !!comp.options,
            hasOriginalWidget: !!comp.originalWidget
          });
        });

        // 强制触发响应式更新
        console.log('🔄 [设备详情] 强制触发响应式更新...');
        nextTick(() => {
          console.log('🔄 [设备详情] nextTick 执行，当前组件数量:', addedComponents.value.length);
        });
      } else {
        console.log('⚠️ [设备详情] 设备暂无配置的组件或数据结构异常');
        console.log('⚠️ [设备详情] panelConfigObj:', deviceData.panelConfigObj);
        console.log('⚠️ [设备详情] widgets:', deviceData.panelConfigObj?.widgets);
        console.log('⚠️ [设备详情] 保持当前addedComponents不变，数量:', addedComponents.value.length);
        // 不重置 addedComponents，保持当前状态
      }

      // 同步全局设置和预览设置
      if (deviceData.panelConfigObj) {
        console.log('📋 [设备详情] 开始同步配置数据...');

        if (deviceData.panelConfigObj.global_config) {
          const oldGlobalSettings = { ...globalSettings.value };
          globalSettings.value = {
            ...globalSettings.value,
            ...deviceData.panelConfigObj.global_config
          };
          console.log('✅ [设备详情] 全局设置已同步');
          console.log('📋 [设备详情] 全局设置变化:', {
            old: oldGlobalSettings,
            new: globalSettings.value
          });
        }

        if (deviceData.panelConfigObj.preview) {
          const oldPreviewSettings = { ...previewSettings.value };

          // 先处理card_attributes的数据格式问题
          let finalCardAttributes: any[] = [];

          if (deviceData.panelConfigObj.preview.card_attributes && Array.isArray(deviceData.panelConfigObj.preview.card_attributes)) {
            console.log('📋 [设备详情] 原始card_attributes:', deviceData.panelConfigObj.preview.card_attributes);

            deviceData.panelConfigObj.preview.card_attributes.forEach((attr: any) => {
              try {
                if (typeof attr === 'string') {
                  // 如果是JSON字符串，解析它
                  const parsedAttr = JSON.parse(attr);
                  finalCardAttributes.push(parsedAttr);
                  console.log('📋 [设备详情] 解析JSON字符串属性:', parsedAttr);
                } else if (typeof attr === 'object') {
                  // 如果已经是对象，直接使用
                  finalCardAttributes.push(attr);
                  console.log('📋 [设备详情] 使用对象属性:', attr);
                }
              } catch (error) {
                console.warn('📋 [设备详情] 解析属性失败:', attr, error);
              }
            });
          }

          // 不要从组件中提取属性来覆盖card_attributes
          // card_attributes 和 widgets 是独立的数据源

          // 同步预览设置，但排除card_attributes
          const { card_attributes, ...otherPreviewSettings } = deviceData.panelConfigObj.preview;
          previewSettings.value = {
            ...previewSettings.value,
            ...otherPreviewSettings,
            card_attributes: finalCardAttributes // 使用处理后的card_attributes
          };

          console.log('✅ [设备详情] 最终card_attributes:', finalCardAttributes);

          // 🔧 设置预览面板的选择状态
          // 基于card_attributes设置选中状态
          if (finalCardAttributes.length > 0) {
            previewSelectedProperties.value = finalCardAttributes.map((attr: any) => attr.modelKey);
            console.log('✅ [设备详情] 预览面板选中属性:', previewSelectedProperties.value);
          } else {
            previewSelectedProperties.value = [];
            console.log('✅ [设备详情] 预览面板无选中属性');
          }

          console.log('✅ [设备详情] 预览设置已同步');
          console.log('📋 [设备详情] 预览设置变化:', {
            old: oldPreviewSettings,
            new: previewSettings.value
          });
          console.log('📋 [设备详情] 最终card_attributes数量:', previewSettings.value.card_attributes.length);

          // 详细检查每个属性
          previewSettings.value.card_attributes.forEach((attr, index) => {
            console.log(`📋 [设备详情] 属性${index + 1}:`, {
              modelKey: attr.modelKey,
              modelName: attr.modelName,
              name: attr.name,
              icon: attr.icon
            });
          });
        }

        console.log('📋 [设备详情] 配置数据同步完成');
      }
    }
  } catch (error) {
    console.error('❌ [设备详情] 获取设备详情失败:', error);
    deviceDetail.value = null;
    addedComponents.value = [];
  } finally {
    deviceDetailLoading.value = false;
  }
}

// 获取产品模型列表API
async function fetchProductModelList(productKey: string) {
  try {
    propertyLoading.value = true;
    console.log('开始获取产品模型列表，productKey:', productKey);

    const response = await requestClient.get('/device/iotProductModel/list', {
      params: {
        productKey: productKey,
        page: 1,
        pageSize: 1000
      }
    });

    console.log('产品模型API响应:', response);

    // 根据API返回结构获取数据
    const items = response?.items || [];

    // 将modelName作为下拉选项
    propertyOptions.value = items.map((item: any) => ({
      label: item.modelName,
      value: item.modelKey,
      ...item // 保留完整的模型信息
    }));

    console.log('获取产品模型成功，数量:', propertyOptions.value.length);
    console.log('模型选项:', propertyOptions.value);

  } catch (error) {
    console.error('获取产品模型列表失败:', error);
    propertyOptions.value = [];
  } finally {
    propertyLoading.value = false;
  }
}

// 暴露方法给父组件
defineExpose({
  open,
  close
});
</script>

<style scoped>
/* 自定义标题栏样式 */
.custom-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.title-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 标题栏选择器样式 */
.title-selector {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 12px;
}

.selector-label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  margin: 0;
}

.close-btn {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.close-btn:hover {
  color: rgba(0, 0, 0, 0.75);
}

/* 面板配置布局样式 */
.panel-container {
  display: flex;
  height: 100%;
  background-color: #f5f5f5;
  position: relative;
  /* 为加载遮罩定位 */
}

/* 左右控制台样式 */
.control-panel {
  width: 250px;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

/* 左侧控制台可点击样式 */
.left-panel {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.left-panel:hover {
  background-color: #f9f9f9;
}

.left-panel {
  border-right: none;
}

.right-panel {
  border-left: none;
}

.panel-title {
  padding: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #000000;
  border-bottom: 1px solid #FFFFFF;
  background-color: #FFFFFF;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 组件列表样式 */
.component-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.component-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.2s ease;
  /* 让卡片高度完全适应图片内容，不限制图片比例 */
  height: auto;
  width: 100%;
}

.component-card:hover {
  background: #f0f0f0;
  border-color: #d9d9d9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.component-image {
  margin-bottom: 8px;
  border-radius: 6px;
  background: #fff;
  border: 1px solid #e8e8e8;
  padding: 4px;
  /* 完全移除尺寸限制，让图片按原始比例显示 */
  display: inline-block;
  /* 改为inline-block，让容器适应图片尺寸 */
}

.component-image img {
  display: block;
  max-width: 160px;
  /* 只限制最大宽度，不限制高度 */
  height: auto;
  /* 高度自动，保持原始比例 */
  /* 完整显示图片，不论是横向还是纵向 */
}

.component-actions {
  width: 100%;
  display: flex;
  justify-content: center;
}

.add-component-btn {
  width: 100%;
  max-width: 120px;
}

/* 右侧卡片中添加的组件样式 */
.added-components-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 100%;
}





/* 加载状态样式 */
.loading-placeholder {
  text-align: center;
  color: #999;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 空状态样式 */
.empty-placeholder {
  text-align: center;
  color: #999;
  padding: 40px 20px;
}

.empty-placeholder p {
  margin: 8px 0;
}



/* 组件图片卡片样式 */
.component-image-card {
  position: relative;
  margin-bottom: 8px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #d9d9d9;
  background: #fff;
}

.component-image-card:hover {
  border-color: #40a9ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.component-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
  margin: 0;
  padding: 0;
  border: none;
}

.delete-btn-overlay {
  position: absolute !important;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 50% !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.component-image-card:hover .delete-btn-overlay {
  opacity: 1;
}

/* 属性列表样式 */
.property-list {
  margin-top: 12px;
}

.property-card {
  margin-bottom: 12px;
}

.property-card:last-child {
  margin-bottom: 0;
}

/* 调试信息样式 */
.debug-info {
  padding: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  margin-top: 8px;
  text-align: center;
  color: #666;
  font-size: 12px;
}

/* 中间被控区域样式 */
.controlled-area {
  flex: 1;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  user-select: none;
  /* 防止拖拽时选中文字 */
}

.controlled-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  /* 改为顶部对齐，避免内容被遮挡 */
  width: 100%;
  min-height: 100%;
  /* 改为最小高度，允许内容超出 */
  padding-top: 60px;
  /* 添加顶部内边距 */
  transition: transform 0.2s ease;
  transform-origin: center center;
}

.controlled-title {
  font-size: 16px;
  color: #ff4d4f;
  font-weight: 500;
  margin-bottom: 20px;
}

/* 卡片容器样式 */
.card-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  /* 顶部对齐，左侧卡片不会跟着右侧卡片变长 */
  width: 80%;
  gap: 20px;
  margin-top: 40px;
  /* 增加顶部间距，确保卡片标题完全显示 */
}

/* 卡片样式 */
.left-card,
.right-card {
  background-color: white;
  width: 45%;
}

/* 左侧卡片 - 固定比例 */
.left-card {
  aspect-ratio: 1 / 2;
  /* 长比高为 1:2 */
  overflow: hidden;
  /* 确保内容不会溢出卡片边界 */
  flex-shrink: 0;
  /* 防止被压缩 */
  height: fit-content;
  /* 高度适应内容，但受aspect-ratio限制 */
}

/* 右侧卡片 - 固定高度与左侧一致 */
.right-card {
  aspect-ratio: 1 / 2;
  /* 与左侧卡片相同的固定比例 */
  overflow: hidden;
  /* 卡片本身不滚动 */
}

/* 左侧卡片特殊样式 - 图片铺满 */
.left-card :deep(.ant-card-body) {
  padding: 0 !important;
  /* 移除所有内边距 */
  height: 100%;
}

/* 右侧卡片特殊样式 */
.right-card :deep(.ant-card-body) {
  height: calc(100% - 57px) !important;
  /* 减去header高度 */
  overflow-y: auto !important;
  /* 允许垂直滚动 */
  overflow-x: hidden !important;
  /* 隐藏水平滚动 */
}

/* Card组件内部样式调整 */
.left-card :deep(.ant-card-head),
.right-card :deep(.ant-card-head) {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.left-card :deep(.ant-card-head-title),
.right-card :deep(.ant-card-head-title) {
  font-size: 14px;
  font-weight: 500;
}

/* 可点击卡片样式 */
.clickable-card {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.clickable-card:active {
  transform: translateY(0);
}

.left-card :deep(.ant-card-body),
.right-card :deep(.ant-card-body) {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

/* 图片容器样式 */
.card-image-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* 隐藏超出部分 */
}

/* 图片样式 - 铺满卡片 */
.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* 铺满容器，可能会裁剪图片 */
  display: block;
}

/* 全屏模态框样式 */
:deep(.fullscreen-modal) {
  .ant-modal {
    max-width: 100vw !important;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0 !important;
    top: 0 !important;
    padding-bottom: 0 !important;
  }

  .ant-modal-content {
    height: 100vh !important;
    border-radius: 0 !important;
  }

  .ant-modal-header {
    border-radius: 0 !important;
    padding: 16px 24px !important;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-modal-body {
    height: calc(100vh - 55px) !important;
    padding: 24px !important;
    overflow: auto !important;
  }

  /* 隐藏底部按钮区域 */
  .ant-modal-footer {
    display: none !important;
  }

  /* 隐藏默认关闭按钮 */
  .ant-modal-close {
    display: none !important;
  }
}

/* 自定义加载样式 */
.panel-container :deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.9) !important;
}

.panel-container :deep(.el-loading-text) {
  color: #1890ff !important;
  font-size: 14px !important;
  margin-top: 12px !important;
}

.panel-container :deep(.el-loading-spinner) {
  color: #1890ff !important;
}

/* 组件设置面板样式 */
.component-settings {
  padding: 16px 0;
}

/* 组件状态样式 */
.component-status {
  margin-bottom: 20px;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.status-pending {
  background: #fff7e6;
  border-color: #ffd591;
  color: #d46b08;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-completed {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #389e0d;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

.setting-item {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.setting-value {
  font-size: 14px;
  color: #666;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.select-property-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.select-property-btn {
  align-self: flex-start;
}

.select-property-tip {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.select-property-warning {
  font-size: 12px;
  color: #fa8c16;
  background: #fff7e6;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ffd591;
}

/* 组件操作按钮样式 */
.component-actions {
  display: flex;
  gap: 8px;
}

.delete-component-btn {
  font-size: 14px;
}

/* 全局设置样式 */
.global-settings {
  padding: 16px 0;
}

.global-settings .setting-item {
  margin-bottom: 16px;
}

.global-settings .setting-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

/* 颜色选择器样式 */
.color-picker-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-preview-wrapper {
  position: relative;
  width: 32px;
  height: 32px;
}

.color-picker-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  border: none;
  padding: 0;
}

.color-display {
  width: 100%;
  height: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.2s ease;
  pointer-events: none;
}

.color-preview-wrapper:hover .color-display {
  border-color: #1890ff;
}

.color-input {
  flex: 1;
}

/* 预览面板样式 */
.preview-panel {
  padding: 16px 0;
}

.preview-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 6px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.help-icon {
  font-size: 16px;
  color: #999;
  cursor: help;
}

.select-property-btn {
  width: 100%;
  height: 32px;
  border-radius: 6px;
}

.select-property-warning {
  margin-top: 8px;
  padding: 8px 12px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
  color: #d46b08;
  font-size: 12px;
  line-height: 1.4;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin: 0;
}

.radio-item input[type="radio"] {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  accent-color: #1890ff;
}

.radio-label {
  font-size: 14px;
  color: #333;
  user-select: none;
}

/* 滑块容器样式 */
.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-wrapper {
  flex: 1;
}

.slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #f0f0f0;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #1890ff;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #1890ff;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.input-number-wrapper {
  position: relative;
  width: 60px;
}

.number-input {
  width: 100%;
  height: 32px;
  padding: 4px 20px 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
  outline: none;
}

.number-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.input-controls {
  position: absolute;
  right: 2px;
  top: 2px;
  bottom: 2px;
  display: flex;
  flex-direction: column;
  width: 16px;
}

.control-btn {
  flex: 1;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 10px;
  padding: 0;
  transition: color 0.2s ease;
}

.control-btn:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.control-btn:first-child {
  border-radius: 0 4px 0 0;
}

.control-btn:last-child {
  border-radius: 0 0 4px 0;
}

/* 预览面板属性选择弹窗样式 */
.property-select-modal-content {
  padding: 16px 0;
}

.property-select-form {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.loading-tip {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 16px 0;
}

.empty-tip {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 16px 0;
}

/* 预览面板属性设置样式 */
.preview-setting-item {
  margin-bottom: 16px;
}

.preview-setting-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}



/* 预览面板属性头部样式 */
.preview-property-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 8px;
}

.preview-property-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.preview-property-icon {
  font-size: 16px;
  color: #1890ff;
  margin-left: 8px;
}

.preview-custom-icon {
  font-size: 16px;
}

/* 预览面板属性编辑内容样式 */
.preview-property-edit-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-edit-form-item {
  width: 100%;
}

.preview-edit-form-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}



.no-component {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 40px 20px;
}

/* 已选属性样式 */
.selected-properties {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.property-tag {
  display: inline-block;
  padding: 4px 8px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  color: #1890ff;
  font-size: 12px;
  line-height: 1.4;
}





/* 属性头部样式 */
.property-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.property-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.property-icon {
  display: flex;
  align-items: center;
}

.custom-icon {
  font-size: 18px;
  color: #1890ff;
}

/* 属性编辑内容样式 */
.property-edit-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.edit-form-item {
  width: 100%;
}

.edit-form-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

/* 属性选择弹窗样式 */
.property-select-content {
  padding: 20px 0;
}

.property-select-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.property-select-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 刷新按钮动画样式 */
.refresh-button {
  transition: all 0.3s ease;
}

.refresh-button:hover {
  background-color: #f0f0f0 !important;
}

.refresh-icon {
  transition: transform 0.3s ease;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 加载状态下的刷新图标 */
.refresh-button.loading .refresh-icon {
  animation: spin 1s linear infinite;
}
</style>
