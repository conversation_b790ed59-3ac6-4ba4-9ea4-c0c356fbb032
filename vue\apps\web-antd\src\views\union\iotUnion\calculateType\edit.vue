<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[800px]">
    <BasicForm>
      <template #computed>
        <Card style="width: 100%;">
          <Space direction="vertical">
            <RadioGroup v-model:value="cycleConfig.radioSelect">
              <Radio :value="1">周期循环</Radio>
              <Radio :value="2">自定义表达式</Radio>
            </RadioGroup>
            <Space>
              <label>每</label>
              <Select v-model:value="cycleConfig.cycle" :options="cycleConfig.selectOption"
                :disabled="cycleConfig.radioSelect == 2" class="w-[150px]"></Select>
              <label>运算一次</label>
            </Space>
            <Space align="start">
              <Form ref="cronFormRef" :model="cycleConfig">
                <FormItem name="cron" :rules="[{ required: true, message: '请输入cron表达式' }]">
                  <Input v-model:value="cycleConfig.cron" :disabled="cycleConfig.radioSelect == 1"
                    placeholder="请输入cron表达式" />
                </FormItem>
              </Form>
              <Button type="primary" @click="openCronModal" :disabled="cycleConfig.radioSelect == 1">生成表达式</Button>
            </Space>
          </Space>
        </Card>
      </template>
      <template #exp>
        <Card style="width: 100%;">
          <Space block style="width: 100%;" direction="vertical">
            <Flex align="start" block style="width: 100%" gap="small">
              <label>计算公式</label>
              <Form style="flex: 1;" ref="expFormRef" :model="tableConfig" :rules="tableConfig.rule">
                <FormItem name="computeExpression">
                  <Textarea v-model:value="tableConfig.computeExpression" style="width: 100%" :rows="1"
                    placeholder="请输入计算公式">
                    </Textarea>
                </FormItem>
              </Form>
            </Flex>
            <Table size="small" align="center" v-if="tableConfig.data.length" style="width: 100%"
              :columns="tableConfig.columns" :data-source="tableConfig.data" :pagination="false">
              <template #bodyCell="{ column, text, record }">
                <!-- 编号 -->
                <template v-if="column.dataIndex === 'fieldCode'">
                  <div class="fieldCode">{{ record.fieldCode }}</div>
                </template>
                <!-- 来源 -->
                <template v-if="column.dataIndex === 'source'">
                  <Select style="width: 100%" v-model:value="record.source"
                    @change="sourceChange(record.fieldCode, $event)" :options="tableConfig.sourceOption"></Select>
                </template>
                <!-- 变量 -->
                <template v-if="column.dataIndex === 'key'">
                  <Select style="width: 100%" v-model:value="record.key" :options="record.keyOption"></Select>
                </template>
                <!-- 统计方式 -->
                <template v-if="column.dataIndex === 'statistics'">
                  <Select style="width: 100%" v-model:value="record.statistics" :options="tableConfig.statisticsOption">
                  </Select>
                </template>
                <!-- 操作 -->
                <template v-if="column.dataIndex === 'function'">
                  <Button @click="tableDelData(record.fieldCode)" type="link" danger>删除</Button>
                </template>
              </template>
            </Table>
            <Space>
              <Button @click="tableAddData" type="link">+ 插入变量</Button>
            </Space>
          </Space>
        </Card>
      </template>
    </BasicForm>
    <JobCronModel :close-on-click-modal="false" class="w-[800px]" @confirm="cronExpressionChanged($event)" />
  </BasicDrawer>
</template>

<script setup lang='ts' name=''>
import { Form, FormItem, Card, Radio, RadioGroup, Space, Select, Input, Button, Textarea, Table, type SelectProps, Flex } from 'ant-design-vue';
import { useVbenDrawer, useVbenForm, useVbenModal } from '@vben/common-ui';
import { computed, onMounted, ref } from 'vue'
import { editSchema } from './model';
import { List as DeviceList } from '#/api/union/iotUnionItem';
import { List } from '#/api/union/iotUnionField';
import { useRoute } from 'vue-router';
import { getSysDictDataListApi } from '#/api';
import type { Rule } from 'ant-design-vue/es/form';
import { cloneDeep } from '@vben/utils';
import { Edit } from '#/api/union/iotUnionField';
import { View } from '#/api/union/iotUnionField';

import jobCronModel from '../../../system/job/job-cron-model.vue';
/** 路由实例 */
const route = useRoute();
/** 编辑/新增flag */
const isUpdate = ref(false);
/** 自定义表单实例 */
const expFormRef = ref()
const cronFormRef = ref()
/** emit */
const emit = defineEmits(['reload']);

/** 标题 */
const title = computed(() => {
  return isUpdate.value ? "编辑" : "新增";
});
/** 计算周期 配置模板 */
interface CycleConfig {
  radioSelect: number;
  cycle: string;
  cron: string;
  selectOption: any[]
}

/** 计算周期 配置 */
const cycleConfig = ref<CycleConfig>({
  radioSelect: 1,
  cycle: "",
  cron: "",
  selectOption: []
})
/** 变量及计算公式 表格配置模板 */
interface TableConfig {
  computeExpression: string;
  data: {
    fieldCode: string,
    source: string,
    key: string | undefined,
    statistics: string,
    keyOption: SelectProps['options'],
  }[];
  columns: any[];
  sourceOption: SelectProps['options'],
  statisticsOption: SelectProps['options'],
  rule: Record<string, Rule[]>
}
/** 变量及计算公式 表格配置 */
const tableConfig = ref<TableConfig>({
  /** 用户输入的内容 */
  computeExpression: "",
  /** 表格保存的数据 */
  data: [],
  /** 列配置 */
  columns: [
    {
      title: '序号',
      dataIndex: 'fieldCode',
      key: 'fieldCode',
      align: "center",
      width: 1
    },
    {
      title: '数据来源',
      dataIndex: 'source',
      key: 'source',
      align: "center",
      width: 225
    },
    {
      title: '变量',
      dataIndex: 'key',
      key: 'key',
      align: "center",
      width: 225
    },
    {
      title: '统计方式',
      dataIndex: 'statistics',
      key: 'statistics',
      align: "center",
      width: 150
    }, {
      title: '操作',
      dataIndex: 'function',
      align: "center",
      width: 1
    },
  ],
  sourceOption: [
    { label: "录入型变量", value: "2" },
    { label: "运算型变量", value: "3" }
  ],
  /** 统计方式选项 #默认 */
  statisticsOption: [
    {
      value: "0",
      label: '原值',
    },
    {
      value: "1",
      label: '累计值',
    },
    {
      value: "2",
      label: '平均值',
    },
    {
      value: "3",
      label: '最大值',
    },
    {
      value: "4",
      label: '最小值',
    },
  ],
  rule: {
    "computeExpression": [
      {
        validator: (_, value) => {
          if (value == "") return Promise.reject('请输入计算公式');
          if (value != "" && tableConfig.value.data.length == 0) return Promise.reject('请插入变量');
          const regex = /^\s*[A-Z]\s*([-+*/%]\s*[A-Z]\s*)+$/;
          if (!regex.test(value)) return Promise.reject('计算公式错误');
          return Promise.resolve();
        }
      },
    ]
  }
})
/** 变量及计算公式 添加表格行 */
function tableAddData() {
  /** 获取最后一条数据的id,无数据时为Ascll64的字符 */
  const lastId = tableConfig.value.data.length > 0 ?
    tableConfig.value.data[tableConfig.value.data.length - 1]!.fieldCode :
    String.fromCharCode(64);

  /** 将最后一条数据的id+1,转换为Ascll码 */
  const fieldCode = String.fromCharCode(lastId.charCodeAt(0) + 1)
  /** 设置默认选择项 */
  const source = ref<any>(tableConfig.value.sourceOption![0]!.value)
  const statistics: any = tableConfig.value.statisticsOption![0]!.value
  /** 推入数据 */
  tableConfig.value.data.push({
    fieldCode: fieldCode,
    source: source.value,
    key: undefined,
    statistics: statistics,
    keyOption: []
  });
  /** 更新推入数据的key默认值 */
  sourceChange(fieldCode, source.value)

  /** 重置用户输入 */
  tableConfig.value.computeExpression = ""
  tableConfig.value.data.forEach((item) => {
    tableConfig.value.computeExpression += item.fieldCode
  })
}
/** 变量及计算公式 删除表格行 */
function tableDelData(fieldCode: string) {
  tableConfig.value.data = tableConfig.value.data.filter(item => item.fieldCode !== fieldCode);
  /** 重置用户输入 */
  tableConfig.value.computeExpression = ""
  tableConfig.value.data.forEach((item) => {
    tableConfig.value.computeExpression += item.fieldCode
  })
}
/** 变量及计算公式 来源变动 */
function sourceChange(fieldCode: any, event: any) {
  /** 筛选出目标设备 */
  const opitonObject = tableConfig.value.sourceOption!.find((item: any) => item.value == event);
  /** 选出fieldCode来源 */
  tableConfig.value.data.forEach((item: any) => {
    if (item.fieldCode == fieldCode) {
      /** 载入变量列表 */
      item.keyOption = opitonObject!.optionsList
      /** 载入变量列默认值 */
      if (opitonObject!.optionsList.length > 0) {
        item.key = opitonObject!.optionsList[0].value
      } else {
        item.key = undefined
      }
    }
  })

}


/** 获取计算周期字典数据 */
async function loadCycle() {
  const data = await getSysDictDataListApi({
    page: 1,
    pageSize: 1000,
    dictId: 105,
    dictType: ''
  });
  if (data.items)
    data.items.forEach((item) => {
      cycleConfig.value.selectOption.push({
        label: item.dictLabel,
        value: item.dictValue
      })
    })
  cycleConfig.value.cycle = cycleConfig.value.selectOption[0].value
  // console.log(data.items)
}
/** 加载数据 */
onMounted(async () => {
  await reloadData()
})
async function reloadData() {
  drawerApi.setState({ confirmLoading: true, loading: true })
  /** 载入设备配置内容 */
  const device = await DeviceList({
    page: 1,
    pageSize: 1000,
    unionKey: route.query.unionId,
  });
  // console.log("onMounted-设备配置内容:", device.items)
  /** 创建临时列表 */
  const varDeviceList: any = []
  /** 遍历请求数据,提取deviceKey,deviceName */
  device.items.forEach((item: any) => {
    /** 获取二级选择框数据(key) */
    const optionsList: any = []
    item.fieldList.forEach((field: any) => {
      optionsList.push({
        value: field.modelKey,
        label: field.modelName,
      })
    })

    /** 添加选项数据 */
    varDeviceList.push({
      value: item.deviceKey,
      label: item.deviceName,
      optionsList
    })
  })
  // console.log("onMounted-设备配置选项数据:", varDeviceList)
  /** 载入录入型变量数据 */
  const input = await List({
    page: 1,
    pageSize: 1000,
    unionKey: route.query.unionId,
    fieldType: "2"
  });
  // console.log("onMounted-录入型变量内容", input.items)
  const varInputList: any = []
  input.items.forEach((item: any) => {
    varInputList.push({
      value: item.modelKey,
      label: item.fieldName
    })
  })
  // console.log("onMounted-录入型变量选项数据:", varInputList)
  /** 载入运算型变量 */
  const calculate = await List({
    page: 1,
    pageSize: 1000,
    unionKey: route.query.unionId,
    fieldType: "3"
  });
  // console.log("onMounted-运算型变量内容", calculate.items)
  const varCalculateList: any = []
  calculate.items.forEach((item: any) => {
    varCalculateList.push({
      value: item.modelKey,
      label: item.fieldName
    })
  })
  // console.log("onMounted-运算型变量选项数据:", varCalculateList)
  tableConfig.value.sourceOption?.forEach((item: any) => {
    if (item.value == "2") {
      item.optionsList = varInputList
    } else if (item.value == "3") {
      item.optionsList = varCalculateList
    }
  })

  /** 合并数据 */
  tableConfig.value.sourceOption = varDeviceList.concat(tableConfig.value.sourceOption)
  // console.log("onMounted-总选项配置:", tableConfig.value.sourceOption)
  await loadCycle()
  drawerApi.setState({ confirmLoading: false, loading: false })
}

/** 表单实例 */
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});
/** 抽屉实例 */
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onBeforeClose: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    /** 打开状态,不做行为 */
    if (!isOpen) {
      return null;
    }
    /** 获取状态数据 */
    const { fieldId, update, view, } = drawerApi.getData();
    if (update) {
      /** 打开加载状态 */
      drawerApi.setState({ confirmLoading: true, loading: true })

      const data:any = await View({ fieldId: fieldId })
      isUpdate.value = update;
      // console.log("编辑的数据:", data)
      await formApi.setValues(data)
      cycleConfig.value.radioSelect = data.computeType
      if (data.computeType == 2) {
        cycleConfig.value.cron = data.cron
      } else {
        cycleConfig.value.cycle = (data.cron.slice(6))
        // console.log("cycle:", data.cron.slice(6))
      }
      tableConfig.value.computeExpression = data.computeExpression
      tableConfig.value.data = data.computeItems ? data.computeItems : []
      drawerApi.setState({ confirmLoading: false, loading: false })
    }
  }
})
/** 计算周期生成器 */
const [JobCronModel, jobCronModelApi] = useVbenModal({
  zIndex: 2001,
  connectedComponent: jobCronModel,
});
function openCronModal() {
  jobCronModelApi.open();
}
function cronExpressionChanged(cron: any) {
  cycleConfig.value.cron = cron
};

// drawerApi.open()
/** 重置表单 */
async function handleCancel() {
  // console.log('handleCancel')
  formApi.resetForm()
  expFormRef.value.resetFields()
  cronFormRef.value.resetFields()
  tableConfig.value.data = [],
    tableConfig.value.computeExpression = ""
  cycleConfig.value.radioSelect = 1
  cycleConfig.value.cycle = cycleConfig.value.selectOption[0].value
}
/** 提交表单 */
async function handleConfirm() {
  /** vbenFrom验证 */
  await formApi.validateField("fieldName")
  await formApi.validateField("modelKey")
  await formApi.validateField("fieldUnit")
  /** 计算周期验证 */
  if (cycleConfig.value.radioSelect == 2) await cronFormRef.value.validateFields()
  /** 变量及计算公式 验证 */
  await expFormRef.value.validateFields()

  const data = await formApi.getValues()
  /** 去除过渡参数 */
  delete data["computed"]
  delete data["exp"]
  if (isUpdate.value != true) {
    delete data["fieldId"]
  }
  /** 计算周期参数 */
  data.computeType = cycleConfig.value.radioSelect
  if (cycleConfig.value.radioSelect == 1) {
    data.cron = "@every " + cycleConfig.value.cycle
  } else if (cycleConfig.value.radioSelect == 2) {
    data.cron = cycleConfig.value.cron
  }
  /** 变量及计算公式参数 */
  data.computeExpression = tableConfig.value.computeExpression
  data.computeItems = tableConfig.value.data.map((item) => { delete item["keyOption"]; return item })
  /**  */
  data.fieldType = 3
  data.unionKey = route.query.unionId
  console.log("请求参数:", data)


  drawerApi.setState({ confirmLoading: true, loading: true })
  await (Edit(data));
  emit("reload")
  await handleCancel();
  drawerApi.close()
  drawerApi.setState({ confirmLoading: false, loading: false })
}

defineExpose({reloadData})
</script>

<style scoped>
.fieldCode {
  width: 28px;
  height: 28px;
  background-image: linear-gradient(180deg, #6fb0ff, #3c78ff);
  font-size: 12px;
  font-weight: 400;
  line-height: 28px;
  text-align: center;
  margin-top: 2px;
  color: #fff;
}
</style>
