<script setup lang="ts">
import { Page, } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import { columns, querySchema, type RowType } from './model';
import { List } from '#/api/project/iotConsumerLoginLog/index';

// 定义接收的属性类型
const props = defineProps({
  consumer: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'consumerId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const response = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          consumerId: props.consumer.consumerId,
          ...formValues,
        });
        return response;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridClass: 'p-0 mt-0',
  formOptions: formOptions,
  gridOptions: gridOptions,
  hideSeparator: true,
});
</script>

<template class="p-0 m-0">
  <div class="p-0 m-0 h-[800px]" >
    <Grid class="flex-1">
    </Grid>
  </div>
</template>
