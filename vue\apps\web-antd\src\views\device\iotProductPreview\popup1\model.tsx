import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public channelId = 0; // 产品ID
  public channelName = ''; // 连接器名称
  public productName = ''; // 产品名称
  public status = 0;
  public categoryId = 0; // 产品分类channelId
  public deviceType = 1; // 设备类型（1-直连设备、2-网关设备、3-子设备、4-监控设备）
  public firmwareType = 1; // 固件类型（1-分包、2-http）
  public networkType = 0; // 连网方式（1-以太网、2-蜂窝、3-WIFI、4-NB、5-其他）
  public isPrivate = 0; // 是否私有化（0-否，1-是）
  public transport = 0; // 通讯协议（1-内置MQTT协议）
  public type = 0; // 通道类型（1-MQTT服务器、2-TCP服务器、3-UDP服务器、4-HTTP服务、5-WebSocket服务、6-数据库服务）
  public vertificateMethod = 0; // 认证方式（1-简单认证、2-加密认证、3-简单+加密）
  public locationWay = 0; // 定位方式(1=ip自动定位，2=设备定位，3=项目定位，4=自定义)
  public imgUrl = null; // 图片地址
  public thingsModelsJson = null; // 物模型JSON（属性、功能、事件）
  public isAuthorize = 0; // 是否启用授权码（0-否，1-是）
  public publishStatus = 1; // 产品状态（1-未发布，2-已发布）
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: 'ID',
    field: 'channelId',
    align: 'center',
    width: -1,
    // type: 'checkbox',
    type: 'radio',
  },
  {
    title: '连接器名称',
    field: 'channelName',
    align: 'left',
    width: -1,
  },
  {
    title: '是否生效',
    field: 'status',
    align: 'left',
    width: -1,
    slots: {  },
  },
  {
    field: 'publishStatus',
    title: '状态',
    formatter: ({ row }) => {
      // 将 '0'/'1' 转换为 '已发布'/'未发布'
      return row.publishStatus === '0' ? '已发布' : '未发布';
    }
  },
  
 
  {
    title: '通道类型',
    field: 'type',
    align: 'left',
    slots: {
      default: ({ row }) => {
        return renderDict(row.type, DictEnum.CHANNEL_TYPE);
      },
    },
    width: -1,
  },
  {
    title: '桥接方向',
    field: 'direction',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.direction, DictEnum.CHANNEL_DIRECTION);
      },
    },
  },
  
];

// 表格列接口
export interface RowType {
  channelId: any;
  channelName: string;
  productName: string;
  categoryId: number;
  categoryName: string;
  deviceType: string;
  firmwareType: string;
  networkType: string;
  isPrivate: number;
  transport: number;
  type: number;
  vertificateMethod: number;
  locationWay: number;
  imgUrl: string;
  thingsModelsJson: string;
  isAuthorize: number;
  publishStatus: number;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
}

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'channelId', label: 'ID' },
  { field: 'channelName', label: '连接器名称' },
  { field: 'productName', label: '产品名称' },
  { field: 'categoryName', label: '产品分类' },
  {
    field: 'deviceType',
    label: '设备类型',
    render(val, _) {
      return renderDict(val, DictEnum.DEVICE_TYPE);
    },
  },
  {
    field: 'firmwareType',
    label: '固件类型',
    render(val, _) {
      return renderDict(val, DictEnum.FIRMWARE_TYPE);
    },
  },
  {
    field: 'networkType',
    label: '连网方式',
    render(val, _) {
      return renderDict(val, DictEnum.NETWORK_TYPE);
    },
  },
  {
    field: 'isPrivate',
    label: '是否私有化',
    render(val, _) {
      return renderDict(val, DictEnum.IOT_YES_NO);
    },
  },
  {
    field: 'transport',
    label: '通讯协议',
    render(val, _) {
      return renderDict(val, DictEnum.TRANSPORT);
    },
  },
  {
    field: 'type',
    label: '通道类型',
    render(val, _) {
      return renderDict(val, DictEnum.CHANNEL_TYPE);
    },
  },
  {
    field: 'vertificateMethod',
    label: '认证方式',
    render(val, _) {
      return renderDict(val, DictEnum.VERTIFICATE_METHOD);
    },
  },
  {
    field: 'locationWay',
    label: '定位方式',
    render(val, _) {
      return renderDict(val, DictEnum.LOCATION_WAY);
    },
  },
  { field: 'imgUrl', label: '图片地址' },
  {
    field: 'isAuthorize',
    label: '是否启用授权码',
    render(val, _) {
      return renderDict(val, DictEnum.IOT_YES_NO);
    },
  },
  {
    field: 'publishStatus',
    label: '产品状态',
    render(val, _) {
      return renderDict(val, DictEnum.PUBLISH_STATUS);
    },
  },
  { field: 'createdAt', label: '创建时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'channelId',
    component: 'Input',
    label: 'ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'inputType',
    label: '脚本事件',
    component: 'Input',
    
    dependencies: { show: () => false, triggerFields: [''] },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'channelName',
    component: 'Input',
    label: '连接器名称',
    componentProps: {
      placeholder: '请输入产品标识',
      disabled: false,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'productName',
    component: 'Input',
    label: '产品名称',
    componentProps: {
      placeholder: '请输入产品名称',
      disabled: false,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'categoryId',
    component: 'TreeSelect',
    label: '产品分类',
    componentProps: {
      getPopupContainer,
    },
    rules: 'selectRequired',
  },
  {
    fieldName: 'deviceType',
    component: 'Select',
    label: '设备类型',
    componentProps: {
      placeholder: '请选择设备类型',
      options: getDictOptions(DictEnum.DEVICE_TYPE),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'firmwareType',
    component: 'Select',
    label: '固件类型',
    componentProps: {
      placeholder: '请选择固件类型',
      options: getDictOptions(DictEnum.FIRMWARE_TYPE),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'networkType',
    component: 'Select',
    label: '连网方式',
    componentProps: {
      placeholder: '请选择连网方式',
      options: getDictOptions(DictEnum.NETWORK_TYPE),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'isPrivate',
    label: '是否私有化',
    component: 'RadioGroup',
    defaultValue: '0',
    componentProps: {
      options: getDictOptions(DictEnum.IOT_YES_NO),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'transport',
    component: 'Select',
    label: '通讯协议',
    componentProps: {
      options: getDictOptions(DictEnum.TRANSPORT),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'type',
    component: 'Select',
    label: '通道类型',
    componentProps: {
      placeholder: '请选择通道类型',
      options: getDictOptions(DictEnum.CHANNEL_TYPE),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'vertificateMethod',
    component: 'Select',
    label: '认证方式',
    componentProps: {
      placeholder: '请选择认证方式',
      options: getDictOptions(DictEnum.VERTIFICATE_METHOD),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'locationWay',
    component: 'Select',
    label: '定位方式',
    componentProps: {
      placeholder: '请选择定位方式',
      options: getDictOptions(DictEnum.LOCATION_WAY),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'isAuthorize',
    component: 'RadioGroup',
    defaultValue: '0',
    label: '是否启用授权码',
    componentProps: {
      options: getDictOptions(DictEnum.IOT_YES_NO),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
  // {
  //   fieldName: 'publishStatus',
  //   component: 'RadioGroup',
  //   label: '产品状态',
  //   defaultValue: '1',
  //   componentProps: {
  //     options: getDictOptions(DictEnum.PUBLISH_STATUS),
  //     onUpdateValue: (e: any) => {
  //       console.log(e);
  //     },
  //   },
  //   rules: 'selectRequired'
  // },
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
];
