import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';
import { renderDict, renderPopoverMemberSumma, type MemberSumma } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
	public id = 0; // 编号
	public consumerId = 0; // 消费用户ID
	public oauthType = null; // 第三方类型
	public appid = ''; // 第三方AppID
	public accessToken = ''; // 访问令牌
	public accessExpires = ''; // 访问令牌过期时间
	public refreshToken = ''; // 刷新令牌
	public refreshExpires = ''; // 刷新令牌过期时间
	public openid = ''; // 第三方OpenID
	public unionid = ''; // 第三方UnionID
	public status = 0; // 关注状态
	public createdBy = 0; // 创建者
	public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
	public updatedAt = ''; // 更新时间

	constructor(state?: Partial<State>) {
		if (state) {
			Object.assign(this, state);
		}
	}
}

export function newState(state: State | Record<string, any> | null): State {
	if (state !== null) {
		if (state instanceof State) {
			return cloneDeep(state);
		}
		return new State(state);
	}
	return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
	{
		fieldName: 'consumerId',
		component: 'InputNumber',
		label: '消费用户ID',
		componentProps: {
			placeholder: '请输入消费用户ID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: z.number({ required_error: '请输入消费用户ID', invalid_type_error: '无效数字' }),
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'oauthType',
		component: 'Select',
		label: '第三方类型',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择第三方类型',
			options: getDictOptions('oauth_type'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'selectRequired',
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'appid',
		component: 'Input',
		label: '第三方AppID',
		componentProps: {
			placeholder: '请输入第三方AppID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'openid',
		component: 'Input',
		label: '第三方OpenID',
		componentProps: {
			placeholder: '请输入第三方OpenID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'status',
		component: 'Select',
		label: '关注状态',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择关注状态',
			options: getDictOptions('follow_type'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'selectRequired',
		formItemClass: 'col-span-1',
	},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
	{
		title: '编号',
		field: 'id',
		align: 'left',
		width: -1,
		type: 'checkbox',
	},
	{
		title: '消费用户ID',
		field: 'consumerId',
		align: 'left',
		width: -1,
	},
	{
		title: '第三方类型', field: 'oauthType', align: 'left', width: -1,
		slots: {
			default: ({ row }) => {
				return renderDict(row.oauthType, 'oauth_type');
			}
		},
	},
	{
		title: '第三方AppID',
		field: 'appid',
		align: 'left',
		width: -1,
	},
	{
		title: '访问令牌',
		field: 'accessToken',
		align: 'left',
		width: -1,
	},
	{
		title: '访问令牌过期时间',
		field: 'accessExpires',
		align: 'left',
		width: -1,
	},
	{
		title: '刷新令牌',
		field: 'refreshToken',
		align: 'left',
		width: -1,
	},
	{
		title: '刷新令牌过期时间',
		field: 'refreshExpires',
		align: 'left',
		width: -1,
	},
	{
		title: '第三方OpenID',
		field: 'openid',
		align: 'left',
		width: -1,
	},
	{
		title: '第三方UnionID',
		field: 'unionid',
		align: 'left',
		width: -1,
	},
	{
		title: '关注状态', field: 'status', align: 'left', width: -1,
		slots: {
			default: ({ row }) => {
				return renderDict(row.status, 'follow_type');
			}
		},
	},
	{
		title: '创建者',
		field: 'createdBy',
		align: 'left',
		width: -1,
		slots: {
			default: ({ row }) => {
				return renderPopoverMemberSumma(row.createdBySumma);
			},
		},
	},
	{
		title: '更新时间',
		field: 'updatedAt',
		align: 'left',
		width: -1,
	},
	{ title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
	id: number;
	consumerId: number;
	oauthType: string;
	appid: string;
	accessToken: string;
	accessExpires: string;
	refreshToken: string;
	refreshExpires: string;
	openid: string;
	unionid: string;
	status: number;
	createdBy: number;
	updatedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
	{ field: 'id', label: '编号' },
	{ field: 'consumerId', label: '消费用户ID' },
	{
		field: 'oauthType',
		label: '第三方类型',
		render(row: any) {
			return renderDict(row.oauthType, 'oauth_type');
		},
	},
	{ field: 'appid', label: '第三方AppID' },
	{ field: 'accessToken', label: '访问令牌' },
	{ field: 'accessExpires', label: '访问令牌过期时间' },
	{ field: 'refreshToken', label: '刷新令牌' },
	{ field: 'refreshExpires', label: '刷新令牌过期时间' },
	{ field: 'openid', label: '第三方OpenID' },
	{ field: 'unionid', label: '第三方UnionID' },
	{
		field: 'status',
		label: '关注状态',
		render(row: any) {
			return renderDict(row.status, 'follow_type');
		},
	},
	{ field: 'createdBy', label: '创建者' },
	{ field: 'updatedAt', label: '更新时间' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
	{
		fieldName: 'id',
		component: 'Input',
		label: '编号',
		dependencies: { show: () => false, triggerFields: [''], },
		componentProps: {
			placeholder: '',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'consumerId',
		component: 'InputNumber',
		label: '消费用户ID',
		componentProps: {
			placeholder: '请输入消费用户ID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: z.number({ required_error: '请输入消费用户ID', invalid_type_error: '无效数字' }),
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'oauthType',
		component: 'Select',
		label: '第三方类型',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择第三方类型',
			options: getDictOptions('oauth_type'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'selectRequired',
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'appid',
		component: 'Input',
		label: '第三方AppID',
		componentProps: {
			placeholder: '请输入第三方AppID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'accessToken',
		component: 'Input',
		label: '访问令牌',
		componentProps: {
			placeholder: '请输入访问令牌',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'accessExpires',
		component: 'DatePicker',
		label: '访问令牌过期时间',
		componentProps: {
			type: 'datetime',
			clearable: true,
			showTime: true,
			shortcuts: 'FMTime',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: 'selectRequired',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'refreshToken',
		component: 'Input',
		label: '刷新令牌',
		componentProps: {
			placeholder: '请输入刷新令牌',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'refreshExpires',
		component: 'DatePicker',
		label: '刷新令牌过期时间',
		componentProps: {
			type: 'datetime',
			clearable: true,
			showTime: true,
			shortcuts: 'FMTime',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: 'selectRequired',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'openid',
		component: 'Input',
		label: '第三方OpenID',
		componentProps: {
			placeholder: '请输入第三方OpenID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'unionid',
		component: 'Input',
		label: '第三方UnionID',
		componentProps: {
			placeholder: '请输入第三方UnionID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'status',
		component: 'Select',
		label: '关注状态',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择关注状态',
			options: getDictOptions('follow_type'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'selectRequired',
		formItemClass: 'col-span-1',
	},
];