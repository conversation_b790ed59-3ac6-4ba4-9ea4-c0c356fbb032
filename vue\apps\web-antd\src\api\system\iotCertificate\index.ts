import { requestClient } from '#/api/request';

// 获取证书表列表
export function List(params:any) {
  return requestClient.get<any>('system/iotCertificate/list', { params });
}

// 删除/批量删除证书表
export function Delete(params:any) {
  return requestClient.post<any>('system/iotCertificate/delete', { ...params });
}

// 添加/编辑证书表
export function Edit(params:any) {
  return requestClient.post<any>('system/iotCertificate/edit', { ...params });
}

// 获取证书表指定详情
export function View(params:any) {
  return requestClient.get<any>('system/iotCertificate/view', { params });
}

// 导出证书表
export function Export(params:any) {
  return requestClient.post<Blob>('/system/iotCertificate/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}