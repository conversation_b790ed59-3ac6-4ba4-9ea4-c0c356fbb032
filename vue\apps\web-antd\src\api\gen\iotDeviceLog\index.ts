import { requestClient } from '#/api/request';

// 获取设备日志列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotDeviceLog/list', { params });
}

// 删除/批量删除设备日志
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotDeviceLog/delete', { ...params });
}

// 添加/编辑设备日志
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotDeviceLog/edit', { ...params });
}

// 获取设备日志指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotDeviceLog/view', { params });
}

// 导出设备日志
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotDeviceLog/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}