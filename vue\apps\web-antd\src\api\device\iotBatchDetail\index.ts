import { requestClient } from '#/api/request';

// 获取设备操作记录列表
export function List(params:any) {
  return requestClient.get<any>('device/iotBatchDetail/list', { params });
}

// 删除/批量删除设备操作记录
export function Delete(params:any) {
  return requestClient.post<any>('device/iotBatchDetail/delete', { ...params });
}

// 添加/编辑设备操作记录
export function Edit(params:any) {
  return requestClient.post<any>('device/iotBatchDetail/edit', { ...params });
}

// 获取设备操作记录指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotBatchDetail/view', { params });
}

// 导出设备操作记录
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotBatchDetail/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}