import { requestClient } from '#/api/request';

// 获取资源监控汇总表列表
export function List(params:any) {
  return requestClient.get<any>('monitor/iotChannelMonitorSummary/list', { params });
}

// 删除/批量删除资源监控汇总表
export function Delete(params:any) {
  return requestClient.post<any>('monitor/iotChannelMonitorSummary/delete', { ...params });
}

// 添加/编辑资源监控汇总表
export function Edit(params:any) {
  return requestClient.post<any>('monitor/iotChannelMonitorSummary/edit', { ...params });
}

// 获取资源监控汇总表指定详情
export function View(params:any) {
  return requestClient.get<any>('monitor/iotChannelMonitorSummary/view', { params });
}

// 导出资源监控汇总表
export function Export(params:any) {
  return requestClient.post<Blob>('/monitor/iotChannelMonitorSummary/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}