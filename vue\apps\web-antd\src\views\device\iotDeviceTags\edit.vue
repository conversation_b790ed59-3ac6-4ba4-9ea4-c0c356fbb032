<template>
  <BasicModal :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm></BasicForm>
  </BasicModal>
</template>
<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { editSchema, type RowType } from './model';
import { update } from 'lodash-es';

const emit = defineEmits<{ confirm: [update: boolean, index: number, tag: RowType] }>();
interface ModalProps {
  index?: number;
  update: boolean;
  tagData?: RowType;
}
const currenIndex = ref(0);
const isUpdate = ref(false);
const currentTag = ref<RowType>();

const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') + '标签': $t('pages.common.add') + '标签';
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [BasicModal, modalApi] = useVbenModal({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    modalApi.setState({ confirmLoading: true, loading: true })
    const { index, update, tagData} = modalApi.getData() as ModalProps;
    currenIndex.value = index? index : 0; 
    isUpdate.value = update;
    currentTag.value = tagData;
    if (isUpdate.value && tagData) {
      await formApi.setValues(tagData);
    }
    modalApi.setState({ confirmLoading: false, loading: false })
    modalApi.setState({ showConfirmButton: true });
    formApi.setState({
      commonConfig: {
        componentProps: {
          readonly: false,
          "only-read": false,
        }
      }
    });
  },
});

async function handleConfirm() {
  try {
    modalApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues()) as RowType;
    //await (Edit(data));
    emit('confirm', isUpdate.value, currenIndex.value, data);
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  modalApi.close();
  await formApi.resetForm();
}

</script>