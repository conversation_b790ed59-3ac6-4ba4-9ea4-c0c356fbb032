<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <template #deviceLocationPicker="slotProps">
        <div style="display: flex; gap: 8px;">
          <Button type="primary" @click="openSelectLocation">地图选点</Button>
        </div>
      </template>
    </BasicForm>
  </BasicDrawer>
  <SelectLocation ref="refSelectLocation" @locationSelected="onLocationSelected" />
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { Button } from 'ant-design-vue';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Alert } from 'ant-design-vue';
import { Edit, View } from '#/api/project/iotProject';
import { editSchema } from './model';
import { List, Tree } from '#/api/regions';
import SelectLocation from './selectLocation/index.vue';
import { getSysDeptTreeApi } from '#/api/system/dept';

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
}
// 部门
async function getDeptTree() {
  const treeRes = await getSysDeptTreeApi();
  const treeData = treeRes.items;
  addFullName(treeData, 'deptName', ' / ');
  return treeData;
}
async function initDeptSelect() {
  // 需要动态更新TreeSelect组件 这里允许为空
  const treeData = await getDeptTree();
  formApi.updateSchema([
    {
      componentProps: {
        fieldNames: { label: 'deptName', value: 'deptId' },
        showSearch: true,
        treeData: treeData,
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        // 选中后显示在输入框的值
        treeNodeLabelProp: 'fullName',
      },
      fieldName: 'deptId',
    },
  ]);
}

interface TreeSelectNode {
  label: string;
  value: string;
  children?: TreeSelectNode[];
  isLeaf?: boolean;
  fullName?: string;
}

const isUpdate = ref(false);
const isView = ref(false);
// 1. 初始化省级数据
// 存储省级数据（使用ref确保响应式）
const provinceList = ref<TreeSelectNode[]>([]);


// 用于跟踪用户是否修改了地图相关字段
const userModifiedMapData = ref<{
  longitude?: number;
  latitude?: number;
  networkAddress?: string;
} | null>(null);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

// 新增：编码处理函数，将完整编码转为省级编码（后四位设为0）
function getProvinceCode(fullCode: string): string {
  if (fullCode.length >= 2) {
    return fullCode.slice(0, 2) + '0000';
  }
  return fullCode; // 异常情况，返回原编码
}

// 编辑模式下，根据编码加载完整区划树并回显名称
async function handleEchoRegion(record: any) {
  if (isUpdate.value && record.region) {
    // 1. 提取省级编码（如 110105 → 110000）
    const provinceCode = getProvinceCode(record.region);
    try {
      // 2. 先加载所有省级节点（如北京市、山东省）
      const allProvincesRes = await List({ parent: '0' });
      const allProvinces = transformTreeData(allProvincesRes.items);
      provinceList.value = allProvinces; // 保留所有省级节点

      // 3. 找到当前的省级节点（如北京市）
      const currentProvinceNode = provinceList.value.find(
        node => node.value === provinceCode
      );
      if (currentProvinceNode) {
        // 4. 加载当前省级节点的子节点（如北京市下的海淀区）
        const childrenRes = await Tree({ parent: provinceCode });
        // 子节点的fullName会自动拼接省级节点的fullName（如"北京市/海淀区"）
        currentProvinceNode.children = transformTreeData(
          childrenRes.items,
          currentProvinceNode.fullName // 传递省级节点的fullName作为父路径
        );
      }
    } catch (error) {
      console.log('加载省级区划树失败:', error);
    }

    // 5. 从映射中获取完整名称（此时包含省的部分）
    const regionFullName = regionNameMap.value.get(record.region);
    if (regionFullName) {
      formApi.setFieldValue('region', {
        value: record.region,
        label: regionFullName, // 如"北京市/海淀区"
      });
    }
  }
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-6',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, update, view } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;
    await initDeptSelect();
    try {
      const response = await List({ parent: '0' });
      const provinceItems = response.items;
      provinceList.value = transformTreeData(provinceItems);
    } catch (error) {
      console.log('加载省级数据失败:', error);
      provinceList.value = [];
    }
    // 回显数据：调用调整后的handleEchoData函数
    if (update || view) {
      const record = await View({ projectId: id });
      // 处理省市区回显
      await handleEchoRegion(record);
      // 处理状态字段
      record.status = record.status === '0' ? true : false;
      await formApi.setValues(record);
    }
    drawerApi.setState({ confirmLoading: false, loading: false });

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      // 监听省市区
      // 动态更新树形选择器配置
      formApi.updateSchema([
        {
          fieldName: 'region',
          component: 'TreeSelect',
          label: '项目所属省市县',
          componentProps: {
            placeholder: '请选择省市县',
            showSearch: true,
            treeNodeFilterProp: 'label',
            allowClear: true,
            treeData: provinceList.value, // 初始只显示省级数据
            fieldNames: { label: 'label', value: 'value', children: 'children' }, // 显式指定字段
            // 选中后显示在输入框的值
            treeNodeLabelProp: 'fullName',
            // 选择事件
            onChange: (value: string) => {
              console.log('选择的地区编码:', value);
            },
            loadData: async (node: TreeSelectNode) => {
              if (node.children && node.children.length > 0) return;
              try {
                const response = await Tree({ parent: node.value });
                const childItems = response.items;
                node.children = transformTreeData(childItems, node.fullName);
                let n = provinceList.value.find((n: any) => {
                  return n.value == node.value
                });
                if (n) {
                  n.children = node.children;
                }
              } catch (error) {
                console.error(`加载 ${node.label} 的下级数据失败:`, error);
                node.children = [];
              }
            },
          }
        }
      ]);
      // 完成
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    data.status = data.status == true ? '0' : '1';
    console.log('data:', JSON.stringify(data, null, 2));

    // 如果用户修改了地图数据，使用用户的修改
    if (userModifiedMapData.value) {
      console.log('🗺️ 检测到用户修改了地图数据，使用用户的选择');
      console.log('🗺️ 用户修改的原始数据:', userModifiedMapData.value);

      data.longitude = userModifiedMapData.value.longitude;
      data.latitude = userModifiedMapData.value.latitude;

      if (userModifiedMapData.value.networkAddress) {
        console.log('🗺️ 设置设备地址:', userModifiedMapData.value.networkAddress);
        data.networkAddress = userModifiedMapData.value.networkAddress;

        // 尝试其他可能的字段名，以防后端期望不同的字段名
        data.address = userModifiedMapData.value.networkAddress;
        data.deviceAddress = userModifiedMapData.value.networkAddress;
        data.location = userModifiedMapData.value.networkAddress;
        data.addr = userModifiedMapData.value.networkAddress;
        data.deviceLocation = userModifiedMapData.value.networkAddress;

        console.log('🗺️ 同时设置多个可能的地址字段');
      } else {
        console.log('🗺️ 用户修改数据中没有地址信息');
      }

      console.log('🗺️ 应用用户修改后的完整数据:', {
        longitude: data.longitude,
        latitude: data.latitude,
        networkAddress: data.networkAddress
      });
    } else {
      console.log('🗺️ 没有检测到用户修改的地图数据');
    }

    // 确保包含必要的ID字段用于编辑
    const drawerData = drawerApi.getData() as ModalProps;
    if (isUpdate.value && drawerData.id && !data.projectId) {
      data.projectId = drawerData.id;
      console.log('🔧 补充设备ID:', data.projectId);
    }

    // 特殊处理：如果是编辑模式且用户修改了地址，尝试使用新增模式的字段结构
    if (isUpdate.value && userModifiedMapData.value && userModifiedMapData.value.networkAddress) {
      console.log('🗺️ 编辑模式下的地址字段特殊处理');

      // 确保地址字段不为空且有效
      const addressValue = userModifiedMapData.value.networkAddress.trim();
      if (addressValue && addressValue !== '未知地址') {
        data.networkAddress = addressValue;
        console.log('🗺️ 强制设置networkAddress字段:', addressValue);

        // 删除可能干扰的字段
        delete data.address;
        delete data.deviceAddress;
        delete data.location;
        delete data.addr;
        delete data.deviceLocation;

        console.log('🗺️ 清理其他地址字段，只保留networkAddress');
      }
    }

    console.log('📤 最终提交的数据:', data);

    // 特别检查最终提交数据中的地址字段
    console.log('🗺️ 最终提交数据中的地址相关字段:', {
      networkAddress: data.networkAddress,
      address: data.address,
      deviceAddress: data.deviceAddress,
      networkAddressType: typeof data.networkAddress,
      networkAddressLength: data.networkAddress ? data.networkAddress.length : 0
    });

    // 尝试强制设置地址字段，确保它不会被忽略
    if (userModifiedMapData.value && userModifiedMapData.value.networkAddress) {
      // 强制设置多个可能的字段名
      const addressValue = userModifiedMapData.value.networkAddress;
      data.networkAddress = addressValue;
      data.address = addressValue;
      data.deviceAddress = addressValue;
      data.location = addressValue;
      data.addr = addressValue;
      data.deviceLocation = addressValue;

      console.log('🗺️ 强制设置地址字段完成，地址值:', addressValue);
    }

    try {
      const result = await Edit(data);
      console.log('✅ Edit API 调用成功:', result);

      emit('reload');
      // 清理用户修改的地图数据
      userModifiedMapData.value = null;
      await handleCancel();
      console.log('✅ 设备编辑完成');
    } catch (apiError) {
      console.error('❌ Edit API 调用失败:', apiError);
      // 检查是否是网络错误或服务器错误
      if (apiError && typeof apiError === 'object') {
        console.error('错误详情:', {
          message: (apiError as any).message,
          status: (apiError as any).status,
          response: (apiError as any).response
        });
      }
      throw apiError; // 重新抛出错误以便外层catch处理
    }
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
// 省市县
// 存储 行政编码 → 名称 的映射
const regionNameMap = ref<Map<string, string>>(new Map());
// 转换数据为级联组件所需格式（递归处理所有层级）
function transformTreeData(items: any[], parentFullName = ''): TreeSelectNode[] {
  return items.map(item => {
    // 拼接当前节点的完整路径（父级路径 + 当前名称）
    const fullName = parentFullName
      ? `${parentFullName}/${item.name}`
      : item.name; // 省级节点无父级，直接用名称
    // 缓存编码和名称的映射
    regionNameMap.value.set(item.code, fullName);
    return {
      label: item.name,
      value: item.code,
      fullName, // 存储完整路径（如"山东省/济南市/章丘区"）
      isLeaf: item.level === 3 || (item.level === 2 && item.children === null),
      // 递归处理子级，传递当前节点的fullName作为父级路径
      children: item.children ? transformTreeData(item.children, fullName) : undefined,
    };
  });
}
// 地图选点相关
const refSelectLocation = ref();


async function openSelectLocation() {
  // 获取表单中当前的经纬度值
  const values = await formApi.getValues();
  const lng = values.longitude;
  const lat = values.latitude;

  console.log('表单中的经纬度:', lng, lat);

  // 传递经纬度给弹窗
  refSelectLocation.value.openModal(lng, lat);
}
// 位置选择弹窗回调
async function onLocationSelected(lng: number, lat: number, address?: string) {
  console.log('🗺️ 地图选点回调 - 选择的位置:', lng, lat, '地址:', address);
  console.log('🗺️ 地址参数类型和值:', typeof address, address);

  // 保存用户修改的地图数据
  userModifiedMapData.value = {
    longitude: lng,
    latitude: lat,
    networkAddress: address || undefined // 确保空字符串不被保存
  };

  console.log('🗺️ 保存用户修改的地图数据:', userModifiedMapData.value);

  // 更新表单中的经纬度值和地址
  const updateData: any = {
    longitude: lng,
    latitude: lat
  };

  // 如果有地址信息，同时更新设备地址字段
  if (address) {
    updateData.networkAddress = address;
  }

  console.log('🗺️ 准备更新表单数据:', updateData);

  // 先尝试单独设置地址字段
  if (address) {
    console.log('🗺️ 单独设置地址字段:', address);
    await formApi.setValues({ networkAddress: address });

    // 验证单独设置是否成功
    const checkValues = await formApi.getValues();
    console.log('🗺️ 单独设置地址后的验证:', checkValues.networkAddress);
  }

  await formApi.setValues(updateData);

  // 验证数据是否正确设置
  const currentValues = await formApi.getValues();
  console.log('🗺️ 表单更新后的完整数据:', currentValues);
  console.log('🗺️ 经纬度字段验证:', {
    longitude: currentValues.longitude,
    latitude: currentValues.latitude,
    networkAddress: currentValues.networkAddress
  });
}

</script>