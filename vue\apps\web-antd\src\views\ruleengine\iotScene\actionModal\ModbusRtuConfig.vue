<script setup lang="ts">
import { useVbenForm } from '@vben/common-ui';
import { modbusRtuFormSchema } from './modbusRtuConfig';
import { ref, watch, defineProps, defineEmits, computed, onMounted } from 'vue';
import { getDictOptions } from '#/utils/dict';
import { DictEnum } from '@vben/constants';
import { Input, Select, Button, Switch, InputNumber, InputGroup } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { cloneDeep } from '@vben/utils';
//引入添加的ModbusRTU指令预览接口
import { ModbusRTU } from '#/api/ruleengine/iotScene';
import { editArray } from '#/utils/bindArray';  // 引入编辑线圈数组的工具函数


const [ModbusForm, modbusFormApi] = useVbenForm({
  schema: modbusRtuFormSchema,
  layout: 'vertical',
  showDefaultActions: false,
  wrapperClass: 'grid-cols-4 gap-x-4',
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',

  },
  //可加 handleValuesChange 实现指令预览自动生成
  handleValuesChange: (values) => {
    // 这里可以处理表单值变化后的逻辑
    // 这里同步所有字段到父组件
    emit('update:useSubAddr', values.useSubAddr);
    emit('update:subAddr', values.subAddr);
    emit('update:modbusFunction', values.modbusFunction);
    emit('update:regAddr', values.regAddr);
    emit('update:coilCount', values.coilCount);
    emit('update:regCount', values.regCount);
    emit('update:coilValue', values.coilValue);
    emit('update:regValue', values.regValue);
    emit('update:coilStatusList', values.coilStatusList);
    emit('update:regValueList', values.regValueList);
    emit('update:dataCheckType', values.dataCheckType);
    emit('update:dataMsg', values.dataMsg);  //同步到父组件里面
    console.log('Form values changed:', values);
  },
});

const props = defineProps<{
  topicType: string;
  customTopic: string;
  useSubAddr: number | string;
  subAddr: number | string;
  modbusFunction: number | string;
  regAddr: number | string;
  coilCount: number;
  regCount: number;
  coilValue: any;
  regValue: any;
  coilStatusList: boolean[];
  regValueList: number[];
  dataCheckType: number | string;
  dataMsg?: string;
}>();

const emit = defineEmits([
  'update:topicType',
  'update:customTopic',
  'update:useSubAddr',
  'update:subAddr',
  'update:modbusFunction',
  'update:regAddr',
  'update:coilCount',
  'update:regCount',
  'update:coilValue',
  'update:regValue',
  'update:coilStatusList',
  'update:regValueList',
  'update:dataCheckType',
  'update:dataMsg',
]);

// 内部状态绑定 v-model
const innerTopicType = ref(props.topicType);
const innerCustomTopic = ref(props.customTopic);


// 双向绑定更新父组件
watch(innerTopicType, val => emit('update:topicType', val));
watch(innerCustomTopic, val => emit('update:customTopic', val));


const topicTypeOptions = getDictOptions(DictEnum.TOPIC_TYPE); // 数据流类型
// 是否显示“自定义数据流”输入框
const showCustomTopicInput = computed(() => props.topicType === '3');


// 生成预览按钮事件
// 暴露表单方法出去
defineExpose({
  validate: () => modbusFormApi.validate(),
  setFieldsValue: (val: Record<string, any>) => {
    Object.entries(val).forEach(([field, value]) => {
      modbusFormApi.setFieldValue(field, value);
    });
  },
});

const modbusFormRef = ref();
const allCoilStatus = ref(false);
watch(allCoilStatus, async (newVal) => {
  const data = await modbusFormApi.getValues();
  const updated = data.coilStatusList.map(() => {
    return newVal;
  });
  await modbusFormApi.setFieldValue("coilStatusList", updated);
});

async function handleGenPreview() {
  console.log('生成预览按钮被点击');
  const { valid } = await modbusFormApi.validate();
  if (!valid) {
    return;
  }
  const data = cloneDeep(await modbusFormApi.getValues());
  const res = await ModbusRTU(data);
  message.success('预览生成成功');
  console.log('预览数据 res:', res);
  // 这里可以处理预览数据，比如弹出一个预览框或者更新某个状态
  await modbusFormApi.setValues({
    dataMsg: res.dataMsg,
  });
}

async function changeInput(e: any) {
  console.log('changeInput', e);
  const oldList = await modbusFormApi.getValues();
  console.log('changeInput newList', oldList);

}

async function changeAllCoilStatus(checked: boolean) {
  const data = await modbusFormApi.getValues();
  const updated = data.modbusParam.coilStatusList.map(() => {
    return checked;
  });
  console.log("changeAllCoilStatus", data);
  await modbusFormApi.setValues({
    "coilStatusList": updated
  });
}
function hex(item: any) {
  if (item) {
    return '0x' + parseInt(item).toString(16).padStart(4, '0').toUpperCase();
  }
  return '0x0000';
}

onMounted(() => {
  modbusFormApi.setValues({
    useSubAddr: props.useSubAddr,
    subAddr: props.subAddr,
    modbusFunction: props.modbusFunction ? String(props.modbusFunction) : '',
    regAddr: props.regAddr,
    coilCount: props.coilCount,
    regCount: props.regCount,
    coilValue: props.coilValue,
    regValue: props.regValue,
    coilStatusList: props.coilStatusList,
    regValueList: props.regValueList,
    dataCheckType: props.dataCheckType ? String(props.dataCheckType) : '',
    dataMsg: props.dataMsg || '',
  });
  innerTopicType.value = props.topicType;
  innerCustomTopic.value = props.customTopic;
  console.log('ModbusRtuConfig mounted, initial values set');

});
</script>

<template>
  <div style="border:1px solid #d9e5f6;padding:12px;border-radius:8px;width: 800px;">
    <!-- 标题 -->
    <h2 style="margin-bottom: 20px;">动作脚本配置</h2>

    <!-- 第二行：数据流类型 + 自定义数据流（条件显示） -->
    <div class="form-row">
      <div class="form-item">
        <label>数据流类型：</label>
        <Select v-model:value="innerTopicType" :options="topicTypeOptions" placeholder="请选择数据流类型" style="width: 200px"
          @change="val => emit('update:topicType', val)" />
      </div>
      <div class="form-item" v-if="showCustomTopicInput" style="margin-left: 32px;">
        <label>自定义数据流：</label>
        <Input v-model:value="innerCustomTopic" placeholder="请输入" style="width: 200px" />
      </div>
    </div>
    <ModbusForm :schemas="modbusRtuFormSchema" ref="modbusFormRef">
      <template #dataPreview="slotProps" class="pb-0">
        <div>
          <Button class="p-0" type="link" @click="handleGenPreview">生成预览</Button>
        </div>
      </template>
      <template #coilStatusList="slotProps">
        <div class="grid grid-cols-8 gap-x-4">
          <div class="col-span-8 mb-4">
            <div class="grid grid-cols-2 gap-x-4">
              <label class="text-sm font-medium col-span-2">全开或全关</label>
              <Switch v-model:checked="allCoilStatus" class="w-10"
                @update:checked="async (e: any) => await changeAllCoilStatus(e)" />
            </div>
          </div>
          <div v-for="item, index in slotProps.value" :key="index" class="col-span-2 mb-4">
            <label class="text-sm font-medium">{{ '#' + index + ' 线圈状态值' }}</label>
            <Switch :key="index" :checked="item"
              @update:checked="async (e) => await editArray(modbusFormApi, 'coilStatusList', slotProps.value, index, e)" />
          </div>
        </div>
      </template>
      <template #regValueList="slotProps">
        <div class="grid grid-cols-8 gap-x-4">
          <div v-for="item, index in slotProps.value" :key="index" class="col-span-4 mb-4">
            <label class="text-sm font-medium">{{ '#' + index + ' 寄存器值' }}</label>
            <InputGroup compact>
              <Input style="width: 70%;" type="number" :key="index" :value="item"
                @update:value="async (e: any) => await editArray(modbusFormApi, 'regValueList', slotProps.value, index, e)"
                :min="0" />
              <Input style="width: 30%;" readonly :key="index" :value="hex(item)" class="w-10" />
            </InputGroup>
          </div>
        </div>
      </template>
    </ModbusForm>
  </div>

</template>

<style scoped>
.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.form-item {
  display: flex;
  align-items: center;
}
</style>
