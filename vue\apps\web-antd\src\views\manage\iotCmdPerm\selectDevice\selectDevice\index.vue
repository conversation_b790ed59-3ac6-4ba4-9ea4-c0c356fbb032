<script lang="ts" setup>
import { useVbenModal} from '@vben/common-ui';

import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';

import { columns, type RowType } from './model';
import type { DeepPartial } from '@vben/types';
import { List,} from '#/api/device/iotDevice';
import { List as CategoryList } from '#/api/device/iotProductCategory';
import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { ref, defineProps } from 'vue';

const emit = defineEmits<{
  // 点击刷新按钮的事件
  deviceSelected: [deviceKey: string, deviceName: string];
}>();

const currentProductKey = ref('');
const gridOptions: VxeTableGridOptions<RowType> = {

  checkboxConfig: {
    highlight: true,
    range: true,
    checkField: 'checked', // 可选
  },
  rowConfig: {
    keyField: 'deviceId',
  },
  columns: columns,
  exportConfig: {},
  height: 400, // 固定表格高度
  keepSource: true,
  showOverflow: false,
  scrollY: {
    enabled: false, // 禁用垂直滚动条
  },
  scrollX: {
    enabled: false, // 禁用水平滚动条
  },
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let result = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
          productKey: currentProductKey.value,
        });

        let categoryArray = await CategoryList({});
        let categoryMap = categoryArray.items;
        result.items.forEach((item: any) => {
          item.categoryName = categoryMap.find(
            (i: any) => item.categoryId === i.categoryId,
          )?.categoryName;
        });
        return result;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});
//确认按钮回调函数
const onConfirm = () => {

  const checkedRecords = gridApi.grid.getCheckboxRecords();
  const deviceKeys = checkedRecords.map(item => item.deviceKey).join(',');
  const deviceNames = checkedRecords.map(item => item.deviceName).join(',');
  emit('deviceSelected', deviceKeys, deviceNames);
  modalApi.close();
};

function openModal() {
  modalApi.open();
}

defineExpose({
  openModal,
});

const [Modal, modalApi] = useVbenModal({ onConfirm: onConfirm ,
  onCancel: () => {
    modalApi.close();
  },
  onOpenChange: (isOpen) => {
    const {productKey} = modalApi.getData();
    if (productKey) {
      currentProductKey.value = productKey;
    }
    if (isOpen) {
      return null;
    }
    modalApi.close();
  },
});
</script>
<template>
  <div>
    <Modal class="w-[1000px] h-[600px]" title="选择设备">
      <div class="h-full overflow-hidden">
        <Grid table-title="设备表" class="h-full">
          <template #status="{ row }">
            <Tag :color="row.status === '0' ? 'green' : 'red'">
              {{ row.status === '0' ? '正常' : '停用' }}
            </Tag>
          </template>
        </Grid>
      </div>
    </Modal>
  </div>
</template>
<style scoped>
/* 隐藏VxeGrid表格的滚动条 */
:deep(.vxe-table--body-wrapper) {
  overflow: hidden !important;
}

:deep(.vxe-table--body) {
  overflow: hidden !important;
}

:deep(.vxe-grid) {
  overflow: hidden !important;
}

/* 隐藏所有滚动条 */
:deep(*::-webkit-scrollbar) {
  display: none !important;
}

:deep(*) {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}
</style>
