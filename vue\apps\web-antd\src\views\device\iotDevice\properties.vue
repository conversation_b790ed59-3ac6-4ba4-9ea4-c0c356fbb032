<script setup lang="ts">
import { onMounted, onUnmounted, reactive, ref, nextTick } from 'vue';

import { cloneDeep } from '@vben/utils';
import { SetProperty, GetProperty, PropertyLatest } from '#/api/device/iotDevice';
import { Card, Button, Tag, RadioGroup, RadioButton, message } from 'ant-design-vue';
import PieChart from '#/components/echarts-vue/piechart.vue';
import { IconifyIcon } from '@vben/icons';
import dayjs from 'dayjs';
import { useVbenModal, useVbenForm } from '@vben/common-ui';
import propertyTrendModal from './property-trend.vue';
import { emitter } from '#/views/_core/profile/mitt';

type ExtFlag = {
    isChart: number,
    isMonitor: number,
    isReadonly: number,
    isHistory: number,
    isSharePerm: number
}
type DeviceProperty = {
    key: string
    name: string
    type: string
    unit: string
    value: any
    ts: string
}

type TSLValueType = {
    type: string,
    min: number,
    max: number,
    maxLength: number,
    trueText: string,
    falseText: string,
    enumList: any[],
    items: any[]
}

type TSLFieldModel = {
    key: string;
    name: string;
    order: number,
    desc: string,
    extFlag: ExtFlag,
    valueType: TSLValueType,
    value: DeviceProperty,
    chartsOption: any
}

const Props = defineProps({
    tslProperties: {
        type: Array as () => TSLFieldModel[],
        default: [],
    },
    deviceKey: {
        type: String,
        default: '',
    },
    productKey: {
        type: String,
        default: {},
    },
    productTransport: {
        type: Number,
        default: '',
    },
    tenantId: {
        type: String,
        default: '',
    },
    properties: {
        type: Object,
        default: new Map(),
    },
});

const state = reactive({
    properties: new Array(),
});
const showType = ref('a');
// 发送订阅标签消息
function handlerSend() {
    console.log("设备属性发送订阅标签消息", ["ws:device:properties:" + Props.tenantId + ":" + Props.deviceKey]);
    emitter.emit('wsDiscribe', ["ws:device:properties:" + Props.tenantId + ":" + Props.deviceKey]);
    emitter.on('wsDataProperty', handleWsDataProperty);
}

function handleWsDataProperty(res: any) {
    console.log("接收到Websocket消息", res);
    if (!res.e || !res.d) {
        console.log("不是有效的设备属性消息", res);
        return;
    }

    if (res.e !== 'ws:device:properties:' + Props.tenantId + ':' + Props.deviceKey || !res.d.propertys) {
        console.log("不是有效的设备属性消息", res);
        return;
    }

    console.log("接收到设备属性变化", res.d);
    state.properties.forEach((element: TSLFieldModel) => {
        if (res.d.propertys[element.key]) {
            let newp = res.d.propertys[element.key]
            element.value.value = newp.value;
            element.value.ts = dayjs(newp.time).format('YYYY-MM-DD HH:mm:ss');
            if (!element.valueType.min || !element.valueType.max) {
                return;
            }
            if (element.valueType.type == 'integer' || element.valueType.type == 'decimal') {
                element.chartsOption = getChartsOption(element.name, newp.value, element.valueType.min, element.valueType.max);
            } else {
                element.chartsOption = {};
            }
        }
    });
}


async function handleGet(key: string) {
    console.log("handleGet: ", key);
    let property = state.properties.find((item: TSLFieldModel) => item.key === key);
    if (property) {
        await GetProperty(
            {
                tenantId: Props.tenantId,
                deviceKey: Props.deviceKey,
                key: property.key,
            });
        message.success('指令下发成功');
    }
}

function handleSend(key: string) {
    console.log("handleSend: ", key);
    let property = state.properties.find((item: TSLFieldModel) => item.key === key);
    if (property) {
        sendProperty.value = {
            key: property.key,
            value: property.value?.value,
            name: property.name,
            type: property.valueType.type,
            unit: property.valueType.unit,
            min: property.valueType.min,
            max: property.valueType.max,
            maxLength: property.valueType.maxLength,
            options: []
        }
        if (property.valueType.type == 'enum') {
            sendProperty.value.options = property.valueType.enumList.map((e: any) => {
                return { label: e.text, value: e.value };
            });
        }
        if (property.valueType.type == 'bool') {
            sendProperty.value.options = [
                { label: property.valueType.trueText, value: 1 },
                { label: property.valueType.falseText, value: 0 }
            ] as any;
        }
        sendModalApi.open();
    }
}

function handleHistory(key: string) {
    console.log("handleHistory: ", key);
    historyModalApi.setData({
        key: key,
        name: state.properties.find((item: TSLFieldModel) => item.key === key)?.name,
        tenantId: Props.tenantId,
        deviceKey: Props.deviceKey,
    });
    historyModalApi.open();
}

const sendProperty = ref({
    key: '',
    value: '',
    name: '',
    type: 'string',
    unit: '',
    max: 100,
    min: 0,
    maxLength: 10,
    options: []
});


// 属性下发弹窗
const [SendModal, sendModalApi] = useVbenModal(
    {
        onCancel() {
            sendModalApi.close();
        },
        async onConfirm() {
            if (!sendFormApi.validate()) {
                return;
            }
            let values = await sendFormApi.getValues();
            console.info('onConfirm tenantId:', Props.tenantId, 'deviceKey:', Props.deviceKey, 'key:', sendProperty.value.key, 'value:', values.p);
            //调用接口发出参数设置指令
            await SetProperty({
                tenantId: Props.tenantId,
                deviceKey: Props.deviceKey,
                key: sendProperty.value.key,
                value: values.p
            });
            message.success('属性下发成功');
            sendModalApi.close();
        },
        onOpenChange: (open) => {
            if (!open) {
                sendFormApi.setValues({
                    p: ''
                });
                return;
            }
            
            // 根据类型更新 schema，避免同时调用多个更新方法
            const currentType = sendProperty.value.type;
            let newSchema;
            
            if (currentType == 'enum' || currentType == 'bool') {
                newSchema = [{
                    fieldName: 'p',
                    component: 'Select',
                    label: sendProperty.value.name,
                    componentProps: {
                        options: sendProperty.value.options
                    },
                }];
            } else if (currentType == 'integer' || currentType == 'decimal') {
                newSchema = [{
                    fieldName: 'p',
                    component: 'InputNumber',
                    label: sendProperty.value.name,
                    componentProps: {
                        placeholder: '',
                    },
                }];
            } else {
                newSchema = [{
                    fieldName: 'p',
                    component: 'Input',
                    label: sendProperty.value.name,
                    componentProps: {
                        placeholder: '',
                    },
                }];
            }
            
            // 先更新 schema，再设置值
            sendFormApi.updateSchema(newSchema);
            nextTick(() => {
                sendFormApi.setValues({
                    p: sendProperty.value.value
                });
            });
        }
    }
);

const [SendForm, sendFormApi] = useVbenForm({
    commonConfig: {
        componentProps: {
            class: 'w-full',
        },
        formItemClass: 'col-span-4',
    },
    layout: 'horizontal',
    schema: [
        {
            fieldName: 'p',
            component: 'Input',
            label: '属性名称',
            componentProps: {
                placeholder: '',
            },
            rules: 'required'
        },
    ],
    showDefaultActions: false,
    wrapperClass: 'grid-cols-4 gap-x-4',
});

const [HistoryModal, historyModalApi] = useVbenModal({
    connectedComponent: propertyTrendModal,
});


onMounted(() => {
    if (!Props.tslProperties) {
        return;
    }
    state.properties = cloneDeep(Props.tslProperties);
    if (Props.properties) {
        state.properties.forEach((element: TSLFieldModel) => {
            if (Props.properties[element.key]) {
                element.value = Props.properties[element.key] || {
                    key: element.key,
                    name: element.name,
                    type: '',
                    unit: '',
                    value: '',
                    ts: ''
                };
                if (!element.valueType.min || !element.valueType.max) {
                    return;
                }
                if (element.valueType.type == 'integer' || element.valueType.type == 'decimal') {
                    element.chartsOption = getChartsOption(element.name, element.value.value, element.valueType.min, element.valueType.max);
                } else {
                    element.chartsOption = {};
                }
            }
        });
    }

    handlerSend();
});

onUnmounted(() => {
    emitter.off('wsDataProperty', handleWsDataProperty);
});

function getChartsOption(name: string, value: number, min: number, max: number) {
    return {
        series: [
            {
                type: 'gauge',
                name: name,
                radius: '90%', //修改表盘大小
                min: min,
                max: max,
                title: {
                    show: true, //控制表盘title(今日预计用电量)字体是否显示
                    fontSize: 14, //控制表盘title(今日预计用电量)字体大小
                    offsetCenter: [0, '105%'], //设置表盘title(今日预计用电量)位置
                },
                axisLine: {
                    lineStyle: {
                        show: true,
                        with: 25,
                        color: [
                            [0.3, '#5977C4'],
                            [0.7, '#C0F0E3'],
                            [1, '#F8D6D7']
                        ],
                    },
                },
                axisTick: {
                    distance: 0,
                    length: 4,
                    lineStyle: {
                        color: 'auto',
                        width: 1
                    }
                },
                axisLabel: {
                    show: max < 1000 ? true : false,
                    distance: 12,
                    fontSize: 12
                },
                splitLine: { // 分割线
                    length: 5,
                    distance: 2,
                    lineStyle: {
                        width: 1,
                        color: 'auto'
                    }
                },
                //splitNumber: auto, //分割线之间的刻度
                detail: {
                    valueAnimation: true,
                    formatter: '{value}',
                    textStyle: {
                        fontSize: 20,
                    },
                    offsetCenter: ['0', '80%'], //表盘数据(30%)位置
                },
                data: [
                    {
                        value: value,
                        name: name,
                        color: 'inherit'
                    },
                ],
            }
        ]
    }
}

function handleShowTypeChange(value: string) {
    cardStyle.value.height = value == 'a' ? '160px' : '260px';
}

const iconStyle = {
    width: '20px',
    height: '20px',
}

const cardStyle = ref({
    height: showType.value == 'a' ? '160px' : '260px',
})

async function handleRefresh() {
    const res = await PropertyLatest({
        tenantId: Props.tenantId,
        deviceKey: Props.deviceKey,
    });

    if (res.data) {
        state.properties.forEach((element: TSLFieldModel) => {
            if (res.data[element.key]) {
                element.value = res.data[element.key];
                if (!element.valueType.min || !element.valueType.max) {
                    return;
                }
                if (element.valueType.type == 'integer' || element.valueType.type == 'decimal') {
                    element.chartsOption = getChartsOption(element.name, element.value.value, element.valueType.min, element.valueType.max);
                }
            }
        });
        message.success('刷新成功');
    }
}

// 获取枚举值的显示文本，格式为：标签名（枚举值）
function getEnumDisplayText(value: any, enumList: any[]): string {
    if (!enumList || enumList.length === 0) {
        return value?.toString() || '--';
    }

    // 查找匹配的枚举项，支持字符串和数字类型的值比较
    const enumItem = enumList.find(item => {
        return String(item.value) === String(value);
    });

    if (enumItem) {
        // 尝试不同的文本字段名
        const text = enumItem.text || enumItem.label || enumItem.name || enumItem.title;
        if (text) {
            return `${text}（${value}）`;
        }
    }

    // 如果找不到对应的枚举项，直接返回值
    return value?.toString() || '--';
}

</script>
<template>
    <Card>
        <SendModal class="w-[400px]" title="属性下发设置">
            <SendForm />
        </SendModal>
        <HistoryModal />
        <template #extra>
            <div class="flex items-center">
                <Button class="flex-1 mr-2" type="primary" @click="handleRefresh">刷新</Button>
                <RadioGroup class="flex-4" v-model:value="showType" style="display: flex; align-items: center;"
                    @update:value="handleShowTypeChange">
                    <RadioButton value="a" class="flex-2 items-center">
                        数值显示
                    </RadioButton>
                    <RadioButton value="b" class="flex-2 items-center">
                        图表显示
                    </RadioButton>
                </RadioGroup>
            </div>

        </template>
        <div class="grid grid-cols-4 gap-4">
            <template v-for="(item, index) in state.properties" :key="item.key">
                <Card class="w-full" :style="cardStyle"
                    v-if="(showType == 'b' && item.chartsOption != undefined) || showType == 'a'">
                    <div v-if="showType == 'a'" class="flex items-center justify-between">
                        <div class="text-base" style="display: flex; align-items: center;">{{ item.name }}</div>
                        <div style="display: flex; align-items: center;" key="extra">
                            <Button v-if="Props.productTransport == 2" type="link" title="主动采集"
                                @click="handleGet(item.key)" style="width: 32px;">
                                <IconifyIcon :style="iconStyle" icon="ant-design:cloud-sync-outlined" />
                            </Button>
                            <Button v-if="Props.productTransport != 2" type="link" title="非Modbus设备不支持主动采集"
                                style="width: 32px; color: gray;">
                                <IconifyIcon :style="iconStyle" icon="ant-design:cloud-sync-outlined" />
                            </Button>
                            <Button v-if="item.extFlag.isReadonly != 1" type="link" title="属性下发"
                                @click="handleSend(item.key)" style="width: 32px;">
                                <IconifyIcon :style="iconStyle" icon="ant-design:cloud-download-outlined" />
                            </Button>
                            <Button v-if="item.extFlag.isReadonly == 1" type="link" title="只读属性" style="width: 32px;">
                                <IconifyIcon :style="iconStyle" icon="ant-design:eye-outlined" />
                            </Button>
                            <Button v-if="item.extFlag.isHistory != 1" title="非历史存储" type="link"
                                style="width: 40px; color: gray;">
                                <IconifyIcon :style="iconStyle" icon="ant-design:area-chart-outlined" />
                            </Button>
                            <Button v-if="item.extFlag.isHistory == 1" type="link" title="历史存储"
                                @click="handleHistory(item.key)" style="width: 40px;">
                                <IconifyIcon :style="iconStyle" icon="ant-design:area-chart-outlined" />
                            </Button>
                            <Tag color='blue' style="width: 40px;">属性</Tag>
                        </div>
                    </div>
                    <div v-if="showType == 'a'" class="mt-2">
                        <label v-if="item.valueType.type == 'bool'" class=" text-2xl">
                            {{ item.value ? (item.value.value == 0 ? item.valueType.falseText : item.valueType.trueText) :
                            '--'}}
                        </label>
                        <label v-if="item.valueType.type == 'enum'" class="text-2xl">
                            {{ item.value ? getEnumDisplayText(item.value.value, item.valueType.enumList) : '--' }}
                        </label>
                        <label v-if="item.valueType.type != 'bool' && item.valueType.type != 'enum'" class="text-2xl">
                            {{ item.value ? item.value.value : '--' }}
                        </label>
                        <label class="text-sm"> {{ item.valueType.unit || '' }} </label>
                    </div>
                    <div v-if="showType == 'a'" class="text-right">
                        {{ item.value ? item.value.ts : '' }}
                    </div>
                    <div v-if="showType == 'b' && item.chartsOption != undefined"
                        style="display: flex; align-items: center;margin-top: 20px; height: 200px;">
                        <PieChart :options="item.chartsOption" style="width: 100%; height: 200px;" />
                    </div>
                </Card>
            </template>

        </div>
    </Card>
</template>