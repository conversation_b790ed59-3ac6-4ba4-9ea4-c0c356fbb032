<script setup lang="ts">
import { useVbenDrawer } from '@vben/common-ui';
import { Description, useDescription } from '#/components/description';
import { viewSchema, type RowType } from './model';
import { View } from '#/api/union/iotUnion';
// 声明抽屉
const [BasicDrawer, drawerApi] = useVbenDrawer({
  // 打开回调
  onOpenChange: handleOpenChange,
});
const [registerDescription, { setDescProps }] = useDescription({
  column: 1,
  schema: viewSchema,
});

/** 抽屉打开回调函数 */
async function handleOpenChange(open: boolean) {
  if (!open) {
    return null;
  }
  const { record } = drawerApi.getData() as { record: RowType };
  const record2 = await View({ unionId: record.unionId });
  setDescProps({ data: record2 }, true);
}
</script>
<template>
  <BasicDrawer :footer="false" class="w-[600px]" title="查看">
    <Description @register="registerDescription"></Description>
  </BasicDrawer>
</template>
