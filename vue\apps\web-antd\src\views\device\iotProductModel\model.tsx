import type { VxeGridProps } from '#/adapter/vxe-table';
import { cloneDeep } from 'lodash-es';
import { z, type VbenFormSchema } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';
import { renderDict } from '#/utils';

export class State {
  public modelId = 0; // 物模型ID
  public productKey = ''; // 产品标识
  public modelName = ''; // 模型名称
  public modelKey = ''; // 模型标识
  public modelOrder = 0; // 模型排序
  public type = ''; // 模型类别（property-属性，function-功能，event-事件，tag-标签）
  public isChart = 0; // 是否图表展示（0-否，1-是）
  public isMonitor = 0; // 是否实时监测（0-否，1-是）
  public isReadonly = 0; // 是否只读数据(0-否，1-是)
  public isHistory = 0; // 是否历史存储 (0-否，1-是）
  public isSharePerm = 0; // 是否设备分享权限(0-否，1-是)
  public dataType = ''; // 数据类型（integer、decimal、string、bool、array、enum、object）
  public specs = null; // 数据定义
  public remark = ''; // 描述
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

const baseColumns: VxeGridProps['columns'] = [
  {
    field: '',
    align: 'center',
    width: 50,
    type: 'checkbox',
  },{
    title: '模型编号',
    field: 'modelId',
    align: 'center',
    width: 100,
  },{
    title: '模型名称',
    field: 'modelName',
    align: 'left',
    width: -1,
  },{
    title: '模型标识',
    field: 'modelKey',
    align: 'left',
    width: -1,
  }
];

const propsColumns: VxeGridProps['columns'] = [
  {
    title: '模型排序',
    field: 'modelOrder',
    align: 'center',
    width: 100,
  },{
    title: '模型类别',
    field: 'type',
    align: 'center',
    width: 100,
    visible: false,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.type, DictEnum.DEVICE_MODEL_TYPE);
        if (found) {
          return found;
        }
        return row.type;
      },
    },
  },{
    title: '是否图表展示',
    field: 'isChart',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.isChart, DictEnum.IOT_YES_NO);
        if (found) {
          return found;
        }
        return row.isChart;
      },
    },
  },{
    title: '是否历史存储',
    field: 'isHistory',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.isHistory, DictEnum.IOT_YES_NO);
        if (found) {
          return found;
        }
        return row.isHistory;
      },
    },
  }
];

// 表单验证规则
export const propertyColumns: VxeGridProps['columns'] = [
  ...baseColumns,
  {
    title: '属性来源类型',
    field: 'fromType',
    align: 'center',
    width: 120,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.fromType, DictEnum.FROM_TYPE);
        if (found) {
          return found;
        }
        return row.fromType;
      },
    },
  },
  ...propsColumns,
  {
    title: '数据类型',
    field: 'dataType',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.dataType, DictEnum.DEVICE_MODEL_DATA_TYPE);
        if (found) {
          return found;
        }
        return row.dataType;
      },
    },
  },
  
  {
    title: '数据定义',
    field: 'specs',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        if (!row.specs) {
          return row.specs;
        }
        if (row.dataType === 'integer' || row.dataType === 'decimal') {
          return "最大值: " + (row.max !== undefined ? row.max : '') + ", 最小值: " + (row.min !== undefined ? row.min : '') + ", 步长: " + (row.step !== undefined ? row.step : '') + ", 单位: " + (row.unit !== undefined ? row.unit : '');

        } else if (row.dataType === 'string') {
          return "最大长度: " + (row.maxLength !== undefined ? row.maxLength : '');
        } else if (row.dataType === 'bool') {
          return "0: " + (row.falseText !== undefined ? row.falseText : '') + ", 1: " + (row.trueText !== undefined ? row.trueText : '');
        } else if (row.dataType === 'enum' && row.enumList) {
          return row.enumList.map((item: any) => {
            return item.value + ": " + (item.text !== undefined ? item.text : '');
          }).join(', ');
        }
        else if (row.dataType === 'object' && row.items) {
          return row.items.map((item: any) => {
            return item.modelName + ": " + (item.dataType !== undefined ? item.dataType : '');
          }).join(', ');

        } else if (row.dataType === 'array') {
          return " 数组元素类型: " + (row.itemType !== undefined ? row.itemType : '') + ", 元素个数: " + (row.arrayCount !== undefined ? row.arrayCount : '');
        }
      },
    },
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: 200,
  },
  { title: '操作', width: 150, slots: { default: 'action' } },
];

export const tagColumns: VxeGridProps['columns'] = [
  ...baseColumns,
  ...propsColumns,
  {
    title: '数据类型',
    field: 'dataType',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.dataType, DictEnum.DEVICE_MODEL_DATA_TYPE);
        if (found) {
          return found;
        }
        return row.dataType;
      },
    },
  },
  {
    title: '数据定义',
    field: 'specs',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        if (!row.specs) {
          return row.specs;
        }
        if (row.dataType === 'integer' || row.dataType === 'decimal') {
          return "最大值: " + (row.max !== undefined ? row.max : '') + ", 最小值: " + (row.min !== undefined ? row.min : '') + ", 步长: " + (row.step !== undefined ? row.step : '') + ", 单位: " + (row.unit !== undefined ? row.unit : '');

        } else if (row.dataType === 'string') {
          return "最大长度: " + (row.maxLength !== undefined ? row.maxLength : '');
        } else if (row.dataType === 'bool') {
          return "0: " + (row.falseText !== undefined ? row.falseText : '') + ", 1: " + (row.trueText !== undefined ? row.trueText : '');
        } else if (row.dataType === 'enum' && row.enumList) {
          return row.enumList.map((item: any) => {
            return item.value + ": " + (item.text !== undefined ? item.text : '');
          }).join(', ');
        }
        else if (row.dataType === 'object' && row.items) {
          return row.items.map((item: any) => {
            return item.modelName + ": " + (item.dataType !== undefined ? item.dataType : '');
          }).join(', ');

        } else if (row.dataType === 'array') {
          return " 数组元素类型: " + (row.itemType !== undefined ? row.itemType : '') + ", 元素个数: " + (row.arrayCount !== undefined ? row.arrayCount : '');
        }
      },
    },
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: 200,
  },
  { title: '操作', width: 200, slots: { default: 'action' } },
];

export const functionColumns: VxeGridProps['columns'] = [
  ...baseColumns,
  ...propsColumns,
  {
    title: '描述',
    field: 'remark',
    align: 'left',
    width: -1,
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: 200,
  },
  { title: '操作', width: 150, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  modelId: number;
  modelName: string;
  productyKey: string;
  modelKey: string;
  modelOrder: number;
  type: string;
  isChart: number;
  isMonitor: number;
  isReadonly: number;
  isHistory: number;
  isSharePerm: number;
  dataType: string;
  specs: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
};

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'modelId',
    component: 'Input',
    label: '模型ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'modelName',
    component: 'Input',
    label: '模型名称',
    componentProps: {
      placeholder: '请输入模型名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'modelKey',
    component: 'Input',
    label: '模型标识',
    componentProps: {
      placeholder: '请输入模型标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'modelOrder',
    component: 'InputNumber',
    label: '模型排序',
    componentProps: {
      placeholder: '请输入模型排序',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({ required_error: '请输入模型排序', invalid_type_error: '无效数字' }),
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'type',
    component: 'Input',
    label: '模型ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    component: 'CheckboxGroup',
    fieldName: 'props',
    formItemClass: 'col-span-4',
    dependencies: {
      componentProps(values, formApi) {
        if (values.type === 'function') {
          return {
            options: [
              {
                label: '只读数据',
                value: 'isReadonly',
              },
              {
                label: '历史存储',
                value: 'isHistory',
              },
              {
                label: '分享权限',
                value: 'isSharePerm',
              },
            ]
          };
        } else if (values.type === 'event') {
          let props = values.props || [];
          if (!props.includes('isReadonly')) {
            props.push('isReadonly')
            values.props = props;
          }
          return {
            options: [
              {
                label: '只读数据',
                value: 'isReadonly',
                disabled: true,
                //默认选中
              },
              {
                label: '历史存储',
                value: 'isHistory',
              },
              {
                label: '分享权限',
                value: 'isSharePerm',
              },
            ]
          };
        }
        return {
          options: [
            {
              label: '图表展示',
              value: 'isChart',
            },
            {
              label: '实时监测',
              value: 'isMonitor',
            },
            {
              label: '只读数据',
              value: 'isReadonly',
            },
            {
              label: '历史存储',
              value: 'isHistory',
            },
            {
              label: '分享权限',
              value: 'isSharePerm',
            },
          ]
        };
      },

      triggerFields: ['type'],
    },
    label: '模型特性',
  },
  {
    fieldName: 'inputs',
    component: 'Input',
    label: '输入参数',
    defaultValue: [],
  },
  {
    fieldName: 'outputs',
    component: 'Input',
    label: '输出参数',
    defaultValue: [],
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.type === 'function';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
        };
      },
      triggerFields: ['type'], // Required property added
    },
  },
  {
    fieldName: 'remark',
    component: 'Textarea',
    label: '描述',
  },
];