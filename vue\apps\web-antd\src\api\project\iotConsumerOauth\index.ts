import { requestClient } from '#/api/request';

// 获取终端用户第三方授权表列表
export function List(params:any) {
  return requestClient.get<any>('project/iotConsumerOauth/list', { params });
}

// 删除/批量删除终端用户第三方授权表
export function Delete(params:any) {
  return requestClient.post<any>('project/iotConsumerOauth/delete', { ...params });
}

// 添加/编辑终端用户第三方授权表
export function Edit(params:any) {
  return requestClient.post<any>('project/iotConsumerOauth/edit', { ...params });
}

// 修改终端用户第三方授权表状态
export function Status(params:any) {
  return requestClient.post<any>('project/iotConsumerOauth/status', { ...params });
}

// 获取终端用户第三方授权表指定详情
export function View(params:any) {
  return requestClient.get<any>('project/iotConsumerOauth/view', { params });
}

// 导出终端用户第三方授权表
export function Export(params:any) {
  return requestClient.post<Blob>('/project/iotConsumerOauth/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}