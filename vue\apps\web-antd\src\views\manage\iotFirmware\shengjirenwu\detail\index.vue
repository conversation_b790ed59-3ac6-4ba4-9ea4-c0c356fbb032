<script lang="ts" setup>
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Card } from 'ant-design-vue';
import { View } from '#/api/manage/iotUpgradeTask';

// 升级任务详情数据
const taskDetail = ref({
  taskId: '',
  taskName: '',
  taskType: '',
  firmwareId: '',
  firmwareName: '',
  firmwareVersion: '',
  productKey: '',
  productName: '',
});

// 弹窗打开时的处理
async function onOpenChange(isOpen: boolean) {
  if (!isOpen) {
    return;
  }

  const { taskId } = modalApi.getData() || {};
  if (taskId) {
    console.log('🚀 升级任务详情弹窗已打开，任务ID:', taskId);
    try {
      const record = await View({ taskId });
      taskDetail.value = {
        taskId: record.taskId || '',
        taskName: record.taskName || '',
        taskType: record.taskType || '',
        firmwareId: record.firmwareId || '',
        firmwareName: record.firmwareName || '',
        firmwareVersion: record.firmwareVersion || '',
        productKey: record.productKey || '',
        productName: record.productName || '',
      };
      console.log('📄 获取到的任务详情:', taskDetail.value);
    } catch (error) {
      console.error('❌ 获取任务详情失败:', error);
    }
  }
}

// 打开弹窗的方法
function openModal() {
  modalApi.open();
}

// 暴露方法给父组件
defineExpose({
  openModal,
});

const [Modal, modalApi] = useVbenModal({
  onOpenChange,
  onCancel: () => {
    modalApi.close();
  },
});
</script>

<template>
  <Modal class="w-[1200px] h-[800px]" title="升级任务详情">
    <!-- 上部导航区域 -->
    <div class="top-section">
      <!-- 左侧卡片 -->
      <Card class="left-card" title="任务基本信息">
        <div class="card-content">
          <p>任务ID: {{ taskDetail.taskId }}</p>
          <p>任务名称: {{ taskDetail.taskName }}</p>
          <p>任务类型: {{ taskDetail.taskType }}</p>
          <p>这里是左侧卡片的内容区域</p>
        </div>
      </Card>

      <!-- 右侧卡片 -->
      <Card class="right-card" title="固件信息">
        <div class="card-content">
          <p>固件ID: {{ taskDetail.firmwareId }}</p>
          <p>固件名称: {{ taskDetail.firmwareName }}</p>
          <p>固件版本: {{ taskDetail.firmwareVersion }}</p>
          <p>这里是右侧卡片的内容区域</p>
        </div>
      </Card>
    </div>

    <!-- 下部导航区域 -->
    <Card class="bottom-card" title="升级详情">
      <div class="card-content">
        <p>产品标识: {{ taskDetail.productKey }}</p>
        <p>产品名称: {{ taskDetail.productName }}</p>
        <p>这里是下部卡片的内容区域</p>
      </div>
    </Card>
  </Modal>
</template>

<style scoped>
/* 上部区域布局 */
.top-section {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  height: 50vh; /* 占据视口高度的50% */
}

/* 左侧卡片 */
.left-card {
  flex: 1;
  height: 100%;
}

/* 右侧卡片 */
.right-card {
  flex: 1;
  height: 100%;
}

/* 下部卡片 */
.bottom-card {
  height: 50vh; /* 占据视口高度的50% */
}

/* 卡片内容样式 */
.card-content {
  padding: 20px;
  height: 100%;
}

.card-content p {
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
}

/* 确保卡片内容区域填满整个卡片 */
:deep(.ant-card-body) {
  height: calc(100% - 57px); /* 减去卡片头部高度 */
  padding: 0;
}
</style>
