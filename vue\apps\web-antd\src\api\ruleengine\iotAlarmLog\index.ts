import { requestClient } from '#/api/request';

// 获取报警记录表列表
export function List(params:any) {
  return requestClient.get<any>('ruleengine/iotAlarmLog/list', { params });
}

// 删除/批量删除报警记录表
export function Delete(params:any) {
  return requestClient.post<any>('ruleengine/iotAlarmLog/delete', { ...params });
}

// 添加/编辑报警记录表
export function Edit(params:any) {
  return requestClient.post<any>('ruleengine/iotAlarmLog/edit', { ...params });
}

// 获取报警记录表指定详情
export function View(params:any) {
  return requestClient.get<any>('ruleengine/iotAlarmLog/view', { params });
}
export function confirm(params:any) {
  return requestClient.post<any>('ruleengine/iotAlarmLog/confirm', {  ...params });
}

///api/v1/ruleengine/iotAlarmLog/confirm
// 导出报警记录表
export function Export(params:any) {
  return requestClient.post<Blob>('/ruleengine/iotAlarmLog/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}