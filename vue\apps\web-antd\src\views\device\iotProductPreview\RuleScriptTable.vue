<script lang="ts" setup>
import { h, reactive, ref, computed, onMounted, defineProps } from 'vue';
import {
  Button,
  message,
  Tag,
  Modal,
  Popconfirm,
  Switch,
} from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import {
  List,
  Export,
  Delete,
  Status,
} from '#/api/ruleengine/iotChannelScript';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
//import editModal from './edit.vue';
import viewModal from './view.vue';

// 定义接收父组件传递参数的props
const props = defineProps({
  productKey: {
    type: String,
    required: true
  },
  productName: {
    type: String,
    required: true
  }
});

type Option = {
  label: string;
  value: string;
};

// 直接使用父组件传递的值构建productOptions
const productOptions = ref<Option[]>([
  {
    label: props.productName,
    value: props.productKey
  }
]);

onMounted(() => {
  // 直接设置下拉选，无需调用接口
  gridApi.formApi.updateSchema([
    {
      fieldName: 'productKey',
      component: 'Select',
      label: '所属产品',
      componentProps: {
        placeholder: '请选择产品',
        options: productOptions.value,
        value: props.productKey, // 固定为父组件传递的值
        disabled: true // 禁用下拉选，防止用户修改
      },
    },
  ]);
});

// const formOptions: VbenFormProps = {
//   // 默认展开
//   collapsed: false,
//   fieldMappingTime: [['date', ['start', 'end']]],
//   schema: querySchema,
//   // 控制表单是否显示折叠按钮
//   showCollapseButton: false,
//   // 是否在字段值改变时提交表单
//   submitOnChange: true,
//   // 按下回车时是否提交表单
//   submitOnEnter: false,
// };
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'scriptId',
  },
  rowConfig: {
    keyField: 'id',
  },
  columns: columns,
  exportConfig: {},
  height: 600,
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          // 忽略表单中的productKey，强制使用父组件传递的值
          productKey: props.productKey, 
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
 // formOptions,
 gridClass: 'p-0 ',
  gridOptions,
  gridEvents,
});

async function handleStatusChange(row: RowType) {
  // 调用后端接口更新状态
  await Status({ scriptId: row.scriptId, status: row.status });
  // 刷新表格数据
  await handleRefresh();
  // 提示用户操作结果
  message.success('操作成功');
}

const [ViewModal, modalApi] = useVbenModal({
  connectedComponent: viewModal,
});
function handlePreview(record: RowType) {
    modalApi.setData({ record });
  modalApi.open();
}
// const [EditDrawer, editDrawerApi] = useVbenDrawer({
//   connectedComponent: editDrawer,
// });
// function handleAdd() {
//   editDrawerApi.setData({ update: false, view: false });
//   editDrawerApi.open();
// }
// function handleEdit(row: RowType) {
//   //id
//   editDrawerApi.setData({ id: row.scriptId, update: true, view: false });
//   editDrawerApi.open();
// }
//  async function handleDelete(row: RowType) {
//   await Delete({ scriptId: [row.scriptId] });
//   message.success('删除成功');
//   await handleRefresh();
// }
// async function handleRefresh() {
//   await gridApi.query();
// }
// function handleMultiDelete() {
//   const rows = gridApi.grid.getCheckboxRecords();
//   const ids: string[] = [];
//   for (const row of rows) {
//     ids.push(row.scriptId);
//   }
//   if (ids.length === 0) {
//     message.error('请至少选择一项要删除的数据');
//     return;
//   }
//   Modal.confirm({
//     title: '提示',
//     okType: 'danger',
//     content: `确认删除选中的${ids.length}条记录吗？`,
//     onOk: async () => {
//       await Delete({ scriptId: ids });
//       message.success('删除成功');
//       await handleRefresh();
//     },
//   });
// }

// async function handleStatusChange(row: RowType) {
//   // await Status({ id: row.id, status: row.status });
//   await Status({ scriptId: row.scriptId, status: row.status });
//   await message.success('操作成功');
//   await handleRefresh();
// }

function handleRefresh() {
    throw new Error('Function not implemented.');
}
</script>
<template>
    <div class="p-0 m-0 ">
      <Grid >
        <template #status="{ row }">
          <Switch
            v-model:checked="row.status"
            :checkedValue="'0'"
            :unCheckedValue="'1'"
           @change="handleStatusChange(row)"
            :disabled="
              !hasAccessByCodes(['cpm:ruleengine:iotChannelScript:status'])
            "
          />
        </template>
        <template #action="{ row }">
          <div class="flex items-center">
            <Button
              class="mr-2 border-none p-0"
              :block="false"
              type="link"
              @click="handlePreview(row)"
              v-access:code="'cpm:ruleengine:iotChannelScript:view'"
            >
              查看
            </Button>
          </div>
        </template>
      </Grid>
      <ViewModal />
    </div>
  </template>