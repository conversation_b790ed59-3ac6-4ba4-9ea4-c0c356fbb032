import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { getDictOptions } from '#/utils/dict';

export class State {
	public msgId = 0; // 自增ID
	public consumerId = 0; // 接受者
	public fromConsumerId = 0; // 发送者
	public category = 1; // 主类型
	public msgType = 1; // 类型
	public msgTitle = ''; // 消息标题
	public msgBody = ''; // 消息内容
	public msgAbstract = ''; // 消息摘要
	public deviceKey = ''; // 设备标识
	public relateConsumerId = 0; // 关联用户
	public relateId = 0; // 关联ID
	public status = 0; // 状态
	public tenantId = ''; // 租户ID
	public createdAt = ''; // 创建时间
	public updatedAt = ''; // 更新时间
	public deletedAt = ''; // 删除时间

	constructor(state?: Partial<State>) {
		if (state) {
			Object.assign(this, state);
		}
	}
}

export function newState(state: State | Record<string, any> | null): State {
	if (state !== null) {
		if (state instanceof State) {
			return cloneDeep(state);
		}
		return new State(state);
	}
	return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
	{
		fieldName: 'status',
		component: 'Select',
		label: '状态',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择状态',
			options: getDictOptions('consumer_msg_status'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},
	// {
	// 	fieldName: 'category',
	// 	component: 'Select',
	// 	label: '主类型',
	// 	defaultValue: null,
	// 	componentProps: {
	// 		placeholder: '请选择主类型',
	// 		options: getDictOptions('consumer_msg_type_main'),
	// 		onUpdateValue: (e: any) => {
	// 			console.log(e);
	// 		},
	// 	},
	// 	rules: 'selectRequired',
	// 	formItemClass: 'col-span-1',
	// },
	{
		fieldName: 'msgType',
		component: 'Select',
		label: '通知类型',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择类型',
			options: getDictOptions('consumer_msg_type'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'msgTitle',
		component: 'Input',
		label: '消息标题',
		componentProps: {
			placeholder: '请输入消息标题',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'deviceKey',
		component: 'Input',
		label: '设备标识',
		componentProps: {
			placeholder: '请输入设备标识',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'consumerId',
		component: 'Select',
		label: '接受者',
		componentProps: {
			placeholder: '请输入接受者',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},];

// 表格列
export const columns: VxeGridProps['columns'] = [
	{
		title: '编号',
		field: 'msgId',
		align: 'center',
		width: 70,
	},
	{
		title: '设备标识',
		field: 'deviceKey',
		align: 'center',
		width: 150,
	},
	{
		title: '创建时间',
		field: 'createdAt',
		align: 'center',
		width: 150,
	},
	{
		title: '接受者',
		field: 'consumer.userName',
		align: 'center',
		width: -1,
	},
	{
		title: '消息标题',
		field: 'msgTitle',
		align: 'center',
		width: -1,
	},
	{
		title: '消息摘要',
		field: 'msgAbstract',
		align: 'center',
		width: -1,
	},
	// {
	// 	title: '消息内容',
	// 	field: 'msgBody',
	// 	align: 'center',
	// 	width: -1,
	// },
	{
		title: '关联状态', field: 'relateStatus', align: 'center', width: 100,
		slots: {
			default: ({ row }) => {
				let dictType = '';
				if (row.msgType === 101) {
					dictType = 'alarm_confirm_state';
				} else if (row.msgType === 200 || row.msgType === 201) {
					dictType = 'consumer_share_state';
				}
				return renderDict(row.relateStatus, dictType);
			}
		},
	},
	{
		title: '状态', field: 'status', align: 'center', width: 100,
		slots: {
			default: ({ row }) => {
				return renderDict(row.status, 'consumer_msg_status');
			}
		},
	},
	{ title: '操作', width: 100, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
	msgId: number;
	consumerId: number;
	fromConsumerId: number;
	category: number;
	msgType: number;
	msgTitle: string;
	msgBody: string;
	msgAbstract: string;
	deviceKey: string;
	relateConsumerId: number;
	relateId: number;
	status: number;
	tenantId: string;
	createdAt: string;
	updatedAt: string;
	deletedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
	{ field: 'msgId', label: '自增ID' },
	{ field: 'consumerId', label: '接受者' },
	{ field: 'fromConsumerId', label: '发送者' },
	{
		field: 'category',
		label: '主类型',
		render(row: any) {
			return renderDict(row.category, 'consumer_msg_type_main');
		},
	},
	{
		field: 'msgType',
		label: '类型',
		render(row: any) {
			return renderDict(row.msgType, 'consumer_msg_type');
		},
	},
	{ field: 'msgTitle', label: '消息标题' },
	{ field: 'msgBody', label: '消息内容' },
	{ field: 'msgAbstract', label: '消息摘要' },
	{ field: 'deviceKey', label: '设备标识' },
	{ field: 'relateConsumerId', label: '关联用户' },
	{ field: 'relateId', label: '关联ID' },
	{
		field: 'status',
		label: '状态',
		render(row: any) {
			return renderDict(row.status, 'consumer_msg_status');
		},
	},
	{ field: 'tenantId', label: '租户ID' },
	{ field: 'createdAt', label: '创建时间' },
	{ field: 'updatedAt', label: '更新时间' },
	{ field: 'deletedAt', label: '删除时间' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
	{
		fieldName: 'msgId',
		component: 'Input',
		label: '自增ID',
		dependencies: { show: () => false, triggerFields: [''], },
		componentProps: {
			placeholder: '',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'consumerId',
		component: 'InputNumber',
		label: '接受者',
		componentProps: {
			placeholder: '请输入接受者',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: z.number({ required_error: '请输入接受者', invalid_type_error: '无效数字' }),
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'fromConsumerId',
		component: 'InputNumber',
		label: '发送者',
		componentProps: {
			placeholder: '请输入发送者',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: null,
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'category',
		component: 'Select',
		label: '主类型',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择主类型',
			options: getDictOptions('consumer_msg_type_main'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'selectRequired',
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'msgType',
		component: 'Select',
		label: '类型',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择类型',
			options: getDictOptions('consumer_msg_type'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'selectRequired',
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'msgTitle',
		component: 'Input',
		label: '消息标题',
		componentProps: {
			placeholder: '请输入消息标题',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'msgBody',
		component: 'Input',
		label: '消息内容',
		componentProps: {
			placeholder: '请输入消息内容',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'msgAbstract',
		component: 'Input',
		label: '消息摘要',
		componentProps: {
			placeholder: '请输入消息摘要',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'deviceKey',
		component: 'Input',
		label: '设备标识',
		componentProps: {
			placeholder: '请输入设备标识',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'relateConsumerId',
		component: 'InputNumber',
		label: '关联用户',
		componentProps: {
			placeholder: '请输入关联用户',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: z.number({ required_error: '请输入关联用户', invalid_type_error: '无效数字' }),
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'relateId',
		component: 'InputNumber',
		label: '关联ID',
		componentProps: {
			placeholder: '请输入关联ID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: null,
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'status',
		component: 'Select',
		label: '状态',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择状态',
			options: getDictOptions('consumer_msg_status'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'selectRequired',
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'tenantId',
		component: 'Input',
		label: '租户ID',
		componentProps: {
			placeholder: '请输入租户ID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: null,
		formItemClass: 'col-span-1',
	},];