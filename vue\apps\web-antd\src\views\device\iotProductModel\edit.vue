<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <template #items="slotProps">
        <div>
            <div v-for="(item, index) in slotProps.value" style="display: flex; align-items: center;">
              <InputGroup compact>
                <Input type="text" :addon-before="'\ ' + item.modelOrder + '\ '" :value="item.modelKey" style="width: 40%;" readonly />
                <Input type="text" :value="item.modelName" width="70%" style="width: 40%;" readonly />
              </InputGroup>
              <Button v-if="!isView" @click="handleEditParam(index)" >编辑</Button>
              <Button v-if="!isView" @click="handleDeleteParam(index)" >删除</Button>
              <Button v-if="isView" @click="handleEditParam(index)" >查看</Button>
            </div>
          <Button v-if="!isView" @click="handleAddParamClick">添加参数</Button>
        </div>
      </template>
      <template #enumList="slotProps">
        <div>
          <div v-for="(item, index) in slotProps.value" style="display: flex; align-items: center;">
            <Input type="text" v-model:value="item.value" placeholder="枚举值 例如:0" /> 
            <Input  type="text" v-model:value="item.text" placeholder="枚举描述"/>
            <Button v-if="!isView" @click="handleDeleteEnum(index)" >删除</Button>
          </div>
        <Button v-if="!isView" @click="handleAddEnum">添加枚举项</Button>
        </div>
      </template>
      <template #itemKeys="slotProps">
        <div style="display: flex; align-items: center;" >
          <Input v-for="(item, index) in slotProps.value" type="number" :value="item" @change="async (e:any)=>{await editArray(formApi, 'itemKeys', slotProps.value, index, e.target.value); }" />
        </div>
      </template>
      <template #formulaList="slotProps">
        <InputGroup class="w-full" style="display: flex; align-items: center;" compact>
          <Input :value="slotProps.value?.join(' ')" class="w-3/4" readonly/>
          <Button type="primary" @click="openFormulaModal" class="w-1/4">生成公式</Button>
        </InputGroup>
      </template>
    </BasicForm>
    <ObjectModal @confirm="handlerObjectSelected" />
    <FormulaModal @confirm="handleFormulaConfirm" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, nextTick, ref, type Ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Button, Input, InputGroup} from 'ant-design-vue';
import { Edit, View } from '#/api/device/iotProductModel';
import { editSchema } from '../iotModel/model';
import objectModal from '../iotModel/object.vue';
import { editArray } from '#/utils/bindArray';
import formulaModal from './formula.vue';

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  productKey: string;
  type: string;  
  update: boolean;
  view: boolean;
}
const currentProductKey = ref('');
const isUpdate = ref(false);
const currentType = ref('');
const isView = ref(false);
const currentTypeName = computed(() => {
  return currentType.value === 'property' ? "属性" : currentType.value === 'function' ? "方法" : currentType.value === 'event' ? "事件" : "标签";
});

const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') + currentTypeName.value  : $t('pages.common.add') + currentTypeName.value;
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-4',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-4 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    formApi.updateSchema([{
      fieldName: 'type',
      component: 'Select',
      label: '模型类别',
      componentProps: {
        placeholder: '请选择',
        onUpdateValue: (e: any) => {
          console.log(e);
        },
        disabled: true,
      },
      defaultValue: 'property',
      rules: 'required',
      formItemClass: 'col-span-2',
  },]);
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, productKey, update, type, view} = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    currentType.value = type;
    isView.value = view;
    currentProductKey.value = productKey;
    if (isUpdate.value || isView.value) {
      const record = await View({ modelId: id });
      record.props = [];
      if(record.isChart === 1){
        record.props.push("isChart");
      }
      if (record.isMonitor === 1) {
        record.props.push("isMonitor");
      }
      if (record.isReadonly === 1) {
        record.props.push("isReadonly");
      }
      if (record.isHistory === 1) {
        record.props.push("isHistory");
      }
      if (record.isSharePerm === 1) {
        record.props.push("isSharePerm");
      }
      await formApi.setValues(record);
    }
    await formApi.setValues({
        productKey: productKey,
        type: type,
    });
    
    drawerApi.setState({ confirmLoading: false, loading: false })
    if(!isView.value){
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
      commonConfig: {
        componentProps: {
          readonly: false,
          "only-read": false,
        }
      }
    });
    }else{
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
      commonConfig: {
        componentProps: {
          readonly: true,
          "only-read": true,
        }
      }
    });
    }
    
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    data.isChart = (data.props && data.props.includes("isChart")) ? 1 : 0;
    data.isMonitor = (data.props &&data.props.includes("isMonitor")) ? 1 : 0;
    data.isReadonly = (data.props &&data.props.includes("isReadonly")) ? 1 : 0;
    data.isHistory = (data.props &&data.props.includes("isHistory")) ? 1 : 0;
    data.isSharePerm = (data.props &&data.props.includes("isSharePerm")) ? 1 : 0;
    data.productKey = currentProductKey.value;
    data.type = currentType.value;
    await (Edit(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

async function handleAddEnum() {
  let datas = await formApi.getValues();
  console.log("handleAddEnum", datas.enumList);
  let latestindex = 0; 
  datas.enumList.forEach((item:any) => {
    if(item.index > latestindex){
      latestindex = item.index;
    }
  });

  await formApi.setValues({ enumList: [...datas.enumList, {index:latestindex+1, value: '', text: ''}] });
}

async function handleDeleteEnum(index:any) {
  console.log("handleDeleteEnum", index);
  let datas = await formApi.getValues();
  datas.enumList = datas.enumList.filter((item:any, i:any) => item.index !== index);
  await formApi.setValues({ enumList: datas.enumList });
}

async function handleDeleteParam(index:any) {
  console.log("handleDeleteParam", index);
  let datas = await formApi.getValues();
  datas.items = datas.items.filter((item:any, i:any) => i !== index);
  await formApi.setValues({ items: datas.items });
}
async function handleEditParam(i:any) {
  let editData =  cloneDeep(await formApi.getValues());
  await ObjectModalApi.setData({index: i, update:true, formData: editData.items[i], view: isView.value});
  ObjectModalApi.open();
}

const [ObjectModal, ObjectModalApi] = useVbenModal({
  connectedComponent: objectModal,
});
async function handleAddParamClick() {
  await ObjectModalApi.setData({update:false});
  ObjectModalApi.open();
}

async function handlerObjectSelected(index:any, update:boolean, datas:any) {
  console.log("handlerObjectSelected", datas);
  let editData = await formApi.getValues();
  let newItems = []
  if (!update){
    newItems = [...editData.items, datas];
  }else{
    newItems = editData.items.map((item:any, i:any) => {
      if (i === index) {
        return datas;
      }
      return item;
    });
  }
  await formApi.setValues({ items: newItems });
}

const [FormulaModal, FormulaModalApi] = useVbenModal({
  connectedComponent: formulaModal,
});

function openFormulaModal() {
  console.log("openFormulaModal");
  FormulaModalApi.setData({ productKey: currentProductKey.value });
  FormulaModalApi.open();
}

async function handleFormulaConfirm(formulaList: any) {
  console.log("handleFormulaConfirm", formulaList);
  await formApi.setValues({ formulaList: formulaList });
}

</script>