<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';
import { ref } from 'vue';
import { Input } from 'ant-design-vue';
import TiandituMap from '../map/index.vue';
import type { TiandituMapInstance } from '../map/types';
import { getTiandituSearchUrl, getTiandituReverseGeocodeUrl, TIANDITU_CONFIG } from '#/config/tianditu';

const emit = defineEmits<{
  locationSelected: [lng: number, lat: number, address?: string];
}>();

// 地图相关
const mapRef = ref<TiandituMapInstance>();
const selectedLocation = ref({
  lng: TIANDITU_CONFIG.DEFAULT_CONFIG.center[0],
  lat: TIANDITU_CONFIG.DEFAULT_CONFIG.center[1],
  address: ''
});

// 地图选点状态
const hasSelectedPoint = ref(false);
let currentMarker: any = null;

// 地址搜索相关
const searchKeyword = ref('');
const searchLoading = ref(false);
const searchResults = ref<Array<{
  name: string;
  address: string;
  lng: number;
  lat: number;
}>>([]);
let searchTimer: any = null;

// 地图就绪事件
const onMapReady = (map: any) => {
  console.log('地图选点弹窗 - 地图初始化完成');

  // 如果已经有选择的点，在地图上显示标记
  if (hasSelectedPoint.value) {
    setTimeout(() => {
      updateMarker(selectedLocation.value.lng, selectedLocation.value.lat);
      // 将地图中心移动到选择的点
      const mapInstance = mapRef.value?.getMapInstance();
      const T = mapRef.value?.getTiandituAPI();
      if (mapInstance && T) {
        const point = new T.LngLat(selectedLocation.value.lng, selectedLocation.value.lat);
        mapInstance.centerAndZoom(point, 15);
      }
    }, 500);
  }
};

// 地图点击事件
const onMapClick = async (event: any) => {
  const { lng, lat } = event.lnglat;
  console.log('地图点击位置:', lng, lat);

  // 获取地址信息
  const address = await getReverseGeocode(lng, lat);

  // 更新选择的位置
  selectedLocation.value = {
    lng: lng,
    lat: lat,
    address: address
  };

  // 标记已选择点
  hasSelectedPoint.value = true;

  // 更新地图标记
  updateMarker(lng, lat);
};

// 更新地图标记
const updateMarker = (lng: number, lat: number) => {
  const mapInstance = mapRef.value?.getMapInstance();
  const T = mapRef.value?.getTiandituAPI();

  if (mapInstance && T) {
    // 移除现有标记
    if (currentMarker) {
      mapInstance.removeOverLay(currentMarker);
    }

    // 创建新标记
    const point = new T.LngLat(lng, lat);
    currentMarker = new T.Marker(point);

    // 创建信息窗口
    const infoWindow = new T.InfoWindow();
    infoWindow.setContent(`
      <div style="padding: 10px; min-width: 200px;">
        <h4 style="margin: 0 0 8px 0; color: #1890ff;">选择的设备位置</h4>
        <p style="margin: 4px 0;"><strong>经度:</strong> ${lng.toFixed(6)}</p>
        <p style="margin: 4px 0;"><strong>纬度:</strong> ${lat.toFixed(6)}</p>
        <p style="margin: 4px 0; color: #666;">点击确认按钮应用此位置</p>
      </div>
    `);

    // 绑定点击事件
    currentMarker.addEventListener('click', () => {
      currentMarker.openInfoWindow(infoWindow);
    });

    // 添加到地图
    mapInstance.addOverLay(currentMarker);

    console.log('标记已更新到位置:', lng, lat);
  }
};

// 地址搜索方法
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    searchResults.value = [];
    return;
  }

  searchLoading.value = true;
  console.log('搜索地址:', searchKeyword.value);

  try {
    // 使用天地图搜索API进行模糊搜索
    const searchUrl = getTiandituSearchUrl(searchKeyword.value.trim());

    const response = await fetch(searchUrl);
    const data = await response.json();

    console.log('天地图搜索结果:', data);

    if (data.status === '0' && data.pois && data.pois.length > 0) {
      // 清空之前的搜索结果
      searchResults.value = [];

      // 添加搜索结果
      data.pois.forEach((poi: any) => {
        searchResults.value.push({
          name: poi.name || searchKeyword.value,
          address: poi.address || poi.name || searchKeyword.value,
          lng: parseFloat(poi.lonlat.split(' ')[0]),
          lat: parseFloat(poi.lonlat.split(' ')[1])
        });
      });

      console.log('找到', searchResults.value.length, '个搜索结果');
    } else {
      console.log('未找到搜索结果，尝试地理编码');
      // 如果搜索失败，尝试地理编码
      await fallbackToGeocoding();
    }

  } catch (error) {
    console.error('搜索出错:', error);
    // 如果出错，尝试精确地理编码
    await fallbackToGeocoding();
  } finally {
    searchLoading.value = false;
  }
};

// 备用的精确地理编码搜索
const fallbackToGeocoding = () => {
  return new Promise<void>((resolve) => {
    try {
      const T = mapRef.value?.getTiandituAPI();
      if (!T) {
        resolve();
        return;
      }

      const geocoder = new T.Geocoder();
      geocoder.getPoint(searchKeyword.value, (result: any) => {
        if (result.getStatus() === 0) {
          const point = result.getLocationPoint();
          if (point) {
            console.log('精确搜索成功:', point);

            searchResults.value = [{
              name: searchKeyword.value,
              address: searchKeyword.value,
              lng: point.lng,
              lat: point.lat
            }];
          } else {
            searchResults.value = [];
          }
        } else {
          searchResults.value = [];
        }
        resolve();
      });
    } catch (error) {
      console.error('精确搜索也失败:', error);
      searchResults.value = [];
      resolve();
    }
  });
};

// 逆地理编码 - 根据经纬度获取地址
const getReverseGeocode = async (lng: number, lat: number): Promise<string> => {
  try {
    console.log('开始逆地理编码:', lng, lat);

    // 使用天地图逆地理编码API
    const reverseUrl = getTiandituReverseGeocodeUrl(lng, lat);
    console.log('逆地理编码请求URL:', reverseUrl);

    const response = await fetch(reverseUrl);

    if (!response.ok) {
      console.error('HTTP请求失败:', response.status, response.statusText);
      return '未知地址';
    }

    const data = await response.json();
    console.log('逆地理编码完整结果:', JSON.stringify(data, null, 2));

    if (data.status === '0' && data.result) {
      const result = data.result;

      // 优先使用 formatted_address
      if (result.formatted_address) {
        console.log('✅ 获取到formatted_address:', result.formatted_address);
        return result.formatted_address;
      }

      // 如果没有 formatted_address，尝试从 addressComponent 构建
      if (result.addressComponent) {
        const addr = result.addressComponent;
        console.log('addressComponent:', addr);

        const addressParts = [
          addr.city,
          addr.address,
          addr.road
        ].filter(Boolean);

        if (addressParts.length > 0) {
          const fullAddress = addressParts.join('');
          console.log('✅ 构建的地址:', fullAddress);
          return fullAddress;
        }
      }

      console.warn('⚠️ 无法从结果中提取地址信息');
      return '未知地址';
    } else {
      console.warn('❌ 逆地理编码API返回错误:', {
        status: data.status,
        msg: data.msg,
        fullResponse: data
      });
      return '未知地址';
    }
  } catch (error) {
    console.error('❌ 逆地理编码请求异常:', error);
    return '未知地址';
  }
};

// 输入变化处理
const handleInputChange = () => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 如果输入为空，清空搜索结果
  if (!searchKeyword.value.trim()) {
    searchResults.value = [];
    return;
  }

  // 设置防抖，500ms后执行搜索
  searchTimer = setTimeout(() => {
    if (searchKeyword.value.trim().length >= 2) {
      handleSearch();
    }
  }, 500);
};

// 选择搜索结果
const selectSearchResult = (result: any) => {
  console.log('选择搜索结果:', result);

  // 更新选择的位置
  selectedLocation.value = {
    lng: result.lng,
    lat: result.lat,
    address: result.address || result.name || '未知地址'
  };

  // 标记已选择点
  hasSelectedPoint.value = true;

  // 更新地图标记
  updateMarker(result.lng, result.lat);

  // 将地图中心移动到选择的点
  const mapInstance = mapRef.value?.getMapInstance();
  const T = mapRef.value?.getTiandituAPI();
  if (mapInstance && T) {
    const point = new T.LngLat(result.lng, result.lat);
    mapInstance.centerAndZoom(point, 15);
  }

  // 清空搜索结果和关键词
  searchResults.value = [];
  searchKeyword.value = '';
};

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = '';
  searchResults.value = [];
  searchLoading.value = false;
  if (searchTimer) {
    clearTimeout(searchTimer);
  }
};



// 确认选择位置
const handleConfirm = () => {
  if (!hasSelectedPoint.value) {
    console.warn('请先在地图上选择一个位置');
    return;
  }

  console.log('确认选择位置:', selectedLocation.value);
  emit('locationSelected', selectedLocation.value.lng, selectedLocation.value.lat, selectedLocation.value.address);
  modalApi.close();
};

const [Modal, modalApi] = useVbenModal({
  title: '地图选点',
  centered: true,
  destroyOnClose: true,
  onConfirm: handleConfirm,
  onCancel: () => {
    console.log('弹窗取消');
    modalApi.close();
  },
  onOpenChange: (isOpen) => {
    if (isOpen) {
      console.log('地图选点弹窗打开');
      return null;
    }
    console.log('地图选点弹窗关闭');
    modalApi.close();
  },
});

// 暴露打开弹窗的方法
const openModal = async (initialLng?: number, initialLat?: number) => {
  // 如果传入了初始经纬度，使用传入的值，否则使用默认值
  if (initialLng !== undefined && initialLat !== undefined && initialLng !== 0 && initialLat !== 0) {
    // 获取初始位置的地址
    const address = await getReverseGeocode(initialLng, initialLat);
    selectedLocation.value = {
      lng: initialLng,
      lat: initialLat,
      address: address
    };
    hasSelectedPoint.value = true;
    console.log('使用表单中的经纬度:', initialLng, initialLat, '地址:', address);
  } else {
    // 重置为默认位置
    hasSelectedPoint.value = false;
    selectedLocation.value = {
      lng: TIANDITU_CONFIG.DEFAULT_CONFIG.center[0],
      lat: TIANDITU_CONFIG.DEFAULT_CONFIG.center[1],
      address: ''
    };
    console.log('使用默认经纬度');
  }

  // 清除现有标记
  if (currentMarker && mapRef.value) {
    const mapInstance = mapRef.value.getMapInstance();
    if (mapInstance) {
      mapInstance.removeOverLay(currentMarker);
      currentMarker = null;
    }
  }

  modalApi.open();
};

defineExpose({
  openModal
});
</script>

<template>
  <div>
    <Modal class="h-[750px] w-[1200px] location-picker-modal">
    <div class="location-picker-content">
      <!-- 选择状态提示和搜索框 -->
      <div class="selection-status">
        <div class="status-left">
          <div v-if="!hasSelectedPoint" class="status-tip">
            <span class="tip-icon">📍</span>
            <span class="tip-text">请在地图上点击选择设备位置</span>
          </div>
          <div v-else class="status-selected">
            <span class="selected-icon">✅</span>
            <div class="selected-content">
              <div class="selected-text">
                已选择位置：经度 {{ selectedLocation.lng.toFixed(6) }}，纬度 {{ selectedLocation.lat.toFixed(6) }}
              </div>
              <div v-if="selectedLocation.address" class="selected-address">
                地址：{{ selectedLocation.address }}
              </div>
              
            </div>
          </div>
        </div>

        <!-- 地址搜索框 -->
        <div class="search-container">
          <Input.Search
            v-model:value="searchKeyword"
            placeholder="请输入地址"
            style="width: 280px"
            allow-clear
            @search="handleSearch"
            @input="handleInputChange"
            @clear="clearSearch"
            :loading="searchLoading"
          />

          <!-- 搜索结果下拉列表 -->
          <div v-if="searchResults.length > 0" class="search-results">
            <div
              v-for="(result, index) in searchResults"
              :key="index"
              class="search-result-item"
              @click="selectSearchResult(result)"
            >
              <div class="result-name">{{ result.name }}</div>
              <div class="result-address">{{ result.address }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 天地图组件 -->
      <div class="map-container">
        <TiandituMap
          ref="mapRef"
          :width="'100%'"
          :height="'100%'"
          :center="TIANDITU_CONFIG.DEFAULT_CONFIG.center"
          :zoom="TIANDITU_CONFIG.DEFAULT_CONFIG.zoom"
          :disable-auto-center="true"
          @map-ready="onMapReady"
          @map-click="onMapClick"
        />
      </div>
    </div>
    </Modal>
  </div>
</template>

<style scoped>
/* 弹窗样式 - 去除滚动条 */
.location-picker-modal :deep(.vben-modal-content) {
  overflow: hidden !important;
}

.location-picker-modal :deep(.vben-modal-body) {
  padding: 0 !important;
  overflow: hidden !important;
}

/* 确保弹窗内容区域无滚动条 */
.location-picker-modal {
  overflow: hidden;
}

.location-picker-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  height: 100%;
  overflow: hidden;
}

.selection-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border-radius: 6px;
  background: #f5f5f5;
  border-left: 4px solid #1890ff;
  gap: 16px;
  flex-shrink: 0; /* 防止被压缩 */
}

.status-left {
  flex: 1;
  display: flex;
  align-items: center; /* 确保左侧内容也垂直居中 */
}

.status-tip {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-selected {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex-wrap: wrap;
}

.selected-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tip-icon, .selected-icon {
  font-size: 16px;
}

.tip-text {
  color: #666;
  font-size: 12px;
}

.selected-text {
  color: #333;
  font-weight: 500;
  font-family: monospace;
}

.selected-address {
  color: #666;
  font-size: 12px;
}

/* 搜索框样式 */
.search-container {
  position: relative;
  min-width: 280px;
}

/* 搜索结果样式 */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.search-result-item {
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.search-result-item:hover {
  background: #f5f5f5;
}

.search-result-item:last-child {
  border-bottom: none;
}

.result-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.result-address {
  font-size: 12px;
  color: #666;
}

.map-container {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  cursor: crosshair;
  min-height: 0; /* 允许flex子项收缩 */
}
</style>
