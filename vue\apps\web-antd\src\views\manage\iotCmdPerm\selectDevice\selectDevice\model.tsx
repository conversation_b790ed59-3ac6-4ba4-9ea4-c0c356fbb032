import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

// 表单验证规则

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox', // 只负责显示复选框
    width: 60,
    align: 'center',
  },
  {
    title: '编号',
    field: 'deviceId',
    align: 'center',
    width: 120,
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'center',
    width: -1,
  },
  {
    title: '设备名称',
    field: 'deviceName',
    align: 'center',
    width: -1,
  },
  {
    title: '所属产品',
    field: 'productName',
    align: 'center',
    width: -1,
  },
  {
    title: '是否启用',
    field: 'status',
    align: 'center',
    width: -1,
    slots: { default: 'status' },
  },
];

// 表格列接口
export interface RowType {
  deviceId: number;
  productKey: string;
  deviceKey: string;
  deviceName: string;
  longitude: number;
  latitude: number;
  firmwareVersion: number;
  isShadow: number;
  imgUrl: string;
  deviceState: number;
  alarmState: number;
  rssi: number;
  thingsModelValue: string;
  networkAddress: string;
  networkIp: string;
  status: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
}
