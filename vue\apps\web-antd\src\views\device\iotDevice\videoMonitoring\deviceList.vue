<template>
  <div class="device-list-container">
    <div class="device-list-header">
      <h3>关联监控设备</h3>
      <Button type="primary" @click="openSelectDeviceModal" class="select-device-btn">
        关联监控设备
      </Button>
    </div>

    <!-- 设备列表 -->
    <div class="device-list-content" :key="forceUpdateKey">
      <div v-for="(device, index) in monitorDeviceList" :key="`${device.cameraKey}-${forceUpdateKey}`"
        class="device-item" :class="{
          'selected-device': selectedDeviceId === device.cameraKey,
          'switching-device': isDeviceSwitching && selectedDeviceId === device.cameraKey
        }" @click="selectDevice(device)">
        <div class="device-info">
          <div class="device-name-wrapper">
            <span class="device-name">{{ device.cameraKey }}</span>
            <span v-if="playingDeviceId === device.cameraKey" class="status-badge playing">播放中</span>
            <span v-else-if="isDeviceSwitching && selectedDeviceId === device.cameraKey"
              class="status-badge switching">切换中</span>
            <span v-else-if="device.cameraState" class="status-badge device-state"
              :class="`state-${device.cameraState}`">{{ getDictLabel('device_state', device.cameraState) }}</span>
          </div>
          <span v-if="device.position" class="device-position">📍 {{ device.position }}</span>
        </div>
        <div class="device-actions">
          <Button type="text" size="small" @click.stop="removeDevice(index)" class="remove-btn">
            删除
          </Button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="monitorDeviceList.length === 0" class="empty-state">
        <div class="empty-icon">📹</div>
        <p>暂无关联的监控设备</p>
        <p class="empty-hint">点击上方按钮添加监控设备</p>
      </div>
    </div>

    <!-- 选择设备弹窗 -->
    <SelectDeviceModal v-model:open="selectDeviceModalOpen" @confirm="handleDeviceSelect" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { Button, message } from 'ant-design-vue';
import SelectDeviceModal from './selectDeviceModal.vue';
import { CameraBindList, GB28181StartReview, EditCameraBind, DeleteCameraBind } from '#/api/device/iotDevice';
import { getDictLabel } from '#/utils/dict';

// 接收父组件传递的设备标识
const props = defineProps<{
  deviceKey?: string; // 主设备的设备标识
}>();

const emit = defineEmits<{
  showVideo: [deviceId: string, videoUrl: string];
}>();

// 监控设备列表
const monitorDeviceList = ref<any[]>([]);
const selectDeviceModalOpen = ref(false);
const selectedDeviceId = ref<string | null>(null); // 当前选中的设备ID（UI显示用）
const playingDeviceId = ref<string | null>(null); // 当前正在播放的设备ID（实际播放状态）
const isDeviceSwitching = ref(false); // 设备切换中状态，防止重复点击
const isRestoring = ref(false); // 正在恢复状态，用于UI提示
const forceUpdateKey = ref(0); // 强制更新key，用于重新渲染

// 获取监控设备列表
const loadMonitorDevices = async () => {
  try {
    console.log('📡 加载监控设备绑定列表，主设备 deviceKey:', props.deviceKey);

    if (!props.deviceKey) {
      console.warn('⚠️ 主设备 deviceKey 为空，无法加载监控设备列表');
      monitorDeviceList.value = [];
      return;
    }

    // 调用监控设备绑定列表接口
    const res = await CameraBindList({
      page: 1,
      pageSize: 100,
      deviceKey: props.deviceKey // 使用传入的主设备 deviceKey
    });

    console.log('📡 CameraBindList 接口响应:', res);

    if (res && res.items) {
      // 直接使用原始数据，不进行转换
      monitorDeviceList.value = res.items;
      console.log('📡 监控设备绑定列表加载完成，共', res.items.length, '条记录:', monitorDeviceList.value);
    } else {
      console.warn('⚠️ 未获取到设备绑定数据');
      monitorDeviceList.value = [];
    }

  } catch (error) {
    console.error('❌ 加载监控设备绑定列表失败:', error);
    monitorDeviceList.value = [];
  }
};

// 恢复到正在播放设备的选中状态
const restoreToPlayingDevice = async () => {
  console.log('🔄 开始恢复到正在播放的设备选中状态...');

  isRestoring.value = true;

  // 先清空选中状态，确保UI更新
  selectedDeviceId.value = null;

  // 强制重新渲染，确保所有卡片状态重置
  forceUpdateKey.value++;

  // 等待Vue完成DOM更新
  await nextTick();

  // 再次确保清空状态
  selectedDeviceId.value = null;

  // 再等待一次DOM更新
  await nextTick();

  // 短暂延迟后设置正确的选中状态
  setTimeout(async () => {
    // 如果有正在播放的设备，则恢复选中状态；如果没有，则保持未选中状态
    if (playingDeviceId.value) {
      selectedDeviceId.value = playingDeviceId.value;
      console.log('🔄 恢复完成，当前选中设备:', playingDeviceId.value);
    } else {
      selectedDeviceId.value = null;
      console.log('🔄 恢复完成，无正在播放的设备，保持未选中状态');
    }

    // 等待最终的DOM更新完成
    await nextTick();

    // 恢复状态完成
    setTimeout(() => {
      isRestoring.value = false;
    }, 200);
  }, 100);
};
const getVideoStreamUrl = async (deviceKey: string) => {
  try {
    console.log('🔗 调用GB28181StartReview接口获取流地址, deviceKey:', deviceKey);

    const response = await GB28181StartReview({
      deviceKey: deviceKey
    });
    console.log('🔗 GB28181StartReview接口完整响应:', JSON.stringify(response, null, 2));

    // 根据实际接口响应结构解析流地址
    let streamUrl = '';
    let streamData = null;

    // 尝试从不同层级获取流数据
    if (response && response.data) {
      streamData = response.data;
    } else if (response) {
      streamData = response;
    }

    if (streamData) {
      console.log('📡 解析到的流数据:', streamData);

      // 优先使用FLV格式，因为Jessibuca对FLV支持最好
      if (streamData.flv) {
        streamUrl = streamData.flv;
        console.log('📡 使用FLV流地址:', streamUrl);
      }
      // 备用方案1: HLS格式
      else if (streamData.hls) {
        streamUrl = streamData.hls;
        console.log('📡 使用HLS流地址:', streamUrl);
      }
      // 备用方案2: RTMP格式
      else if (streamData.rtmp) {
        streamUrl = streamData.rtmp;
        console.log('📡 使用RTMP流地址:', streamUrl);
      }
      // 备用方案3: 通用播放地址
      else if (streamData.publishUrl) {
        streamUrl = streamData.publishUrl;
        console.log('📡 使用推流地址:', streamUrl);
      }
    }

    console.log('📡 最终解析得到的流地址:', streamUrl);

    if (!streamUrl) {
      console.warn('⚠️ 接口未返回有效的流地址');
      throw new Error('接口未返回有效的流地址');
    }

    return streamUrl;
  } catch (error: any) {
    console.error('❌ 调用GB28181StartReview接口失败:', error);
    // 根据错误类型提供更具体的错误信息
    if (error?.response?.status === 500) {
      throw new Error('查询上午云设备对讲播放地址失败，请检查设备是否在线');
    } else if (error?.response?.status === 50) {
      throw new Error('查询上午云设备对讲播放地址失败，请稍后重试');
    } else {
      throw new Error(error?.message || '获取视频流地址失败');
    }
  }
};

// 选择设备
const selectDevice = async (device: any) => {
  console.log('🎥 点击监控设备，设备信息:', device);

  // 防止在切换过程中重复点击
  if (isDeviceSwitching.value) {
    console.log('🔄 设备正在切换中，请稍候...');
    return;
  }

  // 使用 cameraKey 作为设备标识
  const cameraKey = device.cameraKey;

  if (!cameraKey) {
    console.error('❌ cameraKey 缺失，无法播放视频:', device);
    console.log('设备完整信息:', JSON.stringify(device, null, 2));
    return;
  }

  // 如果点击的是当前正在播放的设备，不重复操作
  if (playingDeviceId.value === cameraKey) {
    console.log('🎥 设备已在播放中，无需重复操作');
    // 但是要确保UI选中状态正确
    selectedDeviceId.value = cameraKey;
    return;
  }

  // 立即更新选中状态，给用户即时反馈
  selectedDeviceId.value = cameraKey;

  // 开始切换设备
  isDeviceSwitching.value = true;

  console.log('🔄 准备切换设备:', {
    from: playingDeviceId.value,
    to: cameraKey
  });

  // 显示加载提示
  const loadingMsg = message.loading('正在切换设备并获取视频流地址...', 0);

  try {
    // 调用接口获取视频流URL，使用 cameraKey 作为 deviceKey 参数
    const videoUrl = await getVideoStreamUrl(cameraKey);
    console.log('📡 获取到的视频流URL:', videoUrl);

    if (!videoUrl) {
      loadingMsg();
      message.error('获取视频流地址失败');
      // 切换失败，2秒后恢复到正在播放的设备选中状态
      setTimeout(async () => {
        await restoreToPlayingDevice();
      }, 2000);
      return;
    }

    // 关闭加载提示
    loadingMsg();

    // 通知父组件显示视频（父组件会自动处理之前播放的停止）
    emit('showVideo', cameraKey, videoUrl);

    // 切换成功，更新实际播放的设备ID
    playingDeviceId.value = cameraKey;

    console.log('🎥 设备切换成功:', {
      selected: selectedDeviceId.value,
      playing: playingDeviceId.value
    });
    message.success('设备切换成功，正在加载视频流');

  } catch (error: any) {
    console.error('❌ 设备切换失败:', error);
    loadingMsg(); // 关闭加载提示
    message.error('设备切换失败: ' + (error?.message || '未知错误'));

    // 切换失败，2秒后恢复到正在播放的设备选中状态
    setTimeout(async () => {
      await restoreToPlayingDevice();
    }, 2000);

  } finally {
    // 无论成功还是失败，都要重置切换状态
    isDeviceSwitching.value = false;
  }
};

// 清除选中状态（供外部调用）
const clearSelection = () => {
  selectedDeviceId.value = null;
  playingDeviceId.value = null;
  isDeviceSwitching.value = false;
  isRestoring.value = false;
  console.log('🔄 清除设备选中状态和播放状态');
};

// 更新选中设备（供外部调用）
const updateSelectedDevice = (deviceId: string | null) => {
  selectedDeviceId.value = deviceId;
  playingDeviceId.value = deviceId; // 同步更新播放状态
  isDeviceSwitching.value = false;
  isRestoring.value = false;
  console.log('🔄 更新设备选中状态和播放状态至:', deviceId);
};

// 设置播放状态（供外部调用，用于视频播放成功后确认）
const setPlayingDevice = (deviceId: string | null) => {
  playingDeviceId.value = deviceId;
  selectedDeviceId.value = deviceId; // 同步更新选中状态
  console.log('🔄 设置播放设备状态至:', deviceId);
};

// 停止播放（供外部调用）
const stopPlayingDevice = () => {
  playingDeviceId.value = null;
  // 选中状态保持不变，用户可能还想看到最后选中的设备
  console.log('🔄 停止播放设备');
};

// 暴露方法给父组件
defineExpose({
  clearSelection,
  updateSelectedDevice,
  setPlayingDevice,
  stopPlayingDevice
});

// 打开选择设备弹窗
const openSelectDeviceModal = () => {
  selectDeviceModalOpen.value = true;
  console.log('🔍 打开选择设备弹窗');
};

// 处理设备选择和位置关联
const handleDeviceSelect = async (selectedDevice: any) => {
  console.log('✅ 开始处理设备选择和位置关联:');
  console.log('原始设备数据:', JSON.stringify(selectedDevice, null, 2));
  console.log('主设备 deviceKey:', props.deviceKey);

  // 验证必要的字段
  if (!selectedDevice.deviceKey) {
    console.error('❌ 监控设备的设备标识缺失，无法关联设备');
    message.error('监控设备的设备标识缺失，无法关联设备');
    return;
  }

  if (!props.deviceKey) {
    console.error('❌ 主设备的设备标识缺失，无法关联设备');
    message.error('主设备的设备标识缺失，无法关联设备');
    return;
  }

  if (!selectedDevice.deviceLocation) {
    console.error('❌ 设备位置信息缺失，无法关联设备');
    message.error('设备位置信息缺失，无法关联设备');
    return;
  }

  try {
    console.log('📡 准备调用监控设备绑定接口');

    // 构建绑定请求参数
    const bindParams = {
      deviceKey: props.deviceKey, // 主设备标识
      cameraKey: selectedDevice.deviceKey, // 监控设备标识
      position: selectedDevice.deviceLocation // 位置信息
    };

    console.log('📡 绑定请求参数:', bindParams);

    // 显示加载提示
    const loadingMsg = message.loading('正在关联监控设备...', 0);

    // 调用绑定接口
    const result = await EditCameraBind(bindParams);
    console.log('📡 绑定接口响应:', result);

    // 关闭加载提示
    loadingMsg();

    // 接口调用成功（不抛出异常就认为成功，无论返回值是什么）
    message.success('监控设备关联成功！');
    console.log('✅ 监控设备关联成功');

    // 重新加载监控设备列表
    await loadMonitorDevices();
    console.log('✅ 监控设备列表已刷新');

  } catch (error: any) {
    console.error('❌ 监控设备关联失败:', error);
    message.error('监控设备关联失败: ' + (error?.message || '未知错误'));
  }
};

// 移除设备
const removeDevice = async (index: number) => {
  const device = monitorDeviceList.value[index];
  console.log('🗑️ 准备移除设备:', device);

  if (!device.bindId) {
    console.error('❌ bindId 缺失，无法删除设备绑定');
    message.error('设备绑定ID缺失，无法删除');
    return;
  }

  try {
    // 显示加载提示
    const loadingMsg = message.loading('正在删除设备绑定...', 0);

    // 调用删除接口
    await DeleteCameraBind({
      bindId: device.bindId
    });

    // 关闭加载提示
    loadingMsg();

    console.log('✅ 设备绑定删除成功');
    message.success('设备绑定删除成功');

    // 如果删除的是当前正在播放的设备，清除播放状态
    if (playingDeviceId.value === device.cameraKey) {
      playingDeviceId.value = null;
      console.log('🔄 删除的是正在播放的设备，清除播放状态');
    }

    // 如果删除的是当前选中的设备，清除选中状态
    if (selectedDeviceId.value === device.cameraKey) {
      selectedDeviceId.value = null;
      console.log('🔄 删除的是当前选中的设备，清除选中状态');
    }

    // 重新加载监控设备列表
    await loadMonitorDevices();

  } catch (error: any) {
    console.error('❌ 删除设备绑定失败:', error);
    message.error('删除设备绑定失败: ' + (error?.message || '未知错误'));
  }
};

onMounted(() => {
  loadMonitorDevices();
});
</script>

<style scoped>
.device-list-container {
  width: 300px;
  height: 100vh;
  border-right: 1px solid #e8e8e8;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.device-list-header {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.device-list-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.select-device-btn {
  width: 100%;
}

.device-list-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  max-height: calc(100vh - 120px);
  /* 减去头部高度 */
}

/* 滚动条样式 */
.device-list-content::-webkit-scrollbar {
  width: 6px;
}

.device-list-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.device-list-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.device-list-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.device-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.device-item.selected-device {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.device-item.selected-device:hover {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.device-item.current-device {
  border-color: #52c41a;
  background-color: #f6ffed;
}

.device-item.current-device:hover {
  border-color: #52c41a;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
}

.device-item.switching-device {
  pointer-events: none;
  position: relative;
}

.device-item.switching-device::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(24, 144, 255, 0.1) 30%, rgba(24, 144, 255, 0.1) 70%, transparent 70%);
  animation: switching 1.5s ease-in-out infinite;
  border-radius: 6px;
}

@keyframes switching {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.device-item.switching-device .device-name {
  color: #1890ff;
  font-weight: 600;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.device-name-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-name {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.current-badge {
  background-color: #52c41a;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

.status-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

.status-badge.playing {
  background-color: #1890ff;
  color: #fff;
}

.status-badge.switching {
  background-color: #fa8c16;
  color: #fff;
}

.status-badge.device-state {
  background-color: #722ed1;
  color: #fff;
}

/* 未激活状态 - 黄色 */
.status-badge.device-state.state-1 {
  background-color: #faad14;
  color: #fff;
}

/* 在线状态 - 绿色 */
.status-badge.device-state.state-2 {
  background-color: #52c41a;
  color: #fff;
}

/* 离线状态 - 灰色 */
.status-badge.device-state.state-3 {
  background-color: #8c8c8c;
  color: #fff;
}

.device-id {
  color: #8c8c8c;
  font-size: 12px;
}

.device-position {
  color: #1890ff;
  font-size: 14px;
  margin-top: 2px;
}

.device-created-time {
  color: #52c41a;
  font-size: 10px;
  margin-top: 2px;
}

.device-actions {
  display: flex;
  gap: 8px;
}

.remove-btn {
  color: #ff4d4f;
}

.remove-btn:hover {
  color: #ff7875;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 8px 0;
}

.empty-hint {
  font-size: 12px;
  color: #bfbfbf;
}
</style>
