<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <template #imgUrl="slotProps">
        <ImageUpload v-bind="slotProps" :accept="accept" :max-number=1
          :api="(file: any, progressEvent: any) => uploadApi(file, progressEvent, true)" />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';
import { uploadApi } from '#/api';
import { useVbenForm } from '#/adapter/form';
import { ImageUpload } from '#/components/upload';
import { Edit, View } from '#/api/union/iotUnion';
import { editSchema } from './model';
import { getSysDeptTreeApi } from '#/api/system/dept';
const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
}
/** 图片类型 */
const accept = ref(['jpg', 'jpeg', 'png', 'gif', 'webp']);
/** 编辑/新增flag */
const isUpdate = ref(false);
const isView = ref(false);
/** 标题 */
const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

/** 表单实例 */
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

/** 抽屉实例 */
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    console.log('isOpen onOpenChange', isOpen);
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, update, view, } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;
    if (isUpdate.value || isView.value) {
      const record = await View({ unionId: id });
      /** 修改status值为bolean */
      record.status = record.status == '0' ? true : false;
      await formApi.setValues(record);
    }
    drawerApi.setState({ confirmLoading: false, loading: false })

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});

/** 提交 */
async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    /** status转number */
    data.status = data.status == true ? '0' : '1';
    await (Edit(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}
/** 返回 */
async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}


/** 获取机构树内容,并格式化 */
async function getDeptTree() {
  const treeRes = await getSysDeptTreeApi();
  const treeData = treeRes.items;
  addFullName(treeData, 'deptName', ' / ');
  return treeData;
}
//加载机构选项
async function loadDeptOptions() {
  const treeData = await getDeptTree();
  /** 更新字段 */
  formApi.updateSchema([
    {
      componentProps: {
        /** 显示内容,对应值 */
        fieldNames: { label: 'deptName', value: 'deptId' },
        showSearch: true,
        /** 结构树data */
        treeData: treeData,
        /** 默认展示全部 */
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        treeNodeLabelProp: 'fullName',
        // 选中后显示在输入框的值
        displayRender: (label: any, selected: any, node: any) => {
          return node.props.dataRef.fullName || label;
        },
        // 模糊搜索
        filterTreeNode: (input: string, treeNode: any) => {
          const label = treeNode.deptName || treeNode.fullName || '';
          return label.toLowerCase().includes(input.toLowerCase());
        },
      },
      /** 要修改的字段 */
      fieldName: 'deptId',
    },
  ]);
}
loadDeptOptions()
</script>
