<!-- AlarmForm.vue -->
<template>
    <BasicForm :schema="alarmSchema" />
</template>

<script setup lang="ts">
import { ref, defineProps, watch } from 'vue';
import { useVbenForm, type VbenFormSchema } from '@vben/common-ui';

const props = defineProps<{
    formModel: Record<string, any>;
}>();

// 报警信息表单配置
const alarmSchema: VbenFormSchema[] = [
    {
        fieldName: 'relateAlarm.alarmConfig.alarmName',
        component: 'Input',
        label: '报警名称',
        formItemClass: 'col-span-1',
    },
    {
        fieldName: 'relateAlarm.alarmLevel',
        component: 'Input',
        label: '报警级别',
        componentProps: {
            placeholder: '请选择报警级别',
        },
        formItemClass: 'col-span-1',
    },
    {
        fieldName: 'relateAlarm.startTime',
        component: 'Input',
        label: '开始时间',
        formItemClass: 'col-span-1',
    },
    {
        fieldName: 'relateAlarm.endTime',
        component: 'Input',
        label: '结束时间',
        formItemClass: 'col-span-1',
    },
    {
        fieldName: 'relateAlarm.confirmState',
        component: 'Input',
        label: '处理状态',
        formItemClass: 'col-span-1',
    },
    {
        fieldName: 'relateAlarm.confirmUser',
        component: 'Input',
        label: '处理用户',
        formItemClass: 'col-span-1',
    },
    {
        fieldName: 'relateAlarm.sceneConditions',
        component: 'Input',
        label: '触发条件',
        formItemClass: 'col-span-2',
    },
];

const [BasicForm, formApi] = useVbenForm({
    commonConfig: {
        componentProps: {
            class: 'w-full',
        },
        formItemClass: 'col-span-1',
    },
    layout: 'vertical',
    schema: alarmSchema,
    showDefaultActions: false,
    wrapperClass: 'grid-cols-1 gap-x-4',
});

watch(
    () => props.formModel,
    (newVal) => {
        if (newVal) {
            // 将 formModel 中的值设置到表单
            formApi.setValues(newVal);
            console.log('报警表单数据已更新:', newVal);
        }
    },
    { immediate: true } // 初始渲染时立即执行一次
);
</script>