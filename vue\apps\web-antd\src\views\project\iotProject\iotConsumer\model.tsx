import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict, type MemberSumma } from '#/utils';
import { getDictOptions } from '#/utils/dict';
export class State {
	public consumerId = 0; // 终端用户ID
	public userName = ''; // 用户名
	public userPassword = ''; // 登录密码
	public userSalt = ''; // 加密盐
	public nickName = ''; // 用户昵称
	public realName = null; // 真实姓名
	public sex = null; // 性别（0男 1女 2未知）
	public phone = ''; // 手机
	public email = null; // 邮箱
	public status = 0; // 状态：0=正常，1=停用
	public projectId = 0; // 项目ID
	public tenantId = ''; // 租户ID
	public deptId = 0; // 所属机构
	public createdDept = 0; // 创建部门
	public createdBy = 0; // 创建者
	public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
	public createdAt = ''; // 创建时间
	public updatedBy = 0; // 更新者
	public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
	public updatedAt = ''; // 更新时间
	public deletedBy = 0; // 删除人
	public deletedBySumma?: null | MemberSumma = null; // 删除人摘要信息
	public deletedAt = ''; // 删除时间
	public remark = null; // 备注

	constructor(state?: Partial<State>) {
		if (state) {
			Object.assign(this, state);
		}
	}
}

export function newState(state: State | Record<string, any> | null): State {
	if (state !== null) {
		if (state instanceof State) {
			return cloneDeep(state);
		}
		return new State(state);
	}
	return new State();
}
// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
	// {
	// 	fieldName: 'projectId',
	// 	component: 'Select',
	// 	label: '项目',
	// 	formItemClass: 'col-span-1',
	// },
	{
		fieldName: 'userName',
		component: 'Input',
		label: '用户名',
		componentProps: {
			placeholder: '请输入用户名',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'phone',
		component: 'Input',
		label: '手机',
		componentProps: {
			placeholder: '请输入手机',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'status',
		component: 'Select',
		label: '状态',
		componentProps: {
			placeholder: '请选择状态',
			options: getDictOptions('sys_normal_disable'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
	{
		type: 'checkbox',
		width: 40,
	},
	{
		title: '编号',
		field: 'consumerId',
		align: 'center',
		width: 50,
	},
	{
		title: '头像',
		field: 'avatar',
		align: 'center',
		width: -1,
		slots:
		{
			default: "avatar"
		},
	},
	{
		title: '用户名',
		field: 'userName',
		align: 'center',
		width: -1,
	},
	{
		title: '用户昵称',
		field: 'nickName',
		align: 'center',
		width: -1,
	},
	{
		title: '姓名',
		field: 'realName',
		align: 'center',
		width: -1,
	},
	{
		title: '性别', field: 'sex', align: 'center', width: 50,
		slots: {
			default: ({ row }) => {
				return renderDict(row.sex, 'sys_user_sex');
			}
		},
	},
	{
		title: '手机',
		field: 'phone',
		align: 'center',
		width: 120,
	},
	{
		title: '邮箱',
		field: 'email',
		align: 'center',
		width: -1,
	},
	{
		title: '登录IP',
		field: 'ip',
		align: 'center',
		width: 150,
	},
	{
		title: '状态',
		field: 'status',
		align: 'center',
		width: 100,
		slots:
		{
			default: "status"
		},
	},
	{
		title: '所属机构',
		field: 'deptName',
		align: 'center',
		width: -1,
	},
	{ title: '操作', width: 200, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
	consumerId: number;
	userName: string;
	userPassword: string;
	userSalt: string;
	nickName: string;
	realName: string;
	sex: string;
	phone: string;
	email: string;
	status: string;
	projectId: number;
	tenantId: string;
	deptId: number;
	createdDept: number;
	createdBy: number;
	createdAt: string;
	updatedBy: number;
	updatedAt: string;
	deletedBy: number;
	deletedAt: string;
	remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
	{ field: 'consumerId', label: '终端用户ID' },
	{ field: 'userName', label: '用户名' },
	{ field: 'userPassword', label: '登录密码' },
	{ field: 'userSalt', label: '加密盐' },
	{ field: 'nickName', label: '用户昵称' },
	{ field: 'realName', label: '真实姓名' },
	{
		field: 'sex',
		label: '性别',
		render(row: any) {
			return renderDict(row.sex, 'sys_user_sex');
		},
	},
	{ field: 'phone', label: '手机' },
	{ field: 'email', label: '邮箱' },
	{
		field: 'status',
		label: '状态',
		render(row: any) {
			return renderDict(row.status, 'sys_normal_disable');
		},
	},
	{ field: 'projectId', label: '项目ID' },
	{ field: 'tenantId', label: '租户ID' },
	{ field: 'deptId', label: '所属机构' },
	{ field: 'createdDept', label: '创建部门' },
	{ field: 'createdBy', label: '创建者' },
	{ field: 'createdAt', label: '创建时间' },
	{ field: 'updatedBy', label: '更新者' },
	{ field: 'updatedAt', label: '更新时间' },
	{ field: 'deletedBy', label: '删除人' },
	{ field: 'deletedAt', label: '删除时间' },
	{ field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
	{
		fieldName: 'consumerId',
		component: 'Input',
		label: '终端用户ID',
		dependencies: { show: () => false, triggerFields: [''], },
		componentProps: {
			placeholder: '',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'projectId',
		component: 'Select',
		label: '项目',
	},
	{
		fieldName: 'userName',
		component: 'Input',
		label: '用户名',
		componentProps: {
			placeholder: '请输入用户名',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'userPassword',
		component: 'InputPassword',
		label: '密码',
		componentProps: {
			placeholder: '请输入密码',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
	},
	{
		fieldName: 'confirmPassword',
		component: 'InputPassword',
		componentProps: {
			placeholder: '请再次输入密码',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
	}, {
		fieldName: 'realName',
		component: 'Input',
		label: '真实姓名',
		componentProps: {
			placeholder: '请输入真实姓名',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: null,
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'nickName',
		component: 'Input',
		label: '用户昵称',
		componentProps: {
			placeholder: '请输入用户昵称',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'sex',
		component: 'Select',
		label: '性别',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择性别（0男 1女 2未知）',
			options: getDictOptions('sys_user_sex'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: null,
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'phone',
		component: 'Input',
		label: '手机',
		componentProps: {
			placeholder: '请输入手机',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
		rules: z
			.string()
			.regex(/^1[3,4578]\d{9}$/, { message: '请输入正确的手机号' })
			.optional()
			.or(z.literal('')),
	}, {
		fieldName: 'email',
		component: 'Input',
		label: '邮箱',
		componentProps: {
			placeholder: '请输入邮箱',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'status',
		component: 'Switch',
		label: '状态',
		componentProps: {
			style: {
				width: '20px',
			},
			placeholder: '请选择状态',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'selectRequired',
	}, {
		fieldName: 'remark',
		component: 'Input',
		label: '备注',
		componentProps: {
			placeholder: '请输入备注',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: null,
		formItemClass: 'col-span-1',
	},];