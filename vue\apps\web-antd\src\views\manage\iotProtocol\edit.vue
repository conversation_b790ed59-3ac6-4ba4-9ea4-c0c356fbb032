<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <template #dataFormat="slotProps">
        <Button type="primary" @click="openDataFormatDialog(slotProps)">
          修改协议数据格式
        </Button>
        <Modal v-model:open="dataFormatVisible" title="修改协议数据格式" @ok="handleDataFormatOk" :footer="null">
          <div style="max-height: 60vh; overflow: auto;">
            <span>上报、下发格式一致。</span>
            <div v-if="slotProps.value != undefined" style="margin-top: 8px;">
              <highlight class="w-full h500 code-json" :code="slotProps.value" language="json" :autodetect="false"
                @update:code="async (d: any) => dataFormatValue = d" />
            </div>
            <div style="text-align:right;margin-top:16px;">
              <Button @click="dataFormatVisible = false">取消</Button>
              <Button type="primary" style="margin-left:8px;" @click="handleDataFormatOk">确定</Button>
            </div>
          </div>
        </Modal>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<style>
.h500 code {
  height: 300px;
}
</style>
<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';
import { Modal, message, Button, Textarea } from 'ant-design-vue';
import { useVbenForm } from '#/adapter/form';
import { Edit, View } from '#/api/manage/iotProtocol';
import { editSchema } from './model';

import hljs from 'highlight.js/lib/core';
import go from 'highlight.js/lib/languages/go';
import typescript from 'highlight.js/lib/languages/typescript';
import javascript from 'highlight.js/lib/languages/javascript';
import xml from 'highlight.js/lib/languages/xml';
import sql from 'highlight.js/lib/languages/sql';
import json from 'highlight.js/lib/languages/json';
import 'highlight.js/styles/atom-one-dark.css';
import 'highlight.js/lib/common';
hljs.registerLanguage('ts', typescript);
hljs.registerLanguage('js', javascript);
hljs.registerLanguage('go', go);
hljs.registerLanguage('xml', xml);
hljs.registerLanguage('sql', sql);
hljs.registerLanguage('json', json);
import highlight from '#/adapter/highlightedit';

hljs.registerLanguage('ts', typescript);
hljs.registerLanguage('js', javascript);


const dataFormatVisible = ref(false);
const dataFormatValue = ref('');

// 打开弹窗，回显当前内容
function openDataFormatDialog(slotProps: any) {
  dataFormatValue.value = slotProps.value || '';

  dataFormatVisible.value = true;
}

// 确认按钮
function handleDataFormatOk() {
  // 回填到表单
  formApi.setFieldValue('dataFormat', dataFormatValue.value);
console.log('dataFormatValue.value', dataFormatValue.value);
  dataFormatVisible.value = false;
  message.success('数据格式已修改');
}

//之前的代码-----------------------------
const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
}

const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, update, view, } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;
    await formApi.setValues({ dataFormat: '' });
    if (isUpdate.value || isView.value) {
      const record = await View({ protocolId: id });
      // 强制类型转换 --给protocolType
      if (isUpdate.value && record && record.protocolType !== undefined) {
        record.protocolType = String(record.protocolType);
      }
      await formApi.setValues(record);
    }
    drawerApi.setState({ confirmLoading: false, loading: false })

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    await (Edit(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

</script>
