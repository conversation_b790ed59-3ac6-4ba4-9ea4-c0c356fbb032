import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

export class State {
  public abandonLogId = 0; // 废弃数据ID
  public identify = ''; // 标识符（uuid）
  public modelName = ''; // 物模型名称
  public logType = 0; // 类型（1=属性上报）
  public logValue = ''; // 日志值
  public deviceKey = ''; // 设备标识
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'abandonLogId',
    component: 'InputNumber',
    label: '废弃数据ID',
    componentProps: {
      placeholder: '请输入废弃数据ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '废弃数据ID',
    field: 'abandonLogId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '标识符（uuid）',
    field: 'identify',
    align: 'left',
    width: -1,
 },
  {
    title: '物模型名称',
    field: 'modelName',
    align: 'left',
    width: -1,
 },
  {
    title: '类型（1=属性上报）',
    field: 'logType',
    align: 'left',
    width: -1,
 },
  {
    title: '日志值',
    field: 'logValue',
    align: 'left',
    width: -1,
 },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  {
    title: '备注',
    field: 'remark',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  abandonLogId: number;
  identify: string;
  modelName: string;
  logType: number;
  logValue: string;
  deviceKey: string;
  tenantId: string;
  createdAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'abandonLogId',  label: '废弃数据ID'},
  {  field: 'identify',  label: '标识符（uuid）'},
  {  field: 'modelName',  label: '物模型名称'},
  {  field: 'logType',  label: '类型（1=属性上报）'},
  {  field: 'logValue',  label: '日志值'},
  {  field: 'deviceKey',  label: '设备标识'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdAt',  label: '创建时间'},
  {  field: 'remark',  label: '备注'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'abandonLogId',
    component: 'Input',
    label: '废弃数据ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'identify',
    component: 'Input',
    label: '标识符（uuid）',
    componentProps: {
      placeholder: '请输入标识符（uuid）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'modelName',
    component: 'Input',
    label: '物模型名称',
    componentProps: {
      placeholder: '请输入物模型名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'logType',
    component: 'InputNumber',
    label: '类型（1=属性上报）',
    componentProps: {
      placeholder: '请输入类型（1=属性上报）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入类型（1=属性上报）', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'logValue',
    component: 'Input',
    label: '日志值',
    componentProps: {
      placeholder: '请输入日志值',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'tenantId',
    component: 'Input',
    label: '租户ID',
    componentProps: {
      placeholder: '请输入租户ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];