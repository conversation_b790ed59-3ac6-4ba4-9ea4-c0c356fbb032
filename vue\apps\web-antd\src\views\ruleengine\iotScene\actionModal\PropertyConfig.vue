<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { View as GetProductDetail } from '#/api/device/iotProduct';
import { Input, InputNumber, Select, Button, } from 'ant-design-vue';

const emit = defineEmits(['update:paramsnew', 'update:params']);
const props = defineProps<{
  params: { key?: string | undefined; value?: any }[]
  productKey: string
}>();

//用上面的emit和props，
//当props.params变化时，触发Newemit事件
watch(() => props.params, (newVal) => {
  console.log('修改过后的PropertyConfig 接收到的 params:', newVal);
  emit('update:params', newVal);
}, { immediate: true, deep: true });

//通知父组件更新params
emit('update:params', props.params);


const paramsold = ref<{ key?: string; value?: any }[]>(props.params || []);

// 属性下拉选项和属性详情映射
const propertyOptions = ref<{ label: string; value: string }[]>([]);
const propertyMap = ref<Record<string, any>>({});
// 获取属性值输入框类型和选项
const triggerListExt = ref([
  {
    key: '0',
    type: 'string',
    valueOptions: [],
  },
]
);
// 根据产品key加载属性
async function loadProperties() {
  console.log('PropertyConfig 传入的 productKey:', props.productKey);
  console.log('属性 options:', propertyOptions.value);
  paramsold.value = [];
  triggerListExt.value = [];
  // 如果没有传入 productKey，则清空属性选项和映射
  if (!props.productKey) {
    propertyOptions.value = [];
    propertyMap.value = {};
    return;
  }
  const res = await GetProductDetail({ productKey: props.productKey });
  console.log('GetProductDetail 返回:', res);
  const properties = res?.tsl?.properties || [];
  console.log('接口返回的属性:', res?.tsl?.properties);
  propertyOptions.value = properties
    .filter((p: any) => ['integer', 'decimal', 'string', 'bool', 'enum'].includes(p.valueType?.type))
    .map((p: any) => ({
      label: p.name,
      value: p.key,
    }));
  propertyMap.value = {};
  for (const p of properties) {
    propertyMap.value[p.key] = p;
  }
  console.log('属性 options哈哈哈哈:', propertyOptions.value);
}

// 监听产品变化
watch(() => props.productKey, loadProperties, { immediate: true });
watch(() => paramsold.value, (newVal) => {
  console.log('params变化:', paramsold.value);
  emit('update:paramsnew', paramsold.value);
  emit('update:params', paramsold.value);
}, { immediate: true, deep: true });

// 添加/删除属性
function addParam() {
  if (!paramsold.value) {
    paramsold.value = [];
  }
  paramsold.value.push({ key: '', value: '' });

}
function removeParam(index: number) {
  const arr = (paramsold.value || []).slice();
  arr.splice(index, 1);
  paramsold.value = arr;
}


function handlerConditionKeyChanged(index: number, e: any) {
  console.log('handlerConditionKeyChanged', index, e);
  while (triggerListExt.value.length < index + 1) {
    triggerListExt.value.push({
      key: "",
      type: "",
      valueOptions: [],
    });
  }
  let item = propertyMap.value[e];

  if (!!triggerListExt.value[index] && !!item) {
    triggerListExt.value[index].key = item.key;
    triggerListExt.value[index].type = item.valueType.type;
    if (item.valueType.type === 'enum') {
      triggerListExt.value[index].valueOptions = item.valueType.enumList.map((i: any) => {
        return { label: i.text, value: i.value }
      })
    }
    if (item.valueType.type === 'bool') {
      triggerListExt.value[index].valueOptions = [
        { label: item.valueType.trueText, value: 1 },
        { label: item.valueType.falseText, value: 0 },
      ] as any;
    }
  }
}

const setParamsOld = async (params: any[]) => {
  await loadProperties();
  console.log('setParamsOld 接收到的 params:', params);
  paramsold.value = params || [];
  // 给 triggerListExt 赋值
  triggerListExt.value = paramsold.value.map((item) => {
    const property = propertyMap.value[item.key || 'undefined'];
    if (property && property.valueType) {
      let extItem = {
        key: property.key,
        type: property.valueType.type,
        valueOptions: [],
      };
      if (property.valueType.type === 'enum' && property.valueType.enumList) {
        extItem.valueOptions = property.valueType.enumList.map((i: any) => ({
          label: i.text,
          value: i.value,
        }));
      }
      if (property.valueType.type === 'bool') {
        extItem.valueOptions = [{ label: property.valueType.trueText, value: 1 },
        { label: property.valueType.falseText, value: 0 }] as any;
      }
      return extItem;
    }
    return { key: '', type: '', valueOptions: [] };
  });
  nextTick(() => {

    console.log('setParamsOld 更新后的 paramsold:', paramsold.value, triggerListExt.value);
  });
};

defineExpose({
  setParamsOld,
});

</script>

<template>
  <div style="border:1px solid #d9e5f6;padding:12px;border-radius:8px;">
    <h2 style="margin-bottom: 20px;">动作脚本配置</h2>
    <div v-for="(item, condIdx) in paramsold" :key="condIdx"
      style="display: grid; grid-template-columns: 1fr minmax(120px, 1fr) auto; align-items: center; gap: 12px; margin-bottom: 8px;">
      <Select v-model:value="item.key" placeholder="请选择属性" :options="propertyOptions"
        @change="handlerConditionKeyChanged(condIdx, $event)" style="width: 100%;" show-search
        :filter-option="(input, option) => option && option.label && option.label.toLowerCase().includes(input.toLowerCase())" />

      <Input v-show="triggerListExt[condIdx]?.type == 'string'" v-model:value="item.value" placeholder="请输入参数"
        style="width: 100%;" />
      <InputNumber v-show="triggerListExt[condIdx]?.type == 'integer' || triggerListExt[condIdx]?.type == 'decimal'"
        v-model:value="item.value" placeholder="请输入参数" style="width: 100%;" />
      <Select v-show="triggerListExt[condIdx]?.type == 'enum' || triggerListExt[condIdx]?.type == 'bool'"
        v-model:value="item.value" placeholder="请输入参数" :options="triggerListExt[condIdx]?.valueOptions"
        style="width: 100%;" />
      <Button v-if="condIdx > 0" type="primary" danger @click="removeParam(condIdx)">删除</Button>
    </div>
    <Button type="link" @click="addParam" :disabled="propertyOptions.length === 0">+ 添加属性</Button>
  </div>
</template>
