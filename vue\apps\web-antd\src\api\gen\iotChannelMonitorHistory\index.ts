import { requestClient } from '#/api/request';

// 获取资源监控历史表列表
export function List(params:any) {
  return requestClient.get<any>('monitor/iotChannelMonitorHistory/list', { params });
}

// 删除/批量删除资源监控历史表
export function Delete(params:any) {
  return requestClient.post<any>('monitor/iotChannelMonitorHistory/delete', { ...params });
}

// 添加/编辑资源监控历史表
export function Edit(params:any) {
  return requestClient.post<any>('monitor/iotChannelMonitorHistory/edit', { ...params });
}

// 获取资源监控历史表指定详情
export function View(params:any) {
  return requestClient.get<any>('monitor/iotChannelMonitorHistory/view', { params });
}

// 导出资源监控历史表
export function Export(params:any) {
  return requestClient.post<Blob>('/monitor/iotChannelMonitorHistory/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}