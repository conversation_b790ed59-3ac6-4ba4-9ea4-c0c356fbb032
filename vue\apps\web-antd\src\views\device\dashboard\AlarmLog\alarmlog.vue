<script lang="ts" setup>
import {ref, onMounted } from 'vue';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { List } from '#/api/ruleengine/iotAlarmLog';
import { columns, type RowType } from './model';
import { ListNoPage as ListProduct } from '#/api/device/iotProduct';

const confirmEnable = ref(true);
async function fetchConfirmEnable() {
  try {
    const response = await List({ page: 1, pageSize: 1 });
    confirmEnable.value = response.data?.confirmEnable !== false;
  } catch (error) {
    console.error('获取confirmEnable参数失败:', error);
  }
}
onMounted(() => {
  fetchConfirmEnable();
});
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'alarmLogId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});

async function getProductOptions() {
  const res = await ListProduct({});
  if (!res.items) {
    return [];
  }
  const options = res.items.map((item: any) => ({
    label: item.productName,
    value: item.productKey,
  }));
  return options;
}

onMounted(async () => {
  const res = await getProductOptions();
  gridApi.formApi.updateSchema([{
    fieldName: 'productKey',
    component: 'Select',
    label: '报警产品',
    componentProps: {
      placeholder: '请输入报警产品',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: res,
    },
  }]
  );
});

</script>
<template>
  <div class="p-0" style="height: calc(var(--vben-content-height) - 0px); overflow-y: auto;">
    <Grid class="h-[300px]">
    </Grid>
  </div>
</template>
