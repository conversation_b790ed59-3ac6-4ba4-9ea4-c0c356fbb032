import { requestClient } from '#/api/request';

// 获取产品接入点绑定关系表列表
export function List(params:any) {
  return requestClient.get<any>('device/iotProductEnter/list', { params });
}

// 删除/批量删除产品接入点绑定关系表
export function Delete(params:any) {
  return requestClient.post<any>('device/iotProductEnter/delete', { ...params });
}

// 添加/编辑产品接入点绑定关系表
export function Edit(params:any) {
  return requestClient.post<any>('device/iotProductEnter/edit', { ...params });
}

// 获取产品接入点绑定关系表指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotProductEnter/view', { params });
}

// 导出产品接入点绑定关系表
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotProductEnter/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}