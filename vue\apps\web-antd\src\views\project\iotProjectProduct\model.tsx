import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';

export class State {
  public ppId = 0; // 自增ID
  public projectId = 0; // 项目ID
  public productKey = ''; // 产品标识
  public productAlias = ''; // 产品别名
  public panelConfig = null; // 面板配置
  public userConfig = null; // 用户配置
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
				fieldName: 'projectId',
				component: 'InputNumber',
				label: '项目ID',
				componentProps: {
					placeholder: '请输入项目ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				}, 
        dependencies: {
          show: () => false,
          triggerFields: [''],
        },
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'productKey',
				component: 'Input',
				label: '产品标识',
				componentProps: {
					placeholder: '请输入产品标识',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'productAlias',
				component: 'Input',
				label: '产品别名',
				componentProps: {
					placeholder: '请输入产品别名',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				formItemClass: 'col-span-1',
			},];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '自增ID',
    field: 'ppId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '项目ID',
    field: 'projectId',
    align: 'left',
    width: -1,
 },
  {
    title: '产品标识',
    field: 'productKey',
    align: 'left',
    width: -1,
 },
  {
    title: '产品别名',
    field: 'productAlias',
    align: 'left',
    width: -1,
 },
  {
    title: '面板配置',
    field: 'panelConfig',
    align: 'left',
    width: -1,
 },
  {
    title: '用户配置',
    field: 'userConfig',
    align: 'left',
    width: -1,
 },
  {
    title: '创建部门',
    field: 'createdDept',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  {
    title: '更新时间',
    field: 'updatedAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  ppId: number;
  projectId: number;
  productKey: string;
  productAlias: string;
  panelConfig: string;
  userConfig: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'ppId',  label: '自增ID'},
  {  field: 'projectId',  label: '项目ID'},
  {  field: 'productKey',  label: '产品标识'},
  {  field: 'productAlias',  label: '产品别名'},
  {  field: 'panelConfig',  label: '面板配置'},
  {  field: 'userConfig',  label: '用户配置'},
  {  field: 'createdDept',  label: '创建部门'},
  {  field: 'createdBy',  label: '创建者'},
  {  field: 'createdAt',  label: '创建时间'},
  {  field: 'updatedBy',  label: '更新者'},
  {  field: 'updatedAt',  label: '更新时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
 {
					fieldName: 'ppId',
					component: 'Input',
					label: '自增ID',
					dependencies: {   show: () => false,    triggerFields: [''],   },
					componentProps: {
						placeholder: '',
						onUpdateValue: (e: any) => {
							console.log(e);
						},   
					},
					formItemClass: 'col-span-1',
				},  {
				fieldName: 'projectId',
				component: 'InputNumber',
				label: '项目ID',
				componentProps: {
					placeholder: '请输入项目ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入项目ID', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'productKey',
				component: 'Input',
				label: '产品标识',
				componentProps: {
					placeholder: '请输入产品标识',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'productAlias',
				component: 'Input',
				label: '产品别名',
				componentProps: {
					placeholder: '请输入产品别名',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'panelConfig',
				component: 'Input',
				label: '面板配置',
				componentProps: {
					placeholder: '请输入面板配置',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'userConfig',
				component: 'Input',
				label: '用户配置',
				componentProps: {
					placeholder: '请输入用户配置',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},];