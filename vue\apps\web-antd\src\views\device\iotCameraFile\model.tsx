import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';

export class State {
  public fileId = 0; // 资源ID
  public deviceKey = ''; // 产生资源设备标识
  public fileType = 0; // 资源类型
  public fileName = ''; // 资源名称
  public fileUrl = ''; // 资源路径
  public fileTime = ''; // 资源获取时间
  public tenantId = ''; // 租户ID（报警配置所属租户）
  public deptId = 0; // 所属机构
  public createdAt = ''; // 创建时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
				fieldName: 'deviceKey',
				component: 'Input',
				label: '产生资源设备标识',
				componentProps: {
					placeholder: '请输入产生资源设备标识',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'fileType',
				component: 'InputNumber',
				label: '资源类型',
				componentProps: {
					placeholder: '请输入资源类型',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入资源类型', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'fileTime',
				component: 'DatePicker',
				label: '资源获取时间',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '资源ID',
    field: 'fileId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '产生资源设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
 },
  {
    title: '资源类型',
    field: 'fileType',
    align: 'left',
    width: -1,
 },
  {
    title: '资源名称',
    field: 'fileName',
    align: 'left',
    width: -1,
 },
  {
    title: '资源路径',
    field: 'fileUrl',
    align: 'left',
    width: -1,
 },
  {
    title: '资源获取时间',
    field: 'fileTime',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID（报警配置所属租户）',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '所属机构',
    field: 'deptId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  {
    title: '备注',
    field: 'remark',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  fileId: number;
  deviceKey: string;
  fileType: number;
  fileName: string;
  fileUrl: string;
  fileTime: string;
  tenantId: string;
  deptId: number;
  createdAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'fileId',  label: '资源ID'},
  {  field: 'deviceKey',  label: '产生资源设备标识'},
  {  field: 'fileType',  label: '资源类型'},
  {  field: 'fileName',  label: '资源名称'},
  {  field: 'fileUrl',  label: '资源路径'},
  {  field: 'fileTime',  label: '资源获取时间'},
  {  field: 'tenantId',  label: '租户ID（报警配置所属租户）'},
  {  field: 'deptId',  label: '所属机构'},
  {  field: 'createdAt',  label: '创建时间'},
  {  field: 'remark',  label: '备注'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
 {
					fieldName: 'fileId',
					component: 'Input',
					label: '资源ID',
					dependencies: {   show: () => false,    triggerFields: [''],   },
					componentProps: {
						placeholder: '',
						onUpdateValue: (e: any) => {
							console.log(e);
						},   
					},
					formItemClass: 'col-span-1',
				},  {
				fieldName: 'deviceKey',
				component: 'Input',
				label: '产生资源设备标识',
				componentProps: {
					placeholder: '请输入产生资源设备标识',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'fileType',
				component: 'InputNumber',
				label: '资源类型',
				componentProps: {
					placeholder: '请输入资源类型',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入资源类型', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'fileName',
				component: 'Input',
				label: '资源名称',
				componentProps: {
					placeholder: '请输入资源名称',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'fileUrl',
				component: 'Input',
				label: '资源路径',
				componentProps: {
					placeholder: '请输入资源路径',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'fileTime',
				component: 'DatePicker',
				label: '资源获取时间',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'tenantId',
				component: 'Input',
				label: '租户ID（报警配置所属租户）',
				componentProps: {
					placeholder: '请输入租户ID（报警配置所属租户）',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'deptId',
				component: 'InputNumber',
				label: '所属机构',
				componentProps: {
					placeholder: '请输入所属机构',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入所属机构', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'remark',
				component: 'Input',
				label: '备注',
				componentProps: {
					placeholder: '请输入备注',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},];