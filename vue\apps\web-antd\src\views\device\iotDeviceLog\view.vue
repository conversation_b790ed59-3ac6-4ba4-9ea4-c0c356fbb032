<script setup lang="ts">
import { useVbenModal } from '@vben/common-ui';
import { Description, useDescription } from '#/components/description';
import { viewSchema, type RowType } from './model';
const [BasicModal, modalApi] = useVbenModal({
  onOpenChange: handleOpenChange,
});
const [registerDescription, { setDescProps }] = useDescription({
  column: 1,
  schema: viewSchema,
});
async function handleOpenChange(open: boolean) {
  if (!open) {
    return null;
  }
  const { record } = modalApi.getData() as { record: RowType };
  setDescProps({ data: record }, true);
}
</script>
<template>
  <BasicModal :footer="false" class="w-[600px]" title="查看设备日志">
    <Description @register="registerDescription"></Description>
  </BasicModal>
</template>