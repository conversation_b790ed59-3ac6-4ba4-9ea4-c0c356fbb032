import { requestClient } from '#/api/request';

// 获取场景设备表列表
export function List(params:any) {
  return requestClient.get<any>('ruleengine/iotSceneDevice/list', { params });
}

// 删除/批量删除场景设备表
export function Delete(params:any) {
  return requestClient.post<any>('ruleengine/iotSceneDevice/delete', { ...params });
}

// 添加/编辑场景设备表
export function Edit(params:any) {
  return requestClient.post<any>('ruleengine/iotSceneDevice/edit', { ...params });
}

// 获取场景设备表指定详情
export function View(params:any) {
  return requestClient.get<any>('ruleengine/iotSceneDevice/view', { params });
}

// 导出场景设备表
export function Export(params:any) {
  return requestClient.post<Blob>('/ruleengine/iotSceneDevice/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}