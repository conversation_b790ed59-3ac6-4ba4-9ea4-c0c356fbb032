import { requestClient } from '#/api/request';

// 获取设备报警用户表列表
export function List(params:any) {
  return requestClient.get<any>('device/iotDeviceAlarmUser/list', { params });
}

// 删除/批量删除设备报警用户表
export function Delete(params:any) {
  return requestClient.post<any>('device/iotDeviceAlarmUser/delete', { ...params });
}

// 添加/编辑设备报警用户表
export function Edit(params:any) {
  return requestClient.post<any>('device/iotDeviceAlarmUser/edit', { ...params });
}

// 获取设备报警用户表指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotDeviceAlarmUser/view', { params });
}

// 导出设备报警用户表
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotDeviceAlarmUser/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}