import { h, ref } from 'vue';
import { Tag, Button } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public scriptId = 0; // ID
  public scriptName = ''; // 脚本名称
  public execOrder = 0; // 执行顺序
  public productKey = ''; // 产品标识
  public inputType = 0; // 脚本事件（1=设备上报 2=平台下发 3=设备上线 4=设备离线 5=HTTP接入 6=MQTT接入）
  public outputType = 0; // 输出动作（1=消息重发 2=HTTP接入 6=MQTT接入）

  public content = ''; // 脚本内容
  public status = 0; // 状态：0=正常，1=停用
  public tenantId = ''; // 租户ID
  public deptId = 0; // 所属机构
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'scriptName',
    component: 'Input',
    label: '脚本名称',
    componentProps: {
      placeholder: '请输入脚本名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
    componentProps: {
      placeholder: '请输入所属产品',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '编号',
    field: 'scriptId',
    align: 'center',
    width: 80,
  },
  {
    title: '脚本名称',
    field: 'scriptName',
    align: 'left',
    width: -1,
  },
  {
    title: '所属产品',
    field: 'productName', //productKey对应的名字
    align: 'left',
    width: -1,
  },

  //（1=设备上报 2=平台下发 3=设备上线 4=设备离线 5=HTTP接入 6=MQTT接入）
  {
    title: '脚本事件',
    field: 'inputType',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.inputType, DictEnum.SCRIPT_INPUT_TYPE);
      },
    },
  },

  {
    title: '输出动作',
    field: 'outputType',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.outputType, DictEnum.SCRIPT_OUTPUT_TYPE);
      },
    },
  },

  //0=正常，1=停用
  {
    title: '状态',
    field: 'status',
    align: 'center',
    width: 100,
    slots: { default: 'status' },
  },

  {
    title: '执行顺序',
    field: 'execOrder',
    align: 'center',
    width: 100,
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  scriptId: number;
  scriptName: string;

  ActionScript: string; //脚本语言
  execOrder: number;
  inputType: number;
  outputType: number;

  content: string;
  status: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  remark: string;
}

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'scriptId', label: 'ID' },
  { field: 'scriptName', label: '脚本名称' },
  { field: 'productName', label: '所属产品' },
  { field: 'execOrder', label: '执行顺序' },

  //（1=设备上报 2=平台下发 3=设备上线 4=设备离线 5=HTTP接入 6=MQTT接入）
  {
    field: 'inputType',
    label: '脚本事件',
    render(_, row: any) {
      return renderDict(row.inputType, DictEnum.SCRIPT_INPUT_TYPE);
    },
  },
  //（1=消息重发 2=HTTP接入 6=MQTT接入）
  {
    field: 'outputType',
    label: '输出动作',
    // render(_, row: any) {
    //   return renderDict(String(row.inputType), DictEnum.SCRIPT_OUTPUT_TYPE);
    // },
  },
  {
    field: 'outputType',
    label: '输出动作',
    render(_, row: any) {
      return renderDict(String(row.inputType), DictEnum.SCRIPT_OUTPUT_TYPE);
    },
  },
  { field: 'content', label: '脚本内容' },
  //0=正常，1=停用
  {
    field: 'status',
    label: '状态',
    render(_, row: any) {
      return renderDict(row.status, DictEnum.SCRIPT_STATE);
    },
  },
  // { field: 'tenantId', label: '租户ID' },
  // { field: 'deptId', label: '所属机构' },
  // { field: 'createdDept', label: '创建部门' },
  // { field: 'createdBy', label: '创建者' },
  { field: 'createdAt', label: '创建时间' },
  // { field: 'updatedBy', label: '更新者' },
  { field: 'updatedAt', label: '更新时间' },
  // { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'scriptId',
    component: 'Input',
    label: 'ID',
    dependencies: { show: () => false, triggerFields: [''] },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'scriptName',
    component: 'Input',
    label: '脚本名称',
    componentProps: {
      placeholder: '请输入脚本名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'execOrder',
    component: 'InputNumber',
    label: '执行顺序',
    defaultValue: 1,

    componentProps: {
      placeholder: '请输入执行顺序',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number().min(1, { message: '最小值为1' }),
  },

  //（1=设备上报 2=平台下发 3=设备上线 4=设备离线 5=HTTP接入 6=MQTT接入）

  {
    fieldName: 'inputType',
    component: 'Select',
    label: '脚本事件',
    componentProps: {
      options: [
        {
          label: '设备上报',
          value: '1',
        },
        {
          label: '平台下发',
          value: '2',
        },
        {
          label: '设备上线',
          value: '3',
        },
        {
          label: '设备离线',
          value: '4',
        },
        {
          label: 'HTTP接入',
          value: '5',
        },
        {
          label: 'MQTT接入',
          value: '6',
        },
      ],

      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },

  {
    fieldName: 'productKey',
    component: 'Input',
    label: '所属产品',
    componentProps: {
      readOnly: true,
    },

    dependencies: {
      if(values, formApi) {
        return !(values.inputType === '5' || values.inputType === '6');
      },
      // 只有指定的字段改变时，才会触发
      triggerFields: ['inputType'],
    },
  },
  {
    fieldName: 'inputChannelId',
    component: 'Input',
    label: '接入点',
    componentProps: {
      readOnly: true,
    },

    dependencies: {
      if(values, formApi) {
        return !!(values.inputType === '5' || values.inputType === '6');
      },
      // 只有指定的字段改变时，才会触发
      triggerFields: ['inputType'],
    },
  },

  //（1=消息重发 2=HTTP接入 6=MQTT接入）
  {
    fieldName: 'outputType',
    component: 'Select',
    label: '输出动作',
    componentProps: {
      placeholder: '请输入输出动作',
      options: getDictOptions(DictEnum.SCRIPT_OUTPUT_TYPE),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },

  //0=正常，1=停用
  {
    fieldName: 'status',
    component: 'Switch',
    label: '脚本状态',
    defaultValue: '1',
    controlClass: 'w-[20px]',
    componentProps: {
      onUpdateValue: (e: any) => {
        console.log(e);
      },

      checkedValue: '0',
      unCheckedValue: '1',
    },
    rules: 'required',
  },

  {
    fieldName: 'HttpScriptGeneration',
    label: '脚本生成',
    component: 'Button',

    dependencies: {
      if(values, formApi) {
        return !!(values.outputType === '2');
      },
      // 只有指定的字段改变时，才会触发
      triggerFields: ['outputType'],
    },
  },
  {
    fieldName: 'MQTTScriptGeneration',
    label: '脚本生成',
    component: 'Button',

    dependencies: {
      if(values, formApi) {
        return !!(values.outputType === '3');
      },
      // 只有指定的字段改变时，才会触发
      triggerFields: ['outputType'],
    },
  },

  // {
  //   fieldName: 'scriptGeneration',
  //   label: '脚本生成',
  //   component: Button,
  //   controlClass: 'w-[150px]',
  //   renderComponentContent: () => ({
  //     default: () => 'Http服务脚本',
  //   }),
  //   componentProps: {
  //     type: 'primary',
  //     onclick: () => {
  //       console.log(222);
  //     },
  //   },

  //   dependencies: {
  //     if(values, formApi) {
  //       return !!(values.outputType === '2');
  //     },
  //     // 只有指定的字段改变时，才会触发
  //     triggerFields: ['outputType'],
  //   },
  // },

  {
    fieldName: 'content',
    component: 'Input',
    label: '脚本内容',
    formItemClass: 'col-span-12',
    controlClass: 'w-[100%]',
    componentProps: {
      placeholder: '请输入脚本内容',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
];
