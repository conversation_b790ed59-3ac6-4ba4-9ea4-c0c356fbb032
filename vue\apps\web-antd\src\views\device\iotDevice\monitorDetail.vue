<template>
  <div>
    <Page class="h-full overflow-y-auto">
      <!-- 设备标题信息 -->
      <Card class="mb-4">
        <div class="flex items-center flex-wrap">
          <span class="text-lg font-medium">{{ device.deviceName }}</span>
          <span class="ml-6 text-gray-600">设备编号：{{ device.deviceKey }}</span>
          <span class="ml-6 text-gray-600">设备状态：{{ getDeviceStateLabel(device.deviceState) }}</span>
          <span class="ml-6 text-gray-600">所属产品：</span>
          <Button class="p-0 ml-1" type="link" @click="toProductDetail(product.productId)">{{ product.productName
            }}</Button>
          <span class="ml-6 text-gray-600" v-if="device.gatewayName || device.gatewayKey">
            网关设备：
            <!-- 如果有有效的网关ID，显示为可点击链接 -->
            <Button v-if="device.gatewayId && device.gatewayId !== 0" class="p-0 ml-1" type="link"
              @click="toGatewayDetail(device.gatewayId)">
              {{ device.gatewayName || device.gatewayKey || '未配置' }}
            </Button>
            <!-- 如果没有有效的网关ID，显示为普通文本 -->
            <span v-else class="text-gray-500 ml-1">
              {{ device.gatewayName || device.gatewayKey || '未配置' }}
            </span>
          </span>
        </div>
      </Card>

      <!-- 主要内容区域使用Tabs结构 -->
      <Card style="height:calc(100vh - 200px);" class="overflow-y-auto">
        <Tabs v-model:activeKey="currentTab" @change="onTabChange">
          <!-- 基本信息Tab -->
          <TabPane key="basic" tab="基本信息">
            <label style="font-size: 18px;">设备信息</label>
            <Divider />
            <BasicForm class="gap-[8px] mb-8"></BasicForm>

            <label style="font-size: 18px;">设备连接信息</label>
            <Divider />
            <ConnectForm class="gap-[8px]"></ConnectForm>
          </TabPane>

          <!-- 实时预览Tab -->
          <TabPane key="preview" tab="实时预览">
            <div class="grid grid-cols-1 xl:grid-cols-8 gap-6">
              <!-- 左侧：视频监控区域 -->
              <div class="xl:col-span-6">
                <Card title="视频监控" size="small">
                  <div class="aspect-video bg-gray-900 rounded-lg relative overflow-hidden video-player-container">
                    <!-- 视频播放器区域 -->
                    <div v-if="isStreaming" class="w-full h-full">
                      <!-- 使用Jessibuca播放器 -->
                      <jessibucaPlayer ref="jessibucaRef" :video-url="videoUrl" :has-audio="hasAudio" :autoplay="true"
                        :live="true" :visible="isStreaming" @error="onVideoError" @play="onVideoPlay"
                        @pause="onVideoPause" class="w-full h-full" />
                    </div>

                    <!-- 未开始直播时的占位内容 -->
                    <div v-else class="w-full h-full flex items-center justify-center">
                      <div class="text-center text-gray-400">
                        <div class="text-6xl mb-4">📹</div>
                        <div class="text-lg mb-2">监控设备</div>
                        <div class="text-sm">点击"开始直播"查看实时视频</div>
                      </div>
                    </div>
                  </div>

                  <!-- 设备操作控制 -->
                  <div class="mt-4">
                    <Button type="primary" @click="isStreaming ? stopLiveStream() : startLiveStream()">
                      {{ isStreaming ? '停止直播' : '开始直播' }}
                    </Button>
                  </div>
                </Card>
              </div>

              <!-- 右侧：控制面板 -->
              <div class="xl:col-span-2 flex flex-col h-full space-y-6">
                <!-- 云台控制 -->
                <Card size="small" title="云台控制" class="flex-1">
                  <div class="flex flex-col h-[260px] justify-between items-center px-4 py-2 ">

                    <!-- 控制按钮区域 -->
                    <div class="flex w-full justify-around items-center flex-1">

                      <!-- 左侧：方向控制盘 -->
                      <div class="relative w-24 h-24">
                        <button class="absolute top-0 left-1/2 transform -translate-x-1/2 "
                          @mousedown="startPtzControl('up')" @mouseup="stopPtzControl" :disabled="!isStreaming">
                          <IconifyIcon icon="mdi:chevron-up" class="text-xl" />
                        </button>
                        <button class="absolute left-0 top-1/2 transform -translate-y-1/2"
                          @mousedown="startPtzControl('left')" @mouseup="stopPtzControl" @mouseleave="stopPtzControl"
                          :disabled="!isStreaming">
                          <IconifyIcon icon="mdi:chevron-left" class="text-xl" />
                        </button>
                        <button class="absolute right-0 top-1/2 transform -translate-y-1/2"
                          @mousedown="startPtzControl('right')" @mouseup="stopPtzControl" @mouseleave="stopPtzControl"
                          :disabled="!isStreaming">
                          <IconifyIcon icon="mdi:chevron-right" class="text-xl" />
                        </button>
                        <button class="absolute bottom-0 left-1/2 transform -translate-x-1/2"
                          @mousedown="startPtzControl('down')" @mouseup="stopPtzControl" @mouseleave="stopPtzControl"
                          :disabled="!isStreaming">
                          <IconifyIcon icon="mdi:chevron-down" class="text-xl" />
                        </button>
                        <div class="w-full h-full bg-gray-100 rounded-full flex items-center justify-center shadow-md">
                          <IconifyIcon icon="mdi:target" class="text-2xl text-gray-700" />
                        </div>
                      </div>

                      <!-- 右侧：功能按钮，两列布局 -->
                      <div class="grid grid-cols-2 gap-x-4 gap-y-3 ml-6">
                        <button @mousedown="startPtzControl('zoomin')" @mouseup="stopPtzControl"
                          @mouseleave="stopPtzControl" :disabled="!isStreaming">
                          <IconifyIcon icon="mdi:plus" class="text-xl" />
                        </button>
                        <button @mousedown="startPtzControl('zoomout')" @mouseup="stopPtzControl"
                          @mouseleave="stopPtzControl" :disabled="!isStreaming">
                          <IconifyIcon icon="mdi:minus" class="text-xl" />
                        </button>
                        <button @mousedown="startPtzControl('focusNear')" @mouseup="stopPtzControl"
                          @mouseleave="stopPtzControl" :disabled="!isStreaming">
                          <IconifyIcon icon="mdi:focus-field" class="text-xl" />
                        </button>
                        <button @mousedown="startPtzControl('focusFar')" @mouseup="stopPtzControl"
                          @mouseleave="stopPtzControl" :disabled="!isStreaming">
                          <IconifyIcon icon="mdi:focus-field-horizontal" class="text-xl" />
                        </button>
                        <button @mousedown="startPtzControl('irisOpen')" @mouseup="stopPtzControl"
                          @mouseleave="stopPtzControl" :disabled="!isStreaming">
                          <IconifyIcon icon="mdi:brightness-6" class="text-xl" />
                        </button>
                        <button @mousedown="startPtzControl('irisClose')" @mouseup="stopPtzControl"
                          @mouseleave="stopPtzControl" :disabled="!isStreaming">
                          <IconifyIcon icon="mdi:brightness-4" class="text-xl" />
                        </button>
                      </div>

                    </div>

                    <!-- 滑块区域 -->
                    <div class="flex items-center w-full mt-2 space-x-2">
                      <IconifyIcon icon="mdi:speedometer" class="text-gray-600" />
                      <input type="range" min="1" max="10" v-model="ptzSpeed" class="w-full h-2 accent-blue-500"
                        :disabled="!isStreaming" />
                      <span class="w-10 text-right text-sm text-gray-700">{{ ptzSpeed }}</span>
                    </div>

                  </div>
                </Card>



                <!-- 语音对讲 -->
                <Card size="small" title="语音对讲" class="flex-1">
                  <div class="flex flex-col justify-between items-center h-[260px] px-4 py-2">

                    <!-- 模式选择 -->
                    <div class="flex space-x-6 mt-6">
                      <label class="flex items-center space-x-1 text-sm text-gray-700">
                        <input type="radio" v-model="talkMode" value="broadcast" />
                        <span>喊话 (Broadcast)</span>
                      </label>
                      <label class="flex items-center space-x-1 text-sm text-gray-700">
                        <input type="radio" v-model="talkMode" value="talk" />
                        <span>对讲 (Talk)</span>
                      </label>
                    </div>

                    <!-- 麦克风按钮 -->
                    <div class="flex flex-col items-center space-y-2">
                      <button
                        class="w-16 h-16 bg-blue-500 text-white rounded-full shadow-md hover:bg-blue-600 flex items-center justify-center transition-all duration-200"
                        @click="toggleTalk" :disabled="device.deviceState !== 2">
                        <IconifyIcon icon="mdi:microphone" class="text-2xl" />
                      </button>
                      <span class="text-sm text-gray-600">{{ isTalking ? '松开结束对讲' : '点击开始对讲' }}</span>
                    </div>

                  </div>
                </Card>

              </div>
            </div>
          </TabPane>

          <!-- 设备录像Tab -->
          <TabPane key="record" tab="设备录像">
            <div class="space-y-4">
              <!-- 录像查询条件 -->
              <Card size="small" title="录像查询">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                    <input type="datetime-local" v-model="recordQuery.startTime"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                    <input type="datetime-local" v-model="recordQuery.endTime"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">录像类型</label>
                    <select v-model="recordQuery.recordType"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <option value="">全部</option>
                      <option value="1">定时录像</option>
                      <option value="2">报警录像</option>
                      <option value="3">手动录像</option>
                    </select>
                  </div>
                  <div class="flex items-end">
                    <Button type="primary" @click="searchRecords" class="w-full">
                      查询录像
                    </Button>
                  </div>
                </div>
              </Card>

              <!-- 录像列表 -->
              <Card size="small" title="录像列表">
                <div v-if="recordList.length === 0" class="text-center py-12 text-gray-500">
                  <div class="text-4xl mb-4">📹</div>
                  <div class="text-lg mb-2">暂无录像记录</div>
                  <div class="text-sm">选择时间范围查询录像文件</div>
                </div>

                <div v-else class="space-y-3">
                  <div v-for="record in recordList" :key="record.id"
                    class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 record-item">
                    <div class="flex items-center space-x-4">
                      <div class="w-16 h-12 bg-gray-200 rounded flex items-center justify-center">
                        <span class="text-gray-500 text-xl">📹</span>
                      </div>
                      <div>
                        <div class="font-medium text-gray-900">{{ record.fileName }}</div>
                        <div class="text-sm text-gray-500">
                          {{ record.startTime }} - {{ record.endTime }}
                        </div>
                        <div class="text-xs text-gray-400">
                          时长: {{ record.duration }} | 大小: {{ record.fileSize }}
                        </div>
                      </div>
                    </div>
                    <div class="flex space-x-2">
                      <Button size="small" type="primary" @click="playRecord(record)">
                        播放
                      </Button>
                      <Button size="small" @click="downloadRecord(record)">
                        下载
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      <!-- 编辑设备抽屉 -->
      <EditDrawer @reload="refresh" />
    </Page>
    <div class="space-y-4">
      <!-- 对讲模式 -->
      <div class="flex space-x-2">
        <Button :type="talkMode === 'broadcast' ? 'primary' : 'default'" @click="setTalkMode('broadcast')"
          class="flex-1">
          <IconifyIcon icon="mdi:bullhorn" class="mr-1" />
          喊话
        </Button>
        <Button :type="talkMode === 'talk' ? 'primary' : 'default'" @click="setTalkMode('talk')" class="flex-1">
          <IconifyIcon icon="mdi:phone" class="mr-1" />
          对讲
        </Button>
      </div>

      <!-- 对讲控制 -->
      <div class="text-center">
        <Button shape="circle" size="large" :type="isTalking ? 'primary' : 'default'" @click="toggleTalk"
          :disabled="device.deviceState !== 2" class="w-16 h-16 text-2xl talk-button" :class="{ active: isTalking }">
          <IconifyIcon icon="mdi:microphone" class="text-2xl" />
        </Button>
        <div class="text-xs text-gray-500 mt-2">
          {{ isTalking ? '松开结束对讲' : '点击开始对讲' }}
        </div>
      </div>

      <!-- 音量控制 - 改进版 -->
      <div class="volume-control-area">
        <div class="flex items-center justify-between mb-2">
          <div class="flex items-center space-x-2">
            <IconifyIcon icon="mdi:volume-high" class="text-base text-gray-600" />
            <span class="text-sm text-gray-600">音量</span>
          </div>
          <span class="text-sm font-medium text-blue-600">{{ volume }}%</span>
        </div>
        <div class="volume-slider-container">
          <IconifyIcon icon="mdi:volume-low" class="volume-icon-left" />
          <input type="range" min="0" max="100" v-model="volume" class="volume-slider" />
          <IconifyIcon icon="mdi:volume-high" class="volume-icon-right" />
        </div>
        <!-- 音量预设快捷按钮 -->
        <div class="volume-presets">
          <button class="preset-btn" :class="{ active: volume === 30 }" @click="setVolume(30)">低</button>
          <button class="preset-btn" :class="{ active: volume === 60 }" @click="setVolume(60)">中</button>
          <button class="preset-btn" :class="{ active: volume === 90 }" @click="setVolume(90)">高</button>
          <button class="preset-btn" :class="{ active: volume === 0 }" @click="setVolume(0)">静音</button>
        </div>
      </div>
    </div>
    <!-- 设备录像Tab -->
    <TabPane key="record" tab="设备录像">
      <div class="space-y-4">
        <!-- 录像查询条件 -->
        <Card size="small" title="录像查询">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
              <input type="datetime-local" v-model="recordQuery.startTime"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
              <input type="datetime-local" v-model="recordQuery.endTime"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">录像类型</label>
              <select v-model="recordQuery.recordType"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="">全部</option>
                <option value="1">定时录像</option>
                <option value="2">报警录像</option>
                <option value="3">手动录像</option>
              </select>
            </div>
            <div class="flex items-end">
              <Button type="primary" @click="searchRecords" class="w-full">
                查询录像
              </Button>
            </div>
          </div>
        </Card>

        <!-- 录像列表 -->
        <Card size="small" title="录像列表">
          <div v-if="recordList.length === 0" class="text-center py-12 text-gray-500">
            <div class="text-4xl mb-4">📹</div>
            <div class="text-lg mb-2">暂无录像记录</div>
            <div class="text-sm">选择时间范围查询录像文件</div>
          </div>

          <div v-else class="space-y-3">
            <div v-for="record in recordList" :key="record.id"
              class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 record-item">
              <div class="flex items-center space-x-4">
                <div class="w-16 h-12 bg-gray-200 rounded flex items-center justify-center">
                  <span class="text-gray-500 text-xl">📹</span>
                </div>
                <div>
                  <div class="font-medium text-gray-900">{{ record.fileName }}</div>
                  <div class="text-sm text-gray-500">
                    {{ record.startTime }} - {{ record.endTime }}
                  </div>
                  <div class="text-xs text-gray-400">
                    时长: {{ record.duration }} | 大小: {{ record.fileSize }}
                  </div>
                </div>
              </div>
              <div class="flex space-x-2">
                <Button size="small" type="primary" @click="playRecord(record)">
                  播放
                </Button>
                <Button size="small" @click="downloadRecord(record)">
                  下载
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </TabPane>
    <!-- 编辑设备抽屉 -->
    <EditDrawer @reload="refresh" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Card, Button, Tabs, TabPane, message, Divider } from 'ant-design-vue';
import { Page } from '@vben/common-ui';
import { useVbenDrawer } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { GB28181DeviceInfo, GB28181StartReview, GB28181Ptz } from '#/api/device/iotDevice';
import { detailEditSchema } from './model';
import EditDrawerComponent from './edit.vue';
import { IconifyIcon } from '@vben/icons';
import jessibucaPlayer from './components/jessibucaPlayer.vue';

const route = useRoute();
const router = useRouter();

// 录像记录类型定义
interface RecordItem {
  id: string;
  fileName: string;
  startTime: string;
  endTime: string;
  duration: string;
  fileSize: string;
  recordType: number;
}

// 当前选中的Tab，默认为基本信息
const currentTab = ref('basic');

// 直播状态
const isStreaming = ref(false);

// 视频播放状态
const isPaused = ref(false);
const currentTime = ref('');
const hasAudio = ref(true);
const videoUrl = ref('');

// Jessibuca播放器引用
const jessibucaRef = ref();

// 云台控制状态
const ptzSpeed = ref(5); // 默认速度 5
const isControlling = ref(false);

// 语音对讲状态
const talkMode = ref<'broadcast' | 'talk'>('broadcast');
const isTalking = ref(false);
const volume = ref(80);

// 保存状态
const saving = ref({
  connectInfo: false,
});

// 测试状态
const testing = ref({
  connection: false,
});

// 录像查询条件
const recordQuery = ref({
  startTime: '',
  endTime: '',
  recordType: '',
});

// 录像列表
const recordList = ref<RecordItem[]>([
  // 示例数据，实际使用时从API获取
  // {
  //   id: '1',
  //   fileName: '录像_20241201_140000.mp4',
  //   startTime: '2024-12-01 14:00:00',
  //   endTime: '2024-12-01 14:30:00',
  //   duration: '30分钟',
  //   fileSize: '256MB',
  //   recordType: 1,
  // }
]);

// 创建表单实例
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
  },
  layout: 'horizontal',
  schema: detailEditSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-6 gap-x-4',
});

// 创建设备连接信息表单实例
const [ConnectForm, connectFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
  },
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      fieldName: 'sipServerId',
      label: 'SIP 服务器ID',
      componentProps: {
        placeholder: '请输入SIP服务器ID，如：34020000002000000001',
      },
    },
    {
      component: 'Input',
      fieldName: 'sipServerIp',
      label: 'SIP 服务器IP',
      componentProps: {
        placeholder: '请输入SIP服务器IP地址，如：*************',
      },
    },
    {
      component: 'Input',
      fieldName: 'sipServerPort',
      label: 'SIP 服务器端口',
      componentProps: {
        placeholder: '请输入SIP服务器端口，如：5060',
        type: 'number',
      },
    },
    {
      component: 'Input',
      fieldName: 'sipServerDomain',
      label: 'SIP 服务器域',
      componentProps: {
        placeholder: '请输入SIP服务器域，如：3402000000',
      },
    },
    {
      component: 'Input',
      fieldName: 'deviceUser',
      label: '设备用户名',
      componentProps: {
        placeholder: '请输入设备用户名，如：34020000001320000001',
      },
    },
    {
      component: 'Input',
      fieldName: 'devicePwd',
      label: '设备密码',
      componentProps: {
        placeholder: '请输入设备密码',
        type: 'password',
        showPasswordOn: 'mousedown',
        addonAfter: h(Button, {
          type: 'text',
          size: 'small',
          icon: h(IconifyIcon, {
            icon: 'mdi:content-copy',
            style: { fontSize: '14px' }
          }),
          onClick: () => copyToClipboard('devicePwd'),
          style: {
            border: 'none',
            boxShadow: 'none',
            padding: '0 8px',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }
        })
      },
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-6 gap-x-4',
});

// 设备信息
const device = ref({
  deviceId: route.query.deviceId || route.query.deviceKey,
  productKey: '',
  deviceKey: route.query.deviceKey || route.query.deviceId || '',
  deviceName: '',
  longitude: 0,
  latitude: 0,
  firmwareVersion: 0,
  imgUrl: '',
  deviceState: 0,
  alarmState: 0,
  rssi: 0,
  networkAddress: '',
  networkIp: '',
  status: '',
  tenantId: '',
  createdAt: '',
  activeTime: '',
  lastOnlineTime: '',
  remark: '',
  gatewayKey: '',  // 网关标识
  gatewayName: '', // 网关名称，用于显示
  gatewayId: 0,    // 网关ID，用于存储
  // 新增监控设备相关字段
  sipServerId: '',
  sipServerIp: '',
  sipServerPort: '',
  sipServerDomain: '',
  deviceUsername: '',
  devicePassword: '',
});

// 设备连接信息
const connectInfo = ref({
  sipServerId: '',
  sipServerIp: '',
  sipServerPort: '',
  sipServerDomain: '',
  deviceUser: '',
  devicePwd: '',
});

// 产品信息
const product = ref({
  productId: 0,
  productKey: '',
  productName: '',
  deviceType: '',
  transport: 0,
  channelType: 0,
  thingsModelsJson: '',
});

// 产品选项
const productOptions = ref<{ label: string; value: string }[]>([]);

// 加载产品选项
async function loadProductOptions() {
  try {
    // 这里需要根据实际API调整
    // const res = await ListProduct({ page: 1, pageSize: 1000 });
    // productOptions.value = res.items.map((item: any) => ({
    //   label: item.productName,
    //   value: item.productKey,
    // }));

    // 更新表单schema中的产品选项
    formApi.updateSchema([
      {
        fieldName: 'productKey',
        component: 'Select',
        label: '所属产品',
        componentProps: {
          placeholder: '选择所属产品',
          options: productOptions.value,
          disabled: true,
        },
      }
    ]);
  } catch (error) {
    console.error('加载产品选项失败:', error);
  }
}

// 编辑抽屉
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: EditDrawerComponent,
});

// 跳转到产品详情页面
const toProductDetail = (productId: number) => {
  router.push(`/device/productDetail?productId=${productId}`);
};

// 跳转到网关设备详情页面
const toGatewayDetail = async (gatewayId: number) => {
  if (!gatewayId || gatewayId === 0) {
    console.warn('网关ID无效:', gatewayId);
    message.warning('网关ID无效，无法跳转');
    return;
  }

  console.log('跳转到网关设备详情页面，gatewayId:', gatewayId);

  // 添加时间戳确保创建新的标签页
  const timestamp = Date.now();
  const targetPath = `/device/deviceDetail?deviceId=${gatewayId}&t=${timestamp}`;
  console.log('目标路径:', targetPath);

  // 直接跳转到网关设备详情页面
  await router.push(targetPath);
  console.log('✅ 已跳转到网关设备详情页面');
};

// 获取设备状态标签
const getDeviceStateLabel = (state: number) => {
  switch (state) {
    case 1: return '未激活';
    case 2: return '在线';
    case 3: return '离线';
    default: return '未知';
  }
};

// 加载设备详情
async function loadDeviceDetail() {
  try {
    // GB28181DeviceInfo接口需要deviceKey参数
    let deviceKey = route.query.deviceKey as string;

    // 如果没有deviceKey，但有deviceId，可能需要先通过其他方式获取deviceKey
    if (!deviceKey && route.query.deviceId) {
      console.warn('缺少deviceKey参数，仅有deviceId:', route.query.deviceId);
      // 这里可能需要先调用其他接口获取deviceKey，或者直接使用deviceId作为deviceKey
      deviceKey = route.query.deviceId as string;
    }

    if (!deviceKey) {
      console.error('缺少deviceKey参数');
      message.error('缺少设备标识参数，无法加载设备信息');
      return;
    }

    const record = await GB28181DeviceInfo({ deviceKey });

    // 检查API响应 - 如果直接返回了设备对象，则认为成功
    // 检查是否有设备的关键字段（deviceId, deviceKey等）
    if (!record.deviceId && !record.deviceKey && !record.data) {
      console.error('API响应异常，没有有效设备数据:', record);
      message.error('API响应异常，没有有效设备数据');
      return;
    }

    // 使用API返回的数据结构
    // 如果有data字段则使用data，否则直接使用record
    const deviceData = record.data || record;

    if (deviceData) {
      // 设备基本信息
      device.value = {
        ...device.value,
        ...deviceData,
        // 确保网关信息被正确设置
        gatewayKey: deviceData.gatewayKey || '',
        gatewayName: deviceData.gatewayName || '',
        gatewayId: deviceData.gatewayId || 0,
      };

      // 产品信息
      if (deviceData.product) {
        product.value = { ...product.value, ...deviceData.product };
      }

      // 设备连接信息
      if (deviceData.connectionInfo) {
        connectInfo.value = { ...deviceData.connectionInfo };
        // 设置连接信息表单值
        await connectFormApi.setValues(connectInfo.value);
      } else {
        console.warn('没有找到connectionInfo数据');
      }
    } else {
      console.error('没有找到有效的设备数据');
      message.error('没有找到有效的设备数据');
      return;
    }

    // 设置表单值
    await formApi.setValues({
      ...device.value,
      deviceType: String(product.value.deviceType || ''),
      transport: String(product.value.transport || ''),
      channelType: String(product.value.channelType || ''),
    });

    // 设置表单为只读状态
    formApi.setState({
      commonConfig: {
        componentProps: {
          readonly: true,
          disabled: true,
        }
      }
    });
    message.success('设备信息加载成功');
  } catch (error) {
    console.error('加载设备详情失败:', error);
    message.error('加载设备详情失败');
  }
}

// ====== Jessibuca视频播放器相关方法 ======

// 获取视频流URL - 调用实际接口
const getVideoStreamUrl = async (deviceKey: string) => {
  try {
    const response = await GB28181StartReview({ deviceKey });
    // 根据实际接口响应结构解析流地址
    let streamUrl = '';
    let streamData = null;

    // 尝试从不同层级获取流数据
    if (response && response.data) {
      streamData = response.data;
    } else if (response) {
      streamData = response;
    }

    if (streamData) {

      // 优先使用FLV格式，因为Jessibuca对FLV支持最好
      if (streamData.flv) {
        streamUrl = streamData.flv;
        console.log('📡 使用FLV流地址:', streamUrl);
      }
      // 备用方案1: HLS格式
      else if (streamData.hls) {
        streamUrl = streamData.hls;
      }
      // 备用方案2: RTMP格式
      else if (streamData.rtmp) {
        streamUrl = streamData.rtmp;
      }
      // 备用方案3: 通用播放地址
      else if (streamData.publishUrl) {
        streamUrl = streamData.publishUrl;
      }
    }

    if (!streamUrl) {
      console.warn('⚠️ 接口未返回有效的流地址');
      console.warn('⚠️ 接口响应数据:', response);
      console.warn('⚠️ 可能的问题:');
      console.warn('   1. 设备未在线或未支持视频流');
      console.warn('   2. 流媒体服务器未正确配置');
      console.warn('接口返回的流地址格式异常');

      throw new Error('接口未返回有效的流地址');
    }

    return streamUrl;
  } catch (error) {
    console.error('❌ 调用GB28181StartReview接口失败:', error);
    throw error;
  }
};

// 开始直播 - 调用实际接口获取流地址
const startLiveStream = async () => {
  try {
    if (!device.value.deviceKey) {
      message.error('设备标识为空，无法开始直播');
      return;
    }

    // 显示加载状态
    const loadingMsg = message.loading('正在获取视频流地址...', 0);

    try {
      // 调用实际接口获取视频流URL
      const streamUrl = await getVideoStreamUrl(String(device.value.deviceKey));

      // 关闭加载提示
      loadingMsg();

      if (!streamUrl) {
        message.error('获取视频流地址失败');
        return;
      }

      // 设置视频URL和状态
      videoUrl.value = streamUrl;
      isStreaming.value = true;
      isPaused.value = false;

      // 启动时间更新
      updateCurrentTime();

      // 如果jessibuca播放器已经准备好，直接开始播放
      if (jessibucaRef.value) {
        await nextTick();
        jessibucaRef.value.play(streamUrl);
      }

      message.success('直播已开始');
    } catch (apiError) {
      // 关闭加载提示
      loadingMsg();

      console.error('❌ 获取流地址失败:', apiError);

      // 询问用户是否使用测试视频
      const useTestVideo = confirm(
        '获取实际流地址失败！\n\n' +
        '可能原因：\n' +
        '1. 接口返回格式不正确\n' +
        '2. 设备未在线或未支持视频流\n' +
        '3. 流媒体服务器未配置\n' +
        '4. 网络连接问题\n\n' +
        '建议检查：\n' +
        '- 设备是否在线\n' +
        '- 流媒体服务器是否运行\n' +
        '- 防火墙设置\n\n' +
        '是否使用测试视频进行播放测试？\n' +
        '(确定=使用测试视频，取消=停止操作)'
      );

      if (useTestVideo) {
        // 使用测试视频 - 使用一个简单的测试URL
        const testUrl = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';

        videoUrl.value = testUrl;
        isStreaming.value = true;
        isPaused.value = false;
        updateCurrentTime();

        if (jessibucaRef.value) {
          await nextTick();
          jessibucaRef.value.play(testUrl);
        }

        message.warning('使用测试视频播放');
      } else {
        let errorMsg = '未知错误';
        if (apiError && typeof apiError === 'object' && 'message' in apiError && typeof (apiError as any).message === 'string') {
          errorMsg = (apiError as any).message;
        } else if (apiError && typeof apiError.toString === 'function') {
          errorMsg = apiError.toString();
        }
        message.error('获取视频流地址失败: ' + errorMsg);
        isStreaming.value = false;
      }
    }
  } catch (error: any) {
    console.error('❌ 开始直播失败:', error);
    message.error('开始直播失败: ' + (error?.message || '未知错误'));
    isStreaming.value = false;
  }
};

// 停止直播 - 重写以支持jessibuca
const stopLiveStream = () => {
  try {
    // 停止播放器
    if (jessibucaRef.value) {
      jessibucaRef.value.pause();
    }

    // 重置状态
    isStreaming.value = false;
    isPaused.value = false;
    videoUrl.value = '';

    message.info('直播已停止');
  } catch (error) {
    console.error('❌ 停止直播失败:', error);
    message.error('停止直播失败');
  }
};

// 切换播放/暂停 - 重写以支持jessibuca
const togglePlay = () => {
  if (!jessibucaRef.value) {
    message.warning('播放器未就绪');
    return;
  }

  try {
    if (isPaused.value) {
      jessibucaRef.value.play();
      isPaused.value = false;
      message.info('继续播放');
    } else {
      jessibucaRef.value.pause();
      isPaused.value = true;
      message.info('已暂停');
    }
  } catch (error) {
    console.error('❌ 切换播放状态失败:', error);
    message.error('操作失败');
  }
};

// Jessibuca播放器事件处理
const onVideoPlay = () => {
  isPaused.value = false;
  message.success('视频播放成功！');
};

const onVideoPause = () => {
  isPaused.value = true;
};

const onVideoError = (error: any) => {
  console.error('❌ 视频播放错误:', error);

  // 根据错误类型显示不同的提示信息
  let errorMessage = '视频播放出现错误';

  if (typeof error === 'string') {
    if (error.includes('network') || error.includes('连接')) {
      errorMessage = '网络连接错误，请检查视频流地址是否正确';
    } else if (error.includes('decode') || error.includes('解码')) {
      errorMessage = '视频解码错误，可能视频格式不支持';
    } else if (error.includes('timeout') || error.includes('超时')) {
      errorMessage = '连接超时，请检查网络状态';
    }
  }

  message.error(errorMessage);

  // 发生错误时重置状态
  isStreaming.value = false;
  isPaused.value = false;
};

// ====== 原有方法保持不变 ======

// 更新当前时间
const updateCurrentTime = () => {
  if (isStreaming.value) {
    const now = new Date();
    currentTime.value = now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
    setTimeout(updateCurrentTime, 1000);
  }
};
const stoped = ref(false);

// 云台控制 - 开始
const startPtzControl = async (direction: string) => {
  stoped.value = false;
  isControlling.value = true;
  try {
    if (!device.value.deviceKey) {
      throw new Error('设备标识为空，无法发送云台控制请求');
    }
    //这个返回结果是操作类的，所以response没有被读取
    const response = await GB28181Ptz({
      deviceKey: device.value.deviceKey, // 确保传递设备标识
      cmd: direction, // 控制指令，例如上下左右
      speed: ptzSpeed.value, // 当前速度
    });
  } catch (error) {
    console.error('云台控制失败:', error);
    const errMsg = typeof error === 'object' && error && 'message' in error ? (error as any).message : '未知错误';
    message.error(`云台控制失败: ${errMsg}`);
  }

  setTimeout(() => { stoped.value = true; }, 100);
};

// 云台控制 - 停止
const stopPtzControl = async () => {
  if (isControlling.value) {
    isControlling.value = false;
    while (!stoped.value) {
      await new Promise(resolve => setTimeout(resolve, 100)); // 等待1秒
    }
    try {
      if (!device.value.deviceKey) {
        throw new Error('设备标识为空，无法发送停止云台请求');
      }

      const response = await GB28181Ptz({
        deviceKey: device.value.deviceKey, // 确保传递设备标识
        cmd: 'stop', // 停止指令
        speed: 0, // 停止时速度为0
      });
    } catch (error) {
      console.error('云台停止失败:', error);
      message.error(`云台停止失败: ${typeof error === 'object' && error && 'message' in error ? (error as any).message : '未知错误'}`);
    }
  }
};

// 获取方向名称
const getDirectionName = (direction: string): string => {
  const directionMap: Record<string, string> = {
    'up': '向上',
    'down': '向下',
    'left': '向左',
    'right': '向右',
    'zoomin': '放大',
    'zoomout': '缩小'
  };
  return directionMap[direction] || direction;
};

// 聚焦控制 - 开始
const startFocusControl = (direction: string) => {
  if (device.value.deviceState !== 2) {
    message.warning('设备未在线，无法控制聚焦');
    return;
  }
  // TODO: 调用实际的聚焦控制API
  message.info(`聚焦${direction}控制开始`);
};

// 聚焦控制 - 停止
const stopFocusControl = () => {
  console.log('停止聚焦控制');
  // TODO: 调用实际的停止聚焦API
  message.info('聚焦控制停止');
};

// 光圈控制 - 开始
const startIrisControl = (direction: string) => {
  if (device.value.deviceState !== 2) {
    message.warning('设备未在线，无法控制光圈');
    return;
  }

  console.log('开始光圈控制:', direction);
  // TODO: 调用实际的光圈控制API
  message.info(`光圈${direction}控制开始`);
};

// 光圈控制 - 停止
const stopIrisControl = () => {
  console.log('停止光圈控制');
  // TODO: 调用实际的停止光圈API
  message.info('光圈控制停止');
};

// 语音对讲模式设置
const setTalkMode = (mode: 'broadcast' | 'talk') => {
  talkMode.value = mode;
  message.info(`切换到${mode === 'broadcast' ? '喊话' : '对讲'}模式`);
};

// 切换对讲状态
const toggleTalk = () => {
  if (device.value.deviceState !== 2) {
    message.warning('设备未在线，无法使用对讲功能');
    return;
  }

  isTalking.value = !isTalking.value;
  const action = isTalking.value ? '开始' : '结束';
  const mode = talkMode.value === 'broadcast' ? '喊话' : '对讲';

  console.log(`${action}${mode}:`, { mode: talkMode.value, volume: volume.value });
  // TODO: 调用实际的对讲API

  message.info(`${action}${mode}`);
};

// 设置音量预设值
const setVolume = (vol: number) => {
  volume.value = vol;
  message.info(`音量设置为${vol}%`);
};

// 复制到剪贴板功能
const copyToClipboard = async (fieldName: string) => {
  try {
    const formValues = await connectFormApi.getValues();
    const value = formValues[fieldName];

    if (!value) {
      message.warning('没有可复制的内容');
      return;
    }

    // 使用现代浏览器的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(value);
      message.success('已复制到剪贴板');
    } else {
      // 降级方案：使用传统的 document.execCommand
      const textArea = document.createElement('textarea');
      textArea.value = value;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        message.success('已复制到剪贴板');
      } catch (err) {
        message.error('复制失败，请手动复制');
      } finally {
        document.body.removeChild(textArea);
      }
    }
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败');
  }
};

// 保存设备连接信息
const saveConnectInfo = async () => {
  try {
    saving.value.connectInfo = true;

    // 获取表单数据
    const formData = await connectFormApi.getValues();
    console.log('保存连接信息:', formData);

    // TODO: 调用保存设备连接信息的API
    // await updateDeviceConnectionInfo({
    //   deviceKey: device.value.deviceKey,
    //   ...formData
    // });

    // 更新本地连接信息
    connectInfo.value = {
      sipServerId: formData.sipServerId || '',
      sipServerIp: formData.sipServerIp || '',
      sipServerPort: formData.sipServerPort || '',
      sipServerDomain: formData.sipServerDomain || '',
      deviceUser: formData.deviceUser || '',
      devicePwd: formData.devicePwd || '',
    };

    message.success('连接信息保存成功');
  } catch (error) {
    console.error('保存连接信息失败:', error);
    message.error('保存连接信息失败');
  } finally {
    saving.value.connectInfo = false;
  }
};

// 测试设备连接
const testConnection = async () => {
  try {
    testing.value.connection = true;

    // 获取表单数据
    const formData = await connectFormApi.getValues();
    console.log('测试连接:', formData);

    // 检查必填字段
    if (!formData.sipServerIp || !formData.sipServerPort) {
      message.warning('请填写SIP服务器IP和端口');
      return;
    }

    // TODO: 调用测试连接的API
    // const result = await testDeviceConnection({
    //   deviceKey: device.value.deviceKey,
    //   ...formData
    // });

    // 模拟测试结果
    const success = Math.random() > 0.3; // 70%成功率

    if (success) {
      message.success('设备连接测试成功');
    } else {
      message.error('设备连接测试失败，请检查配置信息');
    }
  } catch (error) {
    console.error('测试连接失败:', error);
    message.error('测试连接失败');
  } finally {
    testing.value.connection = false;
  }
};

// 查看录像
const viewRecords = () => {
  if (device.value.deviceState !== 2) {
    message.warning('设备未在线，无法查看录像');
    return;
  }

  message.info('跳转到录像页面');
  console.log('查看录像:', device.value.deviceKey);
  // TODO: 实现录像查看逻辑
};

// 编辑设备
const handleEdit = () => {
  editDrawerApi.setData({
    id: device.value.deviceId,
    update: true,
    view: false,
  });
  editDrawerApi.open();
};

// 查询录像
const searchRecords = async () => {
  try {
    if (!recordQuery.value.startTime || !recordQuery.value.endTime) {
      message.warning('请选择查询时间范围');
      return;
    }

    console.log('查询录像:', recordQuery.value);
    message.info('正在查询录像...');

    // TODO: 调用实际的录像查询API
    // const response = await queryDeviceRecords({
    //   deviceId: device.value.deviceId,
    //   startTime: recordQuery.value.startTime,
    //   endTime: recordQuery.value.endTime,
    //   recordType: recordQuery.value.recordType,
    // });
    // recordList.value = response.data || [];

    // 临时使用示例数据
    recordList.value = [];
    message.success('录像查询完成');
  } catch (error) {
    console.error('查询录像失败:', error);
    message.error('查询录像失败');
  }
};

// 播放录像
const playRecord = (record: RecordItem) => {
  console.log('播放录像:', record);
  message.info(`播放录像: ${record.fileName}`);
  // TODO: 实现录像播放逻辑
};

// 下载录像
const downloadRecord = (record: RecordItem) => {
  console.log('下载录像:', record);
  message.info(`开始下载: ${record.fileName}`);
  // TODO: 实现录像下载逻辑
};

// Tab切换
const onTabChange = (key: string | number) => {
  currentTab.value = String(key);

  // 如果切换到非实时预览的Tab，关闭直播
  if (key !== 'preview' && isStreaming.value) {
    stopLiveStream();
  }
};

// 刷新数据
const refresh = async () => {
  await loadDeviceDetail();
};

// 页面初始化
onMounted(async () => {
  // 初始化录像查询时间 - 默认查询今天
  const today = new Date();
  const todayStr = today.toISOString().slice(0, 10);
  recordQuery.value.startTime = `${todayStr}T00:00`;
  recordQuery.value.endTime = `${todayStr}T23:59`;

  await loadProductOptions();
  await loadDeviceDetail();
  if(route.query.tab!=undefined){
    currentTab.value=route.query.tab
  }
});
</script>

<style scoped>
/* 页面整体样式 */
.device-function-container {
  display: flex;
  height: 100%;
  gap: 16px;
}

/* 云台控制样式 - 极简专业版 */
.ptz-control-clean {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.control-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
  text-align: center;
  letter-spacing: 0.5px;
}

.ptz-main-layout {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 32px;
  align-items: start;
}

/* 左侧：方向控制 */
.direction-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.direction-pad {
  position: relative;
  width: 140px;
  height: 140px;
  background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 50%;
  border: 3px solid #cbd5e1;
  box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.05);
  transform: scale(1.5);
  /* 等比例放大 */
  margin: auto;
  /* 保持居中 */
}

.direction-btn {
  position: absolute;
  width: 36px;
  height: 36px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid #94a3b8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 18px;
  color: #64748b;
}

.dir-up {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.dir-down {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.dir-left {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.dir-right {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.direction-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  background: linear-gradient(145deg, #e2e8f0 0%, #cbd5e1 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #64748b;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.direction-btn:hover:not(.disabled) {
  background: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
  color: #1d4ed8;
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.direction-btn.disabled {
  background: #f1f5f9;
  border-color: #e2e8f0;
  color: #94a3b8;
  cursor: not-allowed;
  box-shadow: none;
}

/* 速度控制 */
.speed-control {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.control-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.control-slider {
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 100%);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  transition: all 0.2s ease;
}

.control-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: linear-gradient(145deg, #ffffff 0%, #3b82f6 100%);
  border: 2px solid #1d4ed8;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

.control-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.control-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: linear-gradient(145deg, #ffffff 0%, #3b82f6 100%);
  border: 2px solid #1d4ed8;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);
}

.value-display {
  font-size: 13px;
  color: #475569;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.7);
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

/* 右侧：功能按钮区域 */
.function-area {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 12px;
  width: 140px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.control-btn {
  width: 64px;
  height: 64px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid #cbd5e1;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, transparent 0%, rgba(59, 130, 246, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.25s ease;
}

.control-btn:hover:not(.disabled) {
  background: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.25);
}

.control-btn:hover::before {
  opacity: 1;
}

.control-btn:active:not(.disabled) {
  transform: translateY(-1px) scale(0.98);
  box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);
}

.control-btn.disabled {
  background: #f1f5f9;
  border-color: #e2e8f0;
  color: #94a3b8;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.control-icon {
  font-size: 20px;
  color: #64748b;
  transition: all 0.25s ease;
  z-index: 1;
  position: relative;
}

.control-btn:hover:not(.disabled) .control-icon {
  color: #3b82f6;
  transform: scale(1.1);
}

/* 视频播放器样式 */
.video-player-container {
  position: relative;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 8px;
  overflow: hidden;
}

/* 语音对讲样式 */
.talk-button {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 3px solid #cbd5e1;
  color: #64748b;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.talk-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(239, 68, 68, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.talk-button:hover:not(.active) {
  background: linear-gradient(145deg, #fee2e2 0%, #fecaca 100%);
  border-color: #f87171;
  color: #dc2626;
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(248, 113, 113, 0.3);
}

.talk-button:hover::before {
  opacity: 1;
}

.talk-button.active {
  background: linear-gradient(145deg, #ef4444 0%, #dc2626 100%);
  border-color: #b91c1c;
  color: white;
  transform: scale(1.02);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
  animation: pulse-talk 2s infinite;
}

@keyframes pulse-talk {

  0%,
  100% {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
  }

  50% {
    box-shadow: 0 0 30px rgba(239, 68, 68, 0.6);
  }
}

/* 滑块样式 */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: #ddd;
  border-radius: 3px;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #1890ff;
  border-radius: 50%;
  cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #1890ff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* 响应式调整 */
@media (max-width: 1280px) {
  .ptz-main-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .direction-pad {
    width: 120px;
    height: 120px;
  }

  .direction-btn {
    width: 32px;
    height: 32px;
  }

  .function-area {
    justify-self: center;
    max-width: 280px;
  }
}

@media (max-width: 768px) {
  .device-function-container {
    flex-direction: column;
  }
}
</style>
