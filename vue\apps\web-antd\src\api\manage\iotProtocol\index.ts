import { requestClient } from '#/api/request';

// 获取协议表列表
export function List(params:any) {
  return requestClient.get<any>('manage/iotProtocol/list', { params });
}

// 删除/批量删除协议表
export function Delete(params:any) {
  return requestClient.post<any>('manage/iotProtocol/delete', { ...params });
}

// 添加/编辑协议表
export function Edit(params:any) {
  return requestClient.post<any>('manage/iotProtocol/edit', { ...params });
}

// 修改协议表状态
export function Status(params:any) {
  return requestClient.post<any>('manage/iotProtocol/status', { ...params });
}

// 获取协议表指定详情
export function View(params:any) {
  return requestClient.get<any>('manage/iotProtocol/view', { params });
}

// 导出协议表
export function Export(params:any) {
  return requestClient.post<Blob>('/manage/iotProtocol/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}