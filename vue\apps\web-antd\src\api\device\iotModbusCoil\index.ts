import { requestClient } from '#/api/request';

// 获取MudBus线圈配置表列表
export function List(params:any) {
  return requestClient.get<any>('device/iotModbusCoil/list', { params });
}

// 删除/批量删除MudBus线圈配置表
export function Delete(params:any) {
  return requestClient.post<any>('device/iotModbusCoil/delete', { ...params });
}

// 添加/编辑MudBus线圈配置表
export function Edit(params:any) {
  return requestClient.post<any>('device/iotModbusCoil/edit', { ...params });
}

// 获取MudBus线圈配置表指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotModbusCoil/view', { params });
}

// 导出MudBus线圈配置表
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotModbusCoil/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}