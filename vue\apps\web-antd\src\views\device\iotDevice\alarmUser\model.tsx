import type { VxeGridProps } from '#/adapter/vxe-table';
import { cloneDeep } from 'lodash-es';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';


export class State {
  public deviceConsumerId = 0; // ID
  public projectName = ''; // 项目名称
  public userName = ''; // 用户名
  public phone = 0; // 手机号
  public realName = ''; // 真实姓名
  public sex = ''; // 性别
  public shareStatus = 0; // 分享状态，是否被分享
  //缺少一个绑定时间,用最外层的createdAt来作为绑定时间
  public createdAt = ''; // 创建时间
  public ip = ''; // 登录地址

  public deviceKey = ''; // 设备标识
  public aliasName = ''; // 别名
  public consumerId = 0; // 终端用户ID
  public shareConsumerId = 0; // 分享者ID

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表格列
export const columns: VxeGridProps['columns'] = [
  { title: '编号', field: 'deviceConsumerId', width: 80 },
  { title: '项目', field: 'consumer.projectName', width: -1 },
  { title: '用户名', field: 'consumer.userName', width: -1 },
  { title: '手机', field: 'consumer.phone', width: -1 },
  { title: '真实姓名', field: 'consumer.realName', width: -1 },
  {
    title: '性别', field: 'consumer.sex',
    //使用字典映射 SYS_USER_SEX
    slots: {
      default: ({ row }) => {
        return renderDict(row.consumer.sex, DictEnum.SYS_USER_SEX);
      },
    },
    width: -1
  },
  {
    title: '是否被分享', field: 'shareStatus',
    //如果 shareConsumerId !== 0 &&  shareConsumerId !== consumerId  显示 是
    // 否则 显示 否
    slots: {
      default: ({ row }) => {
        return row.shareConsumerId !== 0 && row.shareConsumerId !== row.consumerId ? '是' : '否';
      },
    },
    width: -1
  },
  { title: '绑定时间', field: 'createdAt', width: -1 },
  { title: '登录地址', field: 'consumer.ip', width: -1 },
  {
    title: '操作',
    width: 120,
    align: 'center',
    slots: { default: 'action' }
  },
];

// 表格列接口
export interface RowType {
  deviceConsumerId: number;
  projectName: string;
  userName: string;
  phone: string | number;
  realName: string;
  sex: string;
  shareStatus: number;
  createdAt: string;
  ip: string;
  deviceKey: string;
  aliasName: string;
  consumerId: number;
  shareConsumerId: number;
}




