<script setup lang="ts">
import { ref, h } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { Description, useDescription } from '#/components/description';
import { viewSchema, type RowType, type DeviceDetailType } from './model';
import { View } from '#/api/device/iotBatchOperation';
import { Table } from 'ant-design-vue';
import { renderDict } from '#/utils/render';

// 设备明细表格数据
const deviceDetailData = ref<DeviceDetailType[]>([]);

// 先定义 handleOpenChange 函数
async function handleOpenChange(open: boolean) {
  if (!open) {
    // 清空数据
    deviceDetailData.value = [];
    return;
  }

  try {
    const { record } = drawerApi.getData() as { record: RowType };
    const record2 = await View({ batchId: record.batchId });

    // 先克隆原始 schema
    const baseSchema = [...viewSchema];
    let currentSchema: typeof baseSchema = [];

    // 必须字段
    currentSchema.push(
      { field: 'batchId', label: '编号' },
      {
        field: 'type',
        label: '操作类型',
        render(_: any, row: any) {
          return renderDict(row.type, 'batch_operation_type');
        },
      }
    );

    // type=1 显示产品标识、生产批次、封箱批次
    if (record2.type === '1' || record2.type === 1) {
      currentSchema.push({ field: 'productKey', label: '产品标识' });
      currentSchema.push({ field: 'productionBatch', label: '生产批次' });
      currentSchema.push({ field: 'packingBatch', label: '封箱批次' });
    }

    // type=2 显示接收机构
    if (record2.type === '2' || record2.type === 2) {
      currentSchema.push({ field: 'targetDept', label: '接收机构' });
    }

    // type=4 显示接收项目，如果 projectName 为空则显示 projectId
    if (record2.type === '4' || record2.type === 4) {
      if (record2.projectName) {
        currentSchema.push({ field: 'projectName', label: '接收项目' });
      } else {
        currentSchema.push({ field: 'projectId', label: '接收项目' });
      }
    }

    // 通用字段
    currentSchema.push(
      { field: 'sourceDeptName', label: '原机构' },
      {
        field: 'batchState',
        label: '任务状态',
        render(_: any, row: any) {
          return renderDict(row.batchState, 'batch_state');
        },
      },
      { field: 'total', label: '设备总数' },
      {
        field: 'successRate',
        label: '成功率',
        render(_: any, data: any) {
          const rateText = (!data.total || data.total === 0)
            ? '0%'
            : `${Math.round((data.success || 0) / data.total * 100)}%`;
          return h('span', rateText);
        }
      },
      { field: 'success', label: '成功数量' },
      { field: 'fail', label: '失败数量' },
      { field: 'remark', label: '操作原因' }
    );

    setDescProps({ schema: currentSchema, data: record2 }, true);

    // 获取设备明细数据
    if (record2.detailList && Array.isArray(record2.detailList)) {
      deviceDetailData.value = record2.detailList;
    } else {
      deviceDetailData.value = [];
    }
  } catch (error) {
    console.error('加载详情数据失败:', error);
    deviceDetailData.value = [];
  }
}

// 然后初始化 drawer
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onOpenChange: handleOpenChange,
});

const [registerDescription, { setDescProps }] = useDescription({
  column: 1, // 改为2列布局，实现更紧凑的显示
  schema: viewSchema,
});

// 表格标题渲染函数
const tableTitle = () => h('div', {
  style: {
    fontSize: '16px',
  }
}, '任务明细');

// 设备明细表格列配置
const deviceDetailColumns = [
  {
    title: '设备标识',
    dataIndex: 'deviceKey',
    key: 'deviceKey',
  },
  {
    title: '处理结果',
    dataIndex: 'result',
    key: 'result',
    customRender: ({ record }: { record: DeviceDetailType }) => {
      const label = getDictOptions(DictEnum.BATCH_DETAIL_RESULT).find(
        (item: any) => item.value === String(record.result)
      )?.label;
      return label
        ? `${label}${record.msg ? ` - ${record.msg}` : ''}`
        : '未知结果';
    }
  },
];
</script>
<template>
  <BasicDrawer :footer="false" class="w-[600px]" title="详情" :closable="true" :mask-closable="true">
    <div class="space-y-6">
      <!-- 基本信息 -->
      <Description @register="registerDescription" class="custom-description"></Description>
      <!-- 任务明细 -->
      <div class="mt-6">
        <Table :columns="deviceDetailColumns" :data-source="deviceDetailData" :pagination="false" size="small" bordered
          :title="tableTitle" />
      </div>
    </div>
  </BasicDrawer>
</template>

<style scoped>
:deep(.custom-description .ant-descriptions-item-label) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  text-align: left;
  padding-right: 12px;
  font-weight: 500;
}

:deep(.custom-description .ant-descriptions-item-content) {
  flex: 1;
  min-width: 0;
}

:deep(.custom-description .ant-descriptions-item) {
  padding-bottom: 12px;
}
</style>
