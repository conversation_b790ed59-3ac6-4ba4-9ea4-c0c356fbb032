<script lang="ts" setup>
import { useVbenModal, VbenButton } from '@vben/common-ui';

import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';

import { columns, type RowType } from './model';
import type { DeepPartial } from '@vben/types';
import { List, Export, Delete } from '#/api/device/iotProduct';
import { List as CategoryList } from '#/api/device/iotProductCategory';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { ref, defineProps } from 'vue';

const props = defineProps(['getProductKey']);

const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'productId',
  },
  rowConfig: {
    keyField: 'productId',
  },
  columns: columns,
  exportConfig: {},
  height: '450',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let result = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });

        let categoryArray = await CategoryList({});
        let categoryMap = categoryArray.items;
        result.items.forEach((item: any) => {
          item.categoryName = categoryMap.find(
            (i: any) => item.categoryId === i.categoryId,
          )?.categoryName;
        });
        return result;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  gridClass: ' p-0',
  gridOptions,
  gridEvents,
});
//确认按钮回调函数
const onConfirm = () => {
  const selectedRecord = gridApi.grid.getRadioRecord();

  if (!selectedRecord) {
    console.error('❌ 没有选择任何产品');
    return;
  }

  const productKey = selectedRecord.productKey;
  const productName = selectedRecord.productName;
  const firmwareType = selectedRecord.firmwareType;

  console.log('🔍 选择的产品记录完整信息:', selectedRecord);
  console.log('📋 提取的字段值:', {
    productKey,
    productName,
    firmwareType,
    firmwareType_type: typeof firmwareType
  });

  // 检查 firmwareType 是否存在
  if (firmwareType === undefined || firmwareType === null) {
    console.warn('⚠️ 产品记录中没有 firmwareType 字段，使用默认值');
    // 可以设置一个默认值或者提示用户
    props.getProductKey(productKey, productName, '1'); // 默认值，根据实际情况调整
  } else {
    props.getProductKey(productKey, productName, firmwareType);
  }

  modalApi.close();
};

function openModal() {
  modalApi.open();
}

defineExpose({
  openModal,
});

const [Modal, modalApi] = useVbenModal({ onConfirm: onConfirm });
</script>
<template>
  <div>
    <Modal class="w-[1200px] h-[600px]" title="选择产品">
      <div class="table-wrapper">
        <Grid table-title="产品表"> </Grid>
      </div>
    </Modal>
  </div>
</template>
<style scoped>
/* 表格包装器 */
.table-wrapper {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
}

/* 表格容器样式 */
.table-wrapper :deep(.vxe-grid) {
  border: none;
  height: 100%;
}

/* 表格主体样式 */
.table-wrapper :deep(.vxe-table) {
  height: 100%;
}

/* 表格内容区域 */
.table-wrapper :deep(.vxe-table--body-wrapper) {
  max-height: 400px;
  overflow-y: auto;
}

/* 表格工具栏 */
.table-wrapper :deep(.vxe-toolbar) {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 表格分页器 */
.table-wrapper :deep(.vxe-pager) {
  padding: 8px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 确保表格字段充满宽度 */
.table-wrapper :deep(.vxe-table--header),
.table-wrapper :deep(.vxe-table--body) {
  width: 100% !important;
}

/* 美化滚动条 */
.table-wrapper :deep(.vxe-table--body-wrapper)::-webkit-scrollbar {
  width: 6px;
}

.table-wrapper :deep(.vxe-table--body-wrapper)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-wrapper :deep(.vxe-table--body-wrapper)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-wrapper :deep(.vxe-table--body-wrapper)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
