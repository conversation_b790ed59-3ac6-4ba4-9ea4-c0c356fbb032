<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <Steps :current="current" :items="items" class="mb-6">
    </Steps>
    <div class="steps-content">
      <div v-show="current == 0">
        <Card>
          <Step1Form></Step1Form>
        </Card>
      </div>
      <div v-show="current == 1">
        <Card>
        <Step2Form>
          <template #timeWeek="slotProps">
            <div v-if="slotProps.value" class="flex items-center w-full">
              <TimePicker class="1/4" v-model:value="slotProps.value.time" format="HH:mm:ss" value-format="HH:mm:ss" :disabled="slotProps.disabled" @change="timeWeekChanged(slotProps.value)"></TimePicker>
             <Select class="w-3/4" v-model:value="slotProps.value.week" :options="weekOptions" mode="multiple" value-format="string" :disabled="slotProps.disabled" @change="timeWeekChanged(slotProps.value)"></Select>
            </div>
          </template>
          <template #cron="slotProps">
            <Input v-bind="slotProps" placeholder="请输入cron表达式" ></Input>
            <Button type="primary" @click="openCronModal" :disabled="slotProps.disabled">生成表达式</Button>
          </template>
        </Step2Form>
        </Card>
      </div>
      <div v-show="current == 2">
        <Card class="mb-4">
          <label class="text-sm font-medium w-full mb-2">采集类型</label>
          <RadioGroup class="w-full mb-4" v-model:value="step3Data.pollType" :options="getDictOptions('poll_type')">
          </RadioGroup>
        </Card>
        <Card title="指令脚本配置" class="mb-4" v-show="step3Data.pollType != '1'">
          <ScriptContent ref="scriptContentFormRef" :modelKeysOptions="modelKeysOptions" :formData="step3Data"></ScriptContent>
        </Card>
        <Collapse v-model:activeKey="extshowKey" collapsible="header">
          <CollapsePanel key="1" header="高级选项">
            <ExtendConfigForm ></ExtendConfigForm>
          </CollapsePanel>
        </Collapse>
      </div>
    </div>
    <div class="mt-6 steps-action flex justify-end gap-x-4">
      <Button v-if="current > 0" @click="prev">上一步</Button>
      <Button v-if="current < 2" type="primary" @click="next">下一步</Button>
      <Button
        v-if="current == 2"
        type="primary"
        @click="handleConfirm"
      >
        完成
      </Button> 
      <Button @click="handleCancel">取消</Button>
    </div>
    <JobCronModel :close-on-click-modal="false" class="w-[800px]" @confirm="cronExpressionChanged($event)" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Alert , Steps, Button, Select, TimePicker, Input, RadioGroup, Collapse, CollapsePanel, Card } from 'ant-design-vue';
import { Edit, View } from '#/api/device/iotPollTask';
import { editStep1Schema, editStep2Schema, extendConfigSchema, type Step1DataType, type Step2DataType, type Step3DataType, type FormData } from './model';
import { getDictOptions } from '#/utils/dict';
import ScriptContent from './scriptContent.vue';
import { ListNoPage as ListProduct, View as DetailProduct } from '#/api/device/iotProduct';
import { ListNoPage as ListDevice } from '#/api/device/iotDevice';
import { ListNoPage as ListGroup } from '#/api/device/iotGroup';
import jobCronModel from '../../system/job/job-cron-model.vue';

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
}

const isUpdate = ref(false);
const isView = ref(false);
const Props = defineProps<{
  targetType?: string;
  productKey?: string;
  deviceKey?: string;
  groupId?: number;
}>();
const current = ref(0);
const items = ref([
  {
    title: '基本信息',
  },
  {
    title: '定时设置',
  },
  {
    title: '采集指令',
  },
]);
const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});
const extshowKey = ref(0);
const extFormEnabled = ref(false);
watch(()=> extshowKey.value, (newVal)=>{
  extFormEnabled.value =  true;
});

const scriptContentFormRef = ref<InstanceType<typeof ScriptContent>>();
const step1Data = ref<Step1DataType>({
    taskId: 0,
    taskName: '',
    status: '0',
    remark: '',
    targetType: '1',
    productKey: '',
    deviceKeyList: [],
    disableDeviceKeyList: [],
    groupId: null,
});
    
const weekOptions = [{
  label: '周日',
  value: '1',
},
{
  label: '周一',
  value: '2',
},
{
  label: '周二',
  value: '3',
},
{
  label: '周三',
  value: '4',
},
{
  label: '周四',
  value: '5',
},
{
  label: '周五',
  value: '6',
},
{
  label: '周六',
  value: '7',
},
];



const step2Data = ref<Step2DataType>({
  type: 0,
  timeWeek: {
    time: '',
    week: [],
  },
  cron: '',
});

const modelKeysOptions = ref<any[]>([]);

const step3Data = ref<Step3DataType>({
  pollType: '1',
  pollScriptObj: {
    modelKeys: [],
    dataType: 'hex',
    dataMsg: '',
    useSubAddr: '0',
  },
  modbusParam: {
    useSubAddr: '0',
    subAddr: 1,
    modbusFunction: '1',
    regAddr: 0,
    coilCount: 1,
    regCount: 1,
    coilValue: 0,
    regValue: 0,
    coilStatusList: [],
    regValueList: [],
    dataCheckType: '2',
  },
  extendConfigObj: {
    isParallel: false,
    isAsync: false,
    syncTimeout: 5,
    cmdInterval: 1,
    failRetry: 2,
  }
});
const currentFormData = ref<FormData>({
     ...step1Data.value,
     ...step2Data.value,
     ...step3Data.value,      
  });
const [JobCronModel, jobCronModelApi] = useVbenModal({
  zIndex: 2001,
  connectedComponent: jobCronModel,
});

  async function cronExpressionChanged(cron:any){
    console.log("cron", cron);
    await step2FormApi.setValues({cron: cron});
  };
  const [Step1Form, step1FormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editStep1Schema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [Step2Form, step2FormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editStep2Schema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});


const [ExtendConfigForm, extendConfigFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: extendConfigSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-6 gap-x-4',
});

async function timeWeekChanged(slotProps: any){
  console.log("timeWeek", slotProps.time, slotProps.week);
  let cronList = ['*', '*', '*', '*', '*', '*'];
  if(slotProps.time){
    let items = slotProps.time.toString().split(':');
    if(items.length == 3){
      cronList[0] = parseInt(items[2] || '0').toString() || '*';
      cronList[1] = parseInt(items[1] || '0').toString() || '*';
      cronList[2] = parseInt(items[0] || '0').toString() || '*';
    }   
  }
  if(slotProps.week){
      cronList[3] = '?';
      cronList[5] = slotProps.week.toString() || '?';
    }

    step2Data.value.cron = cronList.join(' ');
    await step2FormApi.setValues({cron: step2Data.value.cron});
}

async function getProductOptions(){
  const res = await ListProduct({});
  if(!res || !res.items){
    return [];
  }
  return res.items.map((item: any) => ({
    label: item.productName,
    value: item.productKey,
  }));
}

async function getGroupOptions(){
  const res = await ListGroup({});
  if(!res || !res.items){
    return [];
  }
  return res.items.map((item: any) => ({
    label: item.groupName,
    value: item.groupId,
  }));
}

async function getDeviceOptions(productKey: string){
  if(!productKey){
    return [];
  }
  const res = await ListDevice({productKey: productKey});
  if(!res || !res.items){
    return [];
  }
  return res.items.map((item: any) => ({
    label: item.deviceName,
    value: item.deviceKey,
  }));
}

function resetStep1Data() {
  step1Data.value = {
    taskId: currentFormData.value.taskId,
    taskName: currentFormData.value.taskName,
    status: currentFormData.value.status,
    remark: currentFormData.value.remark,
    targetType: currentFormData.value.targetType,
    productKey: currentFormData.value.productKey,
    deviceKeyList: currentFormData.value.deviceKeyList,
    disableDeviceKeyList: currentFormData.value.disableDeviceKeyList,
    groupId: currentFormData.value.groupId,
  };
}

function resetStep2Data() {
  step2Data.value = {
    type: currentFormData.value.type||0,
    timeWeek: currentFormData.value.timeWeek||{
      time: '',
      week: [],
    },
    cron: currentFormData.value.cron||'',
  };
}

function resetStep3Data() {
  step3Data.value = {
    pollType: currentFormData.value.pollType,
    pollScriptObj: {...currentFormData.value.pollScriptObj},
    modbusParam: {...currentFormData.value.modbusParam, useSubAddr: String(currentFormData.value.pollScriptObj.useSubAddr)},
    extendConfigObj: {...currentFormData.value.extendConfigObj},
  };
}

function openCronModal(){
  jobCronModelApi.open();
}

async function getModelKeysOptons(productKey: string){
  if(productKey){
    const res = await DetailProduct({productKey: productKey});
    if(!res || !res.tsl){
      return [];
    }
    return res.tsl.properties.map((item: any) => ({
      label: item.name,
      value: item.key,
    }));
  }
  return [];
}

const next = async () => {
  if (current.value == 0) {
    const { valid } = await step1FormApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await step1FormApi.getValues());
    step1Data.value = {...data} as Step1DataType;
    console.log("step1Data", step1Data.value);
  } else if (current.value == 1) {
    const { valid } = await step2FormApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await step2FormApi.getValues());
    step2Data.value = {...data} as Step2DataType;
    console.log("step2Data", step2Data.value);
  }
  current.value++;
  if(current.value == 1){
    console.log("set step2Data", step2Data.value);
    await step2FormApi.setValues(step2Data.value as Step2DataType);
  }else if(current.value == 2){
    modelKeysOptions.value = await getModelKeysOptons(step1Data.value.productKey);
    console.log("modelKeysOptions", modelKeysOptions.value);
    await extendConfigFormApi.setValues(step3Data.value.extendConfigObj);
  }
};
const prev = async () => {
  console.log("current", current.value);
  if (current.value == 2) {
    const data = cloneDeep(await scriptContentFormRef.value?.getFormData());
    step3Data.value.pollScriptObj = data?.pollScriptObj;
    step3Data.value.modbusParam = data?.modbusParam;
    if(extFormEnabled.value){
      const extendConfigData = await extendConfigFormApi.getValues();
      step3Data.value.extendConfigObj = extendConfigData as any;
    }
  } else if (current.value == 1) {
    const data = cloneDeep(await step2FormApi.getValues());
    step2Data.value = {...data} as Step2DataType;
  }
  current.value--;
  console.log("current --", current.value);
  if(current.value == 1){
    await step2FormApi.setValues(step2Data.value as Step2DataType);
  }else if(current.value == 0){
    await step1FormApi.setValues(step1Data.value as Step1DataType);
  }
};


const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  showConfirmButton: false,
  showCancelButton: false,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, update} = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    if (isUpdate.value) {
      const record = await View({ taskId: id });
      currentFormData.value = {
        ...record,
        targetType: String(record.targetType),
        pollType: String(record.pollType),
        pollScriptObj: {
          ...record.pollScriptObj,
          useSubAddr: record.pollScriptObj ? String(record.pollScriptObj.useSubAddr) : '0',
          dataCheckType: record.pollScriptObj ? String(record.pollScriptObj.dataCheckType) : '1',
        },
        modbusParam: {
          ...record.modbusParam,
          useSubAddr: record.modbusParam ? String(record.pollScriptObj.useSubAddr) : '0',
          dataCheckType: record.modbusParam ? String(record.modbusParam.dataCheckType) : '2',
          modbusFunction: record.modbusParam ? String(record.modbusParam.modbusFunction) : '1',
        },
      };
    }else{
      currentFormData.value = {
        taskId: 0,
        taskName: '',
        status: '0',
        remark: '',
        targetType: Props.targetType ? Props.targetType : '1',
        productKey: Props.productKey || '',
        deviceKeyList: Props.deviceKey ? [Props.deviceKey] : [],
        disableDeviceKeyList: [],
        groupId: Props.groupId || null,
        cron: '',
        pollType: '1',
        type: 0,
        timeWeek: {
          time: '',
          week: [],
        },
        pollScriptObj: {
          modelKeys: [],
          dataType: 'hex',
          dataMsg: '',
          useSubAddr: '0',
        },
        modbusParam: {
          useSubAddr: '0',
          subAddr: 1,
          modbusFunction: '1',
          regAddr: 0,
          coilCount: 1,
          regCount: 1,
          coilValue: 0,
          regValue: 0,
          coilStatusList: [],
          regValueList: [],
          dataCheckType: '2',
        },
        extendConfigObj: {
          isParallel: false,
          isAsync: false,
          syncTimeout: 5,
          cmdInterval: 1,
          failRetry: 2,
        },
      };
    }
    current.value = 0;
    resetStep1Data();
    resetStep2Data();
    resetStep3Data();
    const productOptions = await getProductOptions();
    const groupOptions = await getGroupOptions();
    step1FormApi.updateSchema([
      {
        fieldName: 'productKey',
        component: 'Select',
        componentProps: {
          options: productOptions,
          onChange: async (value: any) => {
            if(value != step1Data.value.productKey){
              await step1FormApi.setValues({deviceKeyList: []});
            }
          },
          showSearch: true,
          filterOption: (input: any, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
        },
        dependencies: {
          show: (values: any) => values.targetType == 1 || values.targetType == 2,
          triggerFields: ['targetType'],
        },
      },
      {
        fieldName: 'deviceKeyList',
        component: 'Select',
        dependencies: {
          show: (values: any) => values.targetType == 1,
          componentProps: async (values: any) => ({
            mode: 'multiple',
            options: await getDeviceOptions(values.productKey),
            showSearch: true,
            filterOption: (input: any, option: any) => {
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          }),
          triggerFields: ['targetType', 'productKey'],
        },
      },
      {
        fieldName: 'groupId',
        component: 'Select',
        dependencies: {
          show: (values: any) => values.targetType == 3,
          componentProps: async (values: any) => ({
            options: groupOptions,
            showSearch: true,
            filterOption: (input: any, option: any) => {
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          }),
          triggerFields: ['targetType'],
        },
      },
    ]);
    await step1FormApi.setValues(step1Data.value as Step1DataType);
    drawerApi.setState({ confirmLoading: false, loading: false });
  },
});


async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const res = await scriptContentFormRef.value?.validate() as any;
    if(!res || !res.valid){
      return;
    }
    const data = cloneDeep(await scriptContentFormRef.value?.getFormData());
    console.log("scriptContentFormRef.value?.getFormData()", data);
    step3Data.value.pollScriptObj = data?.pollScriptObj;
    step3Data.value.modbusParam = data?.modbusParam;
    if(extFormEnabled.value){
      const extendConfigData = await extendConfigFormApi.getValues();
      step3Data.value.extendConfigObj = extendConfigData as any;
    }
    console.log("submit Data",{...step1Data.value, ...step2Data.value, ...step3Data.value});
    await Edit({...step1Data.value, ...step2Data.value, ...step3Data.value});
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  drawerApi.close();
}

</script>