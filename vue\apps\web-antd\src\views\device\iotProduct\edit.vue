<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <template #imgUrl="slotProps">
        <ImageUpload v-bind="slotProps" :accept="accept" :max-number=1
          :api="(file, progressEvent) => uploadApi(file, progressEvent, true)" />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep, getPopupContainer, listToTree } from '@vben/utils';
import { List as CategoryList } from '#/api/device/iotProductCategory';
import { ImageUpload } from '#/components/upload';
import { useVbenForm } from '#/adapter/form';
import { Edit, View } from '#/api/device/iotProduct';
import { editSchema } from './model';
import { uploadApi } from '#/api';

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
}

const accept = ref(['jpg', 'jpeg', 'png', 'gif', 'webp']);
const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});
async function setupCategorySelect() {
  // categoryArray
  const categoryArray = await CategoryList({});
  console.log('setupCategorySelect', categoryArray);

  const categoryTree = listToTree(categoryArray.items, { id: 'categoryId', pid: 'parentId' });
  const fullCategoryTree = categoryTree;
  addFullName(fullCategoryTree, 'categoryName', ' / ');


  formApi.updateSchema([
    {
      componentProps: {
        fieldNames: {
          label: 'categoryName',
          value: 'categoryId',
        },
        getPopupContainer,
        // 设置弹窗滚动高度 默认256
        listHeight: 300,
        showSearch: true,
        treeData: fullCategoryTree,
        treeDefaultExpandAll: false,
        // 默认展开的树节点

        treeLine: { showLeafIcon: false },
        // 筛选的字段
        treeNodeFilterProp: 'categoryName',
        treeNodeLabelProp: 'fullName',
      },
      fieldName: 'categoryId',
    },
  ]);
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, update, view, } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;
    await setupCategorySelect();

    if (isUpdate.value || isView.value) {
      const record = await View({ productId: id });
      await formApi.setValues(record);
      await formApi.setValues({
        'deviceType': record.deviceType.toString(),
        'firmwareType': record.firmwareType.toString(),
        'networkType': record.networkType.toString(),
        'transport': record.transport.toString(),
        'channelType': record.channelType.toString(),
        'vertificateMethod': record.vertificateMethod.toString(),
        'locationWay': record.locationWay.toString(),
        'isPrivate': record.isPrivate.toString(),
        'isAuthorize': record.isAuthorize.toString(),
        'publishStatus': record.publishStatus.toString(),
        'onlineTimeout': record.onlineTimeout.toString(),
      });
    }
    drawerApi.setState({ confirmLoading: false, loading: false })

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    data.publishStatus = 1;
    await (Edit(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

</script>
