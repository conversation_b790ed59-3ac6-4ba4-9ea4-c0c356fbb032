import { requestClient } from '#/api/request';

// 获取终端用户设备分组绑定表列表
export function List(params:any) {
  return requestClient.get<any>('project/iotConsumerDeviceGroup/list', { params });
}

// 删除/批量删除终端用户设备分组绑定表
export function Delete(params:any) {
  return requestClient.post<any>('project/iotConsumerDeviceGroup/delete', { ...params });
}

// 添加/编辑终端用户设备分组绑定表
export function Edit(params:any) {
  return requestClient.post<any>('project/iotConsumerDeviceGroup/edit', { ...params });
}

// 获取终端用户设备分组绑定表指定详情
export function View(params:any) {
  return requestClient.get<any>('project/iotConsumerDeviceGroup/view', { params });
}

// 导出终端用户设备分组绑定表
export function Export(params:any) {
  return requestClient.post<Blob>('/project/iotConsumerDeviceGroup/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}