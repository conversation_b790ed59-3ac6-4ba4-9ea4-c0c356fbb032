import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
export class State {
  public cjt188Id = 0; // ID
  public productKey = ''; // 产品标识
  public meterType = ''; // 仪表类型
  public fields = ''; // 数据与物模型绑定JSON
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}


// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '物模型字段标识',
    field: 'key',
    align: 'left',
    width: -1,
  },  
  {
    title: '物模型字段名称',
    field: 'modelName',
    align: 'left',
    width: -1,
  },
  {
    title: 'CJ/T 188 数据对象类型',
    field: 'dataType',
    align: 'left',
    width: -1,
    slots: {
      default:  'dataType_edit',
    },
  },
  {
    title: 'CJ/T 188  数据字段名称',
    field: 'field',
    align: 'left',
    width: -1,
    slots: {
      default: 'field_edit',
    },
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

export interface FieldType {
  key: string;
  modelName: string;
  field: string;
  dataType: string;
}
// 表格列接口
export interface RowType {
  cjt188Id: number;
  productKey: string;
  meterType: string;
  fieldsObj: FieldType[];
  tenantId: string;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'cjt188Id', label: 'ID' },
  { field: 'productKey', label: '产品标识' },
  { field: 'meterType', label: '仪表类型' },
  { field: 'tenantId', label: '租户ID' },
  { field: 'createdAt', label: '创建时间' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'cjt188Id',
    component: 'Input',
    label: 'ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: { show: () => false, triggerFields: [''], },
    rules: 'required'
  },
  {
    fieldName: 'meterType',
    component: 'Input',
    label: '仪表类型',
    componentProps: {
      placeholder: '请输入仪表类型',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  }
];