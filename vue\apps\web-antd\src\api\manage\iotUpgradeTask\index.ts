import { requestClient } from '#/api/request';

// 获取升级任务表列表
export function List(params:any) {
  return requestClient.get<any>('manage/iotUpgradeTask/list', { params });
}

// 删除/批量删除升级任务表
export function Delete(params:any) {
  return requestClient.post<any>('manage/iotUpgradeTask/delete', { ...params });
}

// 添加/编辑升级任务表
export function Edit(params:any) {
  return requestClient.post<any>('manage/iotUpgradeTask/edit', { ...params });
}

// 获取升级任务表指定详情
export function View(params:any) {
  return requestClient.get<any>('manage/iotUpgradeTask/view', { params });
}

// 导出升级任务表
export function Export(params:any) {
  return requestClient.post<Blob>('/manage/iotUpgradeTask/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}