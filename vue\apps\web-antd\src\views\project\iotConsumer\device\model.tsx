
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { VbenFormSchema } from '@vben/common-ui';

import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
	{
		fieldName: 'productKey',
		component: 'Select',
		label: '所属产品',
	},
	{
		fieldName: 'deviceKey',
		component: 'Input',
		label: '设备标识',
		componentProps: {
			disabled: false,
			placeholder: '请输入设备标识',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
	},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
	{
		title: '编号',
		field: 'deviceConsumerId',
		align: 'center',
		width: 60,
	},
	{
		title: '设备名称',
		field: 'device.deviceName',
		align: 'center',
		width: -1,
	},
	{
		title: '设备别名',
		field: 'aliasName',
		align: 'center',
		width: -1,
	},
	{
		title: '设备标识',
		field: 'device.deviceKey',
		align: 'center',
		width: -1,
	},
	{
		title: '所属产品',
		field: 'device.productName',
		align: 'center',
		width: -1,
	},
	{
		title: '版本',
		field: 'device.firmwareVersion',
		align: 'center',
		width: 100,
	},
	{
		title: '分享状态',
		field: 'shareStatus',
		align: 'center',
		width: 100,
		slots: {
			default: ({ row }) => {
				return renderDict(row.shareStatus, DictEnum.DEVICE_SHARE_STATUS);
			}
		},
	},
	{
		title: '激活时间',
		field: 'device.activeTime',
		align: 'center',
		width: -1,
	},
	{
		title: '在线状态',
		field: 'device.deviceState',
		align: 'center',
		width: 100,
		slots: { default: 'deviceState' },
	},
	{ title: '操作', width: 150, slots: { default: 'action' } },
];

export interface RowType {
	deviceId: number;
	productKey: string;
	deviceKey: string;
	deviceName: string;
	longitude: number;
	latitude: number;
	firmwareVersion: number;
	isShadow: number;
	imgUrl: string;
	deviceState: number;
	alarmState: number;
	rssi: number;
	thingsModelValue: string;
	networkAddress: string;
	networkIp: string;
	status: string;
	tenantId: string;
	deptId: number;
	createdDept: number;
	createdBy: number;
	createdAt: string;
	updatedBy: number;
	updatedAt: string;
	deletedBy: number;
	deletedAt: string;
	remark: string;
	device: {
		deviceName: string;
		deviceKey: string;
		productName: string;
		firmwareVersion: string;
		activeTime: string;
	};
}

// 分享表格列
export const shareInfoColumns: VxeGridProps['columns'] = [
	{
		title: '编号',
		field: 'deviceConsumerId',
		align: 'center',
		width: 60,
	},
	{
		title: '用户ID',
		field: 'consumer.consumerId',
		align: 'left',
		width: 100,
	},
	{
		title: '用户名',
		field: 'consumer.userName',
		align: 'left',
		width: 100,
	},
	{
		title: '绑定时间',
		field: 'createdAt',
		align: 'left',
		width: -1,
	}
];


