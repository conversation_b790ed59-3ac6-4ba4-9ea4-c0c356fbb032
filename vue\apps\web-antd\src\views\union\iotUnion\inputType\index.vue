<template>
  <Page auto-content-height>
    <Grid table-title="录入型变量列表">
      <!-- 表格工具栏 -->
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleAdd">新增</Button>
          <!-- <Button>删除</Button> -->
        </Space>
      </template>
      <!-- 属性类型 -->
      <template #fieldType="{ row }">
        {{ getDeviceStateLabel(row.fieldType) }}
      </template>
      <!-- 属性状态 -->
      <template #status="{ row }">
        <Tag :style="{
          borderRadius: '4px',
          minWidth: '48px',
          textAlign: 'center',
          color: row.status === '0'? '#52c41a' : row.status === '1' ? '#faad14' : '#909399',
          background: row.status === '0' ? '#f6ffed' : row.status === '1'  ? '#fffbe6' : '#f4f6fa',
          border: row.status === '0'? '1px solid #b7eb8f': row.status ==='1' ? '1px solid #ffe58f': '1px solid #d9d9d9'
        }">
          {{ row.status == "0" ? '启用' : '禁用' }}
        </Tag>
      </template>
      <!-- 表格右侧功能 -->
      <template #action="{ row }">
        <Space>
          <Button type="link" @click="handleEdit(row)">修改</Button>
          <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
        </Space>
      </template>
    </Grid>
  </Page>
  <EditDrawer @reload="reloadData"></EditDrawer>
</template>

<script setup lang='ts' name=''>
import { Button, message, Popconfirm,Space,Tag } from 'ant-design-vue';
import { getVxePopupContainer } from '@vben/utils'
import { useVbenVxeGrid, type VxeTableGridOptions } from '@vben/plugins/vxe-table';
import { ref } from 'vue'
import { Delete } from '#/api/union/iotUnionField';
import { columns, querySchema } from './model';
import { Page, useVbenDrawer, type VbenFormProps } from '@vben/common-ui';
import editDrawer from './edit.vue';
import { List } from '#/api/union/iotUnionField';
import { useRoute } from 'vue-router';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

/** 路由实例 */
const route = useRoute();
const deviceStateOptions = getDictOptions(DictEnum.FROM_TYPE); // 设备状态字典
function getDeviceStateLabel(value: string | number) {
  const found = deviceStateOptions.find(opt => String(opt.value) === String(value));
  return found ? found.label : value;
}
/** 属性类型（2-录入型变量 3-运算型变量） */
const fieldType = ref(2)
/** 表格配置 */
const gridOptions: VxeTableGridOptions = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'fieldId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page },formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          unionKey: route.query.unionId,
          fieldType: fieldType.value,
          ...formValues
        });
      },
    },
  },
  toolbarConfig: {
    /** 列筛选 */
    // custom: true,
    /** 导出 */
    export: true,
    /** 刷新 */
    refresh: true,
    /** 未知 */
    // resizable: true,
    /** 未知 */
    // search: true,
    /** 全屏 */
    // zoom: true,
  },
}
/** 表格表单配置 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
/** 表格实例 */
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions
});

/** 抽屉实例 */
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});

/** 新增 */
function handleAdd() {
  editDrawerApi.setData({ update: false });
  editDrawerApi.open();
}
/** 改 */
function handleEdit(row: any) {
  editDrawerApi.setData({ data: row, update: true, view: false });
  editDrawerApi.open();
}

/** 删除 */
async function handleDelete(row: any) {
  const res = await Delete({ fieldId: row.fieldId });
  message.success("删除成功");
  reloadData()
}

/** 刷新数据 */
function reloadData() {
  gridApi.reload()
}
</script>

<style scoped>
</style>
