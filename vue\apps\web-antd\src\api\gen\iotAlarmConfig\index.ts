import { requestClient } from '#/api/request';

// 获取报警配置表列表
export function List(params:any) {
  return requestClient.get<any>('ruleengine/iotAlarmConfig/list', { params });
}

// 删除/批量删除报警配置表
export function Delete(params:any) {
  return requestClient.post<any>('ruleengine/iotAlarmConfig/delete', { ...params });
}

// 添加/编辑报警配置表
export function Edit(params:any) {
  return requestClient.post<any>('ruleengine/iotAlarmConfig/edit', { ...params });
}

// 修改报警配置表状态
export function Status(params:any) {
  return requestClient.post<any>('ruleengine/iotAlarmConfig/status', { ...params });
}

// 获取报警配置表指定详情
export function View(params:any) {
  return requestClient.get<any>('ruleengine/iotAlarmConfig/view', { params });
}

// 导出报警配置表
export function Export(params:any) {
  return requestClient.post<Blob>('/ruleengine/iotAlarmConfig/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}