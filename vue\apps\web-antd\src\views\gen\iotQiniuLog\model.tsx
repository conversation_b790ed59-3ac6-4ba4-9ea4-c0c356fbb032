import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';

export class State {
  public id = 0; // ID
  public message = ''; // 请求参数
  public result = null; // 处理结果
  public success = 0; // 处理状态 1成功 0失败
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 更新时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
				fieldName: 'message',
				component: 'Input',
				label: '请求参数',
				componentProps: {
					placeholder: '请输入请求参数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'success',
				component: 'InputNumber',
				label: '处理状态 1成功 0失败',
				componentProps: {
					placeholder: '请输入处理状态 1成功 0失败',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入处理状态 1成功 0失败', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'createdAt',
				component: 'RangePicker',
				label: '创建时间',
				componentProps: {
					type: 'datetimerange',
					clearable: true,
					valueFormat: 'FMDateRange',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:null,
				formItemClass: 'col-span-1',
			},];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: 'ID',
    field: 'id',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '请求参数',
    field: 'message',
    align: 'left',
    width: -1,
 },
  {
    title: '处理结果',
    field: 'result',
    align: 'left',
    width: -1,
 },
  {
    title: '处理状态 1成功 0失败',
    field: 'success',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  {
    title: '更新时间',
    field: 'updatedAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  id: number;
  message: string;
  result: string;
  success: number;
  createdAt: string;
  updatedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'id',  label: 'ID'},
  {  field: 'message',  label: '请求参数'},
  {  field: 'result',  label: '处理结果'},
  {  field: 'success',  label: '处理状态 1成功 0失败'},
  {  field: 'createdAt',  label: '创建时间'},
  {  field: 'updatedAt',  label: '更新时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
 {
					fieldName: 'id',
					component: 'Input',
					label: 'ID',
					dependencies: {   show: () => false,    triggerFields: [''],   },
					componentProps: {
						placeholder: '',
						onUpdateValue: (e: any) => {
							console.log(e);
						},   
					},
					formItemClass: 'col-span-1',
				},  {
				fieldName: 'message',
				component: 'Input',
				label: '请求参数',
				componentProps: {
					placeholder: '请输入请求参数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'result',
				component: 'Input',
				label: '处理结果',
				componentProps: {
					placeholder: '请输入处理结果',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'success',
				component: 'InputNumber',
				label: '处理状态 1成功 0失败',
				componentProps: {
					placeholder: '请输入处理状态 1成功 0失败',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入处理状态 1成功 0失败', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},];