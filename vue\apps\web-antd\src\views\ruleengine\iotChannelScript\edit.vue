
<template>
  <div>
    <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
      <BasicForm>
        <!-- 所属产品选择表格 -->

        <template #productKey="slotProps">
          <Input placeholder="请输入" v-bind="slotProps">
            <template #addonAfter>
              <span @click="selectAffiliatedProduct"> 选择 </span>
            </template>
          </Input>
        </template>

        <template #inputChannelId="slotProps">
          <Input placeholder="请选择接入点" v-bind="slotProps">
            <template #addonAfter>
              <span @click="selectBridge(999, 1)"> 选择 </span>
            </template>
          </Input>
        </template>

        <template #HttpScriptGeneration>
          <Button type="primary" @click="selectBridge(4, 2)"
            >Http服务脚本</Button
          >
        </template>

        <template #MQTTScriptGeneration>
          <Button type="primary" @click="selectBridge(1, 2)"
            >MQTT桥接脚本</Button
          >
        </template>
        <template #content="slotProps">
          <div v-if="slotProps.value != undefined">
            <highlight
              class="w-full h500 code-ts"
              :code="slotProps.value"
              language="ts"
              :autodetect="false"
              @update:code="async (d:any)  => await formApi.setFieldValue('content', d)"
            />
          </div>
        </template>
      </BasicForm>
      <template #prepend-footer>
        <Button
          type="primary"
          style="background-color: #13ce66"
          @click="onValidate"
          >验证</Button
        >
      </template>
    </BasicDrawer>

    <!-- 所属产品弹窗 -->
    <Popup1 ref="refPopup1" :getProductKey="getProductKey" />

    <Popup2 ref="refPopup2" :getBridge="getBridge" />
  </div>
</template>
<style>  
.h500 code {
  height: 500px;
}
</style>
<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Alert, Input, Button , message} from 'ant-design-vue';
import { Edit, View, Validate } from '#/api/ruleengine/iotChannelScript';
import { editSchema } from './model';

import Popup1 from './popup/popup1/index.vue';
import Popup2 from './popup/popup2/index.vue';

import hljs from 'highlight.js/lib/core';
import go from 'highlight.js/lib/languages/go';
import typescript from 'highlight.js/lib/languages/typescript';
import javascript from 'highlight.js/lib/languages/javascript';
import xml from 'highlight.js/lib/languages/xml';
import sql from 'highlight.js/lib/languages/sql';
import json from 'highlight.js/lib/languages/json';
import 'highlight.js/styles/atom-one-dark.css';
import 'highlight.js/lib/common';
hljs.registerLanguage('ts', typescript);
hljs.registerLanguage('js', javascript);
hljs.registerLanguage('go', go);
hljs.registerLanguage('xml', xml);
hljs.registerLanguage('sql', sql);
hljs.registerLanguage('json', json);
import  highlight  from '#/adapter/highlightedit';

hljs.registerLanguage('ts', typescript);
hljs.registerLanguage('js', javascript);

//脚本代码验证
const onValidate = async () => {
  const content = cloneDeep(await formApi.getValues()).content;
  console.log(content);

  const res = await Validate({ content: content });
  message.success("验证成功");
};

const refPopup1 = ref();
const refPopup2 = ref();

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
}

const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

//获得所属产品Key
const productKey = ref();
const getProductKey = (Key: string, Name: string) => {
  //使用productName显示，提交的时候转换成productKey
  formApi.setValues({ productKey: Name });
  productKey.value = Key;
};

//选择所属产品
const selectAffiliatedProduct = () => {
  refPopup1.value.openModal();
};

const [BasicForm, formApi] = useVbenForm({
  wrapperClass: 'grid-cols-12', // 24栅格,
  commonConfig: {
    // componentProps: {
    //   class: 'w-full',
    // },

    controlClass: 'w-[235px]',
    formItemClass: 'col-span-6',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true });
    const { id, update, view } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;
    await formApi.setValues({ content: '' });
    if (isUpdate.value || isView.value) {
      const record = await View({ scriptId: id });
      record.inputType = String(record.inputType);
      record.outputType = String(record.outputType);

      await formApi.setValues(record);
    }

    drawerApi.setState({ confirmLoading: false, loading: false });

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            'only-read': true,
          },
        },
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            'only-read': false,
          },
        },
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true });
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());

    if (
      data.inputType == '1' ||
      data.inputType == '2' ||
      data.inputType == '3' ||
      data.inputType == '4'
    ) {
      //将productName 替换为productKey进行提交

      data.productKey = productKey.value ? productKey.value : data.productKey;

      //判断inputChannelId属性是否存在，存在则删除此属性
      if (data?.hasOwnProperty?.('inputChannelId')) {
        delete data['inputChannelId'];
      }
    } else {
      //将channelName 替换为channelId进行提交
      data.inputChannelId = channelId.value
        ? channelId.value
        : data.inputChannelId;
      if (data?.hasOwnProperty?.('productKey')) {
        delete data['productKey'];
      }
    }

    await Edit(data);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false });
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

//选择自动生成脚本
const selectBridge = async (type: number, direction: number) => {
  if (type === 999) {
    const data = cloneDeep(await formApi.getValues());
    if (data.inputType == '5') {
      type = 4;
    } else if (data.inputType == '6') {
      type = 1;
    }
  }

  refPopup2.value.openModal(type, direction);
};

// 接入点ID
const channelId = ref();
// 获得接入接出点
const getBridge = async (
  paramChannelId: string,
  channelName: string,
  direction: string,
) => {
  //
  if (direction == '1') {
    //使用channelName显示，提交的时候转换成channelId
    formApi.setValues({ inputChannelId: channelName });
    channelId.value = paramChannelId;
  }
  if (direction == '2') {
    let currentData = await formApi.getValues();
    //给Content增加一行
    let bridgeName = '';
    console.log("currentData.outputType", currentData.outputType);
    if (currentData.outputType == 2) {
      bridgeName = "httpBridgeID";
    } else if (currentData.outputType == 3) {
      bridgeName = "mqttBridgeID";
    }
    let added = '// 执行Action动作参数(脚本由系统自动生成)\nmsgContext.setData("'+bridgeName+'", '+paramChannelId+');';
    formApi.setValues({ content: currentData.content + "\n" + added });
  }
};
</script>
