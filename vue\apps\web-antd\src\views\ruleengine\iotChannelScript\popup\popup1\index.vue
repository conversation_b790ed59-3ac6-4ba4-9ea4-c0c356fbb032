<script lang="ts" setup>
import { useVbenModal, VbenButton } from '@vben/common-ui';

import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';

import { columns, type RowType } from './model';
import type { DeepPartial } from '@vben/types';
import { List, Export, Delete } from '#/api/device/iotProduct';
import { List as CategoryList } from '#/api/device/iotProductCategory';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { ref, defineProps } from 'vue';

const props = defineProps(['getProductKey']);

const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'productId',
  },
  rowConfig: {
    keyField: 'productId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let result = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });

        let categoryArray = await CategoryList({});
        let categoryMap = categoryArray.items;
        result.items.forEach((item: any) => {
          item.categoryName = categoryMap.find(
            (i: any) => item.categoryId === i.categoryId,
          )?.categoryName;
        });
        return result;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});
//确认按钮回调函数
const onConfirm = () => {
  const productKey = gridApi.grid.getRadioRecord().productKey;
  const productName = gridApi.grid.getRadioRecord().productName;
  props.getProductKey(productKey, productName);
  modalApi.close();
};

function openModal() {
  modalApi.open();
}

defineExpose({
  openModal,
});

const [Modal, modalApi] = useVbenModal({ onConfirm: onConfirm });
</script>
<template>
  <div>
    <Modal class="h-[600px] w-[1000px]" title="选择产品">
      <div class="table-content">
        <Grid table-title="产品表"> </Grid>
      </div>
    </Modal>
  </div>
</template>
<style scoped>
.table-content {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
