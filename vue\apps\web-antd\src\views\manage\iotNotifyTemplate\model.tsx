import { h, ref } from 'vue';
import { Button, message, type Tag, type UploadChangeParam } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep, range, values } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { UploadOutlined } from '@vben/icons';

import {ListNoPage} from '#/api/manage/iotNotifyChannel'
//引入端口ListNoPage，传参，因由两组间定义第三组件内容
async function listChannelOptions(a: any, b:any){
  const data = await ListNoPage({channelType: a, serviceType: b})
  console.log("listChannel", data)
  if(!data || !data.items){
    return [];
  }
  const options = data.items.map((item: any) => {
    return {label: item.channelName, value: item.channelId}
  })
  return options
}

export class State {
  public id = 0; // 通知模板ID
  public templateName = ''; // 通知模板名称
  public businessType = null; // 业务类型
  public channelType = null; // 渠道类型
  public serviceType = null; // 服务商
  public channelId = 0; // 渠道ID
  


  public templateParam = null; // 模板参数JSON
  public status = 0; // 状态
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'templateName',
    component: 'Input',
    label: '通知模板名称',
    componentProps: {
      placeholder: '请输入通知模板名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'businessType',
    component: 'Select',
    label: '业务类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择业务类型',
      options: getDictOptions('notify_business_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'channelType',
    component: 'Select',
    label: '渠道类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择渠道类型',
      options: getDictOptions('notify_channel_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '编号',
    field: 'id',
    align: 'center',
    width: 80,
  },
  {
    title: '通知模板名称',
    field: 'templateName',
    align: 'left',
    width: -1,
  },
  {
    title: '业务类型', field: 'businessType', align: 'center', width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.businessType, 'notify_business_type');
      }
    },
  },
  {
    title: '渠道类型', field: 'channelType', align: 'center', width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.channelType, 'notify_channel_type');
      }
    },
  },
  {
    title: '服务商',
    field: 'serviceType',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        // 根据渠道类型动态选择服务商字典
        let dictType = DictEnum.NOTIFY_PROVIDER_SMS;
        if (row.channelType) {
          switch (row.channelType) {
            case 1: // 短信
            case 3: // 语音
              dictType = DictEnum.NOTIFY_PROVIDER_SMS;
              break;
            case 2: // 微信
              dictType = DictEnum.NOTIFY_PROVIDER_WECHAT;
              break;
            case 4: // 邮箱
              dictType = DictEnum.NOTIFY_PROVIDER_EMAIL;
              break;
            case 5: // 钉钉
              dictType = DictEnum.NOTIFY_PROVIDER_DING;
              break;
            case 6: // MQTT
              dictType = DictEnum.NOTIFY_PROVIDER_MQTT;
              break;
          }
        }
        return renderDict(row.serviceType, dictType);
      }
    },
  },
  {
    title: '渠道编号',
    field: 'channelId',
    align: 'center',
    width: 100,
  },
  {
    title: '状态', field: 'status', align: 'center', width: 100,
    slots: { default: 'status' },
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: 180,
  },
  {
    title: '更新时间',
    field: 'updatedAt',
    align: 'center',
    width: 180,
  },
  // {
  //   title: '备注',
  //   field: 'remark',
  //   align: 'left',
  //   width: -1,
  // },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  id: number;
  templateName: string;
  businessType: number;
  channelType: number;
  serviceType: number;
  channelId: number;
  templateParam: string;
  status: string;
  imgUrl: string;
  FileUpload: string;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'id', label: '通知模板ID' },
  { field: 'templateName', label: '通知模板名称' },


  {
    field: 'businessType',
    label: '业务类型',
    render: (_, row: any) => {
      return renderDict(row.businessType, DictEnum.NOTIFY_BUSINESS_TYPE);
    },
  },
  {
    field: 'channelType',
    label: '渠道类型',
    render: (_, row: any) => {
      return renderDict(row.channelType, DictEnum.NOTIFY_CHANNEL_TYPE);
    },
  },
  {
    field: 'serviceType',
    label: '服务商',
    render: (_, row: any) => {
      // 根据渠道类型动态选择服务商字典
      let dictType = DictEnum.NOTIFY_PROVIDER_SMS;
      if (row.channelType) {
        switch (row.channelType) {
          case 1: // 短信
          case 3: // 语音
            dictType = DictEnum.NOTIFY_PROVIDER_SMS;
            break;
          case 2: // 微信
            dictType = DictEnum.NOTIFY_PROVIDER_WECHAT;
            break;
          case 4: // 邮箱
            dictType = DictEnum.NOTIFY_PROVIDER_EMAIL;
            break;
          case 5: // 钉钉
            dictType = DictEnum.NOTIFY_PROVIDER_DING;
            break;
          case 6: // MQTT
            dictType = DictEnum.NOTIFY_PROVIDER_MQTT;
            break;
          default:
            dictType = DictEnum.NOTIFY_PROVIDER_SMS;
        }
      }
      return renderDict(row.serviceType, dictType);
    },
  },
  { field: 'channelId', label: '渠道ID' },
  { field: 'templateParam', label: '模板参数JSON' },
  
  {
    field: 'status',
    label: '状态',
    render: (_, row: any) => {
      return renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
    },
  },
  { field: 'tenantId', label: '租户ID' },
  { field: 'createdDept', label: '创建部门' },
  { field: 'createdBy', label: '创建者' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'updatedBy', label: '更新者' },
  { field: 'updatedAt', label: '更新时间' },
  { field: 'deletedBy', label: '删除人' },
  { field: 'deletedAt', label: '删除时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'id',
    component: 'Input',
    label: '通知模板ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'templateName',
    component: 'Input',
    label: '通知模板名称',
    componentProps: {
      placeholder: '请输入通知模板名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'businessType',
    component: 'Select',
    label: '业务类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择业务类型',
      options: getDictOptions('notify_business_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired'
  },
  {
    fieldName: 'channelType',
    component: 'Select',
    label: '渠道类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择渠道类型',
      options: getDictOptions('notify_channel_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired'
  },
  {
    fieldName:'serviceType',
    component: 'Select',
    label: '服务商',
    defaultValue: null,
    componentProps: ( formModel ) => {
        let dictType = DictEnum.NOTIFY_PROVIDER_SMS;

        // let dictType ;
        if (formModel) {
     
          
            if (formModel.channelType == '1' || formModel.channelType == '3') {
                dictType = DictEnum.NOTIFY_PROVIDER_SMS;
            } else if (formModel.channelType == 2) {
                dictType = DictEnum.NOTIFY_PROVIDER_WECHAT;
            } else if (formModel.channelType == '4') {
                dictType = DictEnum.NOTIFY_PROVIDER_EMAIL;
            } else if (formModel.channelType == '5') {
                dictType = DictEnum.NOTIFY_PROVIDER_DING;
            } else if (formModel.channelType == '6') {
                dictType = DictEnum.NOTIFY_PROVIDER_MQTT;
            }
        }
        return {
            placeholder: '请选择服务商',
            options: getDictOptions(dictType),
            onUpdateValue: (e: any) => {
                console.log('服务商变更:', e);
            },
      };
    },
    rules:'selectRequired'
  },
  {
    fieldName: 'channelId',
    component: 'Select',
    label: '渠道ID',
    componentProps: {
      placeholder: '请输入渠道ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    //由values.channelType, values.serviceType的值，从端口获取参数
    dependencies: {
      componentProps: async(data, formModel ) => {
        if(!!data.channelType && !!data.serviceType){
          let optionsValue = await listChannelOptions(data.channelType, data.serviceType);
          console.log("dependencies optionsValue ", optionsValue)
          return {
            placeholder: '请输入渠道ID',
            onUpdateValue: (e: any) => {
              console.log(e);
            },
            options: optionsValue,
             
          };
        }
        return {
          placeholder: '请输入渠道ID',
          onUpdateValue: (e: any) => {
            console.log(e);
          },
          options: [],
           
      };
       
    },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: z.number({ required_error: '请输入渠道ID', invalid_type_error: '无效数字' })
  },
  //1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111
 
  
  {
    fieldName: 'smsAli.phoneNumber',
    component: 'Input',
    label: '下发手机号码',
    componentProps: {
      placeholder: '多个逗号分隔',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 1 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName: 'smsAli.templateId',
    component: 'Input',
    label: '模板 ID',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 1 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName: 'smsAli.signName',
    component: 'Input',
    label: '短信签名内容',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 1 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'smsAli.content',
    component: 'Input',
    label: '短信内容',
    componentProps: {
      placeholder: '多个参数逗号分隔',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 1 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'smsTencent.phoneNumber',
    component: 'Input',
    label: '下发手机号码',
    componentProps: {
      placeholder: '多个逗号分隔',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 1 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'smsTencent.sdkAppId',
    component: 'Input',
    label: '短信 SdkAppId',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 1 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'smsTencent.templateId',
    component: 'Input',
    label: '模板 ID',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 1 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'smsTencent.signName',
    component: 'Input',
    label: '短信签名内容',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 1 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'smsTencent.content',
    component: 'Input',
    label: '短信内容',
    componentProps: {
      placeholder: '多个参数逗号分隔',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 1 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartApp.toUserId',
    component: 'Input',
    label: '用户ID',
    componentProps: {
      placeholder: '多个逗号分隔',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartApp.templateId',
    component: 'Input',
    label: '模板 ID',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartApp.url',
    component: 'Input',
    label: '跳转链接',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartApp.content',
    component: 'Input',
    label: '内容',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartCorp.msgType',
    component: 'Input',
    label: '选择类型',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartCorp.toUserId',
    component: 'Input',
    label: '用户ID',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartCorp.msgTitle',
    component: 'Input',
    label: '消息标题',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2&& data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartCorp.msgContent',
    component: 'Input',
    label: '消息内容',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartCorp.url',
    component: 'Input',
    label: '跳转链接',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartCorp.img',
    component: 'Input',
    label: '图片链接',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartOfficial.templateId',
    component: 'Input',
    label: '模板 ID',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartOfficial.appId',
    component: 'Input',
    label: '跳转小程序appID',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartOfficial.appPath',
    component: 'Input',
    label: '跳转小程序路径',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartOfficial.content',
    component: 'Input',
    label: '模板内容',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartOfficial.msgTitle',
    component: 'Input',
    label: '消息标题',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartOfficial.msgContent',
    component: 'Input',
    label: '消息内容',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartOfficial.url',
    component: 'Input',
    label: '跳转链接',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechartOfficial.img',
    component: 'Input',
    label: '图片链接',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 2 && data.serviceType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'voiceAli.phoneNumber',
    component: 'Input',
    label: '下发手机号码',
    componentProps: {
      placeholder: '多个逗号分隔',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 3 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'voiceAli.templateId',
    component: 'Input',
    label: '模板 ID',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 3 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'voiceAli.content',
    component: 'Input',
    label: '模板内容',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 3 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'voiceAli.palyCount',
    component: 'Input',
    label: '播放次数1 - 3',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 3 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'voiceAli.palyVolume',
    component: 'Input',
    label: '播放音量0 - 100',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 3 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'voiceAli.playRate',
    component: 'Input',
    label: '语速控制-500 - 500',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 3 && data.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'voiceTencent.phoneNumber',
    component: 'Input',
    label: '下发手机号码',
    componentProps: {
      placeholder: '多个逗号分隔',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 3, maxRows: 2 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 3 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName: 'voiceTencent.sdkAppId',
    component: 'Input',
    label: 'SdkAppId',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 3, maxRows: 2 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 3 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'voiceTencent.templateId',
    component: 'Input',
    label: '模板 ID',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 3, maxRows: 2 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 3 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'voiceTencent.content',
    component: 'Input',
    label: '模板内容',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 3, maxRows: 2 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 3 && data.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName: 'email.toMail',
    component: 'Input',
    label: '发送邮箱',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName: 'email.msgTitle',
    component: 'Input',
    label: '消息标题',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName: 'email.msgContent',
    component: 'Input',
    label: '消息内容',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName: 'email.file',
    component: 'Input',
    label: '附件',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName: 'ding.msgType',
    component: 'Input',
    label: '消息类型',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 5){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'ding.toDeptId',
    component: 'Input',
    label: '部门ID',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 5){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'ding.isAllUser',
    component: 'Input',
    label: '是否所有人',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 5){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'ding.toUserId',
    component: 'Input',
    label: '员工ID',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 5){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'ding.msgTitle',
    component: 'Input',
    label: '消息标题',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 5){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'ding.msgContent',
    component: 'Input',
    label: '消息内容',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 5){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'ding.url',
    component: 'Input',
    label: '跳转链接',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 5){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'ding.img',
    component: 'Input',
    label: '图片链接',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (data, formApi) => {
        if (data.channelType == 5){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
 
  //1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111
  {
    fieldName: 'status',
    component: 'Switch',
    label: '状态',
    defaultValue: false,
    componentProps: {
      style: {
        width: '20px',
      },
      placeholder: '请选择状态',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired',
  },
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
   
];//