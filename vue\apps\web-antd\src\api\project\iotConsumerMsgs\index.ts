import { requestClient } from '#/api/request';

// 获取终端用户消息表列表
export function List(params:any) {
  return requestClient.get<any>('project/iotConsumerMsgs/list', { params });
}

// 删除/批量删除终端用户消息表
export function Delete(params:any) {
  return requestClient.post<any>('project/iotConsumerMsgs/delete', { ...params });
}

// 添加/编辑终端用户消息表
export function Edit(params:any) {
  return requestClient.post<any>('project/iotConsumerMsgs/edit', { ...params });
}

// 修改终端用户消息表状态
export function Status(params:any) {
  return requestClient.post<any>('project/iotConsumerMsgs/status', { ...params });
}

// 获取终端用户消息表指定详情
export function View(params:any) {
  return requestClient.get<any>('project/iotConsumerMsgs/view', { params });
}

// 导出终端用户消息表
export function Export(params:any) {
  return requestClient.post<Blob>('/project/iotConsumerMsgs/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}