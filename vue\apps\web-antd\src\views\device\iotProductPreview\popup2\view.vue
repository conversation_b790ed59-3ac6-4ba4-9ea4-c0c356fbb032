<script setup lang="ts">
import type { SysClient } from '#/api/system/client';
import { computed, nextTick, ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';
import { useVbenForm } from '#/adapter/form';
import { Description, useDescription } from '#/components/description';
import { viewSchema, type RowType } from './model';
import { View } from '#/api/ruleengine/iotChannel';
const [BasicModal, ModalApi] = useVbenModal({
  onOpenChange: handleOpenChange,
});
const [registerDescription, { setDescProps }] = useDescription({
  column: 1,
  schema: viewSchema,
});
async function handleOpenChange(open: boolean) {
  if (!open) {
    return null;
  }

  const { record } = ModalApi.getData() as { record: RowType };
  console.log('record', record);
  const record2 = await View({ channelId: record.channelId });
  setDescProps({ data: record2 }, true);
}
</script>
<template>
  <BasicModal :footer="false" class="w-[600px]" title="查看">
    <Description @register="registerDescription"></Description>
  </BasicModal>
</template>
