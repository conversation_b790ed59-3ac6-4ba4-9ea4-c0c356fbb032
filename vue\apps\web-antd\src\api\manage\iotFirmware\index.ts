import { requestClient } from '#/api/request';

// 获取固件信息表列表
export function List(params:any) {
  return requestClient.get<any>('manage/iotFirmware/list', { params });
}

// 删除/批量删除固件信息表
export function Delete(params:any) {
  return requestClient.post<any>('manage/iotFirmware/delete', { ...params });
}

// 添加/编辑固件信息表
export function Edit(params:any) {
  return requestClient.post<any>('manage/iotFirmware/edit', { ...params });
}

// 操作固件信息表开关
export function Switch(params:any) {
  return requestClient.post<any>('manage/iotFirmware/switch', { ...params });
}

// 获取固件信息表指定详情
export function View(params:any) {
  return requestClient.get<any>('manage/iotFirmware/view', { params });
}

// 导出固件信息表
export function Export(params:any) {
  return requestClient.post<Blob>('/manage/iotFirmware/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}