/** 获取Mqtt统计所有节点的流入流出连接和主题 */
export class PointData {
  /** 流入消息速率 */
  inPerSec = 0;
  /** 流出消息速率 */
  outPerSec = 0;
  /** 总连接数(当前) */
  connectAll = 0;
  /** 在线连接数(当前) */
  connectOnline = 0;
  /** 总主题数(当前) */
  topicAll = 0;
  /** 总订阅数(当前) */
  subscribeAll = 0;

  constructor(state?: Partial<PointData>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}
/** 统计MQTT指定通道下的当前运行状态信息 */
export class PointInfo {
  /** 汇总ID */
  public imssId = 0;
  /** 通道类型（1-MQTT服务器、2-TCP服务器、3-UDP服务器、4-HTTP服务、5-WebSocket服务、6-数据库服务） */
  public channelType = 0;
  /** 资源ID */
  public channelId = 0;
  /** 分片ID */
  public shardId = 0;
  /** 历史链接总数 */
  public historyConnected = 0;
  /** 历史主题数 */
  public historyTopic = 0;
  /** 历史订阅数 */
  public historySubscribe = 0;
  /** 历史共享订阅数 */
  public historySharedSubscribe = 0;
  /** 历史接收数据大小字节 */
  public historyRecvSize = 0;
  /** 历史接收数据数量 */
  public historyRecvCount = 0;
  /** 历史发送数据大小字节 */
  public historySendSize = 0;
  /** 历史发送数据数量 */
  public historySendCount = 0;
  /** 历史认证数量 */
  public historyAuthTotal = '0';
  /** 历史认证成功数量 */
  public historyAuthSucceed = '0';
  /** 历史认证失败数量 */
  public historyAuthFailed = '0';
  /** 当前在线数 */
  public currentClients = 0;
  /** 当前连接数 */
  public currentConnected = 0;
  /** 当前断开连接数 */
  public currentDisconnected = 0;
  /** 当前主题数 */
  public currentTopic = 0;
  /** 当前订阅数 */
  public currentSubscribe = 0;
  /** 当前共享订阅数 */
  public currentSharedSubscribe = 0;
  /** 当前接收数据大小字节 */
  public currentRecvSize = 0;
  /** 当前接收数据数量 */
  public currentRecvCount = 0;
  /** 当前最后接收时间 */
  public currentRecvTime = '';
  /** 当前发送数据大小字节 */
  public currentSendSize = 0;
  /** 当前发送数据数量 */
  public currentSendCount = 0;
  /** 当前最后发送时间 */
  public currentSendTime = '';
  /** 当前认证数量 */
  public currentAuthTotal = '0';
  /** 当前认证成功数量 */
  public currentAuthSucceed = '0';
  /** 当前认证失败数量 */
  public currentAuthFailed = '0';
  /** 当前节点名称 */
  public nodeName = '';
  /** 当前节点启动时间 */
  public startTime = '';
  /** 当前节点进程内存大小字节 */
  public processMemSize = 0;
  /** 当前节点线程或协程数量 */
  public processThreadSize = 0;
  /** 系统CPU数量 */
  public sysCpuNum = 0;
  /** 系统CPU核心数量 */
  public sysCpuCores = 0;
  /** 系统CPU使用百分比 */
  public sysCpuUse = 0;
  /** 系统CPU负载 */
  public sysCpuLoad = 0;
  /** 系统内存总数 */
  public sysMemTotal = 0;
  /** 系统内存已使用 */
  public sysMemUse = 0;
  /** 系统硬盘总数 */
  public sysDiskTotal = 0;
  /** 系统硬盘已使用 */
  public sysDiskUse = 0;
  /** 服务器IP内网 */
  public sysIpLan = '';
  /** 服务器IP外网 */
  public sysIpWan = '';
  /** 程序版本 */
  public appVersion = '';
  /** 租户ID */
  public tenantId = '';
  /** 创建时间 */
  public createdAt = '';
  /** 更新时间 */
  public updatedAt = '';

  constructor(state?: Partial<PointInfo>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}
/** 统计MQTT指定通道下的历史统计信息 */
