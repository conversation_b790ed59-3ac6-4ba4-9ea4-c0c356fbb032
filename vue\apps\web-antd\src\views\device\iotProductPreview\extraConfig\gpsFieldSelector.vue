<template>
  <div>
    <Modal class="w-[40vw] h-[50vh]" :title="modalTitle">
      <BasicForm></BasicForm>
    </Modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { List } from '#/api/device/iotProductModel';
import { message } from 'ant-design-vue';

const emit = defineEmits(['confirm']);

console.log('gpsFieldSelector 组件开始加载...');

// 定义接口类型
interface ModelOption {
  label: string;
  value: string;
}

// 动态标题映射
const TITLE_MAP: Record<string, string> = {
  latAndLongKey: '选择经纬度物模型字段',
  iccidKey: '选择ICCID物模型字段',
  imsiKey: '选择IMSI物模型字段',
}

// 使用 useVbenModal 的 connectedComponent 方式，通过 modalApi.getData() 获取数据
const modalData = ref<any>({});
const currentProductKey = ref('');
const currentKey = ref('');

// 动态计算标题
const modalTitle = computed(() => {
  // 优先使用传入的 title
  if (modalData.value?.title) {
    return modalData.value.title
  }

  // 其次根据 key 来判断
  const key = currentKey.value
  if (key && TITLE_MAP[key]) {
    return TITLE_MAP[key]
  }

  // 默认标题
  return '选择物模型字段'
})

// 先声明 modalApi
const [Modal, modalApi] = useVbenModal({
  onConfirm: () => handleConfirm(),
  onCancel: () => handleCancel(),
  onOpenChange: (open) => {
    if (!open) {
      handleCancel();
      return;
    }
    modalApi.setState({
      confirmLoading: true,
      loading: true,
    })
    const { key, productKey, initialValue, title } = modalApi.getData();
    console.log('Modal 打开时获取的数据:', { key, productKey, initialValue, title });
    currentProductKey.value = productKey || '';
    modalData.value = { initialValue, title, key };
    currentKey.value = key || '';

    loadInitialData();
    modalApi.setState({
      confirmLoading: false,
      loading: false,
    })
  },
});


const loading = ref(false);
const loadingData = ref(false);
const selectedType = ref('');
const selectedModelKey = ref('');
const selectedOutputKey = ref('');
const errorMessage = ref('');

// 下拉框选项
const typeOptions = ref<ModelOption[]>([
  { label: '属性', value: 'property' },
  { label: '功能', value: 'function' },
  { label: '事件', value: 'event' }
]);
const modelKeyOptions = ref<ModelOption[]>([]);
const functionOutputMap = ref<Record<string, ModelOption[]>>({});
const eventOutputMap = ref<Record<string, ModelOption[]>>({});

const outputKeyOptions = ref<ModelOption[]>([]);

// 计算属性：是否可以确认选择
const isValid = computed(() => {
  return selectedType.value && selectedModelKey.value && selectedOutputKey.value;
});

// 加载初始数据
async function loadInitialData() {
  if (!currentProductKey.value) {
    console.warn('没有 productKey，无法加载数据');
    return;
  }

  // 从 modalData 中获取初始值
  const initialValue = modalData.value?.initialValue || {};

  try {
    // 如果有初始值，按顺序设置
    if (initialValue && initialValue.modelType) {
      const { modelType, modelKey, outputKey } = initialValue;

      // 1. 先设置类型并加载对应的模型选项（保留modelKey）
      selectedType.value = modelType;
      await handleTypeChange(modelType, true);

      // 2. 设置模型Key（保留outputKey）
      if (modelKey) {
        selectedModelKey.value = modelKey;
        await handleModelKeyChange(modelKey, true);
      }

      // 3. 设置输出Key
      if (outputKey) {
        selectedOutputKey.value = outputKey;
      }

      // 4. 最后设置表单初始值
      await formApi.setValues({
        modelType: selectedType.value,
        modelKey: selectedModelKey.value,
        outputKey: selectedOutputKey.value
      });
    } else {
      // 如果没有初始值，默认选择第一个类型
      selectedType.value = 'property';
      selectedModelKey.value = '';
      selectedOutputKey.value = '';

      await handleTypeChange(selectedType.value);

      await formApi.setValues({
        modelType: selectedType.value,
        modelKey: '',
        outputKey: ''
      });
    }
  } catch (error) {
    console.error('加载初始数据失败:', error);
  }
}


// 处理类型变更
async function handleTypeChange(value: string, preserveModelKey = false) {
  selectedType.value = value;

  if (!preserveModelKey) {
    selectedModelKey.value = '';
    selectedOutputKey.value = '';
    // 清空表单相关字段
    if (formApi) {
      await formApi.setFieldValue('modelKey', '');
      await formApi.setFieldValue('outputKey', '');
    }
  }

  try {
    // 统一使用 List API 获取物模型数据
    const modelRes = await List({
      productKey: currentProductKey.value,
      type: value,
      page: 1,
      pageSize: 1000
    });
    const items = modelRes.items || [];
    // 根据接口返回的数据结构构建选项
    modelKeyOptions.value = items.map((item: any) => ({
      label: item.modelName || item.name,
      value: item.modelKey || item.key
    }));

    if (modelKeyOptions.value.length === 0) {
      console.warn(`没有找到类型为 ${value} 的物模型`);
    }

    // 把fucnction 和 event 类型的输出参数选项清空
    if (value === 'function') {
      functionOutputMap.value = {};
      items.forEach((item: any) => {
        const outputs = item.outputs || [];
        functionOutputMap.value[item.modelKey || item.key] = outputs.map((param: any) => ({
          label: param.modelName,
          value: param.modelKey
        }));
      });
      outputKeyOptions.value = functionOutputMap.value[selectedModelKey.value] || [];
    } else if (value === 'event') {
      eventOutputMap.value = {};
      items.forEach((item: any) => {
        const inputs = item.inputs || [];
        eventOutputMap.value[item.modelKey || item.key] = inputs.map((param: any) => ({
          label: param.modelName,
          value: param.modelKey
        }));
      });
      outputKeyOptions.value = eventOutputMap.value[selectedModelKey.value] || [];
    } else {
      outputKeyOptions.value = [];
    }
  } catch (error) {
    console.error('获取模型数据失败:', error);
    modelKeyOptions.value = [];
  }
}

// 处理模型Key变更
async function handleModelKeyChange(value: string, preserveOutputKey = false) {
  selectedModelKey.value = value;

  if (!preserveOutputKey) {
    selectedOutputKey.value = '';
  }

  if (selectedType.value === 'function') {
    // 如果是 function 类型，获取对应的输出参数
    outputKeyOptions.value = functionOutputMap.value[value] || [];
  } else if (selectedType.value === 'event') {
    // 如果是 event 类型，获取对应的输出参数
    outputKeyOptions.value = eventOutputMap.value[value] || [];
  } else {
    // 对于其他类型，清空输出参数选项
    outputKeyOptions.value = [];
  }
}

// 处理输出Key变更
function handleOutputKeyChange(value: string) {
  selectedOutputKey.value = value;
}

// 确认按钮回调函数
async function handleConfirm() {
  if (!formApi.validate()) {
    return;
  }

  const formData = await formApi.getValues();

  // 非空校验
  const { modelType, modelKey, outputKey } = formData;
  if (!modelType || !modelKey || (modelType !== 'property' && !outputKey)) {
    message.error('请确保所有字段都已选择完整');
    modalApi.setState({
      confirmLoading: false,
    });
    return;
  }

  try {
    // 构造选择的字段对象
    emit('confirm', { key: currentKey.value, formData: formData });
    modalApi.close();
  } finally {
  }
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: [{
    fieldName: 'modelType',
    label: '模型类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择类型',
      options: typeOptions,
      //onChange: handleTypeChange,
    },
  }, {
    fieldName: 'modelKey',
    label: '模型标识',
    component: 'Select',
    componentProps: {
      placeholder: '请选择模型Key',
      options: modelKeyOptions,
    },
    dependencies: {
      componentProps: async (values: any) => {
        if (values.modelType && values.modelType !== selectedType.value) {
          await handleTypeChange(values.modelType);
        }
        return {
          options: modelKeyOptions.value,
        };
      },
      triggerFields: ['modelType'],
    },
  }, {
    fieldName: 'outputKey',
    label: '输出参数',
    component: 'Select',
    componentProps: {
      placeholder: '请选择输出Key',
      options: outputKeyOptions,
      onChange: handleOutputKeyChange,
    },
    dependencies: {
      show: (values: any) => {
        return values.modelType === 'function' || values.modelType === 'event';
      },
      componentProps: (values: any) => {
        if (values.modelKey && values.modelKey !== selectedModelKey.value) {
          handleModelKeyChange(values.modelKey);
        }
        return {
          options: outputKeyOptions.value,
        };
      },
      triggerFields: ['modelKey', 'modelType'],
    },
  }],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});
function handleCancel() {
  modalApi.close();
}
</script>

<style scoped>
.json-viewer {
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  background-color: white;
  border-radius: 4px;
  padding: 8px;
}

.json-line {
  margin: 2px 0;
}

.json-key {
  color: #0070c0;
  font-weight: bold;
}

.json-value {
  color: #009688;
}

.json-colon {
  margin: 0 4px;
}

.json-bracket {
  color: #777;
  margin-left: 4px;
}

.json-comment {
  color: #777;
  margin-left: 4px;
}

.json-expand {
  cursor: pointer;
  color: #777;
  margin-right: 4px;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pl-12 {
  padding-left: 3rem;
}
</style>
