<script setup lang="ts">
import { computed, nextTick, ref, watch, watchEffect } from 'vue';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import { useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VbenFormProps } from '#/adapter/form';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Alert, Button, Input, Row } from 'ant-design-vue';
import { View, List } from '#/api/device/iotModel';
import { modelQuerySchema, modelColumns, objectEditSchema, type RowType } from './model';

const emit = defineEmits<{ confirm: [index: number, update: boolean, data: any] }>();

interface ModalProps {
  index?: number;
  update: boolean;
  object: boolean;
  type: string;
  formData: any;
  title: string;
  view: boolean;
}

const currentIndex = ref(-1);
const isUpdate = ref(false);
const isObject = ref(false);
const currentType = ref('property');
const currentTitle = ref('');
const isView = ref(false);
const [BasicModal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
  onConfirm: handleConfirm,
  async onOpenChange(isOpen: boolean) {
    if (!isOpen) {
      return null;
    }
    modalApi.setState({ confirmLoading: true, loading: true })
    const { index, update, object, type, title, formData, view } = modalApi.getData() as ModalProps;
    currentIndex.value = index ? index : 0;
    isUpdate.value = update !== undefined ? update : false;
    isObject.value = object !== undefined ? object : false;
    currentType.value = type !== undefined ? type : 'property';
    currentTitle.value = title !== undefined ? title : '';
    isView.value = view !== undefined ? view : false;
    
    // 设置表单值，但要确保 type 字段使用传入的 type 值
    if (formData) {
      console.log("formData", formData)
      formData.type = currentType.value;
      console.log("formData after config before set", formData)
      await formApi.setValues(formData);
      console.log("formApi after set", await formApi.getValues());
    } else {
      // 如果没有 formData，只设置 type 值
      await formApi.setValues({ type: currentType.value });
    }
    if (isView.value) {
      modalApi.setState({ showConfirmButton: false });
    } else {
      modalApi.setState({ showConfirmButton: true });
    }
    modalApi.setState({ confirmLoading: false, loading: false });
  }
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: modelQuerySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  showDefaultActions: true,

};

const gridOptions: VxeTableGridOptions<RowType> = {
  radioConfig: {
    highlight: true,
    labelField: 'modelName',
    checkMethod({ row }) {
      if (isObject.value) {
        // 在标签页也可以显示列表
        if (currentType.value === 'tag') {
          return row.type === 'property'; // tag 可以选择 property 类型的数据
        }
        // 其他类型保持原有逻辑
        return row.type === currentType.value;
      }
      return row.dataType === 'integer' || row.dataType === 'string' || row.dataType === 'decimal' || row.dataType === 'enum' || row.dataType === 'bool';
    }
  },
  rowConfig: {
    keyField: 'modelId',
  },
  columns: modelColumns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const gridEvents = {
  radioChange: handleCheckboxChange,
};
async function handleCheckboxChange() {
  const id = gridApi.grid.getRadioRecord().modelId;
  const record = await View({ modelId: id });
  record.props = [];
  if (record.isChart === 1) {
    record.props.push("isChart");
  }
  if (record.isMonitor === 1) {
    record.props.push("isMonitor");
  }
  if (record.isReadonly === 1) {
    record.props.push("isReadonly");
  }
  if (record.isHistory === 1) {
    record.props.push("isHistory");
  }
  if (record.isSharePerm === 1) {
    record.props.push("isSharePerm");
  }

  // 保持模型类别不被修改
  record.type = currentType.value;

  await formApi.setValues(record);
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-4',
  },
  layout: 'vertical',
  schema: objectEditSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-4 gap-x-3',
});

async function handleConfirm() {
  try {
    modalApi.setState({ confirmLoading: true, loading: false })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    data.isChart = data.props && data.props.includes('isChart') ? 1 : 0;
    data.isMonitor = data.props && data.props.includes('isMonitor') ? 1 : 0;
    data.isReadonly = data.props && data.props.includes('isReadonly') ? 1 : 0;
    data.isHistory = data.props && data.props.includes('isHistory') ? 1 : 0;
    data.isSharePerm = data.props && data.props.includes('isSharePerm') ? 1 : 0;
    console.log("handleConfirm", currentIndex.value, data);
    await emit("confirm", currentIndex.value, isUpdate.value, data);
    modalApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.setState({ confirmLoading: false, loading: false })
  }
}


async function handleAddEnum() {
  let datas = await formApi.getValues();
  console.log("handleAddEnum", datas.enumList);
  await formApi.setValues({ enumList: [...datas.enumList, { value: '', text: '' }] });
}

async function handleDeleteEnum(index: any) {
  console.log("handleDeleteEnum", index);
  let datas = await formApi.getValues();
  datas.enumList = datas.enumList.filter((item: any, i: any) => i !== index);
  await formApi.setValues({ enumList: datas.enumList });
}

const modelClass = computed(() => {
  return isView.value ? 'flex w-[400px] gap-[8px] h-full' : 'flex w-[1000px] gap-[8px] h-full';
});

</script>
<template>
  <BasicModal :title="currentTitle" :class="modelClass">
    <div class="flex gap-[16px] h-full ">
      <Grid v-if="!isView" class="flex gap-[8px] h-full border-[1px] border-[#dcdfe6] rounded-[4px]">
      </Grid>
      <BasicForm>
        <template #enumList="slotProps">
          <div>
            <div v-for="(item, index) in slotProps.value" :key="index" style="display: flex; align-items: center;">
              <Input type="text" v-model:value="item.value" placeholder="枚举值 例如:0" />
              <Input type="text" v-model:value="item.text" placeholder="枚举描述" />
              <Button @click="handleDeleteEnum(index)">删除</Button>
            </div>
            <Button @click="handleAddEnum">添加枚举项</Button>
          </div>
        </template>
      </BasicForm>
    </div>
  </BasicModal>
</template>
