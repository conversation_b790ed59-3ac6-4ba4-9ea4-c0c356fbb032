import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

export class State {
  public categoryId = 0; // 产品分类ID
  public categoryCode = ''; // 分类标识
  public categoryName = ''; // 分类名称
  public parentId = 0; // 上级分类
  public orderNum = 0; // 排序
  public tenantId = ''; // 租户ID
  public deptId = 0; // 所属机构
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'categoryName',
    component: 'Input',
    label: '分类名称',
    componentProps: {
      placeholder: '请输入分类名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '分类名称',
    field: 'categoryName',
    align: 'left',
    width: -1,
    treeNode: true,
  },
  {
    title: '分类标识',
    field: 'categoryCode',
    align: 'left',
    width: -1,
 },
  {
    title: '上级分类',
    field: 'parentName',
    align: 'left',
    width: -1,
 },
  {
    title: '排序',
    field: 'orderNum',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 180, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  categoryId: number;
  categoryCode: string;
  categoryName: string;
  parentId: number;
  orderNum: number;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'categoryId',  label: '产品分类ID'},
  {  field: 'categoryCode',  label: '分类标识'},
  {  field: 'categoryName',  label: '分类名称'},
  {  field: 'parentName',  label: '上级分类'},
  {  field: 'orderNum',  label: '排序'},
  {  field: 'remark',  label: '备注'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'categoryId',
    component: 'Input',
    label: '产品分类ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'categoryCode',
    component: 'Input',
    label: '分类标识',
    componentProps: {
      placeholder: '请输入分类标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'categoryName',
    component: 'Input',
    label: '分类名称',
    componentProps: {
      placeholder: '请输入分类名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'parentId',
    component: 'TreeSelect',
    defaultValue: 0,
    label: '上级分类',
    componentProps: {
      getPopupContainer,
    },
  rules:'selectRequired'
},
  {
    fieldName: 'orderNum',
    component: 'InputNumber',
    label: '排序',
    componentProps: {
      placeholder: '请输入排序',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入排序', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];