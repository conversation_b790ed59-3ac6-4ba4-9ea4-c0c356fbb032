<script lang="ts" setup>
import { ref, } from 'vue';
import { Button, } from 'ant-design-vue';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { List } from '#/api/project/iotDeviceConsumer';
import { columns, type RowType } from './model';
import { useRouter } from 'vue-router';

const props = defineProps<{ deviceKey: string }>();

const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'deviceConsumerId',
  },
  columns: columns,
  exportConfig: {},
  height: 800,
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const res = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
          deviceKey: props.deviceKey,
        });
        console.log('iotDevicebindConsumer List接口返回:', res);
        return res;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});
const router = useRouter();

async function handleRefresh() {
  await gridApi.query();
}
function handleView(row: RowType) {
  const consumerId = row.consumerId;
  if (consumerId) {
    router.push(`/project/consumerDetail?consumerId=${consumerId}`);
  } else {
    alert('终端用户ID不存在');
  }
}

// 暴露方法给父组件调用
defineExpose({
  handleRefresh
});
</script>
<template>
  <Grid>
    <template #action="{ row }">
      <Button :block="false" type="link" @click="handleView(row)" v-access:code="'cpm:device:iotDevice:edit'">
        详情
      </Button>
    </template>
  </Grid>
</template>
