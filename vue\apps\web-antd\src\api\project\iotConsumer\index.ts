import { requestClient } from '#/api/request';

// 获取终端用户表列表
export function List(params: any) {
  return requestClient.get<any>('project/iotConsumer/list', { params });
}

// 删除/批量删除终端用户表
export function Delete(params: any) {
  return requestClient.post<any>('project/iotConsumer/delete', { ...params });
}

// 添加/编辑终端用户表
export function Edit(params: any) {
  return requestClient.post<any>('project/iotConsumer/edit', { ...params });
}

// 修改终端用户表状态
export function Status(params: any) {
  return requestClient.post<any>('project/iotConsumer/status', { ...params });
}

// 操作终端用户表开关
export function Switch(params: any) {
  return requestClient.post<any>('project/iotConsumer/switch', { ...params });
}

// 获取终端用户表指定详情
export function View(params: any) {
  return requestClient.get<any>('project/iotConsumer/view', { params });
}

// 导出终端用户表
export function Export(params: any) {
  return requestClient.post<Blob>('/project/iotConsumer/export', { ...params }, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}

// 获取终端用户统计信息api/v1/project/iotConsumer/statistic
export function Statistic(params: any) {
  return requestClient.get<any>('project/iotConsumer/statistic', { params });
}

// 终端用户重置密码
// api/v1/project/iotConsumer/resetPassword
export function ResetPassword(params: any) {
  return requestClient.post<any>('project/iotConsumer/resetPassword', { ...params });
}
