import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict, renderPopoverMemberSumma, type MemberSumma } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public protocolId = 0; // 协议ID
  public protocolName = ''; // 协议名称
  public protocolCode = ''; // 协议编码
  public protocolPrimary = ''; // 协议摘要
  public uploadUrl = null; // 上传地址
  public protocolType = 1; // 协议类型
  public dataFormat = ''; // 数据格式
  public status = 0; // 状态
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedBySumma?: null | MemberSumma = null; // 删除人摘要信息
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'protocolName',
    component: 'Input',
    label: '协议名称',
    componentProps: {
      placeholder: '请输入协议名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'protocolCode',
    component: 'Input',
    label: '协议编码',
    componentProps: {
      placeholder: '请输入协议编码',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '编号',
    field: 'protocolId',
    align: 'center',
    width: 60,
  },
  {
    title: '协议名称',
    field: 'protocolName',
    align: 'center',
    width: -1,
  },
  {
    title: '协议编码',
    field: 'protocolCode',
    align: 'center',
    width: -1,
  },
  {
    title: '协议摘要',
    field: 'protocolPrimary',
    align: 'center',
    width: -1,
  },
  {
    title: '上传地址',
    field: 'uploadUrl',
    align: 'center',
    width: -1,
  },
  {
    title: '协议类型', field: 'protocolType', align: 'center', width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(String(row.protocolType), 'transport');
      }
    },
  },
  {
    title: '状态', field: 'status', align: 'center', width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, 'sys_normal_disable');
      }
    },
  },
  { title: '操作', width: 150, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  protocolId: number;
  protocolName: string;
  protocolCode: string;
  protocolPrimary: string;
  uploadUrl: string;
  protocolType: number;
  dataFormat: string;
  status: string;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'protocolId', label: '编号' },
  { field: 'protocolName', label: '协议名称' },
  { field: 'protocolCode', label: '协议编码' },
  {
    field: 'protocolPrimary', label: '协议摘要',
    render(_, row: any) {
      return h('div', { style: { 'white-space': 'pre-wrap' } }, row.protocolPrimary);
    }
  },
  {
    field: 'uploadUrl', label: '上传地址',
    render(_, row: any) {
      return h('div', { style: { 'white-space': 'pre-wrap' } }, row.uploadUrl);
    }
  },
  {
    field: 'protocolType',
    label: '协议类型',
    render(_, row: any) {
      return renderDict(row.protocolType, DictEnum.TRANSPORT);
    },
  },
  {
    field: 'dataFormat', label: '数据格式',
    span: 2,
    render(_, row: any) {
      return h('div', { style: { 'white-space': 'pre-wrap' } }, row.dataFormat);
    }
  },
  {
    field: 'status',
    label: '状态',
    render(_, row: any) {
      return renderDict(row.status, DictEnum.SCRIPT_STATE);
    },
  },
  { field: 'createdAt', label: '创建时间' },
  { field: 'updatedAt', label: '更新时间' },
  {
    field: 'remark', label: '备注',
    render(_, row: any) {
      return h('div', { style: { 'white-space': 'pre-wrap' } }, row.remark);
    },
  },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'protocolId',
    component: 'Input',
    label: '协议ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'protocolName',
    component: 'Input',
    label: '协议名称',
    componentProps: {
      placeholder: '请输入协议名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'protocolCode',
    component: 'Input',
    label: '协议编码',
    componentProps: {
      placeholder: '请输入协议编码',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'protocolPrimary',
    component: 'Input',
    label: '协议摘要',
    componentProps: {
      placeholder: '请输入协议摘要',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'uploadUrl',
    component: 'Input',
    label: '上传地址',
    componentProps: {
      placeholder: '请输入上传地址',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
  {
    fieldName: 'protocolType',
    component: 'Select',
    label: '协议类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择协议类型',
      options: getDictOptions('transport'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired'
  },
  {
    fieldName: 'dataFormat',
    component: 'Input',
    label: '数据格式',
    componentProps: {
      placeholder: '请输入数据格式',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'status',
    component: 'RadioGroup',
    label: '状态',
    componentProps: {
      options: getDictOptions('sys_normal_disable'),
      onUpdateChecked: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'remark',
    component: 'Textarea',
    label: '备注',
    componentProps: {
      type: 'textarea',
      rows: 3,
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];
