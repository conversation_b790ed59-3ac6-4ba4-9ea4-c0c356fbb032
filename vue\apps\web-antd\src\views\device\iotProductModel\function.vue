<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[700px]">
    <BasicForm>
      <template #inputs="slotProps">
        <div>
          <div v-for="(item, index) in slotProps.value" :key="index" style="display: flex; align-items: center;">
            <InputGroup compact>
              <Input type="text" :addon-before="'\ ' + item.modelOrder + '\ '" :value="item.modelKey"
                style="width: 50%;" readonly />
              <Input type="text" :value="item.modelName" width="80%" style="width: 40%;" readonly />
            </InputGroup>
            <!-- 只有在事件定义页面时才显示关联逻辑 -->
            <InputGroup compact v-if="currentType === 'event'">
              <Input :value="'关联'" style="width: 60px; background-color: #fafafa; border-right: 0;" readonly />
              <Select v-model:value="item.mapping.type" style="width: 70px; border-left: 0;"
                :options="[{ label: '属性', value: 'property' }, { label: '标签', value: 'tag' }]" :readonly="isView"
                @change="(value) => handleMappingTypeChange(String(value), index, true)" placeholder="类型" clearable="true"
                :filterOption="(input, option) => option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0">
              </Select>
              <Select v-model:value="item.mapping.key" style="width: 100px; border-left: 0;"
                :options="getMappingOptions(item.mapping.type, index, true)"
                :loading="mappingLoading[`inputs_${index}`]" placeholder="映射项"
                :key="`input_select_${index}_${item.mapping.type}`" showSearch
                :filterOption="(input, option) => option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0">
              </Select>
            </InputGroup>

            <Button v-if="isView" type="link" size="small" @click="handleEditInputs(index)">查看</Button>
            <Button v-if="!isView" type="link" size="small" @click="handleEditInputs(index)">编辑</Button>
            <Button v-if="!isView" type="link" size="small" @click="handleDeleteInputs(index)">删除</Button>
          </div>
          <Button v-if="!isView" @click="handleAddInputsClick">添加参数</Button>
        </div>
      </template>
      <template #outputs="slotProps">
        <div>
          <div v-for="(item, index) in slotProps.value" :key="index" style="display: flex; align-items: center;">
            <InputGroup compact>
              <Input type="text" :addon-before="'\ ' + item.modelOrder + '\ '" :value="item.modelKey"
                style="width: 60%;" readonly />
              <Input type="text" :value="item.modelName" style="width: 40%;" readonly />
            </InputGroup>
            <InputGroup compact>
              <Input :value="'关联'" style="width: 20%; background-color: #fafafa; border-right: 0;" readonly />
              <Select v-model:value="item.mapping.type" style="width: 30%; border-left: 0;" :allowClear="true"
                :options="[{ label: '属性', value: 'property' }, { label: '标签', value: 'tag' }]" :disabled="isView"
                @change="(value) => handleMappingTypeChange(String(value), index, false)" placeholder="类型" 
                :filterOption="(input, option) => option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0">
              </Select>
              <Select v-model:value="item.mapping.key" style="width: 50%; border-left: 0;" :allowClear="true"
                :options="getMappingOptions(item.mapping.type, index, false)"
                :loading="mappingLoading[`outputs_${index}`]" placeholder="映射项"
                :key="`output_select_${index}_${item.mapping.type}`" showSearch
                :disabled="isView"
                :filterOption="(input, option) => option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0">
              </Select>
            </InputGroup>

            <Button v-if="isView" type="link" size="small" @click="handleEditOutputs(index)">查看</Button>
            <Button v-if="!isView" type="link" size="small" @click="handleEditOutputs(index)">编辑</Button>
            <Button v-if="!isView" type="link" size="small" @click="handleDeleteOutputs(index)">删除</Button>
          </div>
          <Button v-if="!isView" @click="handleAddOutputsClick">添加参数</Button>
        </div>
      </template>
      <!-- <template #enumList="slotProps">
        <div>
          <div v-for="(item, index) in slotProps.value" style="display: flex; align-items: center;">
            <Input type="text" v-model:value="item.value" placeholder="枚举值 例如:0" />
            <Input  type="text" v-model:value="item.text" placeholder="枚举描述"/>
            <Button  @click="handleDeleteEnum(index)" >删除</Button>
          </div>
        <Button @click="handleAddEnum">添加枚举项</Button>
        </div>
      </template>
      <template #itemKeys="slotProps">
        <div style="display: flex; align-items: center;" >
            <Input v-for="(item, index) in slotProps.value" type="number" :value="item" />
        </div>
      </template> -->
    </BasicForm>
    <ObjectModal @confirm="handlerObjectSelected" />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, reactive } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Alert, Button, Input, InputGroup, Select } from 'ant-design-vue';
import { Edit, View, List } from '#/api/device/iotProductModel';
import { editSchema } from './model';
import objectModal from '../iotModel/object.vue';

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  productKey: string;
  type: string;
  update: boolean;
  view: boolean;
}
const currentProductKey = ref('');
const isUpdate = ref(false);
const currentType = ref('');
const isView = ref(false);
const currentTypeName = computed(() => {
  return currentType.value === 'function' ? "方法" : "事件";
});

const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') + currentTypeName.value : $t('pages.common.add') + currentTypeName.value;
});
const isInput = ref(true);
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-4',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-4 gap-x-4',
});

// 修改映射相关的响应式数据定义
const mappingOptions = reactive<Record<string, any[]>>({});
const mappingLoading = reactive<Record<string, boolean>>({});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }

    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, productKey, update, type, view } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    currentType.value = type;
    currentProductKey.value = productKey;
    isView.value = view;

    // 清空映射选项缓存
    Object.keys(mappingOptions).forEach(key => {
      delete mappingOptions[key];
    });

    if (isUpdate.value || isView.value) {
      const record = cloneDeep(await View({ modelId: id }));
      record.props = [];
      if (record.isChart === 1) {
        record.props.push("isChart");
      }
      if (record.isMonitor === 1) {
        record.props.push("isMonitor");
      }
      if (record.isReadonly === 1) {
        record.props.push("isReadonly");
      }
      if (record.isHistory === 1) {
        record.props.push("isHistory");
      }
      if (record.isSharePerm === 1) {
        record.props.push("isSharePerm");
      }

      // 确保映射结构存在
      if (record.inputs?.length > 0) {
        record.inputs = record.inputs.map((input: any) => ({
          ...input,
          mapping: input.mapping || { type: '', key: '' }
        }));

        // 预加载已有映射类型的选项
        const uniqueTypes = [...new Set(record.inputs.map((input: any) => input.mapping?.type).filter(Boolean))];
        for (const mappingType of uniqueTypes) {
          await handleMappingTypeChange(mappingType as string, 0, true);
        }
      }

      if (record.outputs?.length > 0) {
        record.outputs = record.outputs.map((output: any) => ({
          ...output,
          mapping: output.mapping || { type: '', key: '' }
        }));

        // 预加载已有映射类型的选项
        const uniqueTypes = [...new Set(record.outputs.map((output: any) => output.mapping?.type).filter(Boolean))];
        for (const mappingType of uniqueTypes) {
          await handleMappingTypeChange(mappingType as string, 0, false);
        }
      }

      await formApi.setValues(record);
    }

    await formApi.setValues({
      productKey: productKey,
      type: currentType.value,
    });

    drawerApi.setState({ confirmLoading: false, loading: false })
    if (!isView.value) {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    }
  },
});

function getModelProperty(model: any) {
  let props = [];
  if (model.isChart === 1) {
    props.push("isChart");
  }
  if (model.isMonitor === 1) {
    props.push("isMonitor");
  }
  if (model.isReadonly === 1) {
    props.push("isReadonly");
  }
  if (model.isHistory === 1) {
    props.push("isHistory");
  }
  if (model.isSharePerm === 1) {
    props.push("isSharePerm");
  }
  return props;
}

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    data.isChart = (data.props && data.props.includes("isChart")) ? 1 : 0;
    data.isMonitor = (data.props && data.props.includes("isMonitor")) ? 1 : 0;
    data.isReadonly = (data.props && data.props.includes("isReadonly")) ? 1 : 0;
    data.isHistory = (data.props && data.props.includes("isHistory")) ? 1 : 0;
    data.isSharePerm = (data.props && data.props.includes("isSharePerm")) ? 1 : 0;

    data.productKey = currentProductKey.value;
    data.type = currentType.value;
    await (Edit(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

async function handleDeleteOutputs(index: any) {
  console.log("handleDeleteParam", index);
  let datas = await formApi.getValues();
  datas.outputs = datas.outputs.filter((item: any, i: any) => i !== index);
  await formApi.setValues({ outputs: datas.outputs });
}
async function handleEditOutputs(i: any) {
  isInput.value = false;
  let editData = cloneDeep(await formApi.getValues());
  let outPut = editData.outputs[i];
  outPut.props = getModelProperty(outPut);
  await ObjectModalApi.setData({ index: i, update: true, formData: outPut, view: isView.value });
  ObjectModalApi.open();
}

const [ObjectModal, ObjectModalApi] = useVbenModal({
  connectedComponent: objectModal,
});
async function handleAddOutputsClick() {
  isInput.value = false;
  await ObjectModalApi.setData({ update: false });
  ObjectModalApi.open();
}

async function handleDeleteInputs(index: any) {
  console.log("handleDeleteParam", index);
  let datas = await formApi.getValues();
  datas.inputs = datas.inputs.filter((item: any, i: any) => i !== index);
  await formApi.setValues({ inputs: datas.inputs });
}
async function handleEditInputs(i: any) {
  isInput.value = true;
  let editData = cloneDeep(await formApi.getValues());
  let input = editData.inputs[i];
  input.props = getModelProperty(input);
  await ObjectModalApi.setData({ index: i, update: true, formData: input, view: isView.value });
  ObjectModalApi.open();
}

async function handleAddInputsClick() {
  isInput.value = true;
  await ObjectModalApi.setData({ update: false });
  ObjectModalApi.open();
}

// 获取映射选项的API调用函数 - 修改显示格式
async function fetchMappingOptions(type: string, productKey: string) {
  try {
    const response = await List({
      page: 1,
      pageSize: 1000,
      productKey: productKey,
      type: type
    });

    // 修改数据提取逻辑，根据实际返回的数据结构
    let records = [];

    // 如果response直接是数组
    if (Array.isArray(response)) {
      records = response;
    }
    // 如果response.data是数组
    else if (response && response.data && Array.isArray(response.data)) {
      records = response.data;
    }
    // 如果response.records是数组
    else if (response && response.records && Array.isArray(response.records)) {
      records = response.records;
    }
    // 如果response.items是数组
    else if (response && response.items && Array.isArray(response.items)) {
      records = response.items;
    }
    // 如果都不是，尝试获取数组类型的属性
    else if (response && typeof response === 'object') {
      // 查找第一个数组类型的属性
      for (const [key, value] of Object.entries(response)) {
        if (Array.isArray(value)) {
          records = value;
          break;
        }
      }
    }

    if (!Array.isArray(records)) {
      console.warn('无法提取到数组数据, response:', response);
      return [];
    }

    const options = records.map((item: any) => {
      return {
        // 只显示 modelName，不拼接 modelKey
        label: item.modelName || item.name || item.modelKey || '未知',
        value: item.modelKey || item.key || item.id || item.value
      };
    });

    return options;
  } catch (error) {
    console.error('获取映射选项失败:', error);
    return [];
  }
}

// 处理映射类型变化 - 修改以确保响应式更新
async function handleMappingTypeChange(type: string, index: number, isInput: boolean) {
  if (!type || !currentProductKey.value) {
    return;
  }

  const cacheKey = `${type}_${currentProductKey.value}`;
  const loadingKey = `${isInput ? 'inputs' : 'outputs'}_${index}`;

  // 清空当前映射值
  try {
    const formData = cloneDeep(await formApi.getValues());
    const targetArray = isInput ? formData.inputs : formData.outputs;
    if (targetArray && targetArray[index] && targetArray[index].mapping) {
      targetArray[index].mapping.key = '';
      await formApi.setValues(isInput ? { inputs: targetArray } : { outputs: targetArray });
    }
  } catch (error) {
    console.error('清空映射值失败:', error);
  }

  // 如果已有缓存，直接使用
  if (mappingOptions[cacheKey] && mappingOptions[cacheKey].length > 0) {
    return;
  }

  // 设置加载状态
  mappingLoading[loadingKey] = true;

  try {
    // 获取选项数据
    const options = await fetchMappingOptions(type, currentProductKey.value);

    // 确保响应式更新
    mappingOptions[cacheKey] = options;

    // 强制触发响应式更新
    await nextTick();
  } catch (error) {
    console.error('获取映射选项失败:', error);
    mappingOptions[cacheKey] = [];
  } finally {
    mappingLoading[loadingKey] = false;
  }
}

// 获取映射选项 - 修改返回逻辑
function getMappingOptions(type: string, index: number, isInput: boolean) {
  if (!type || !currentProductKey.value) {
    return [];
  }

  const cacheKey = `${type}_${currentProductKey.value}`;
  const options = mappingOptions[cacheKey] || [];
  return options;
}

async function handlerObjectSelected(index: any, update: boolean, datas: any) {
  // 使用深拷贝确保数据可变
  const newData = cloneDeep(datas);

  // 只有在事件定义页面且是输入参数时，或者任何页面的输出参数时，才创建映射对象
  const shouldCreateMapping = (currentType.value === 'event' && isInput.value) || !isInput.value;

  if (shouldCreateMapping) {
    // 确保映射对象存在
    if (!newData.mapping) {
      newData.mapping = {
        type: '',
        key: '',
      };
    }
  } else {
    // 在功能定义页面的输入参数中，移除映射对象
    delete newData.mapping;
  }

  let editData = cloneDeep(await formApi.getValues());
  let oldItems = isInput.value ? (editData.inputs || []) : (editData.outputs || []);
  let newItems = [];

  if (!update) {
    newItems = [...oldItems, newData];
  } else {
    newItems = oldItems.map((item: any, i: any) => {
      if (i === index) {
        return newData;
      }
      return item;
    });
  }

  if (isInput.value) {
    await formApi.setValues({ inputs: newItems });
  } else {
    await formApi.setValues({ outputs: newItems });
  }
}
</script>

<style scoped>
/* 确保无缝连接效果的样式 */
:deep(.ant-input-group-compact .ant-input:not(:first-child):not(:last-child),
  .ant-input-group-compact .ant-select:not(:first-child):not(:last-child)) {
  border-left-width: 0;
  border-right-width: 1px;
}

:deep(.ant-input-group-compact .ant-select:not(:first-child)) {
  border-left-width: 0;
}

:deep(.ant-input-group-compact .ant-select:not(:last-child) .ant-select-selector) {
  border-right-width: 0;
}

:deep(.ant-input-group-compact .ant-input:not(:last-child)) {
  border-right-width: 0;
}

/* 确保选择框的焦点状态不影响布局 */
:deep(.ant-select-focused .ant-select-selector) {
  border-right-width: 1px !important;
  border-left-width: 1px !important;
  box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
}
</style>
