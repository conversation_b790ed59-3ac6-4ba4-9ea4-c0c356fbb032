<script lang="ts" setup>
import { h, ref, onMounted } from 'vue';
import {
  Button, message, Popconfirm, Card, RadioGroup,
  RadioButton, Tag
} from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl } from '@vben/access';
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, Delete } from '#/api/project/iotProjectProduct';
import { MdiPlus, MdiExport,  } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import editDrawer from './edit.vue';
import editTowDrawer from './edittow.vue';
import DispositionModal from './disposition/index.vue';
import { IconifyIcon } from '@vben/icons';
import { DictEnum } from '@vben/constants';
import { getDictOptions, getDictLabel } from '#/utils/dict';
import { processImageUrl, handleImageError } from '#/utils/image';

// 接收父组件传递的项目信息
const props = defineProps<{
  project: {
    projectId: string | number;
    projectName: string;
  };
}>();

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const cardListColumns = [{
  field: 'list',
  slots: {
    default: 'list',
  },
}];

const cardListPager = {
  pageSizes: [12, 24, 36, 48, 60],
  pageSize: 12,
};

const gridType = ref('2');
function handleChangeGrid(value: string) {
  gridType.value = value;
  configGridOptions();
}

function configGridOptions() {
  gridApi.setGridOptions({
    checkboxConfig: {
      highlight: true,
      labelField: 'projectProductId',
    },
    border: gridType.value == '2' ? 'none' : 'default',
    columns: gridType.value == '2' ? cardListColumns : columns,
    showHeader: gridType.value == '2' ? false : true,
    pagerConfig: gridType.value == '2' ? cardListPager : {},
  });
  gridApi.reload();
}

const gridOptions: VxeTableGridOptions<RowType> = {
  rowConfig: {
    keyField: 'projectProductId',
  },
  columns: columns,
  exportConfig: {},
  height: '600',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let result = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          projectId: props.project.projectId, // 传递项目ID
          ...formValues,
        });

        // 处理卡片模式的数据结构
        if (gridType.value == '2') {
          let newItems = [{ list: result.items }];
          result.items = newItems;
        }
        return result;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  //formOptions,
  gridOptions,
  gridEvents,
});
// const [ViewModal, modalApi] = useVbenModal({
//   connectedComponent: detailModal,
// });
// function handlePreview(record: RowType) {
//   modalApi.setData(record);
//   modalApi.open();
// }



const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});

const [EditTowDrawer, editTowDrawerApi] = useVbenDrawer({
  connectedComponent: editTowDrawer,
});

const dispositionModalRef = ref();

function handleAdd(item?: any) {
  if (item && item.ppId) {
    // 传递设备信息到抽屉
    editDrawerApi.setData({
      ppId: item.ppId,
      productId: item.productId,
      productName: item.productAlias || item.productKey,
      update: false,
      view: false
    });
  } else {
    editDrawerApi.setData({ update: false, view: false });
  }
  editDrawerApi.open();
}

// 删除函数
async function handleDelete(row: RowType) {
  try {
    console.log('删除按钮被点击，行数据:', row);
    await Delete({ ppId: [row.ppId] });
    message.success('删除成功');
    await handleRefresh();
  } catch (error: any) {
    console.error('删除失败:', error);
    message.error('删除失败，请重试');
  }
}


async function handleRefresh() {
  await gridApi.query();
}

async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '产品表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success('导出成功');
};


// 新增/编辑关联产品
function handleDisposition(item: any = {}) {
  console.log('打开新增/编辑关联产品:', item);

  if (item.ppId) {
    // 编辑模式
    editTowDrawerApi.setData({
      id: item.ppId,
      update: true,
      view: false,
      projectId: props.project?.projectId,
    });
  } else {
    // 新增模式
    editTowDrawerApi.setData({
      update: false,
      view: false,
      projectId: props.project?.projectId,
    });
  }

  editTowDrawerApi.open();
}

// 面板配置
function handlePanelConfig(item: any = {}) {
  console.log('打开面板配置:', item);
  dispositionModalRef.value?.open({
    ppId: item.ppId || 0,
    productKey: item.productKey || '',
    productAlias: item.productAlias || '',
  });
}

// 查看产品详情
function handleView(item: any) {
  console.log('查看产品详情:', item);
  // 可以根据需要实现跳转逻辑，比如跳转到产品详情页
  // router.push(`/device/productDetail?productId=${item.productId}`);
}

const deviceTypeOptions = getDictOptions(DictEnum.DEVICE_TYPE);
function getDeviceTypeLabel(value: string | number) {
  const option = deviceTypeOptions.find(opt => opt.value == value);
  return option ? option.label : value;
}

onMounted(() => {
  configGridOptions();
});

// 暴露方法给父组件调用
defineExpose({
  handleRefresh
});
</script>

<template>
 

    <Grid table-title="关联产品列表">
 <template #toolbar-tools>
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiPlus) " @click="() => handleDisposition({})"
          v-access:code="'cpm:device:iotProduct:edit'">
          新增
        </Button>
      
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiExport)" @click="handleExport"
          v-access:code="'cpm:device:iotProduct:export'">
          导出
        </Button>
        <RadioGroup class="mr-2 flex items-center" @change="handleChangeGrid($event.target.value)"
          v-model:value="gridType">
          <RadioButton value="1"
            style="display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0;">
            <IconifyIcon icon="ant-design:menu-outlined" />
          </RadioButton>
          <RadioButton value="2"
            style="display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0;">
            <IconifyIcon icon="mdi:view-grid" />
          </RadioButton>
        </RadioGroup>
      </template>

      <template #list="{ row }">
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4">
          <Card v-for="item in row.list" :key="item.projectProductId" class="w-full"
            :bodyStyle="{ padding: '14px 14px 0 14px' }">
            <!-- 卡片头部：图片+名称+状态 -->
            <div class="flex items-center text-left w-full">
              <!-- 左侧图片 -->
              <img :src="processImageUrl(item.product?.imgUrl)" alt="产品图片"
                style="width: 62px; height: 62px; border-radius: 8px; object-fit: cover; margin-right: 12px;"
                @error="handleImageError" />
              <!-- 中间：产品名+状态 -->
              <div class="flex-1 text-left">
                <div class="flex justify-between">
                  <div class="flex-1 text-lg">
                    <div class="overflow-hidden text-overflow-ellipsis h-[30px]">
                      {{ item.productAlias || item.productKey }}
                    </div>
                  </div>
                 
                </div>
                <div>
                  <Tag color="default">
                    {{ item.productKey }}
                  </Tag>
                </div>
              </div>
            </div>

            <!-- 下面内容 -->
            <div class="mt-4 mb-4">
              <div style="display: flex;">
                <!-- 左侧：所属分类、设备类型 -->
                <div style="flex: 1; text-align: left;">
                
                  <div class="mt-2">
                    关联时间：{{ item.createdAt }}
                  </div>
                </div>
              </div>
            </div>
            <template #actions>
              <Button type="link" class="h-6 custom-link-button" @click="handlePanelConfig(item)">
                面板配置
              </Button>
              <Button type="link" class="h-6 custom-link-button" @click="handleAdd(item)">
                用户配置
              </Button>

              <Popconfirm
                title="你确定要删除吗？"
                @confirm="handleDelete(item)"
              >
                <Button type="link" class="h-6 custom-link-button" danger>
                  删除
                </Button>
              </Popconfirm>
            </template>
          </Card>
        </div>
      </template>

      <template #action="{ row }">
        <div class="flex items-center">
         <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handlePanelConfig(row)"
            v-access:code="'cpm:device:iotProduct:edit'" :disabled="row.publishStatus === 2">
            面板配置
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleAdd(row)"
            v-access:code="'cpm:device:iotProduct:edit'" :disabled="row.publishStatus === 2"
            >
            用户配置
          </Button>
          <Popconfirm
            title="你确定要删除吗？"
            @confirm="handleDelete(row)"
          >
            <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </div>
      </template>
    
    </Grid>
    <EditDrawer @reload="handleRefresh" />
    <EditTowDrawer @reload="handleRefresh" />
    <DispositionModal ref="dispositionModalRef" />

</template>
<style scoped>
.custom-link-button {
  color: #666666 !important;
}

.custom-link-button:hover {
  color: #333333 !important;
}

.custom-link-button:focus {
  color: #666666 !important;
}
</style>
