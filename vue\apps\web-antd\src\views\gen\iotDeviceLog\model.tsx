import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

export class State {
  public deviceLogId = 0; // 设备日志ID
  public deviceKey = ''; // 设备标识
  public optUuid = null; // 操作uuid
  public messageType = 0; // 类型（1-上线 2-离线 3-属性上报 4-事件上报 5-指令下发 6-指令回复）
  public message = ''; // 原始数据
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'deviceLogId',
    component: 'InputNumber',
    label: '设备日志ID',
    componentProps: {
      placeholder: '请输入设备日志ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '设备日志ID',
    field: 'deviceLogId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
 },
  {
    title: '操作uuid',
    field: 'optUuid',
    align: 'left',
    width: -1,
 },
  {
    title: '类型（1-上线 2-离线 3-属性上报 4-事件上报 5-指令下发 6-指令回复）',
    field: 'messageType',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  deviceLogId: number;
  deviceKey: string;
  optUuid: string;
  messageType: number;
  message: string;
  tenantId: string;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'deviceLogId',  label: '设备日志ID'},
  {  field: 'deviceKey',  label: '设备标识'},
  {  field: 'optUuid',  label: '操作uuid'},
  {  field: 'messageType',  label: '类型（1-上线 2-离线 3-属性上报 4-事件上报 5-指令下发 6-指令回复）'},
  {  field: 'message',  label: '原始数据'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdAt',  label: '创建时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'deviceLogId',
    component: 'Input',
    label: '设备日志ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'optUuid',
    component: 'Input',
    label: '操作uuid',
    componentProps: {
      placeholder: '请输入操作uuid',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'messageType',
    component: 'InputNumber',
    label: '类型（1-上线 2-离线 3-属性上报 4-事件上报 5-指令下发 6-指令回复）',
    componentProps: {
      placeholder: '请输入类型（1-上线 2-离线 3-属性上报 4-事件上报 5-指令下发 6-指令回复）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入类型（1-上线 2-离线 3-属性上报 4-事件上报 5-指令下发 6-指令回复）', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'message',
    component: 'Input',
    label: '原始数据',
    componentProps: {
      placeholder: '请输入原始数据',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'tenantId',
    component: 'Input',
    label: '租户ID',
    componentProps: {
      placeholder: '请输入租户ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
];