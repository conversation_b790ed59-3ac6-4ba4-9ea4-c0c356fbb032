<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import type { VbenFormProps } from '#/adapter/form';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { ListNoPage as ListProduct } from '#/api/device/iotProduct';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { DeviceBind, DeviceSelectList } from '#/api/project/iotProject';
import { getDictOptions } from '#/utils/dict';
import { DictEnum } from '@vben/constants';
import { message, Modal } from 'ant-design-vue';
import { columns, querySchema, type RowType } from './model';
import type { DeepPartial } from '@vben/types';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import { useRoute } from 'vue-router';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();


const projectId = ref();
const deviceRoute = useRoute();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
  productOptions: any[];
}
// 定义stateMap的类型
interface StateMapItem {
  label: string | number;
  color: string;
  bg: string;
  border: string;
}
const stateMap: Record<string, StateMapItem> = {
  '0': {
    label: '正常',
    color: '#52c41a',
    bg: '#f6ffed',
    border: '1px solid #b7eb8f'
  },
  '1': {
    label: '停用',
    color: '#faad14',
    bg: '#fffbe6',
    border: '1px solid #ffe58f'
  }
};

function getStateLabelAndStyle(value: string | number) {
  const key = String(value);
  return stateMap[key] || {
    label: value,
    color: '#909399',
    bg: '#f4f6fa',
    border: '1px solid #d9d9d9'
  };
}
// —— 产品选项加载 ——
const originalProOptions = ref<any[]>([]); // 保存原始的产品选项
async function loadProductOptionsWithDeviceType() {
  try {
    const res = await ListProduct({
      page: 1,
      pageSize: 1000,
    });

    if (res && res.items) {
      // 返回包含完整产品信息的选项
      return res.items.map((item: any) => ({
        label: item.productName,
        value: item.productKey,
        deviceType: item.deviceType, // 包含设备类型
        ...item // 包含所有产品信息
      }));
    }
    return [];
  } catch (error) {
    console.error('加载产品选项失败:', error);
    return [];
  }
}
type Option = {
  label: string;
  value: string;
  deviceType?: number | string; // 添加deviceType字段
};
const productOptions = ref<Option[]>([]);
async function loadProductOptions() {
  const res = await ListProduct({
    page: 1,
    pageSize: 1000
  });
  if (!res || !res.items) {
    productOptions.value = [];
  } else {
    productOptions.value = res.items.map((item: any) => ({
      label: item.productName,
      value: item.productKey,
      deviceType: item.deviceType, // 保存deviceType信息
    }));
  }
  console.log('产品选项加载完成，包含deviceType:', productOptions.value);
}

// 从表格数据中提取产品选项
function extractProductOptionsFromTableData() {
  if (!gridApi || !gridApi.grid) return;

  const tableData = gridApi.grid.getTableData().fullData || [];
  const uniqueProducts = new Map();

  // 从表格数据中提取唯一的产品
  tableData.forEach((item: any) => {
    if (item.productKey && item.productName) {
      uniqueProducts.set(item.productKey, {
        label: item.productName,
        value: item.productKey,
      });
    }
  });

  // 转换为数组并更新产品选项
  const extractedOptions = Array.from(uniqueProducts.values());
  if (extractedOptions.length > 0) {
    // 合并原有的产品选项和从表格提取的选项，去重
    const allOptions = [...productOptions.value];
    extractedOptions.forEach(option => {
      if (!allOptions.find(existing => existing.value === option.value)) {
        allOptions.push(option);
      }
    });
    productOptions.value = allOptions;
    console.log('从表格数据中提取的产品选项:', extractedOptions);
    console.log('合并后的产品选项:', productOptions.value);
  }
}

// 更新产品选项到表单
function updateProductOptionsToForm() {
  if (gridApi && gridApi.formApi && gridApi.formApi.updateSchema) {
    gridApi.formApi.updateSchema([
      {
        fieldName: 'productKey',
        component: 'Select',
        label: '所属产品',
        componentProps: {
          placeholder: '选择所属产品',
          showSearch: true,
          allowClear: true,
          options: productOptions.value,
          filterOption: (input: any, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          onUpdateValue: (e: any) => {
            console.log(e);
          },
        },
      },
    ]);
  }
}
async function handleCancel() {
  drawerApi.close();
  if (gridApi && gridApi.formApi && gridApi.formApi.resetForm) {
    await gridApi.formApi.resetForm();
  }
}
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel, // 取消逻辑
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    console.log('🔍 编辑抽屉打开');
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, update, view, productOptions } = drawerApi.getData() as ModalProps;
    console.log('📋 抽屉数据:', { id, update, view, productOptions });
    projectId.value = id;

    const allProducts = await loadProductOptionsWithDeviceType();
    originalProOptions.value = allProducts;
    // if (productOptions && productOptions.length > 0) {
    //   gridApi.formApi.updateSchema([
    //     {
    //       fieldName: 'productKey',
    //       component: 'Select',
    //       label: '所属产品',
    //       componentProps: {
    //         placeholder: '请选择状态',
    //         options: productOptions,
    //         showSearch: true,
    //         filterOption: (input: any, option: any) => {
    //           return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    //         },
    //       },
    //     },
    //   ]);
    // }
    drawerApi.setState({ confirmLoading: false, loading: false });
  }
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'deviceId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let res = await DeviceSelectList({
          page: page.currentPage,
          pageSize: page.pageSize,
          projectId: deviceRoute.query.projectId, // 使用路由参数获取项目ID
          ...formValues,
        });
        res.items.forEach((item: any) => {
          item.productName =
            productOptions.value.find((i: any) => item.productKey === i.value)
              ?.label || '';
        });
        return res;
      },
      querySuccess: () => {
        // 数据加载成功后，从表格数据中提取产品选项并更新搜索表单
        setTimeout(() => {
          extractProductOptionsFromTableData();
          updateProductOptionsToForm();
        }, 100); // 延迟一点确保表格数据已经渲染
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: true,
    search: true,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

// —— 提交逻辑（可扩展） ——
async function handleConfirm() {
  const rows = gridApi.grid.getCheckboxRecords();
  const deviceKeys: string[] = [];
  for (const row of rows) {
    deviceKeys.push(row.deviceKey);
  }
  if (deviceKeys.length === 0) {
    message.error('请至少选择一项要绑定的数据');
    return;
  }
  await DeviceBind({
    projectId: projectId.value, // 主项目ID
    deviceKeys: deviceKeys
  });
  await handleRefresh();
}
async function handleRefresh() {
  await gridApi.query();
}

onMounted(async () => {
  await loadProductOptions();
  // 在gridApi初始化后更新表单选项
  updateProductOptionsToForm();

  // 1. 获取路由参数
  const { productName, productKey } = deviceRoute.query;
  if (productKey) {
    // 2. 设置表单字段
    gridApi.formApi.setFieldValue('productKey', productKey); // 你的下拉框字段名是 productKey
    // 3. 自动触发一次搜索
    console.log('自动触发搜索', productName);
    // gridApi.query();
  }
});
</script>
<template>
  <BasicDrawer :close-on-click-modal="false" title="选择设备" class="w-[800px]">
    <Grid table-title="设备列表">
      <!-- 表格内容（保留设备列表展示） -->
      <template #status="{ row }">
        <Tag :style="{
          color: getStateLabelAndStyle(row.status).color,
          background: getStateLabelAndStyle(row.status).bg,
          border: getStateLabelAndStyle(row.status).border,
          borderRadius: '4px'
        }">
          {{ getStateLabelAndStyle(row.status).label }}
        </Tag>
      </template>
    </Grid>
  </BasicDrawer>
</template>
