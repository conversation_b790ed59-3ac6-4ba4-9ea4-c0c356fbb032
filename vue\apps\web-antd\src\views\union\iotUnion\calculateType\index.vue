<template>
  <Page auto-content-height>
    <Grid table-title="运算型变量列表">
      <template #toolbar-tools>
        <Space>
          <Button type="primary" @click="handleAdd">新增</Button>
          <Button>删除</Button>
        </Space>
      </template>
      <template #computeType="{ row }">
        {{ row.computeType == 1 ? "周期循环" : "自定义时间段" }}
      </template>
      <template #isSave="{ row }">
        <Tag :style="{
          borderRadius: '4px',
          minWidth: '48px',
          textAlign: 'center',
          color: row.isSave === 1 ? '#52c41a' : row.isSave === 0 ? '#faad14' : '#909399',
          background: row.isSave === 1 ? '#f6ffed' : row.isSave === 0 ? '#fffbe6' : '#f4f6fa',
          border: row.isSave === 1 ? '1px solid #b7eb8f' : row.isSave === 0 ? '1px solid #ffe58f' : '1px solid #d9d9d9'
        }">
          {{ row.isSave == 0 ? '否' : '是' }}
        </Tag>
      </template>
      <template #status="{ row }">
        <Tag :style="{
          borderRadius: '4px',
          minWidth: '48px',
          textAlign: 'center',
          color: row.status === '0' ? '#52c41a' : row.status === '1' ? '#faad14' : '#909399',
          background: row.status === '0' ? '#f6ffed' : row.status === '1' ? '#fffbe6' : '#f4f6fa',
          border: row.status === '0' ? '1px solid #b7eb8f' : row.status === '1' ? '1px solid #ffe58f' : '1px solid #d9d9d9'
        }">
          {{ row.status == "0" ? '启用' : '禁用' }}
        </Tag>
      </template>
      <!-- 表格右侧功能 -->
      <template #action="{ row }">
        <Space>
          <Button type="link" @click="handleEdit(row)">修改</Button>
          <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
            @confirm="handleDelete(row)">
            <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      </template>
    </Grid>
  </Page>
  <EditDrawer ref="editDrawerRefs" @reload="reloadData"></EditDrawer>
</template>

<script setup lang='ts' name=''>
import { Button, Space, message, Popconfirm } from 'ant-design-vue';
import { useVbenVxeGrid, type VxeTableGridOptions } from '@vben/plugins/vxe-table';
import { getVxePopupContainer } from '@vben/utils'
import { Page, useVbenDrawer, type VbenFormProps } from '@vben/common-ui';
import { ref } from 'vue'
import { useRoute } from 'vue-router';
import { columns, querySchema } from './model';
import editDrawer from './edit.vue';
import { Delete } from '#/api/union/iotUnionField';
import { List } from '#/api/union/iotUnionField';
/** 路由实例 */
const route = useRoute();
/** 属性类型（2-录入型变量 3-运算型变量） */
const fieldType = ref(3)

/** 表格配置 */
const gridOptions: VxeTableGridOptions = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'fieldId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          unionKey: route.query.unionId,
          fieldType: fieldType.value,
          ...formValues
        });
      },
    },
  },
  toolbarConfig: {
    /** 列筛选 */
    // custom: true,
    /** 导出 */
    export: true,
    /** 刷新 */
    refresh: true,
    /** 未知 */
    // resizable: true,
    /** 未知 */
    // search: true,
    /** 全屏 */
    // zoom: true,
  },
}
/** 表格表单配置 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
/** 表格实例 */
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions
});

/** 抽屉实例 */
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});

/** 新增 */
function handleAdd() {
  editDrawerApi.setData({ update: false });
  editDrawerApi.open();
}
/** 改 */
async function handleEdit(row: any) {
  // console.log("edit")
  /** 获取详细数据 */
  editDrawerApi.setData({ fieldId: row.fieldId, update: true, view: false });
  editDrawerApi.open();
}

/** 删除 */
async function handleDelete(row: any) {
  const res = await Delete({ fieldId: row.fieldId });
  message.success("删除成功");
  reloadData()
}

/** 刷新数据 */
function reloadData() {
  gridApi.reload()
}
const editDrawerRefs = ref()
function reloadEditData() {
  try {
    editDrawerRefs.value.reloadData()
  }catch(e){

  }
}
defineExpose({
  reloadEditData
})
</script>

<style scoped></style>
