<template>
  <BasicModal :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm></BasicForm>
  </BasicModal>
</template>
<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Alert } from 'ant-design-vue';
import { Edit, View } from '#/api/device/iotDeviceRelation';
import { editSchema } from './model';
import { ListNoPage as ListProduct } from '#/api/device/iotProduct';
import { ListNoPage as ListDevice } from '#/api/device/iotDevice';

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
  parentProductKey: string;
  parentDeviceKey: string;
}

const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? '设置子设备地址' : '添加子设备';
});

const getDeviceList = async (productKey: string) => {
  if (!productKey) {
    return [];
  }

  const res = await ListDevice({productKey: productKey });
  if(!res || !res.items){
    return [];
  }
  return res.items.map((item: any) => ({ label: item.deviceName, value: item.deviceKey }));
}

const getProductList = async () => {
  const res = await ListProduct({deviceType: '3'});
  console.log(res);
  let productOptions = [];
  if(!res || !res.items){
    productOptions = [];
  }else{
    productOptions = res.items.map((item: any) => ({ label: item.productName, value: item.productKey }));
  }
  formApi.updateSchema([
    {
      fieldName: 'productKey',
      component: 'Select',
      label: '子设备产品',
      dependencies: {
        show: () => true,
        triggerFields: [''],
      },
      componentProps: {
        placeholder: '请选择子设备产品',
        onUpdateValue: (e: any) => {
          console.log(e);
        },
        options: productOptions,
      },
    rules:'required'
  },
  {
    fieldName: 'deviceKey',
    component: 'Select',
    label: '子设备',
    dependencies: {
      show: () => true,
      componentProps: async (values: any) => {
        console.log(values);
        return {
          placeholder: '请选择子设备',
          options: await getDeviceList(values.productKey),
        }
      },
      triggerFields: ['productKey'],
    },
  }
  ]);
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [BasicModal, modalApi] = useVbenModal({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    modalApi.setState({ confirmLoading: true, loading: true })
    const { id, update, view, parentProductKey, parentDeviceKey } = modalApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;
    if (isUpdate.value) {
      formApi.updateSchema([
      {
        fieldName: 'productKey',
        component: 'Input',
        label: '子设备产品标识',
        dependencies: {   show: () => false,    triggerFields: [''],   },
        componentProps: {
          placeholder: '请输入子设备产品标识',
          onUpdateValue: (e: any) => {
            console.log(e);
          },
        },
      rules:'required'
    },
      {
        fieldName: 'deviceKey',
        component: 'Input',
        label: '子设备标识',
        dependencies: {   show: () => false,    triggerFields: [''],   },
        componentProps: {
          placeholder: '请输入子设备标识',
          onUpdateValue: (e: any) => {
            console.log(e);
          },
        },
      rules:'required'
    }
      ]);
      const record = await View({ relationId: id });
      await formApi.setValues(record);
    }else{
      await getProductList();
      await formApi.setValues({ parentProductKey: parentProductKey, parentDeviceKey: parentDeviceKey });
    }

    modalApi.setState({ confirmLoading: false, loading: false })


    modalApi.setState({ showConfirmButton: true });
    formApi.setState({
      commonConfig: {
        componentProps: {
          readonly: false,
          "only-read": false,
        }
      }
    });
  
  },
});

async function handleConfirm() {
  try {
    modalApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    await (Edit(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  modalApi.close();
  await formApi.resetForm();
}

</script>