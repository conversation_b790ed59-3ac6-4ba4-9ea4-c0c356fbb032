import { requestClient } from '#/api/request';

// 获取设备拓扑关系表列表
export function List(params:any) {
  return requestClient.get<any>('device/iotDeviceRelation/list', { params });
}

// 删除/批量删除设备拓扑关系表
export function Delete(params:any) {
  return requestClient.post<any>('device/iotDeviceRelation/delete', { ...params });
}

// 添加/编辑设备拓扑关系表
export function Edit(params:any) {
  return requestClient.post<any>('device/iotDeviceRelation/edit', { ...params });
}

// 获取设备拓扑关系表指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotDeviceRelation/view', { params });
}

// 导出设备拓扑关系表
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotDeviceRelation/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}