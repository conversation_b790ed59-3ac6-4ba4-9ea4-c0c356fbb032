import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

import { Switch } from '#/api/project/iotConsumer';
import type { DescItem } from '#/components/description';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
	{
		fieldName: 'loginMethod',
		component: 'Select',
		label: '登录类型',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择类型',
			options: getDictOptions('consumer_login_method'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'createdAt',
		component: 'RangePicker',
		label: '登录时间',
		componentProps: {
			type: 'daterange',
			clearable: true,
			valueFormat: 'YYYY-MM-DD HH:mm:ss',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
	},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
	{
		title: '编号',
		field: 'infoId',
		align: 'center',
		width: 60,
	},
	{
		title: '时间',
		field: 'createdAt',
		align: 'center',
		width: -1,
	},
	{
		title: '登录账号',
		field: 'loginName',
		align: 'center',
		width: -1,
	},
	{
		title: '登录类型',
		field: 'loginMethod',
		align: 'center',
		width: -1,
		slots: {
			default: ({ row }) => {
				return renderDict(row.loginMethod, 'consumer_login_method');
			}
		},
	},
	{
		title: '登录地点',
		field: 'loginLocation',
		align: 'center',
		width: -1,
	},
	{
		title: '浏览器类型',
		field: 'browser',
		align: 'center',
		width: 100,
	},
	{
		title: '操作系统',
		field: 'os',
		align: 'center',
		width: 100,
	},
	{
		title: '登录状态',
		field: 'status',
		align: 'center',
		width: -1,
		slots: {
			default: ({ row }) => {
				return renderDict(row.status, 'consumer_login_statue');
			}
		},
	},
	{
		title: '登录模块',
		field: 'logModule',
		align: 'center',
		width: 100,
	},
	{
		title: '提示消息',
		field: 'msg',
		align: 'center',
		width: 100,
	},
];

export interface RowType {
	infoId: number;
	consumerId: number;
	loginName: string;
	ipaddr: string;
	loginLocation: string;
	loginMethod: number;
	browser: string;
	os: string;
	ststus: number;
	msg: string;
	logModule: string;
	createdAt: string;
}



