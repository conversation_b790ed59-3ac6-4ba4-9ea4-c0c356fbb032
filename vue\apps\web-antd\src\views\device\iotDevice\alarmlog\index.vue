<script lang="ts" setup>
import { h, reactive, ref, computed, onMounted } from 'vue';
import { Button, message, Tag, Modal, Popconfirm, Switch } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenModal, useVbenForm } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, confirm } from '#/api/ruleengine/iotAlarmLog';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import editModal from './edit.vue';

const props = defineProps<{
  productKey: string;
  deviceKey: string;        // 接收 deviceKey 参数
}>();


// 操作按钮事件处理
const emit = defineEmits(['delete', 'edit']);

const confirmEnable = ref(true);
async function fetchConfirmEnable() {
  try {
    const response = await List({ page: 1, pageSize: 1 });
    confirmEnable.value = response.data?.confirmEnable !== false;
  } catch (error) {
    console.error('获取confirmEnable参数失败:', error);
  }
}
onMounted(() => {
  fetchConfirmEnable();
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'alarmLogId',
  },
  rowConfig: {
    keyField: 'alarmLogId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
          deviceKey: props.deviceKey,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  gridClass: 'p-0 mt-0',
  formOptions: formOptions,
  gridOptions: gridOptions,
  hideSeparator: true,
  gridEvents: gridEvents,
});

onMounted(() => {
  fetchConfirmEnable();
});

const [EditModal, editModalApi] = useVbenModal({
  connectedComponent: editModal,
});

function handleEdit(row: RowType, confirmState: String) {
  editModalApi.setData({ id: row.alarmLogId, confirmState: confirmState, update: true, view: false });
  editModalApi.open();
}
async function handleConfirm(row: RowType) {
  try {
    await confirm({
      alarmLogId: row.alarmLogId,
      confirmState: 1, // 设置处理状态为"确认报警"
    });
    message.success('确认报警成功');
    await handleRefresh();
  } catch (error) {
    message.error('确认报警失败');
  }
}

async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '报警记录表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success("导出成功");
}

async function handleRefresh() {
  await gridApi.query();
  console.log('表格数据已刷新');
  //throw new Error('Function not implemented.');
}

</script>
<template class="p-0 m-0">
  <div class="p-0 m-0 h-[800px]" auto-content-height >
    <Grid>
      <template #action="{ row }">
        <div class="flex items-center" v-if="row.confirmEnable">
          <AccessControl :codes="['cpm:ruleengine:iotChannelScript:delete']" type="code">
            <Popconfirm title="确认报警吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleConfirm(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                确认报警
              </Button>
            </Popconfirm>
          </AccessControl>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row, '2')"
            v-access:code="'cpm:ruleengine:iotChannelScript:edit'">
            躁扰
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row, '3')"
            v-access:code="'cpm:ruleengine:iotChannelScript:edit'">
            测试
          </Button>

        </div>
      </template>
    </Grid>
    <EditModal @reload="handleRefresh" @close="editModalApi.close()" />
  </div>
</template>