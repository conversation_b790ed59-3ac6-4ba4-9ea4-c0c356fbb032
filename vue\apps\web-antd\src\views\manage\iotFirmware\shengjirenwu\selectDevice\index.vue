<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import type { VbenFormProps } from '#/adapter/form';
import type { DeepPartial } from '@vben/types';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { ref, computed, nextTick } from 'vue';
import { Tag } from 'ant-design-vue';

// 导入设备表的列定义和API
import { columns, type RowType } from '../../shebeibiao/model';
import { List } from '#/api/device/iotDevice';

const emit = defineEmits<{
  deviceSelected: [deviceKeys: string, deviceNames: string, deviceCount: number];
}>();

// 当前产品Key，用于过滤设备
const currentProductKey = ref<string>('');

// 设置产品Key的方法
function setProductKey(productKey: string) {
  currentProductKey.value = productKey;
  console.log('🔍 设备选择组件接收到产品Key:', productKey);
}

// 简单的查询表单配置
const querySchema = [
  {
    fieldName: 'deviceName',
    component: 'Input',
    label: '设备名称',
    componentProps: {
      placeholder: '请输入设备名称',
    },
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
    },
  },
];

// 表单配置 - 隐藏搜索表单
const formOptions: VbenFormProps = {
  schema: [], // 空的schema，不显示任何搜索字段
  showDefaultActions: false,
  showCollapseButton: false,
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  actionWrapperClass: 'hidden', // 隐藏操作区域
  wrapperClass: 'hidden', // 隐藏整个表单
};

// 表格配置
const gridOptions: VxeTableGridOptions<RowType> = {
  columns,
  checkboxConfig: {
    highlight: true,
    range: true,
  },
  exportConfig: {},
  height: 400, // 固定表格高度
  keepSource: true,
  pagerConfig: {},
  scrollY: {
    enabled: false, // 禁用垂直滚动条
  },
  scrollX: {
    enabled: false, // 禁用水平滚动条
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        try {
          const queryParams = {
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };

          // 强制添加产品Key过滤条件
          if (currentProductKey.value) {
            queryParams.productKey = currentProductKey.value;
            console.log('🔍 使用产品Key过滤设备:', currentProductKey.value);
          }

          console.log('🔍 设备选择查询参数:', queryParams);
          const result = await List(queryParams);
          console.log('🔍 设备查询结果:', result);
          return result;
        } catch (error) {
          console.error('🔍 设备查询失败:', error);
          throw error;
        }
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: true,
    resizable: false,
    search: false, // 隐藏搜索功能
    zoom: false,
  },
};

// 表格事件
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};

const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}

const [Grid, gridApi] = useVbenVxeGrid({
  //formOptions,
  gridOptions,
  gridEvents,
});

// 确认按钮回调函数
const onConfirm = () => {
  const checkedRecords = gridApi.grid.getCheckboxRecords();
  
  if (checkedRecords.length === 0) {
    console.warn('请选择至少一个设备');
    return;
  }
  
  const deviceKeys = checkedRecords.map(item => item.deviceKey).join(',');
  const deviceNames = checkedRecords.map(item => item.deviceName).join(',');
  const deviceCount = checkedRecords.length;
  
  console.log('选中的设备:', { deviceKeys, deviceNames, deviceCount });
  emit('deviceSelected', deviceKeys, deviceNames, deviceCount);
  modalApi.close();
};

function openModal() {
  modalApi.open();
}

defineExpose({
  openModal,
  setProductKey,
});

const [Modal, modalApi] = useVbenModal({
  onConfirm: onConfirm,
  onCancel: () => {
    modalApi.close();
  },
  onOpenChange: (isOpen) => {
    if (isOpen) {
      console.log('🔍 设备选择弹窗打开，当前产品Key:', currentProductKey.value);
      // 弹窗打开时，表格会自动加载数据，不需要手动调用查询
      return null;
    }
    modalApi.close();
  },
});
</script>

<template>
  <div>
    <Modal class="w-[1200px] h-[600px]" title="选择设备">
      <div class="h-full overflow-hidden">
        <Grid table-title="设备列表" class="h-full">
          <template #status="{ row }">
            <Tag :color="row.status === '0' ? 'green' : 'red'">
              {{ row.status === '0' ? '在线' : '离线' }}
            </Tag>
          </template>
          <template #action="{ row }">
            <div class="flex items-center justify-center">
              <span class="text-gray-400">-</span>
            </div>
          </template>
        </Grid>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
/* 隐藏VxeGrid表格的滚动条 */
:deep(.vxe-table--body-wrapper) {
  overflow: hidden !important;
}

:deep(.vxe-table--body) {
  overflow: hidden !important;
}

:deep(.vxe-grid) {
  overflow: hidden !important;
}

/* 隐藏所有滚动条 */
:deep(*::-webkit-scrollbar) {
  display: none !important;
}

:deep(*) {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}
</style>
