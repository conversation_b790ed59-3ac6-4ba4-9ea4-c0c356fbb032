<script lang="ts" setup>
import { h, reactive, ref, computed } from 'vue';
import { Button, message, Tag, Modal, Popconfirm, Switch } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, Delete, deleteAll } from '#/api/device/iotDeviceAbandonLog';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import { defineProps } from 'vue';

const props = defineProps<{
  deviceKey: string;
  productKey: string;
}>();

// import editDrawer from './edit.vue';
// import viewDrawer from './view.vue';
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
    commonConfig: {
    componentProps: {
      class: 'mt-0', // 或者 'mt-1', 'mt-2' 等
    },
  },
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'deviceKey',
  },
  rowConfig: {
    keyField: 'deviceKey',
  },
  columns: columns,
  exportConfig: {},
  height: 600,
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
 proxyConfig: {
  ajax: {
    query: async ({ page }, formValues) => {
      const res = await List({
        page: page.currentPage,
        pageSize: page.pageSize,
        deviceKey: props.deviceKey,      // 这里加上
       
        ...formValues,
      });
       console.log('List接口返回数据:', res); // 这里打印
      return res;
    },
  },
},
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
  gridClass: 'p-0 ',
  hideSeparator: true,
});
async function handleDelete(row: RowType) {
  await Delete({ abandonLogId: [row.abandonLogId] });
  message.success("删除成功");
  await handleRefresh();
}
async function handleRefresh() {
  await gridApi.query();
   console.log('表格数据', gridApi.grid.getTableData().fullData);
}
async function handleMultiDelete() {
    // 获取表格的所有数据
    const allRecords = gridApi.grid.getTableData().fullData;
    if (allRecords.length === 0) {
        message.error('表格中没有数据可删除');
        return;
    }
    const deviceKey = props.deviceKey;
    console.log('即将传递给 deleteAll 的参数：', { deviceKey });
    Modal.confirm({
        title: '提示',
        okType: 'danger',
        // 可根据需求调整提示内容
        content: `确认删除与设备标识 ${deviceKey} 相关的记录吗？`,
        onOk: async () => {
            await deleteAll({ deviceKey });
            message.success("删除成功");
            await handleRefresh();
        },
    });
}
async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '指令权限表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success("导出成功");
}
</script>
<template>
  <div class="p-0 m-0 ">
    <Grid>
     

      <template #reset-before>
        <Button class="mr-2" :icon="h(MdiDelete)"
          @click="handleMultiDelete" v-access:code="'cpm:manage:iotCmdPerm:delete'">
          删除全部
        </Button>
      </template>
      <template #action="{ row }">
        <div class="flex items-center justify-center">
          <AccessControl :codes="['cpm:manage:iotCmdPerm:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
  </div>
</template>