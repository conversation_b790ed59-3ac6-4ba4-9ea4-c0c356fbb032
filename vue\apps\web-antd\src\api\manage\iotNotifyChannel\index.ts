import { requestClient } from '#/api/request';

// 获取通知渠道表列表
export function List(params:any) {
  return requestClient.get<any>('manage/iotNotifyChannel/list', { params });
}

// 删除/批量删除通知渠道表
export function Delete(params:any) {
  return requestClient.post<any>('manage/iotNotifyChannel/delete', { ...params });
}

// 添加/编辑通知渠道表
export function Edit(params:any) {
  return requestClient.post<any>('manage/iotNotifyChannel/edit', { ...params });
}

// 修改通知渠道表状态
export function Status(params:any) {
  return requestClient.post<any>('manage/iotNotifyChannel/status', { ...params });
}

// 获取通知渠道表指定详情
export function View(params:any) {
  return requestClient.get<any>('manage/iotNotifyChannel/view', { params });
}

// 导出通知渠道表
export function Export(params:any) {
  return requestClient.post<Blob>('/manage/iotNotifyChannel/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}
//引入端口，/api/v1/manage/iotNotifyChannel/listNoPage，去掉/api/v1/
export function ListNoPage(params:any) {
  return requestClient.get<any>('manage/iotNotifyChannel/listNoPage', { params });
}