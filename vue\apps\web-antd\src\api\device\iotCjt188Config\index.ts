import { requestClient } from '#/api/request';

// 获取CJ/T 188配置表列表
export function List(params:any) {
  return requestClient.get<any>('device/iotCjt188Config/list', { params });
}

// 删除/批量删除CJ/T 188配置表
export function Delete(params:any) {
  return requestClient.post<any>('device/iotCjt188Config/delete', { ...params });
}

// 添加/编辑CJ/T 188配置表
export function Edit(params:any) {
  return requestClient.post<any>('device/iotCjt188Config/edit', { ...params });
}

// 获取CJ/T 188配置表指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotCjt188Config/view', { params });
}

// 导出CJ/T 188配置表
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotCjt188Config/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}