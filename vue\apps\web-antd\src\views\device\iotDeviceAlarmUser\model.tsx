import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma, renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public alarmUserId = 0; // ID
  public deviceKey = ''; // 设备标识
  public productKey = ''; // 产品标识
  public userId = ''; // 用户ID
  public level = null; // 接警级别
  public tenantId = ''; // 租户ID
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: 'ID',
    field: 'alarmUserId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
 },
  {
    title: '产品标识',
    field: 'productKey',
    align: 'left',
    width: -1,
 },
  {
    title: '用户ID',
    field: 'userId',
    align: 'left',
    width: -1,
 },
 {
  title: '备注',
  field: 'remark',
  align: 'left',
  width: -1,
},
  {
    title: '创建者',
    field: 'createdBy',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) =>  {
      return renderPopoverMemberSumma(row.createdBySumma);
    },
 },
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  alarmUserId: number;
  deviceKey: string;
  productKey: string;
  userId: string;
  level: number;
  tenantId: string;
  createdBy: number;
  createdAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'alarmUserId',  label: 'ID'},
  {  field: 'deviceKey',  label: '设备标识'},
  {  field: 'productKey',  label: '产品标识'},
  {  field: 'userId',  label: '用户ID'},
  {
				field: 'level',
				label: '接警级别',
				render(row: any) {
					return renderDict(row.level, 'alarm_user_level');
				},
			},
			  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdBy',  label: '创建者'},
  {  field: 'createdAt',  label: '创建时间'},
  {  field: 'remark',  label: '备注'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'alarmUserId',
    component: 'Input',
    label: 'ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'userId',
    component: 'Input',
    label: '用户ID',
    componentProps: {
      placeholder: '请输入用户ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {    
			fieldName: 'level',    
			component: 'Select',    
			label: '接警级别',    
			defaultValue: null,    
			componentProps: {    
				placeholder: '请选择接警级别',    
				options: getDictOptions('alarm_user_level'),    
				onUpdateValue: (e: any) => {    
					console.log(e);    
				},  
			},
			rules:'selectRequired'
		},
		  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];