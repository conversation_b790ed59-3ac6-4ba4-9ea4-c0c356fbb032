import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public detailId = 0; // 明细ID
  public taskId = 0; // 任务ID
  public productKey = ''; // 产品标识
  public deviceKey = ''; // 设备标识
  public upgradeType = 0; // 升级类型
  public optUuid = null; // 操作uuid
  public upgradeProgress = 0; // 升级进度
  public upgradeStatus = null; // 升级状态
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 更新时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'taskId',
    component: 'InputNumber',
    label: '任务ID',
    componentProps: {
      placeholder: '请输入任务ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'upgradeStatus',
    component: 'Select',
    label: '升级状态',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择升级状态',
      options: getDictOptions('device_upgrade_status'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '明细ID',
    field: 'detailId',
    align: 'center',
    width: -1,
    type: 'checkbox',
   // visible: false,
  },
    {
    title: '设备名称',
    field: 'deviceName',
    align: 'center',
    width: 150,
  },
    {
    title: '设备标识',
    field: 'deviceKey',
    align: 'center',
    width: 200,
  },
//  {
//     title: '任务ID',
//     field: 'taskId',
//     align: 'center',
//     width: 100,
   
//   },
//   {
//     title: '任务名称',
//     field: 'taskName',
//     align: 'center',
//     width: 150,
   
//   },
  {
    title: '产品标识',
    field: 'productKey',
    align: 'left',
    width: -1,
     visible: false,
  },
  {
    title: '当前版本号',
    field: 'firmwareVersion',
    align: 'center',
    width: 150,
  },
  // {
  //   title: '升级类型',
  //   field: 'upgradeType',
  //   align: 'left',
  //   width: -1,
  // },
  // {
  //   title: '操作uuid',
  //   field: 'optUuid',
  //   align: 'left',
  //   width: -1,
  // },
    {
    title: '升级状态',
    field: 'upgradeStatus',
    align: 'center',
    width: 120,
    slots: {
      default: ({ row }) => {
        const statusText = renderDict(row.upgradeStatus, 'device_upgrade_status');
        const status = String(row.upgradeStatus);

        // 根据状态值设置颜色
        let backgroundColor = '#f5f5f5'; // 默认灰色（待升级、停止）
        let borderColor = '#d9d9d9';
        let textColor = '#666';

        switch (status) {
          case '2': // 升级中
            backgroundColor = '#fff7e6';
            borderColor = '#ffa940';
            textColor = '#fa8c16';
            break;
          case '3': // 升级成功
            backgroundColor = '#f6ffed';
            borderColor = '#52c41a';
            textColor = '#52c41a';
            break;
          case '4': // 升级失败
            backgroundColor = '#fff2f0';
            borderColor = '#ff4d4f';
            textColor = '#ff4d4f';
            break;
          default: // 待升级(1)、停止(5)等
            backgroundColor = '#f5f5f5';
            borderColor = '#d9d9d9';
            textColor = '#666';
            break;
        }

        return (
          <div style={{
            display: 'inline-block',
            padding: '4px 8px',
            backgroundColor,
            border: `1px solid ${borderColor}`,
            borderRadius: '4px',
            color: textColor,
            fontSize: '12px',
            fontWeight: '500'
          }}>
            {statusText}
          </div>
        );
      }
    },
  },
  {
    title: '升级进度',
    field: 'upgradeProgress',
    align: 'center',
    width: 150,
    slots: {
      default: ({ row }) => {
        const progress = row.upgradeProgress || 0;
        return (
          <div style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '4px' }}>
            <div style={{ fontSize: '12px', color: '#666' }}>{progress}%</div>
            <div style={{
              width: '80px',
              height: '6px',
              backgroundColor: '#f0f0f0',
              borderRadius: '3px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${progress}%`,
                height: '100%',
                backgroundColor: '#52c41a',
                borderRadius: '3px',
                transition: 'width 0.3s ease'
              }}></div>
            </div>
          </div>
        );
      }
    },
  },

  {
    title: '更新时间',
    field: 'updatedAt',
    align: 'center',
    width: 200,
  },

];

// 表格列接口
export interface RowType {
  detailId: number;
  taskId: number;
  productKey: string;
  deviceKey: string;
  upgradeType: number;
  optUuid: string;
  upgradeProgress: number;
  upgradeStatus: number;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'detailId', label: '明细ID' },
  { field: 'taskId', label: '任务ID' },
  { field: 'productKey', label: '产品标识' },
  { field: 'deviceKey', label: '设备标识' },
  { field: 'upgradeType', label: '升级类型' },
  { field: 'optUuid', label: '操作uuid' },
  { field: 'upgradeProgress', label: '升级进度' },
  {
    field: 'upgradeStatus',
    label: '升级状态',
    render(row: any) {
      return renderDict(row.upgradeStatus, 'device_upgrade_status');
    },
  },
  { field: 'tenantId', label: '租户ID' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'updatedAt', label: '更新时间' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'detailId',
    component: 'Input',
    label: '明细ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'taskId',
    component: 'InputNumber',
    label: '任务ID',
    componentProps: {
      placeholder: '请输入任务ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({ required_error: '请输入任务ID', invalid_type_error: '无效数字' })
  },
  {
    fieldName: 'optUuid',
    component: 'Input',
    label: '操作uuid',
    componentProps: {
      placeholder: '请输入操作uuid',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
  {
    fieldName: 'upgradeProgress',
    component: 'InputNumber',
    label: '升级进度',
    componentProps: {
      placeholder: '请输入升级进度',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
  {
    fieldName: 'upgradeStatus',
    component: 'Select',
    label: '升级状态',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择升级状态',
      options: getDictOptions('device_upgrade_status'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];