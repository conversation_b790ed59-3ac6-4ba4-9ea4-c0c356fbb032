
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

export class State {
  public cmdPermId = 0; // 指令权限ID
  public abandonLogId = 0; // 指令权限ID
  public userName = ''; // 用户名称
  public logValue = ''; // 用户名称
  public productKey = ''; // 产品标识
  public deviceName = ''; // 产品标识
  public productName = ''; // 产品标识
  public deviceKey = ''; // 设备标识
  public functions = null; // 设备功能标识符列表，多个用,分隔
  public startTime = ''; // 开始时间
  public endTime = ''; // 结束时间
  public operInterval = 0; // 方法执行最小时间间隔（S）
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedAt = ''; // 更新时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '上报时间',
    componentProps: {
      showTime: true, // 启用时间选择
      format: 'YYYY-MM-DD HH:mm:ss', // 显示格式
      valueFormat: 'YYYY-MM-DD HH:mm:ss', // 值格式
      clearable: true,
      placeholder: ['开始时间', '结束时间'],
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
   {
    title: '上报时间',
    field: 'createdAt',
    align: 'center', // 居中对齐
    width: 200, // 设置宽度为200px
 },

  {
    title: '备注',
    field: 'remark',
    align: 'center', // 居中对齐
    width: -1,
  //  type: 'checkbox',
  },
{
  title: '日志值',
  field: 'logValue',
  align: 'center', // 居中对齐
  width: -1,

},
{
    title: '设备标识',
    field: 'deviceKey',
    align: 'center', // 居中对齐
    width: -1,
      visible: false,
 },
 {
    title: '废弃数据id',
    field: 'abandonLogId',
    align: 'center', // 居中对齐
    width: -1,
      visible: false,
 },

  {
    title: '操作',
    width: 120,
    align: 'center', // 表头居中
    slots: { default: 'action' }
  },
];

// 表格列接口
export interface RowType {
  cmdPermId: number;
  abandonLogId: number;
  userName: string;
  logValue: string; // 日志值
  productName: string;
  deviceName: string;
  productKey: string;
  deviceKey: string;
  functions: string;
  startTime: string;
  endTime: string;
  operInterval: number;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'cmdPermId',  label: '指令权限ID'},
  {
  field: 'userName',
  label: '用户名称',
 // slot: 'userNameSlot', // 自定义插槽名
},
  {  field: 'productKey',  label: '产品标识'},
  {  field: 'deviceKey',  label: '设备标识'},
  {  field: 'functions',  label: '指令'},
  {  field: 'startTime',  label: '开始时间'},
  {  field: 'endTime',  label: '结束时间'},
  {  field: 'operInterval',  label: '方法执行最小时间间隔（S）'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdDept',  label: '创建部门'},
  {  field: 'createdBy',  label: '创建者'},
  {  field: 'createdAt',  label: '创建时间'},
  {  field: 'updatedBy',  label: '更新者'},
  {  field: 'updatedAt',  label: '更新时间'},
  {  field: 'remark',  label: '备注'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'cmdPermId',
    component: 'Input',
    label: '指令权限ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
{
  fieldName: 'userId',
  component: 'Select',
  label: '用户名称',
  componentProps: {
    placeholder: '请选择用户名称',
    // options 动态设置
  },
},
  {
  fieldName: 'productKey',
  component: 'Select',
  label: '产品',
  componentProps: {
    placeholder: '请选择产品',
    options: [],
    onChange: (val: string) => {
      console.log('productKey onChange', val);
      fetchTslFunctions(val); // 直接调用你的获取指令方法
    },
  },
},
 {
  fieldName: 'deviceKey',
  component: 'Input',
  label: '设备',
  componentProps: {
    placeholder: '请选择设备',
    readonly: true, // 不允许手动输入
  },
},
{
  fieldName: 'deviceName',
  component: 'Input',
  //label: '设备名称',
  componentProps: {
    
    readonly: true,
     hidden: true,  
  },
   // 不在表单里显示，只用于存储
},
{
  fieldName: 'functions',
  component: 'Input', // 这里用 Input 隐藏，实际用插槽渲染
 
  componentProps: {
    style: { display: 'none' },
  },
},
{
  fieldName: 'tslFunctionsCheck',
  component: 'div',
  label: '指令',
  dependencies: {
    show: (values) => !!values.productKey,
    triggerFields: ['productKey']
  },
},
  {
    fieldName: 'startTime',
    component: 'DatePicker',
    label: '开始时间',
    componentProps: {
      type: 'datetime',
      clearable: true,
      shortcuts: 'FMTime',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'endTime',
    component: 'DatePicker',
    label: '结束时间',
    componentProps: {
      type: 'datetime',
      clearable: true,
      shortcuts: 'FMTime',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'operInterval',
    component: 'InputNumber',
    label: '方法执行最小时间间隔（S）',
    componentProps: {
      placeholder: '请输入方法执行最小时间间隔（S）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入方法执行最小时间间隔（S）', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];

function fetchTslFunctions(val: string) {
  throw new Error('Function not implemented.');
}
