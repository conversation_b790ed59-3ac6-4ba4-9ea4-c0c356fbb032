<script lang="ts" setup>
import { h, reactive, ref, computed, onMounted } from 'vue';
import { Button, message, Tag, Modal, Popconfirm, Switch, Image, TreeSelect } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { addFullName, getVxePopupContainer } from '@vben/utils';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, Delete, Status } from '#/api/union/iotUnion';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import editDrawer from './edit.vue';
import viewDrawer from './view.vue';
import { getSysDeptTreeApi } from '#/api/system/dept';
import { router } from '#/router';

/** 表格顶部表单配置 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
/** 表格配置 */
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'id',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
/** 表格事件配置 */
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};

/** 表格复选框选中状态 */
const CheckboxChecked = ref(false);
/** 复选框变动处理 */
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
/** 表格实例 */
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/** 表格右侧操作按钮打开的侧边栏(查看) */
const [ViewDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: viewDrawer,
});

// function handlePreview(record: RowType) {
//   drawerApi.setData({ record });
//   drawerApi.open();
// }
/** 表格右侧操作按钮打开的侧边栏(编辑/增加) */
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});
/** 增 */
function handleAdd() {
  editDrawerApi.setData({ update: false, view: false });
  editDrawerApi.open();
}
/** 改 */
function handleEdit(row: RowType) {
  editDrawerApi.setData({ id: row.unionId, update: true, view: false });
  editDrawerApi.open();
}
/** 删 */
async function handleDelete(row: RowType) {
  await Delete({ unionId: [row.unionId] });
  message.success("删除成功");
  await handleRefresh();
}
/** 刷新 */
async function handleRefresh() {
  await gridApi.query();
}
/** 批量删除 */
function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const ids: string[] = [];
  for (const row of rows) {
    ids.push(row.unionId);
  }
  if (ids.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await Delete({ unionId: ids });
      message.success("删除成功");
      await handleRefresh();
    },
  });
}
/** 导出 */
async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '组合设备表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success("导出成功");
}
/** 状态切换 */
async function handleStatusChange(row: RowType) {
  await Status({ unionId: row.unionId, status: row.status });
  await message.success("操作成功")
  await handleRefresh();
}

/** 获取机构树内容,并格式化 */
async function getDeptTree() {
  const treeRes = await getSysDeptTreeApi();
  const treeData = treeRes.items;
  addFullName(treeData, 'deptName', ' / ');
  return treeData;
}
/** 加载机构选项到树选择框 */
async function loadDeptOptions() {
  const treeData = await getDeptTree();
  /** 更新字段 */
  gridApi.formApi.updateSchema([
    {
      componentProps: {
        /** 显示内容,对应值 */
        fieldNames: { label: 'deptName', value: 'deptId' },
        showSearch: true,
        /** 结构树data */
        treeData: treeData,
        /** 默认展示全部 */
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        treeNodeLabelProp: 'fullName',
        // 选中后显示在输入框的值
        displayRender: (label: any, selected: any, node: any) => {
          return node.props.dataRef.fullName || label;
        },
        // 模糊搜索
        filterTreeNode: (input: string, treeNode: any) => {
          const label = treeNode.deptName || treeNode.fullName || '';
          return label.toLowerCase().includes(input.toLowerCase());
        },
      },
      /** 要修改的字段 */
      fieldName: 'deptId',
    },
  ]);
}

onMounted(() => {
  loadDeptOptions()
})

function handleDetail(unionId: number) {
  /** 获取表格全部数据 */
  const unionData = gridApi.grid.getData();
  /** 筛选出同id数据 */
  const currentUnion = unionData.find((item: any) => item.unionId === unionId);
  /** 拼接url */
  const url = `/iotUnion/detail?unionId=${currentUnion.unionId}`;
  // const url = `/iotUnion/unionDetail?unionId=${currentUnion.unionId}`;
  router.push(url);
}
</script>
<template>
  <Page auto-content-height>
    <Grid table-title="组合设备表">
      <!-- 顶部工具栏 -->
      <template #toolbar-tools>
        <Button class="mr-2 flex items-center " type="primary" :icon="h(MdiPlus)" @click="handleAdd"
          v-access:code="'cpm:union:iotUnion:edit'">
          新增
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :disabled="!CheckboxChecked" :icon="h(MdiDelete)"
          @click="handleMultiDelete" v-access:code="'cpm:union:iotUnion:delete'">
          删除
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiExport)" @click="handleExport"
          v-access:code="'cpm:union:iotUnion:export'">
          导出
        </Button>
      </template>
      <!-- 图片加载 -->
      <template #imgUrl="{ row }">
        <Image :src="row.imgUrl" v-if="row.imgUrl" :height="30" :width="30" />
      </template>
      <!-- 状态切换 -->
      <template #status="{ row }">
        <Switch v-model:checked="row.status" :checkedValue="'0'" :unCheckedValue="'1'" @change="handleStatusChange(row)"
          :disabled="!hasAccessByCodes(['cpm:union:iotUnion:status'])" />
      </template>
      <!-- 右侧功能 -->
      <template #action="{ row }">
        <div class="flex items-center">
          <Button class="mr-2 border-none p-0" :block="false" type="link"
           @click="handleDetail(row.unionId)" v-access:code="'cpm:union:iotUnion:view'">
            详情
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row)"
            v-access:code="'cpm:union:iotUnion:edit'">
            修改
          </Button>
          <AccessControl :codes="['cpm:union:iotUnion:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
    <EditDrawer @reload="handleRefresh" />
    <ViewDrawer />
  </Page>
</template>
