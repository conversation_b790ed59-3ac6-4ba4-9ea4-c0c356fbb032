import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

export class State {
  public itemId = 0; // 子设备ID
  public unionKey = ''; // 组合设备标识
  public deviceKey = ''; // 设备标识
  public fieldJson = ''; // 关联子设备变量JSON
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'itemId',
    component: 'InputNumber',
    label: '子设备ID',
    componentProps: {
      placeholder: '请输入子设备ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '子设备ID',
    field: 'itemId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '组合设备标识',
    field: 'unionKey',
    align: 'left',
    width: -1,
 },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
 },
  {
    title: '关联子设备变量JSON',
    field: 'fieldJson',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  itemId: number;
  unionKey: string;
  deviceKey: string;
  fieldJson: string;
  tenantId: string;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'itemId',  label: '子设备ID'},
  {  field: 'unionKey',  label: '组合设备标识'},
  {  field: 'deviceKey',  label: '设备标识'},
  {  field: 'fieldJson',  label: '关联子设备变量JSON'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdAt',  label: '创建时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'itemId',
    component: 'Input',
    label: '子设备ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'unionKey',
    component: 'Input',
    label: '组合设备标识',
    componentProps: {
      placeholder: '请输入组合设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'fieldJson',
    component: 'Input',
    label: '关联子设备变量JSON',
    componentProps: {
      placeholder: '请输入关联子设备变量JSON',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'tenantId',
    component: 'Input',
    label: '租户ID',
    componentProps: {
      placeholder: '请输入租户ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
];