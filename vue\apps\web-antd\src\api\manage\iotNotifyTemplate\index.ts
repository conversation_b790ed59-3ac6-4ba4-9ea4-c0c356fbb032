import { requestClient } from '#/api/request';

// 获取通知模板表列表
export function List(params:any) {
  return requestClient.get<any>('manage/iotNotifyTemplate/list', { params });
}

// 删除/批量删除通知模板表
export function Delete(params:any) {
  return requestClient.post<any>('manage/iotNotifyTemplate/delete', { ...params });
}

// 添加/编辑通知模板表
export function Edit(params:any) {
  return requestClient.post<any>('manage/iotNotifyTemplate/edit', { ...params });
}

// 修改通知模板表状态
export function Status(params:any) {
  return requestClient.post<any>('manage/iotNotifyTemplate/status', { ...params });
}

// 获取通知模板表指定详情
export function View(params:any) {
  return requestClient.get<any>('manage/iotNotifyTemplate/view', { params });
}

// 导出通知模板表
export function Export(params:any) {
  return requestClient.post<Blob>('/manage/iotNotifyTemplate/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}