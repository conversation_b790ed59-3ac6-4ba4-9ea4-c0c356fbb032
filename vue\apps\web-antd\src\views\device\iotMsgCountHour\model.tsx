import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';

export class State {
	public id = 0; // ID
	public deviceKey = null; // 设备标识
	public msgTime = ''; // 消息时间("yyyy-MM-dd HH:00:00")
	public num = 0; // 消息数量
	public tenantId = ''; // 租户ID
	public createdAt = ''; // 创建时间

	constructor(state?: Partial<State>) {
		if (state) {
			Object.assign(this, state);
		}
	}
}

export function newState(state: State | Record<string, any> | null): State {
	if (state !== null) {
		if (state instanceof State) {
			return cloneDeep(state);
		}
		return new State(state);
	}
	return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
	{
		fieldName: 'deviceKey',
		component: 'Input',
		label: '设备标识',
		componentProps: {
			placeholder: '请输入设备标识',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},{
		fieldName: 'tenantId',
		component: 'Input',
		label: '租户ID',
		componentProps: {
			placeholder: '请输入租户ID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: null,
		formItemClass: 'col-span-1',
	}, {
		label: '消息时间',
		fieldName: 'msgTime',
		component: 'RangePicker',
		componentProps: {
			clearable: true,
			showTime: '{ format: \'HH\' }',
			format: 'YYYY-MM-DD HH:00:00',
			valueFormat: 'YYYY-MM-DD HH:mm:ss',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, ];

// 表格列
export const columns: VxeGridProps['columns'] = [
	{
		title: 'ID',
		field: 'id',
		align: 'left',
		width: -1,
		type: 'checkbox',
	},
	{
		title: '设备标识',
		field: 'deviceKey',
		align: 'left',
		width: -1,
	},
	{
		title: '消息时间',
		field: 'msgTime',
		align: 'left',
		width: -1,
	},
	{
		title: '消息数量',
		field: 'num',
		align: 'left',
		width: -1,
	},
	{
		title: '租户ID',
		field: 'tenantId',
		align: 'left',
		width: -1,
	},
	{
		title: '创建时间',
		field: 'createdAt',
		align: 'left',
		width: -1,
	},
	{ title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
	id: number;
	deviceKey: string;
	msgTime: string;
	num: number;
	tenantId: string;
	createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
	{ field: 'id', label: 'ID' },
	{ field: 'deviceKey', label: '设备标识' },
	{ field: 'msgTime', label: '消息时间("yyyy-MM-dd HH:00:00")' },
	{ field: 'num', label: '消息数量' },
	{ field: 'tenantId', label: '租户ID' },
	{ field: 'createdAt', label: '创建时间' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
	{
		fieldName: 'id',
		component: 'Input',
		label: 'ID',
		dependencies: { show: () => false, triggerFields: [''], },
		componentProps: {
			placeholder: '',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'deviceKey',
		component: 'Input',
		label: '设备标识',
		componentProps: {
			placeholder: '请输入设备标识',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'msgTime',
		component: 'DatePicker',
		label: '消息时间',
		componentProps: {
			showTime: '{ format: \'HH\' }',
			format: 'YYYY-MM-DD HH:00:00',
			clearable: true,
			inputReadOnly: true,
			valueFormat: 'YYYY-MM-DD HH:00:00',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: 'selectRequired',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'num',
		component: 'InputNumber',
		label: '消息数量',
		componentProps: {
			placeholder: '请输入消息数量',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: z.number({ required_error: '请输入消息数量', invalid_type_error: '无效数字' }),
		formItemClass: 'col-span-1',
	},];