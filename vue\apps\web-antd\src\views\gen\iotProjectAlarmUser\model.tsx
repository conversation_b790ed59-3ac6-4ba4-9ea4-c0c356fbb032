import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';

export class State {
  public id = 0; // ID
  public pointId = 0; // 安装点ID
  public projectUserId = 0; // 项目员工id
  public level = 0; // 接警级别（1-一级、2-二级、3-三级、4-四级）
  public tenantId = ''; // 租户ID
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'id',
    component: 'InputNumber',
    label: 'ID',
    componentProps: {
      placeholder: '请输入ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: 'ID',
    field: 'id',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '安装点ID',
    field: 'pointId',
    align: 'left',
    width: -1,
 },
  {
    title: '项目员工id',
    field: 'projectUserId',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建者',
    field: 'createdBy',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) =>  {
      return renderPopoverMemberSumma(row.createdBySumma);
    },
 },
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  id: number;
  pointId: number;
  projectUserId: number;
  level: number;
  tenantId: string;
  createdBy: number;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'id',  label: 'ID'},
  {  field: 'pointId',  label: '安装点ID'},
  {  field: 'projectUserId',  label: '项目员工id'},
  {  field: 'level',  label: '接警级别（1-一级、2-二级、3-三级、4-四级）'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdBy',  label: '创建者'},
  {  field: 'createdAt',  label: '创建时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'id',
    component: 'Input',
    label: 'ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'pointId',
    component: 'InputNumber',
    label: '安装点ID',
    componentProps: {
      placeholder: '请输入安装点ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入安装点ID', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'projectUserId',
    component: 'InputNumber',
    label: '项目员工id',
    componentProps: {
      placeholder: '请输入项目员工id',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入项目员工id', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'tenantId',
    component: 'Input',
    label: '租户ID',
    componentProps: {
      placeholder: '请输入租户ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
];