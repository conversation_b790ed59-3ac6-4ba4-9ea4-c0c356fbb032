import { requestClient } from '#/api/request';

// 获取产品分类表列表
export function List(params:any) {
  return requestClient.get<any>('device/iotProductCategory/list', { params });
}

// 删除/批量删除产品分类表
export function Delete(params:any) {
  return requestClient.post<any>('device/iotProductCategory/delete', { ...params });
}

// 添加/编辑产品分类表
export function Edit(params:any) {
  return requestClient.post<any>('device/iotProductCategory/edit', { ...params });
}

// 获取产品分类表指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotProductCategory/view', { params });
}

// 导出产品分类表
export function Export(params:any) {
  return requestClient.post<Blob>('device/iotProductCategory/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}