import { requestClient } from '#/api/request';

// 获取指令权限表列表
export function List(params:any) {
  return requestClient.get<any>('manage/iotCmdPerm/list', { params });
}

// 删除/批量删除指令权限表
export function Delete(params:any) {
  return requestClient.post<any>('manage/iotCmdPerm/delete', { ...params });
}

// 添加/编辑指令权限表
export function Edit(params:any) {
  return requestClient.post<any>('manage/iotCmdPerm/edit', { ...params });
}

// 获取指令权限表指定详情
export function View(params:any) {
  return requestClient.get<any>('manage/iotCmdPerm/view', { params });
}

// 导出指令权限表
export function Export(params:any) {
  return requestClient.post<Blob>('/manage/iotCmdPerm/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}