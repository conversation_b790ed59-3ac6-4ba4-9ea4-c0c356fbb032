<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';
import { Card, Button, message, Popconfirm } from 'ant-design-vue';
import { AccessControl } from '@vben/access';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ListNoPage as ListProduct } from '#/api/device/iotProduct/index';
import { ListWithDeviceCount } from '#/api/project/iotConsumerGroup/index';
import { columns, querySchema, type RowType } from './model';
import { List, Delete } from '#/api/project/iotDeviceConsumer';
import shareInfoModal from './shareInfo.vue'
import { DictEnum } from '@vben/constants';
import { getDictLabel, getDictOptions } from '#/utils/dict';
import { router } from '#/router';

// 定义接收的属性类型
const props = defineProps({
  consumer: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

type Option = {
  label: string;
  value: string;
  deviceType?: number | string; // 添加deviceType字段
};

const productOptions = ref<Option[]>([]);
async function loadProductOptions() {
  const res = await ListProduct({
    page: 1,
    pageSize: 1000,
  });
  if (!res || !res.items) {
    productOptions.value = [];
  } else {
    productOptions.value = res.items.map((item: any) => ({
      label: item.productName,
      value: item.productKey,
      deviceType: item.deviceType, // 保存deviceType信息
    }));
  }
  gridApi.formApi.updateSchema([
    {
      fieldName: 'productKey',
      component: 'Select',
      label: '所属产品',
      componentProps: {
        placeholder: '请选择所属产品',
        onUpdateValue: (e: any) => {
          console.log(e);
        },
        options: productOptions.value,
        showSearch: true,

        filterOption: (input: any, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
      },
    },
  ]);
}

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'consumerId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const response = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          consumerId: props.consumer.consumerId,
          groupId: currentGroupId.value || 0,
          ...formValues,
        });
        return response;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridClass: 'p-0 mt-0',
  formOptions: formOptions,
  gridOptions: gridOptions,
  hideSeparator: true,
});

type Group = {
  groupName: string;
  groupId?: number | string;
  groupOrder?: number | string; // 添加deviceType字段
  deviceCount: number;
};

const groups = ref<Group[]>([]);

async function loadGroups() {
  const res = await ListWithDeviceCount({
    consumerId: props.consumer.consumerId,
  });
  if (res && res.items) {
    // 转换接口数据为需要的格式
    groups.value = res.items
      .map((item: any) => ({
        groupName: item.groupName,
        groupId: item.groupId,
        groupOrder: item.groupOrder,
        deviceCount: item.deviceCount,
      }))
      .sort((a: { groupOrder: number; }, b: { groupOrder: number; }) => b.groupOrder - a.groupOrder); // 按 groupOrder 降序排列
  } else {
    groups.value = [];
  }
}

const totalDeviceCount = computed(() => {
  return groups.value.reduce((sum, group) => sum + (group.deviceCount || 0), 0);
});

const currentGroupId = ref<number | string | null>(null);
function handleGroupClick(group: any) {
  console.log('点击分组:', group);
  currentGroupId.value = group.groupId; // 保存选中的分组ID
  handleRefresh(); // 刷新表格
}
function handleGroupClick0() {
  currentGroupId.value = 0; // 保存选中的分组ID
  handleRefresh(); // 刷新表格
}

const [ShareInfoModal, shareInfoModalApi] = useVbenModal({
  connectedComponent: shareInfoModal,
  showConfirmButton: false
});
function shareInfo(row: RowType) {
  shareInfoModalApi.setData({ deviceKey: row.deviceKey, deviceName: row.device.deviceName, consumerId: props.consumer.consumerId });
  shareInfoModalApi.open();
}

async function handleRefresh() {
  await gridApi.query();
}

const deviceStateOptions = getDictOptions(DictEnum.DEVICE_STATE); // 设备状态字典

function getDeviceStateLabel(value: string | number) {
  const found = deviceStateOptions.find(opt => String(opt.value) === String(value));
  return found ? found.label : value;
}

const handleDetail = (deviceId: number, tabKey?: string) => {
  // 通过deviceId在表格数据中查找对应的设备信息
  const deviceData = gridApi.grid.getData();
  const currentDevice = deviceData.find((item: any) => item.deviceId === deviceId);

  if (currentDevice) {
    // 通过productKey查找对应的产品信息
    const productInfo = productOptions.value.find((p: any) => p.value === currentDevice.productKey);

    // 判断是否为监控设备（deviceType = 4）
    const isMonitorDevice = productInfo && (
      productInfo.deviceType === 4 ||
      productInfo.deviceType === '4'
    );

    console.log('设备详情跳转判断:', {
      deviceId,
      productKey: currentDevice.productKey,
      productInfo,
      isMonitorDevice
    });

    let url = '';
    if (isMonitorDevice) {
      // 监控设备跳转到监控详情页，传递deviceKey参数
      url = `/device/monitorDetail?deviceKey=${currentDevice.deviceKey}`;
    } else {
      // 普通设备跳转到普通详情页
      url = `/device/deviceDetail?deviceId=${deviceId}`;
    }

    if (tabKey) {
      url += `&tab=${tabKey}`;
    }

    router.push(url);
  } else {
    console.error('未找到设备信息，使用默认详情页');
    let url = `/device/deviceDetail?deviceId=${deviceId}`;
    if (tabKey) {
      url += `&tab=${tabKey}`;
    }
    router.push(url);
  }
};
onMounted(() => {
  loadProductOptions();
  loadGroups();
});

/** 解除绑定 */
async function unbind(row: any) {
  console.log(row)
  console.log(row.deviceConsumerId)
  const res = await Delete({ deviceConsumerId: row.deviceConsumerId })
  message.success("解绑成功")
  loadProductOptions();
  loadGroups();
  gridApi.query()
}
</script>

<template class="p-0 m-0">
  <div class="p-0 m-0 h-[800px]">
    <div class="flex h-full gap-[8px] ">
      <Card title="分组列表" class="w-[240px]">
        <Card type="inner" class="mb-4" @click="handleGroupClick0()">
          <div class="flex items-center gap-2">
            <span>全部</span>
            <span>({{ totalDeviceCount }})</span>
          </div>
        </Card>
        <Card v-if="groups.length > 0" v-for="(group, index) in groups" :key="index" class="mb-4" type="inner"
          @click="handleGroupClick(group)">
          <div class="flex items-center gap-2">
            <span>{{ group.groupName }}</span>
            <span>({{ group.deviceCount }})</span>
          </div>
        </Card>
      </Card>
      <Grid class="flex-1">
        <template #action="{ row }">
          <div class="flex items-center">
            <Button @click="unbind(row)" class="mr-2 border-none p-0" :block="false" type="link" danger>
              解除绑定
            </Button>
            <Button class="mr-2 border-none p-0" :block="false" type="link"
              v-access:code="'cpm:project:iotConsumer:view'" @click="handleDetail(row.device.deviceId)">
              设备详情
            </Button>
            <Button v-if="row.shareStatus === 1" class="mr-2 border-none p-0" :block="false" type="link"
              v-access:code="'cpm:project:iotConsumer:edit'" @click="shareInfo(row)">
              分享列表
            </Button>
          </div>
        </template>
        <template #deviceState="{ row }">
          <Tag :style="{
            borderRadius: '4px',
            minWidth: '48px',
            textAlign: 'center',
            color: row.device.deviceState === 2 ? '#52c41a' : row.device.deviceState === 1 ? '#faad14' : '#909399',
            background: row.device.deviceState === 2 ? '#f6ffed' : row.device.deviceState === 1 ? '#fffbe6' : '#f4f6fa',
            border: row.device.deviceState === 2
              ? '1px solid #b7eb8f'
              : row.device.deviceState === 1
                ? '1px solid #ffe58f'
                : '1px solid #d9d9d9'
          }">
            {{ getDeviceStateLabel(row.device.deviceState) }}
          </Tag>
        </template>
      </Grid>
    </div>
    <ShareInfoModal />
  </div>
</template>
