import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { getDictOptions } from '#/utils/dict';

export class State {
	public logId = 0; // 自增ID
	public consumerId = 0; // 操作用户
	public deviceKey = ''; // 设备标识
	public optType = 1; // 类型
	public optTitle = ''; // 消息标题
	public reqBody = ''; // 请求数据
	public resBody = ''; // 响应数据
	public isAsync = null; // 是否异步
	public resCode = 0; // 响应码
	public resMessage = 0; // 响应描述
	public tenantId = ''; // 租户ID
	public createdAt = ''; // 创建时间

	constructor(state?: Partial<State>) {
		if (state) {
			Object.assign(this, state);
		}
	}
}

export function newState(state: State | Record<string, any> | null): State {
	if (state !== null) {
		if (state instanceof State) {
			return cloneDeep(state);
		}
		return new State(state);
	}
	return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
	{
		fieldName: 'optType',
		component: 'Select',
		label: '操作类型',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择操作类型',
			options: getDictOptions('consumer_opt_type'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'deviceKey',
		component: 'Input',
		label: '设备标识',
		componentProps: {
			placeholder: '请输入设备标识',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'createdAt',
		component: 'RangePicker',
		label: '创建时间',
		componentProps: {
			type: 'datetimerange',
			clearable: true,
			valueFormat: 'FMDateRange',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},];

// 表格列
export const columns: VxeGridProps['columns'] = [
	{
		title: '',
		align: 'center',
		width: 100,
		type: 'checkbox',
	},
	{
		title: '编号',
		field: 'logId',
		align: 'center',
		width: 100,
	},
	{
		title: '时间',
		field: 'createdAt',
		align: 'center',
		width: 200,
	},
	{
		title: '操作类型', field: 'optType', align: 'center', width: -1,
		slots: {
			default: ({ row }) => {
				return renderDict(row.optType, 'consumer_opt_type');
			}
		},
	},
	{
		title: '消息标题',
		field: 'optTitle',
		align: 'center',
		width: -1,
	},
	{
		title: '是否异步', field: 'isAsync', align: 'center', width: -1,
		slots: {
			default: ({ row }) => {
				return renderDict(row.isAsync, 'sys_yes_no_num');
			}
		},
	},
	{
		title: '响应码',
		field: 'resCode',
		align: 'center',
		width: -1,
	},
	{
		title: '响应描述',
		field: 'resMessage',
		align: 'center',
		width: -1,
	},
	{ title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
	logId: number;
	consumerId: number;
	deviceKey: string;
	optType: number;
	optTitle: string;
	reqBody: string;
	resBody: string;
	isAsync: number;
	resCode: number;
	resMessage: number;
	tenantId: string;
	createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
	{ field: 'logId', label: '编号' },
	{ field: 'createdAt', label: '时间' },
	{ field: 'consumerId', label: '操作用户ID' },
	{
		field: 'userName',
		label: '操作用户名',
		render(_, data) {
			return data.consumer?.userName || '-';
		},
	},
	{ field: 'deviceKey', label: '设备标识' },
	{
		field: 'deviceName',
		label: '设备名称',
		render(_, data) {
			return data.device.deviceName || '-';
		},
	},
	{
		field: 'deviceModel',
		label: '设备型号',
		render(_, data) {
			return data.device.deviceModel || '-';
		},
	},
	{
		field: 'optType',
		label: '操作类型',
		render(row: any) {
			return renderDict(row.optType, 'consumer_opt_type');
		},
	},
	{ field: 'optTitle', label: '消息标题' },
	{
		field: 'isAsync',
		label: '是否异步',
		render(_, data) {
			return renderDict(data.isAsync, 'sys_yes_no_num');
		},
	},
	{ field: 'reqBody', label: '请求数据' },
	{ field: 'resBody', label: '响应数据' },
	{ field: 'resCode', label: '响应码' },
	{ field: 'resMessage', label: '响应描述' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
	{
		fieldName: 'logId',
		component: 'Input',
		label: '自增ID',
		dependencies: { show: () => false, triggerFields: [''], },
		componentProps: {
			placeholder: '',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'consumerId',
		component: 'InputNumber',
		label: '操作用户',
		componentProps: {
			placeholder: '请输入操作用户',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: z.number({ required_error: '请输入操作用户', invalid_type_error: '无效数字' }),
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'deviceKey',
		component: 'Input',
		label: '设备标识',
		componentProps: {
			placeholder: '请输入设备标识',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'optType',
		component: 'Select',
		label: '类型',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择类型',
			options: getDictOptions('consumer_opt_type'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'selectRequired',
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'optTitle',
		component: 'Input',
		label: '消息标题',
		componentProps: {
			placeholder: '请输入消息标题',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'reqBody',
		component: 'Input',
		label: '请求数据',
		componentProps: {
			placeholder: '请输入请求数据',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'resBody',
		component: 'Input',
		label: '响应数据',
		componentProps: {
			placeholder: '请输入响应数据',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'isAsync',
		component: 'RadioGroup',
		label: '是否异步',
		componentProps: {
			options: getDictOptions('sys_yes_no_num')
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'resCode',
		component: 'InputNumber',
		label: '响应码',
		componentProps: {
			placeholder: '请输入响应码',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: z.number({ required_error: '请输入响应码', invalid_type_error: '无效数字' }),
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'resMessage',
		component: 'InputNumber',
		label: '响应描述',
		componentProps: {
			placeholder: '请输入响应描述',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: z.number({ required_error: '请输入响应描述', invalid_type_error: '无效数字' }),
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'tenantId',
		component: 'Input',
		label: '租户ID',
		componentProps: {
			placeholder: '请输入租户ID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	},];