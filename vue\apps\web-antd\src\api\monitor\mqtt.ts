/** mqtt页面相关接口 */

import { requestClient } from '#/api/request';

/** 获取Mqtt统计所有节点的流入流出连接和主题 */
export const getAllInOutConnectTopic = () => {
  return requestClient.get('/monitor/iotMqtt/allInOutConnectTopic');
};

/** 统计MQTT指定通道下的当前运行状态信息 */
export const postChannelCurrentInfo = (params: any) => {
  return requestClient.post('/monitor/iotMqtt/channelCurrentInfo', { ...params });
};

/** 统计MQTT指定通道下的历史统计信息 */
export const postChannelHistory = (params: any) => {
  return requestClient.post('/monitor/iotMqtt/channelHistory', { ...params });
}
