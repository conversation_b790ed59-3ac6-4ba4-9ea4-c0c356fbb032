import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { getDictOptions } from '#/utils/dict';
import { getPopupContainer } from '@vben/utils';

export class State {
	public msgId = 0; // 自增ID
	public consumerId = 0; // 接受者
	public fromConsumerId = 0; // 发送者
	public category = 1; // 主类型
	public msgType = 1; // 类型
	public msgTitle = ''; // 消息标题
	public msgBody = ''; // 消息内容
	public msgAbstract = ''; // 消息摘要
	public deviceKey = ''; // 设备标识
	public relateConsumerId = 0; // 关联用户
	public relateId = 0; // 关联ID
	public status = 0; // 状态
	public tenantId = ''; // 租户ID
	public createdAt = ''; // 创建时间
	public updatedAt = ''; // 更新时间
	public deletedAt = ''; // 删除时间

	constructor(state?: Partial<State>) {
		if (state) {
			Object.assign(this, state);
		}
	}
}

export function newState(state: State | Record<string, any> | null): State {
	if (state !== null) {
		if (state instanceof State) {
			return cloneDeep(state);
		}
		return new State(state);
	}
	return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
	{
		fieldName: 'msgType',
		component: 'Select',
		label: '通知类型',
		defaultValue: null,
		componentProps: {
			placeholder: '请选择类型',
			options: getDictOptions('consumer_msg_type'),
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'msgTitle',
		component: 'Input',
		label: '消息标题',
		componentProps: {
			placeholder: '请输入消息标题',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	},];

// 表格列
export const columns: VxeGridProps['columns'] = [
	{
		title: '编号',
		field: 'msgId',
		align: 'center',
		width: 70,
	},
	{
		title: '创建时间',
		field: 'createdAt',
		align: 'center',
		width: 150,
	},
	{
		title: '消息标题',
		field: 'msgTitle',
		align: 'center',
		width: -1,
	},
	{
		title: '消息摘要',
		field: 'msgAbstract',
		align: 'center',
		width: -1,
	},
	// {
	// 	title: '消息内容',
	// 	field: 'msgBody',
	// 	align: 'center',
	// 	width: -1,
	// },
	{
		title: '状态', field: 'status', align: 'center', width: 120,
		slots: {
			default: ({ row }) => {
				return renderDict(row.status, 'consumer_msg_status');
			}
		},
	},
	{ title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
	msgId: number;
	consumerId: number;
	fromConsumerId: number;
	category: number;
	msgType: number | string;
	msgTitle: string;
	msgBody: string;
	msgAbstract: string;
	deviceKey: string;
	relateConsumerId: number;
	relateId: number;
	status: number;
	tenantId: string;
	createdAt: string;
	updatedAt: string;
	deletedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [

	{ field: 'createdAt', label: '创建时间' },
	{ field: 'deviceKey', label: '设备标识' },
	{ field: 'projectKey', label: '所属产品' },
	{
		field: 'msgType',
		label: '消息类型',
		render(row: any) {
			return renderDict(row.msgType, 'consumer_msg_type');
		},
	},
	// {
	// 	field: 'category',
	// 	label: '主类型',
	// 	render(row: any) {
	// 		return renderDict(row.category, 'consumer_msg_type_main');
	// 	},
	// },
	{ field: 'msgTitle', label: '消息标题' },
	{ field: 'msgAbstract', label: '消息摘要' },
	{ field: 'msgBody', label: '消息内容' },
	{ field: 'consumerId', label: '接受者' },
	{ field: 'consumerId', label: '接受者手机' },
	{ field: 'fromConsumerId', label: '发送者' },
	{ field: 'fromConsumerId', label: '发送者手机' },
	{ field: 'projectName', label: '所属项目' },
	{ field: 'deptName', label: '所属机构' },
];


// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
	{
		fieldName: 'createdAt',
		component: 'Input',
		label: '时间',
		componentProps: {
			placeholder: '时间',
			readonly: true, 
			style: { width: '100%' },
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'deviceKey',
		component: 'Input',
		label: '设备标识',
		componentProps: {
			placeholder: '设备标识',
			readonly: true,
			style: { width: '100%' },
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'device.productKey',
		component: 'Input',
		label: '产品标识',
		componentProps: {
			placeholder: '产品标识',
			readonly: true,
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'device.deviceName',
		component: 'Input',
		label: '设备名称',
		componentProps: {
			placeholder: '设备名称',
			readonly: true,
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'device.deviceModel',
		component: 'Input',
		label: '设备型号',
		componentProps: {
			placeholder: '设备型号',
			readonly: true,
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'msgType',
		component: 'Input',
		label: '消息类型',
		defaultValue: null,
		componentProps: {
			readonly: true,
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'msgTitle',
		component: 'Input',
		label: '消息标题',
		componentProps: {
			placeholder: '消息标题',
			readonly: true,
		},
		formItemClass: 'col-span-2', // 单独占一行（100%宽度）
	},
	{
		fieldName: 'msgAbstract',
		component: 'Input',
		label: '消息摘要',
		componentProps: {
			placeholder: '消息摘要',
			readonly: true,
		},
		formItemClass: 'col-span-2', // 单独占一行
	},
	{
		fieldName: 'consumer.userName',
		component: 'Input',
		label: '接受者',
		componentProps: {
			placeholder: '接受者',
			readonly: true,
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'consumer.phone',
		component: 'Input',
		label: '接受者手机',
		componentProps: {
			placeholder: '接受者手机',
			readonly: true,
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'fromConsumer.userName',
		component: 'Input',
		label: '发送者',
		componentProps: {
			placeholder: '发送者',
			readonly: true,
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'fromConsumer.phone',
		component: 'Input',
		label: '发送者手机',
		componentProps: {
			placeholder: '发送者手机',
			readonly: true,
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'consumer.projectName',
		component: 'Input',
		label: '所属项目',
		componentProps: {
			placeholder: '所属项目',
			readonly: true,
		},
		formItemClass: 'col-span-1',
	},
	{
		fieldName: 'deptName',
		component: 'Input',
		label: '所属机构',
		componentProps: {
			placeholder: '所属机构',
			readonly: true,
		},
		formItemClass: 'col-span-1',
	},
];