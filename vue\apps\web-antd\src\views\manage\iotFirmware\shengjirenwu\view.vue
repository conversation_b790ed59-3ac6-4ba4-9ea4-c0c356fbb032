<script setup lang="ts">
import { ref, computed } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { Card } from 'ant-design-vue';
import { View } from '#/api/manage/iotUpgradeTask';
import { getDictOptions } from '#/utils/dict';
import dayjs from 'dayjs';
import Table1 from './table1/index.vue';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import type { EchartsUIType } from '@vben/plugins/echarts';
import { watch } from 'vue';

// 明细表组件引用
const refDetailTable = ref();

// 图表组件引用
const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 升级任务详情数据
const taskDetail = ref({
  taskId: '',
  taskName: '',
  taskType: '',
  deviceCount: 0,
  taskTime: '',
  remark: '',
  createdAt: '',
  // 其他可能需要的字段
  firmwareId: '',
  firmwareName: '',
  firmwareVersion: '',
  productKey: '',
  productName: '',
  upgradeType: '',
  taskStatus: '',
});

// 升级统计数据
const upgradeStats = ref({
  waitingDevice: 0,    // 待升级
  progressDevice: 0,   // 升级中
  successDevice: 0,    // 升级成功
  failedDevice: 0,     // 升级失败
});

// 刷新加载状态
const refreshLoading = ref(false);

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-';
  try {
    return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
  } catch (error) {
    console.error('日期格式化失败:', error);
    return date || '-';
  }
};

// 获取任务类型字典值
const getTaskTypeLabel = (taskType: any) => {
  if (!taskType) return '-';
  const dictOptions = getDictOptions('upgrade_task_type');
  const matchedOption = dictOptions.find(opt => opt.value == taskType);
  return matchedOption ? matchedOption.label : taskType;
};

// 计算图表数据
const chartData = computed(() => {
  const total = upgradeStats.value.waitingDevice +
                upgradeStats.value.progressDevice +
                upgradeStats.value.successDevice +
                upgradeStats.value.failedDevice;

  if (total === 0) {
    return [];
  }

  return [
    {
      name: '待升级',
      value: upgradeStats.value.waitingDevice,
      percentage: ((upgradeStats.value.waitingDevice / total) * 100).toFixed(1),
      color: '#d9d9d9'
    },
    {
      name: '升级中',
      value: upgradeStats.value.progressDevice,
      percentage: ((upgradeStats.value.progressDevice / total) * 100).toFixed(1),
      color: '#fa8c16'
    },
    {
      name: '升级成功',
      value: upgradeStats.value.successDevice,
      percentage: ((upgradeStats.value.successDevice / total) * 100).toFixed(1),
      color: '#52c41a'
    },
    {
      name: '升级失败',
      value: upgradeStats.value.failedDevice,
      percentage: ((upgradeStats.value.failedDevice / total) * 100).toFixed(1),
      color: '#ff4d4f'
    }
  ].filter(item => item.value > 0); // 只显示有数据的项
});

// 计算总设备数
const totalDevices = computed(() => {
  return upgradeStats.value.waitingDevice +
         upgradeStats.value.progressDevice +
         upgradeStats.value.successDevice +
         upgradeStats.value.failedDevice;
});

// 渲染环形图
const renderChart = () => {
  console.log('🎨 开始渲染环形图...');
  console.log('📊 chartRef.value:', chartRef.value);
  console.log('📊 chartData.value:', chartData.value);
  console.log('📊 totalDevices:', totalDevices.value);

  if (!chartRef.value) {
    console.warn('⚠️ chartRef.value 不存在');
    return;
  }

  if (chartData.value.length === 0) {
    console.warn('⚠️ chartData 为空');
    return;
  }

  try {
    renderEcharts({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}台 ({d}%)'
      },
      legend: {
        show: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5px',
        textStyle: {
          fontSize: 11,
          color: '#666'
        },
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 15
      },
      series: [
        {
          name: '升级统计',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '40%'],
          avoidLabelOverlap: false,
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: false
            }
          },
          labelLine: {
            show: false
          },
          color: ['#d9d9d9', '#fa8c16', '#52c41a', '#ff4d4f'],
          data: chartData.value.map(item => ({
            value: item.value,
            name: item.name
          }))
        }
      ]
    });
    console.log('✅ 环形图渲染成功');
  } catch (error) {
    console.error('❌ 环形图渲染失败:', error);
  }
};

// 弹窗打开时的处理
async function onOpenChange(isOpen: boolean) {
  if (!isOpen) {
    return;
  }

  const { record } = modalApi.getData() || {};
  if (record && record.taskId) {
    console.log('🚀 升级任务查看弹窗已打开，任务ID:', record.taskId);
    try {
      const result = await View({ taskId: record.taskId });
      console.log('📄 获取到的任务详情:', result);

      taskDetail.value = {
        taskId: result.taskId || '',
        taskName: result.taskName || '',
        taskType: result.taskType || '',
        deviceCount: result.deviceCount || 0,
        taskTime: result.taskTime || '',
        remark: result.remark || '',
        createdAt: result.createdAt || '',
        // 其他字段
        firmwareId: result.firmwareId || '',
        firmwareName: result.firmwareName || '',
        firmwareVersion: result.firmwareVersion || '',
        productKey: result.productKey || '',
        productName: result.productName || '',
        upgradeType: result.upgradeType || '',
        taskStatus: result.taskStatus || '',
      };

      // 获取升级统计数据
      upgradeStats.value = {
        waitingDevice: result.waitingDevice || 0,
        progressDevice: result.progressDevice || 0,
        successDevice: result.successDevice || 0,
        failedDevice: result.failedDevice || 0,
      };

      console.log('📊 升级统计数据:', upgradeStats.value);

      // 渲染图表 - 增加延迟确保DOM完全渲染
      setTimeout(() => {
        renderChart();
      }, 500);

      // 任务详情加载完成后，刷新明细表
      if (refDetailTable.value && taskDetail.value.taskId) {
        console.log('🔄 刷新升级任务明细表');
        setTimeout(() => {
          try {
            refDetailTable.value?.refreshTable();
          } catch (error) {
            console.error('🔄 刷新明细表失败:', error);
          }
        }, 500); // 增加延迟时间，确保表格完全初始化
      }
    } catch (error) {
      console.error('❌ 获取任务详情失败:', error);
    }
  }
}

const [Modal, modalApi] = useVbenModal({
  onOpenChange,
  onCancel: () => {
    modalApi.close();
  },
});

// 打开弹窗的方法
function openModal(record?: any) {
  if (record) {
    modalApi.setData({ record });
  }
  modalApi.open();
}

// 监听图表数据变化，重新渲染图表
watch(chartData, () => {
  console.log('📊 chartData 数据变化，重新渲染图表');
  setTimeout(() => {
    renderChart();
  }, 300);
}, { deep: true });

// 刷新升级统计数据
const refreshUpgradeStats = async () => {
  if (!taskDetail.value.taskId) {
    console.warn('⚠️ 任务ID不存在，无法刷新统计数据');
    return;
  }

  refreshLoading.value = true;

  try {
    console.log('🔄 开始刷新升级统计数据...');
    const result = await View({ taskId: taskDetail.value.taskId });

    if (result) {
      // 更新统计数据
      upgradeStats.value = {
        waitingDevice: result.waitingDevice || 0,
        progressDevice: result.progressDevice || 0,
        successDevice: result.successDevice || 0,
        failedDevice: result.failedDevice || 0,
      };

      console.log('📊 统计数据刷新成功:', upgradeStats.value);

      // 重新渲染图表
      setTimeout(() => {
        renderChart();
      }, 100);

      // 刷新明细表
      if (refDetailTable.value && refDetailTable.value.refreshTable) {
        refDetailTable.value.refreshTable();
      }
    }
  } catch (error) {
    console.error('❌ 刷新统计数据失败:', error);
  } finally {
    refreshLoading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  openModal,
});
</script>

<template>
  <Modal class="w-[1200px] h-[800px]" title="升级任务详情">
    <!-- 上部区域 -->
    <div class="top-section">
      <!-- 左侧卡片 - 任务基本信息 -->
      <Card class="left-card" title="任务信息">
        <div class="card-content">
          <div class="info-item">
            <span class="label">任务ID:</span>
            <span class="value">{{ taskDetail.taskId || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">任务名称:</span>
            <span class="value">{{ taskDetail.taskName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">任务类型:</span>
            <span class="value">{{ getTaskTypeLabel(taskDetail.taskType) }}</span>
          </div>
          <div class="info-item">
            <span class="label">设备数量:</span>
            <span class="value">{{ taskDetail.deviceCount || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="label">预定时间:</span>
            <span class="value">{{ formatDate(taskDetail.taskTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">任务描述:</span>
            <span class="value">{{ taskDetail.remark || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">添加时间:</span>
            <span class="value">{{ formatDate(taskDetail.createdAt) }}</span>
          </div>
        </div>
      </Card>

      <!-- 右侧卡片 - 升级统计 -->
      <Card class="right-card">
        <template #title>
          <div class="card-title-wrapper">
            <span>升级统计</span>
            <Button
              type="text"
              size="small"
              :loading="refreshLoading"
              @click="refreshUpgradeStats"
              class="refresh-btn"
            >
              🔄 刷新
            </Button>
          </div>
        </template>
        <div class="stats-container">
          <!-- 环形图容器 -->
          <div class="chart-container">
            <div class="pie-chart">
              <!-- 中心显示总数 -->
              <div class="chart-center">
                <div class="total-number">{{ totalDevices }}</div>
                <div class="total-label">总设备</div>
              </div>

              <!-- 使用EchartsUI组件 -->
              <EchartsUI
                ref="chartRef"
                class="pie-echarts"
              />
            </div>
          </div>

          <!-- 统计列表 -->
          <div class="stats-list">
            <div v-for="item in chartData" :key="item.name" class="stats-item">
              <div class="stats-indicator" :style="{ backgroundColor: item.color }"></div>
              <div class="stats-info">
                <div class="stats-name">{{ item.name }}</div>
                <div class="stats-value">{{ item.value }}台 ({{ item.percentage }}%)</div>
              </div>
            </div>

            <!-- 如果没有数据 -->
            <div v-if="chartData.length === 0" class="no-data">
              暂无统计数据
            </div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 下部区域 - 升级任务明细表 -->
    <Card class="bottom-card" title="任务信息">
      <div class="detail-table-container">
        <Table1
          :task-id="taskDetail.taskId"
          ref="refDetailTable"
        />
      </div>
    </Card>
  </Modal>
</template>

<style scoped>
/* 上部区域布局 */
.top-section {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  height: 380px; /* 减小上部区域高度 */
}

/* 左侧卡片 */
.left-card {
  flex: 1;
  height: 100%;
}

/* 右侧卡片 */
.right-card {
  flex: 1;
  height: 100%;
  overflow: hidden; /* 防止内容溢出 */
}

/* 下部卡片 - 自适应表格内容高度 */
.bottom-card {
  /* flex: 1; 占用剩余空间 */
  height:550px;
}

/* 卡片内容样式 */
.card-content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  color: #666;
  font-size: 14px;
  margin-right: 12px;
  white-space: nowrap;
  min-width: 80px;
  font-weight: 500;
}

.info-item .value {
  color: #262626;
  font-size: 14px;
  flex: 1;
  word-break: break-all;
}

/* 确保卡片内容区域填满整个卡片 */
:deep(.ant-card-body) {
  height: calc(100% - 57px); /* 减去卡片头部高度 */
  padding: 0;
}

/* 上部卡片的内容区域需要padding */
.top-section :deep(.ant-card-body) {
  padding: 20px;
}

/* 下部卡片的内容区域不需要padding，让表格填满 */
.bottom-card :deep(.ant-card-body) {
  padding: 0 !important;
  margin: 0 !important;
}

/* 明细表容器样式 */
.detail-table-container {
  padding: 0;
  margin: 0;
}

/* 让表格自然展示，不限制高度 */
.detail-table-container :deep(.vxe-grid) {
  border: none;
  margin: 0;
}

/* 去掉表格内部留白 */
.detail-table-container :deep(.vxe-table) {
  margin: 0;
}

.detail-table-container :deep(.vxe-table--body-wrapper) {
  margin: 0;
  padding: 0;
}

/* 表格单元格样式 */
.detail-table-container :deep(.vxe-body--column) {
  padding-left: 8px;
  padding-right: 8px;
}

.detail-table-container :deep(.vxe-header--column) {
  padding-left: 8px;
  padding-right: 8px;
}

/* 调整明细表工具栏样式 */
.detail-table-container :deep(.vxe-toolbar) {
  padding: 8px 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

/* 统计容器样式 */
.stats-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  overflow: hidden; /* 防止内容溢出 */
}

/* 图表容器 */
.chart-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

/* 环形图 */
.pie-chart {
  position: relative;
  width: 260px;
  height: 260px;
}

/* 图表中心 */
.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  pointer-events: none;
}

.total-number {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  line-height: 1;
}

.total-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* EchartsUI组件样式 */
.pie-echarts {
  width: 260px !important;
  height: 260px !important;
}

/* 统计列表 */
.stats-list {
  flex: 1;
  overflow: hidden; /* 防止内容溢出 */
  max-height: 150px; /* 限制最大高度 */
}

/* 统计项 */
.stats-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.stats-item:last-child {
  margin-bottom: 0;
}

/* 统计指示器 */
.stats-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
}

/* 统计信息 */
.stats-info {
  flex: 1;
}

.stats-name {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
  margin-bottom: 2px;
}

.stats-value {
  font-size: 12px;
  color: #666;
}

/* 无数据提示 */
.no-data {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 40px 0;
}

/* 卡片标题样式 */
.card-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.refresh-btn {
  color: #666;
  padding: 4px;

  &:hover {
    color: #1890ff;
    background-color: #f0f0f0;
  }

  &:focus {
    color: #1890ff;
  }
}
</style>