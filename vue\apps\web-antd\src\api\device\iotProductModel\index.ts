import { requestClient } from '#/api/request';

// 获取产品物模型列表
export function List(params:any) {
  return requestClient.get<any>('device/iotProductModel/list', { params });
}

// 删除/批量删除产品物模型
export function Delete(params:any) {
  return requestClient.post<any>('device/iotProductModel/delete', { ...params });
}

// 添加/编辑产品物模型
export function Edit(params:any) {
  return requestClient.post<any>('device/iotProductModel/edit', { ...params });
}

// 获取产品物模型指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotProductModel/view', { params });
}

// 导出产品物模型
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotProductModel/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}