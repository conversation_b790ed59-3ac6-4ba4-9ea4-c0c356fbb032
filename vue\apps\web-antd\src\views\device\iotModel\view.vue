<script setup lang="ts">
import type { SysClient } from '#/api/system/client';
import { computed, nextTick, ref } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';
import { Description, useDescription } from '#/components/description';
import { viewSchema, type RowType } from './model';
import { View } from '#/api/device/iotModel';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onOpenChange: handleOpenChange,
});
const [registerDescription, { setDescProps }] = useDescription({
  column: 1,
  schema: viewSchema,
});
async function handleOpenChange(open: boolean) {
  if (!open) {
    return null;
  }
  const { record } = drawerApi.getData() as { record: RowType };
  const record2 = await View({ modelId: record.modelId });
  let currentSchema = cloneDeep(viewSchema);
  if (record2.dataType === 'integer' || record2.dataType === 'decimal') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'min',
        render(_, data) {
          return (data.min !== undefined ? data.min : '') + ' 到' + (data.max !== undefined ? data.max : '');
        },

        label: '取值范围',
      },
      {
        field: 'unit',
        label: '单位',
      },
      {
        field: 'step',
        label: '步长',
      },
    ];
  }
  if (record2.dataType === 'decimal') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'decimals',
        label: '小数位数',
      },
    ];
  }
  if (record2.dataType === 'string') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'maxLength',
        label: '最大长度',
      },
    ];
  }
  if (record2.dataType === 'bool') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'trueText',
        label: '布尔值',
        render(_, data) {
          return '0: ' + (data.falseText !== undefined ? data.falseText : '') + ' , 1: ' + (data.trueText !== undefined ? data.trueText : '');
        },
      },
    ];
  }
  if (record2.dataType === 'array') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'arrayCount',
        label: '元素个数',
      },
      {
        field: 'itemKeys',
        label: '元素标识',
        render(_, data) {
          return data.itemKeys.join(',');
        },
      },
      {
        field: 'itemType',
        label: '元素类型',
        render(_, data) {
          return renderDict(data.itemType, DictEnum.DEVICE_MODEL_DATA_TYPE);
        },
      },
    ];
    if (record2.itemType === 'object') {
      currentSchema = [
        ...currentSchema,
        {
          field: 'items',
          label: '对象参数',
          render(_, data) {
            return data.items
              .map((item: any) => {
                return item.modelName + ': ' + (item.dataType !== undefined ? item.dataType : '');
              })
              .join(', ');
          },
        },
      ];
    }
  }
  if (record2.dataType === 'enum') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'enumList',
        label: '枚举项',
        render(_, data) {
          return data.enumList
            .map((item: any) => {
              return item.value + ': ' + (item.text !== undefined ? item.text : '');
            })
            .join(' , ');
        },
      },
    ];
  }
  if (record2.dataType === 'object') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'items',
        label: '对象参数',
        render(_, data) {
          return data.items
            .map((item: any) => {
              return item.modelName + ': ' + (item.dataType !== undefined ? item.dataType : '');
            })
            .join(', ');
        },
      },
    ];
  }
  setDescProps({ data: record2, schema: currentSchema }, true);
}
</script>
<template>
  <BasicDrawer :footer="false" class="w-[600px]" title="查看">
    <Description @register="registerDescription"></Description>
  </BasicDrawer>
</template>
