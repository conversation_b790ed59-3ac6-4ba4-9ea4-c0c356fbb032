<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <div class="p-4">
      <!-- 标题 -->
    

      <!-- 全局选项 -->
      <div class="mb-8">
        <h4 class="text-base font-medium mb-2">全局选项</h4>
        <p class="text-gray-500 text-sm mb-4">对于普通设备用户的设备产品，可设置设备或设备第二维码等一些其他，最终用户可在 App 中主动添加设备。</p>

        <!-- 允许用户添加设备 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium mb-2">允许用户添加设备</h5>
          <p class="text-gray-500 text-sm mb-3">允许用户在 App 应用中添加该类设备。</p>
          <Switch v-model:checked="formData.allowUserAdd" />
        </div>

        <!-- 允许多用户添加 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium mb-2">允许多用户添加</h5>
          <p class="text-gray-500 text-sm mb-3">是否允许多个用户添加同一个设备。</p>
          <Switch v-model:checked="formData.allowMultiUserAdd" />
        </div>
      </div>

      <!-- 设备码选项 -->
      <div class="mb-8">
        <h4 class="text-base font-medium mb-4">设备码选项</h4>
        <p class="text-gray-500 text-sm mb-4">用户可通过设备码添加设备，请在设备信息中设置设备码。</p>

        <!-- 自定义显示文字 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium mb-2">自定义显示文字</h5>
          <p class="text-gray-500 text-sm mb-3">显示在用户输入设备码的页面，未设置则显示系统默认文字。</p>
          <Input v-model:value="formData.customDisplayText" placeholder="请输入自定义显示文字" />
        </div>

        <!-- 显示提示图片 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium mb-2">显示提示图片</h5>
          <p class="text-gray-500 text-sm mb-3">开启后，在用户输入设备码的页面显示示图片。</p>
          <Switch v-model:checked="formData.showHintImage" />
        </div>

        <!-- 上传提示图片 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium mb-2">上传提示图片</h5>
          <div class="mb-3">
            <!-- 使用统一的图片上传组件 -->
            <ImageUpload
              v-model:value="formData.hintImageUrl"
              :accept="accept"
              :max-number="1"
              :api="(file, progressEvent) => uploadApi(file, progressEvent, true)"
            />
          </div>
          <p class="text-gray-500 text-sm">
            支持的图片格式：PNG、图片文件大小不超过 256KB。
          </p>
          <!-- 图片预览和删除 -->
          <div v-if="formData.hintImageUrl" class="mt-4">
            <img :src="formData.hintImageUrl" alt="提示图片" class="max-w-xs max-h-32 border rounded" />
            <div class="mt-2">
              <Button size="small" @click="handleRemoveImage">删除图片</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { Switch, Input, Button, message } from 'ant-design-vue';

import { useVbenDrawer } from '@vben/common-ui';
import { SetUserConfig, ViewDevice } from '#/api/project/iotProjectProduct';
import { ImageUpload } from '#/components/upload';
import { uploadApi } from '#/api';

const emit = defineEmits<{ reload: [] }>();

interface ModalProps {
  productId?: number | string;
  productName?: string;
  ppId?: number;
  update: boolean;
  view: boolean;
}

// 表单数据
const formData = ref({
  allowUserAdd: false,           // 允许用户添加设备
  allowMultiUserAdd: false,      // 允许多用户添加
  customDisplayText: '',         // 自定义显示文字
  showHintImage: false,          // 显示提示图片
  hintImageUrl: '',              // 提示图片URL
});

const isUpdate = ref(false);
const isView = ref(false);
const fileInput = ref<HTMLInputElement>();

const title = computed(() => {
  if (isView.value) {
    return '查看配置';
  }
  return isUpdate.value ? '编辑配置' : '配置用户添加设备';
});

const accept = ref(['jpg', 'jpeg', 'png', 'gif', 'webp']);

// 删除图片
function handleRemoveImage() {
  formData.value.hintImageUrl = '';
}

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }

    drawerApi.setState({ confirmLoading: true, loading: true });

    const { ppId, productId, productName, update, view } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;

    // 重置表单数据
    formData.value = {
      allowUserAdd: false,
      allowMultiUserAdd: false,
      customDisplayText: '',
      showHintImage: false,
      hintImageUrl: '',
    };

    // 如果有ppId，获取设备详情来回填用户配置
    if (ppId) {
      try {
        console.log('🔧 [用户配置] 获取设备详情，ppId:', ppId);

        const response = await ViewDevice(ppId);
        const deviceDetail = response.data || response;

        console.log('✅ [用户配置] 设备详情获取成功:', deviceDetail);

        // 回填用户配置数据
        if (deviceDetail.userConfigObj) {
          formData.value = {
            allowUserAdd: deviceDetail.userConfigObj.allow_customer_claim ?? false,
            allowMultiUserAdd: deviceDetail.userConfigObj.allow_multi_customer_claim ?? false,
            customDisplayText: deviceDetail.userConfigObj.add_device_code_tips_text || '',
            showHintImage: deviceDetail.userConfigObj.add_device_code_tips_show_img ?? false,
            hintImageUrl: deviceDetail.userConfigObj.add_device_code_tips_img || '',
          };

          console.log('✅ [用户配置] 用户配置数据回填完成:', formData.value);
        } else {
          console.log('⚠️ [用户配置] 设备暂无用户配置数据');
        }
      } catch (error) {
        console.error('❌ [用户配置] 获取设备详情失败:', error);
        message.error('获取设备配置失败');
      }
    }

    drawerApi.setState({ confirmLoading: false, loading: false });

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
    } else {
      drawerApi.setState({ showConfirmButton: true });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true });

    const { ppId } = drawerApi.getData() as ModalProps;

    if (!ppId) {
      message.error('设备信息不存在');
      return;
    }

    // 调用设置用户配置API
    const requestData = {
      ppId: ppId,
      userConfigObj: {
        allow_customer_claim: formData.value.allowUserAdd,
        allow_multi_customer_claim: formData.value.allowMultiUserAdd,
        add_device_code_tips_text: formData.value.customDisplayText,
        add_device_code_tips_show_img: formData.value.showHintImage,
        add_device_code_tips_img: formData.value.hintImageUrl
      }
    };

    console.log('保存用户配置:', requestData);
    await SetUserConfig(requestData);

    message.success('用户配置保存成功');
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
    message.error('用户配置保存失败');
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false });
  }
}

async function handleCancel() {
  drawerApi.close();

  // 重置表单数据
  formData.value = {
    allowUserAdd: false,
    allowMultiUserAdd: false,
    customDisplayText: '',
    showHintImage: false,
    hintImageUrl: '',
  };
}

</script>

