<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <template #wechartCorp.img="slotProps">
        <ImageUpload v-bind="slotProps" :accept="accept" :max-number=1 />
      </template>
      <template #wechartOfficial.img="slotProps">
        <ImageUpload v-bind="slotProps" :accept="accept" :max-number=1 />
      </template>
      <template #ding.img="slotProps">
        <ImageUpload v-bind="slotProps" :accept="accept" :max-number=1 />
      </template>
      <template #FileUpload="slotProps">
        <FileUpload v-bind="slotProps" :accept="accept" :max-number=1 />
      </template>
    </BasicForm>
    
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Alert } from 'ant-design-vue';
import { Edit, View } from '#/api/manage/iotNotifyTemplate';
import { editSchema } from './model';

import { ImageUpload } from '#/components/upload';
import FileUploadModal from './file-upload-modal.vue';
import FileUpload from '#/components/upload/src/file-upload.vue';

const accept = ref(['jpg', 'jpeg', 'png', 'gif', 'webp']);
const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
}

const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, update, view, } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;
    if (isUpdate.value || isView.value) {
      const record = await View({ id: id });
      record.channelType = String(record.channelType)
      record.businessType = String(record.businessType)
      record.serviceType = String(record.serviceType)
      record.status = record.status == '0' ? true: false;
      await formApi.setValues(record);
    }
    // else{
    //   await formApi.setValues({email:{sslEnable:false, authEnable:false  }});
    // }
    drawerApi.setState({ confirmLoading: false, loading: false })

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
    }
  });



async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    data.status = data.status == true? '0': '1';
    await Edit(data);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false });
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

</script>
//