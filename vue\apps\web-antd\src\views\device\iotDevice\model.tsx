import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { getDictOptions } from '#/utils/dict';
import { DictEnum } from '@vben/constants';
import { renderDict } from '#/utils';
import { getPopupContainer } from '@vben/utils';
import { h } from 'vue';
import { IconifyIcon } from '@vben/icons';

export class State {
  public deviceId = 0; // 设备ID
  public productKey = ''; // 产品标识
  public deviceKey = ''; // 设备标识
  public deviceName = ''; // 设备名称
  public longitude = 0; // 设备坐标（经度）
  public latitude = 0; // 设备坐标（纬度）
  public firmwareVersion = 0; // 固件版本号
  public isShadow = 0; // 是否启用设备影子(0=禁用，1=启用)
  public imgUrl = null; // 图片地址
  public deviceState = 1; // 设备状态（1-未激活，2-禁用，3-在线，4-离线）
  public alarmState = 0; // 报警状态（0-正常 1-场景报警)
  public rssi = 0; // 信号强度（信号极好4格[-55— 0]，信号好3格[-70— -55]，信号一般2格[-85— -70]，信号差1格[-100— -85]）
  public thingsModelValue = null; // 物模型值
  public networkAddress = null; // 设备所在地址
  public networkIp = null; // 设备入网IP
  public status = 0; // 数据状态（"0"正常 "1"停用)
  public tenantId = ''; // 租户ID
  public deptId = 0; // 所属机构
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注
  public deviceModel = ''; // 设备默认型号
  public hardwareVersion = ''; // 硬件版本号
  public onlineTimeout = 0; // 在线超时时间设置
  public deptName = ''; // 所属机构名称

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'deviceName',
    component: 'Input',
    label: '设备名称',
    componentProps: {
      placeholder: '请输入设备名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
  },
  {
    fieldName: 'projectId',
    component: 'Select',
    label: '所属项目',
    componentProps: {
      placeholder: '请选择项目',
      showSearch: true,
      allowClear: true,
      options: [], // 将在index.vue中动态设置
      filterOption: (input: string, option: any) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
    },
  },
  {
    fieldName: 'status',
    component: 'Select',
    label: '状态',
    componentProps: {
      placeholder: '请选择状态',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
  },
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'deptId',
    component: 'TreeSelect',
    label: '所属机构',
    componentProps: {
      getPopupContainer,
    },
  },
  {
    fieldName: 'includeChildren',
    component: 'Switch',
    label: '包含下级机构',
    formItemClass: 'w-[10px]',
    componentProps: {
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '编号',
    field: 'deviceId',
    align: 'center',
    width: 60,
  },
  {
    title: '设备名称',
    field: 'deviceName',
    align: 'left',
    width: -1,
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
    slots: { default: 'deviceKey' },
    // slots: {
    //   default: ({ row }) => {
    //     return h('div', { class: 'flex items-center justify-center' }, [
    //       h('span', null, row.deviceKey),
    //       h(IconifyIcon, {
    //         icon: 'ant-design:qrcode-outlined',
    //         style: { color: '#409eff', width: '18px', height: '18px', marginLeft: '4px' }
    //       })
    //     ]);
    //   },
    // },
  },

  {
    title: '所属产品',
    field: 'productName',
    align: 'left',
    width: -1,
  },
  // {
  //   title: '设备坐标（经度）',
  //   field: 'longitude',
  //   align: 'left',
  //   width: -1,
  // },
  // {
  //   title: '设备坐标（纬度）',
  //   field: 'latitude',
  //   align: 'left',
  //   width: -1,
  // },
  {
    title: '版本',
    field: 'firmwareVersion',
    align: 'center',
    width: 100,
  },
  {
    title: '在线状态',
    field: 'deviceState',
    align: 'center',
    width: 100,
    slots: { default: 'deviceState' },
  },
  {
    title: '报警状态',
    field: 'alarmState',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.alarmState, DictEnum.ALARM_STATE);
        if (found) {
          return found;
        }
        return row.alarmState;
      },
    },
  },
  {
    title: '是否启用',
    field: 'status',
    align: 'center',
    width: 100,
    slots: { default: 'status' },
  },
  // 新增所属机构字段 deptId
  {
    title: '所属机构',
    field: 'deptName',
    align: 'center',
    width: 100,
  },
  {
    title: '所属项目',
    field: 'projectName',
    align: 'left',
    width: -1,
  },
  // {
  //   title: '在线超时设置',
  //   field: 'onlineTimeout',
  //   align: 'left',
  //   width: -1,
  // },
  // {
  //   title: '最后在线时间',
  //   field: 'lastOnlineTime',
  //   align: 'left',
  //   width: -1,
  // },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
  },
  { title: '操作', width: 150, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  deviceId: number;
  productKey: string;
  deviceKey: string;
  deviceName: string;
  longitude: number;
  latitude: number;
  firmwareVersion: number;
  isShadow: number;
  imgUrl: string;
  deviceState: number;
  alarmState: number;
  rssi: number;
  thingsModelValue: string;
  networkAddress: string;
  networkIp: string;
  status: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
  deviceModel: string; // 设备默认型号
  hardwareVersion: string; // 硬件版本号
  onlineTimeout: number; // 在线超时时间设置
}

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'deviceId', label: '设备ID' },
  { field: 'productKey', label: '产品标识' },
  { field: 'deviceKey', label: '设备标识' },
  { field: 'deviceName', label: '设备名称' },
  { field: 'longitude', label: '设备坐标（经度）' },
  { field: 'latitude', label: '设备坐标（纬度）' },
  { field: 'firmwareVersion', label: '固件版本号' },
  { field: 'isShadow', label: '是否启用设备影子' },
  { field: 'imgUrl', label: '图片地址' },
  { field: 'deviceState', label: '设备状态' },
  { field: 'alarmState', label: '报警状态' },
  { field: 'rssi', label: '信号强度' },
  { field: 'thingsModelValue', label: '物模型值' },
  { field: 'networkAddress', label: '设备所在地址' },
  { field: 'networkIp', label: '设备入网IP' },
  { field: 'status', label: '是否启用' },
  { field: 'tenantId', label: '租户ID' },
  { field: 'deptId', label: '所属机构' },
  { field: 'createdDept', label: '创建部门' },
  { field: 'createdBy', label: '创建者' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'updatedBy', label: '更新者' },
  { field: 'updatedAt', label: '更新时间' },
  { field: 'deletedBy', label: '删除人' },
  { field: 'deletedAt', label: '删除时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'deviceId',
    component: 'Input',
    label: '设备ID',
    dependencies: { show: () => false, triggerFields: [''] },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
    componentProps: {
      placeholder: '选择所属产品',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    disabled: false,
    rules: 'required',
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    disabled: false,
    // rules: 'required',
  },
  {
    fieldName: 'deviceName',
    component: 'Input',
    label: '设备名称',
    componentProps: {
      placeholder: '请输入设备名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    disabled: false,
    rules: 'required',
  },
  {
    fieldName: 'firmwareVersion',
    component: 'Input',
    label: '固件版本号',
    componentProps: {
      placeholder: '请输入固件版本号',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    disabled: false,
    rules: null,
  },
  {
    fieldName: 'deviceModel',
    component: 'Input',
    label: '设备默认型号',
    componentProps: {
      placeholder: '请输入设备默认型号',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    disabled: false,
    rules: null,
  },
  {
    fieldName: 'hardwareVersion',
    component: 'Input',
    label: '硬件版本号',
    componentProps: {
      placeholder: '请输入硬件版本号',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    disabled: false,
    rules: null,
  },
  {
    fieldName: 'longitude',
    component: 'InputNumber',
    label: '设备坐标（经度）',
    componentProps: {
      placeholder: '请输入设备坐标（经度）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    disabled: false,
    rules: null,
  },
  {
    fieldName: 'latitude',
    component: 'InputNumber',
    label: '设备坐标（纬度）',
    componentProps: {
      placeholder: '请输入设备坐标（纬度）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    disabled: false,
    rules: null,
  },
  {
    fieldName: 'deviceLocationPicker',
    component: 'Input',
    label: '设备位置选择',
    componentProps: {
      placeholder: '点击按钮选择设备位置',
      readonly: true,
    },
    formItemClass: 'col-span-2',
    disabled: false,
    rules: null,
  },
  {
    fieldName: 'isShadow',
    component: 'RadioGroup',
    label: '是否启用设备影子',
    componentProps: {
      placeholder: '请选择是否启用设备影子',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: getDictOptions(DictEnum.IOT_YES_NO),
    },
    defaultValue: '0',
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'networkAddress',
    component: 'Input',
    label: '设备地址',
    componentProps: {
      placeholder: '请输入设备所在地址',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    rules: null,
  },

  {
    fieldName: 'onlineTimeout',
    component: 'InputNumber',
    label: '在线超时时间设置',
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    disabled: false,
    suffix: () => '秒',
  },
  {
    fieldName: 'status',
    component: 'Input',
    label: '是否启用',
    dependencies: { show: () => false, triggerFields: [''] },
    defaultValue: '0',
  },
  {
    fieldName: 'tags',
    component: 'Input',
    label: '标签',
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    rules: null,
  },
  {
    fieldName: 'imgUrl',
    component: 'Input',
    label: '设备图片',
  },
];

// 详情页设备信息
export const detailEditSchema: VbenFormSchema[] = [
  {
    fieldName: 'deviceId',
    component: 'Input',
    label: '设备ID',
    componentProps: {
      disabled: false,
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: { show: () => false, triggerFields: [''] },
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      disabled: false,
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
    componentProps: {
      placeholder: '选择所属产品',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'deviceType',
    component: 'Select',
    label: '设备类型',
    componentProps: {
      placeholder: '请选择设备类型',
      options: getDictOptions(DictEnum.DEVICE_TYPE),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'firmwareVersion',
    component: 'Input',
    label: '固件版本号',
    componentProps: {
      disabled: false,
      placeholder: '请输入固件版本号',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
  {
    fieldName: 'channelType',
    component: 'Select',
    label: '通道类型',
    componentProps: {
      placeholder: '请选择通道类型',
      options: getDictOptions(DictEnum.CHANNEL_TYPE),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'transport',
    component: 'Select',
    label: '通讯协议',
    componentProps: {
      options: getDictOptions(DictEnum.TRANSPORT),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'deptName',
    component: 'Input',
    label: '所属机构',
    componentProps: {
      disabled: false,
      readonly: true,
      placeholder: ' ',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
  {
    fieldName: 'productionBatch',
    component: 'Input',
    label: '生产批次',
    componentProps: {
      disabled: false,
      readonly: true,
      placeholder: ' ',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
  {
    fieldName: 'packingBatch',
    component: 'Input',
    label: '封箱批次',
    componentProps: {
      disabled: false,
      readonly: true,
      placeholder: ' ',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
  {
    fieldName: 'device.deviceModel',
    component: 'Input',
    label: '设备型号',
    componentProps: {
      disabled: false,
      readonly: true,
      placeholder: ' ',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
  {
    fieldName: 'product.categoryName',
    component: 'Input',
    label: '产品分类',
    componentProps: {
      disabled: false,
      readonly: true,
      placeholder: ' ',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
  {
    fieldName: 'createdAt',
    component: 'Input',
    label: '创建时间',
    componentProps: {
      disabled: false,
      readonly: true,
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'activeTime',
    component: 'Input',
    label: '激活时间',
    componentProps: {
      disabled: false,
      readonly: true,
      placeholder: ' ',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
{
    fieldName: 'lastOnlineTime',
    component: 'Input',
    label: '最后在线时间',
    componentProps: {
      disabled: false,
      readonly: true,
      placeholder: ' ',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },  {
    fieldName: 'projectName',
    component: 'Input',
    label: '所属项目',
    componentProps: {
      disabled: false,
      readonly: true,
      placeholder: ' ',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },  {
    fieldName: 'onlineTimeout',
    component: 'Input',
    label: '在线超时时间',
    componentProps: {
      disabled: false,
      readonly: true,
      placeholder: ' ',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'remark',
    component: 'Textarea',
    label: '备注',
    componentProps: {
      disabled: false,
      placeholder: ' ',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-4',
  },
];

// 表格列
export const propertyTrendColumns: VxeGridProps['columns'] = [
  {
    title: '时间',
    field: 'ts',
    align: 'left',
    width: -1,
  },
  {
    title: '属性值',
    field: 'value',
    align: 'left',
    width: -1,
  },
  {
    title: '数据单位',
    field: 'unit',
    align: 'left',
    width: -1,
  },
];

// 表格列
export const forSelectColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '设备名称',
    field: 'deviceName',
    align: 'left',
    width: -1,
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
  },
  {
    title: '所属产品',
    field: 'productName',
    align: 'left',
    width: -1,
  },
  {
    title: '在线状态',
    field: 'deviceState',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.deviceState, DictEnum.DEVICE_STATE);
        if (found) {
          return found;
        }
        return row.deviceState;
      },
    },
  },
  {
    title: '是否启用',
    field: 'status',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
      }
    },
  },
];
