import { requestClient } from '#/api/request';

// 获取场景脚本列表
export function List(params:any) {
  return requestClient.get<any>('ruleengine/iotSceneScript/list', { params });
}

// 删除/批量删除场景脚本
export function Delete(params:any) {
  return requestClient.post<any>('ruleengine/iotSceneScript/delete', { ...params });
}

// 添加/编辑场景脚本
export function Edit(params:any) {
  return requestClient.post<any>('ruleengine/iotSceneScript/edit', { ...params });
}

// 获取场景脚本指定详情
export function View(params:any) {
  return requestClient.get<any>('ruleengine/iotSceneScript/view', { params });
}

// 导出场景脚本
export function Export(params:any) {
  return requestClient.post<Blob>('/ruleengine/iotSceneScript/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}

//验证规则脚本
export function Validate(params: any) {
  return requestClient.post<any>('ruleengine/iotChannelScript/validate', {
    ...params,
  });
}