import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';

export class State {
  public id = 0; // ID
  public certificate = ''; // 证书内容
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间
  public deletedAt = ''; // 删除时间
  public deletedBy = 0; // 删除者
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'id',
    component: 'InputNumber',
    label: 'ID',
    componentProps: {
      placeholder: '请输入ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    }, rules: null,
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'datetimerange',
      clearable: true,
      valueFormat: 'FMDateRange',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    }, rules: null,
    formItemClass: 'col-span-1',
  },];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    width: 50,
    type: 'checkbox',
  },
  {
    title: '编号',
    field: 'id',
    align: 'center',
    width: 100,
  },
  {
    title: '导入时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
  },
  {
    title: '导入者',
    field: 'createdBy',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderPopoverMemberSumma(row.createdBySumma);
      },
    },
  },
  {
    title: '备注',
    field: 'remark',
    align: 'left',
    width: -1,
  },
  {
    title: '更新时间',
    field: 'updatedAt',
    align: 'left',
    width: -1,
  },
  { title: '操作', width: 200, align: 'center', slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  id: number;
  certificate: string;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedAt: string;
  deletedBy: number;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'id', label: 'ID' },
  { field: 'certificate', label: '证书内容' },
  { field: 'createdAt', label: '导入时间' },
  { field: 'createdBy', label: '导入者' },
  { field: 'updatedAt', label: '更新时间' },
  { field: 'updatedBy', label: '更新者' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'id',
    component: 'Input',
    label: 'ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  },{
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    formItemClass: 'col-span-1',
  },];

  // 导入字段列表
export const importSchema: VbenFormSchema[] = [
  {
    fieldName: 'id',
    component: 'Input',
    label: 'ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'certificate',
    component: 'Textarea',
    label: '证书内容',
    formItemClass: 'col-span-1',
    componentProps: {
      placeholder: '请输入证书内容',
      autoSize: { minRows: 5, maxRows: 10 },
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  }, {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    formItemClass: 'col-span-1',
  },];