<script setup lang="ts">
import type { SysClient } from '#/api/system/client';
import { computed, nextTick, ref } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';
import { useVbenForm } from '#/adapter/form';
import { Description, useDescription } from '#/components/description';
import { viewSchema, type RowType } from './model';
import { View } from '#/api/device/iotPollTask';
import { Divider } from 'ant-design-vue';
import IotPollTaskLog from '../iotPollTaskLog/index.vue';
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onOpenChange: handleOpenChange,
});
const [registerDescription, { setDescProps }] = useDescription({
  column: 1,
  schema: [],
});

const currentTaskId = ref<number>();

async function handleOpenChange(open: boolean) {
  if (!open) {
    currentTaskId.value = undefined;
    return null;
  }
  const { record } = drawerApi.getData() as { record: RowType };
  const record2 = await View({ taskId: record.taskId });
  currentTaskId.value = record.taskId;
  setDescProps({ data: record2, schema: viewSchema(record2) }, true);
}
</script>
<template>
  <BasicDrawer :footer="false" class="w-[1200px]" title="查看">
    <Description @register="registerDescription"></Description>

    <Divider>采集任务流水</Divider>

    <IotPollTaskLog
      v-if="currentTaskId"
      :task-id="currentTaskId"
      :embedded="true"
    />
  </BasicDrawer>
</template>