import { requestClient } from '#/api/request';

// 获取项目设备关联接警人列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotProjectAlarmUser/list', { params });
}

// 删除/批量删除项目设备关联接警人
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotProjectAlarmUser/delete', { ...params });
}

// 添加/编辑项目设备关联接警人
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotProjectAlarmUser/edit', { ...params });
}

// 获取项目设备关联接警人指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotProjectAlarmUser/view', { params });
}

// 导出项目设备关联接警人
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotProjectAlarmUser/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}