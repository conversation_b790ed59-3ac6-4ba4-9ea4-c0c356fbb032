import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

export class State {
  public alarmSceneId = 0; // 报警配置ID
  public alarmConfigId = 0; // 报警配置ID
  public sceneId = 0; // 场景ID
  public tenantId = ''; // 租户ID

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'alarmSceneId',
    component: 'InputNumber',
    label: '报警配置ID',
    componentProps: {
      placeholder: '请输入报警配置ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '报警配置ID',
    field: 'alarmSceneId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '报警配置ID',
    field: 'alarmConfigId',
    align: 'left',
    width: -1,
 },
  {
    title: '场景ID',
    field: 'sceneId',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  alarmSceneId: number;
  alarmConfigId: number;
  sceneId: number;
  tenantId: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'alarmSceneId',  label: '报警配置ID'},
  {  field: 'alarmConfigId',  label: '报警配置ID'},
  {  field: 'sceneId',  label: '场景ID'},
  {  field: 'tenantId',  label: '租户ID'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'alarmSceneId',
    component: 'Input',
    label: '报警配置ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'alarmConfigId',
    component: 'InputNumber',
    label: '报警配置ID',
    componentProps: {
      placeholder: '请输入报警配置ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入报警配置ID', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'sceneId',
    component: 'InputNumber',
    label: '场景ID',
    componentProps: {
      placeholder: '请输入场景ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入场景ID', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'tenantId',
    component: 'Input',
    label: '租户ID',
    componentProps: {
      placeholder: '请输入租户ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
];