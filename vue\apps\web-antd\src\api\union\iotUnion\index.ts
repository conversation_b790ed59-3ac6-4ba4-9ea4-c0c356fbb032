import { requestClient } from '#/api/request';

// 获取组合设备表列表
export function List(params:any) {
  return requestClient.get<any>('union/iotUnion/list', { params });
}

// 删除/批量删除组合设备表
export function Delete(params:any) {
  return requestClient.post<any>('union/iotUnion/delete', { ...params });
}

// 添加/编辑组合设备表
export function Edit(params:any) {
  return requestClient.post<any>('union/iotUnion/edit', { ...params });
}

// 修改组合设备表状态
export function Status(params:any) {
  return requestClient.post<any>('union/iotUnion/status', { ...params });
}

// 获取组合设备表指定详情
export function View(params:any) {
  return requestClient.get<any>('union/iotUnion/view', { params });
}

// 导出组合设备表
export function Export(params:any) {
  return requestClient.post<Blob>('/union/iotUnion/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}