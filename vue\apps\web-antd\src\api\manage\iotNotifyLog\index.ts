import { requestClient } from '#/api/request';

// 获取通知日志表列表
export function List(params:any) {
  return requestClient.get<any>('manage/iotNotifyLog/list', { params });
}

// 删除/批量删除通知日志表
export function Delete(params:any) {
  return requestClient.post<any>('manage/iotNotifyLog/delete', { ...params });
}

// 添加/编辑通知日志表
export function Edit(params:any) {
  return requestClient.post<any>('manage/iotNotifyLog/edit', { ...params });
}

// 获取通知日志表指定详情
export function View(params:any) {
  return requestClient.get<any>('manage/iotNotifyLog/view', { params });
}

// 导出通知日志表
export function Export(params:any) {
  return requestClient.post<Blob>('/manage/iotNotifyLog/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}