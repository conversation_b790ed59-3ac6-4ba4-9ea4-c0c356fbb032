import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

export class State {
  public componentId = 0; // 自增ID
  public componentKey = ''; // 组件标识
  public componentName = ''; // 组件名称
  public imgUrl = ''; // 组件图片地址
  public componentConfig = null; // 面板默认配置
  public attrConfig = null; // 属性配置
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 更新时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
				fieldName: 'componentKey',
				component: 'Input',
				label: '组件标识',
				componentProps: {
					placeholder: '请输入组件标识',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '编号',
    field: 'componentId',
    align: 'left',
    width: 50,
  },
  {
    title: '组件标识',
    field: 'componentKey',
    align: 'left',
    width: 180,
 },
  {
    title: '组件名称',
    field: 'componentName',
    align: 'left',
    width: 180,
 },
  {
    title: '组件图片',
    field: 'imgUrl',
    align: 'left',
    width: 260,
	slots: {
		default: 'imgUrl',
	},
 },
  {
    title: '面板默认配置',
    field: 'componentConfig',
    align: 'left',
    width: -1,
 },
  {
    title: '属性配置',
    field: 'attrConfig',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  componentId: number;
  componentKey: string;
  componentName: string;
  imgUrl: string;
  componentConfig: string;
  attrConfig: string;
  createdAt: string;
  updatedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'componentId',  label: '自增ID'},
  {  field: 'componentKey',  label: '组件标识'},
  {  field: 'componentName',  label: '组件名称'},
  {  field: 'imgUrl',  label: '组件图片地址'},
  {  field: 'componentConfig',  label: '面板默认配置'},
  {  field: 'attrConfig',  label: '属性配置'},
  {  field: 'createdAt',  label: '创建时间'},
  {  field: 'updatedAt',  label: '更新时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
 {
					fieldName: 'componentId',
					component: 'Input',
					label: '自增ID',
					dependencies: {   show: () => false,    triggerFields: [''],   },
					componentProps: {
						placeholder: '',
						onUpdateValue: (e: any) => {
							console.log(e);
						},   
					},
					formItemClass: 'col-span-1',
				},  {
				fieldName: 'componentKey',
				component: 'Input',
				label: '组件标识',
				componentProps: {
					placeholder: '请输入组件标识',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'componentName',
				component: 'Input',
				label: '组件名称',
				componentProps: {
					placeholder: '请输入组件名称',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'imgUrl',
				component: 'Input',
				label: '组件图片地址',
				componentProps: {
					placeholder: '请输入组件图片地址',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'componentConfig',
				component: 'Input',
				label: '面板默认配置',
				componentProps: {
					placeholder: '请输入面板默认配置',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'attrConfig',
				component: 'Input',
				label: '属性配置',
				componentProps: {
					placeholder: '请输入属性配置',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},];