<script lang="ts" setup>
import { h, reactive, ref, computed, onMounted, watch } from 'vue';

// 添加props定义
const props = defineProps<{
  productKey?: string;
  productName?: string;
  firmwareType?: number;
}>();

console.log('🔍 Management组件初始化 - 接收到的props:', props);
import { useRouter } from 'vue-router';
import { Button, message, Modal, Popconfirm } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
const router = useRouter();
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, Delete, View } from '#/api/manage/iotFirmware';
import { ListNoPage as ListProduct } from '#/api/device/iotProduct';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import editDrawer from './edit.vue';
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'firmwareId',
  },
  rowConfig: {
    keyField: 'firmwareId',
  },
  columns: columns,
  exportConfig: {},
  height: 600, // 设置表格高度为400px，超出部分会显示滚动条
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        console.log('🔍 表格查询开始');
        console.log('📋 page参数:', page);
        console.log('📋 formValues参数:', formValues);
        console.log('📋 props.productKey:', props.productKey);
        console.log('📋 props.productName:', props.productName);

        const queryParams = {
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        };

        // 如果父组件传递了productKey，添加到查询参数中
        if (props.productKey) {
          queryParams.productKey = props.productKey;
          console.log('✅ 添加父组件productKey到查询参数:', props.productKey);
        } else {
          console.log('⚠️ 父组件未传递productKey');
        }

        console.log('🚀 最终查询参数:', queryParams);

        const result = await List(queryParams);
        console.log('📊 查询结果:', result);
        return result;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
 // formOptions,
  gridOptions,
  gridClass: 'p-0 ',
  gridEvents,
});

// 监听props变化
watch(() => props.productKey, (newProductKey, oldProductKey) => {
  console.log('🔄 props.productKey变化:', { oldProductKey, newProductKey });
  if (newProductKey && newProductKey !== oldProductKey) {
    console.log('🔄 productKey变化，重新查询表格数据');
    handleRefresh();
  }
}, { immediate: true });

watch(() => props.productName, (newProductName, oldProductName) => {
  console.log('🔄 props.productName变化:', { oldProductName, newProductName });
}, { immediate: true });

// 页面加载时动态设置产品下拉选项
onMounted(async () => {
  try {
    console.log('开始加载产品数据...');
    const productRes = await ListProduct({});
    console.log('产品数据响应:', productRes);

    const productItems = Array.isArray(productRes.items) ? productRes.items : [];
    console.log('产品列表:', productItems);

    const productOptions = productItems.map((item: any) => ({
      label: item.productName || item.productKey, // 显示产品名称，如果没有则显示productKey
      value: item.productKey, // 存储productKey
    }));

    console.log('产品选项:', productOptions);

    // 动态更新搜索表单中的产品下拉选项
    gridApi.formApi.updateSchema([
      {
        fieldName: 'productKey',
        componentProps: {
          options: productOptions,
          placeholder: '请选择产品名称',
          allowClear: true,
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().includes(input.toLowerCase());
          },
        },
      },
    ]);

    console.log('✅ 产品下拉选项设置成功');
  } catch (error) {
    console.error('❌ 加载产品数据失败:', error);
  }
});
// 查看固件详情 - 参考dashboard的跳转方式
async function handlePreview(record: RowType) {
  console.log(' 点击查看按钮，记录信息:', record);
  console.log(' 固件ID:', record.firmwareId);

  // 构建包含所有必要参数的URL，确保所有值都不为 undefined
  const params = new URLSearchParams();

  if (record.firmwareId !== undefined && record.firmwareId !== null) {
    params.set('firmwareId', record.firmwareId.toString());
  }
  if (record.firmwareName) {
    params.set('firmwareName', record.firmwareName);
  }
  if (record.productKey) {
    params.set('productKey', record.productKey);
  }
  if (record.productName) {
    params.set('productName', record.productName);
  }
  if (record.firmwareVersion) {
    params.set('firmwareVersion', record.firmwareVersion);
  }
  if (record.createdAt) {
    params.set('createdAt', record.createdAt);
  }
  if (record.remark) {
    params.set('remark', record.remark);
  }

  // 获取升级统计数据
  try {
    const firmwareDetail = await View({ firmwareId: record.firmwareId });
    console.log('🔍 获取到的固件详情:', firmwareDetail);

    // 添加升级统计数据到URL参数
    if (firmwareDetail.deviceCount !== undefined) {
      params.set('deviceCount', firmwareDetail.deviceCount.toString());
    }
    if (firmwareDetail.upgradeSuccess !== undefined) {
      params.set('upgradeSuccess', firmwareDetail.upgradeSuccess.toString());
    }
    if (firmwareDetail.upgradeProgress !== undefined) {
      params.set('upgradeProgress', firmwareDetail.upgradeProgress.toString());
    }
    if (firmwareDetail.upgradeFailed !== undefined) {
      params.set('upgradeFailed', firmwareDetail.upgradeFailed.toString());
    }

    console.log('🔍 传递的升级统计数据:', {
      deviceCount: firmwareDetail.deviceCount,
      upgradeSuccess: firmwareDetail.upgradeSuccess,
      upgradeProgress: firmwareDetail.upgradeProgress,
      upgradeFailed: firmwareDetail.upgradeFailed,
    });
  } catch (error) {
    console.error('❌ 获取升级统计数据失败:', error);
  }

  const targetUrl = `/manage/iotFirmwareDetail?${params.toString()}`;
  console.log('🔍 构建的URL参数:', params.toString());
  console.log(' 准备跳转到:', targetUrl);
  console.log('🔍 记录的所有字段:', Object.keys(record));
  console.log('🔍 字段详情检查:', {
    firmwareId: record.firmwareId,
    firmwareName: record.firmwareName,
    productKey: record.productKey,
    productName: record.productName,
    firmwareVersion: record.firmwareVersion,
    firmwareType: record.firmwareType,
    isLatest: record.isLatest,
    createdAt: record.createdAt,
    remark: record.remark,
  });
  console.log('🔍 传递的参数:', {
    firmwareId: record.firmwareId,
    firmwareName: record.firmwareName,
    productKey: record.productKey,
    productName: record.productName,
    firmwareVersion: record.firmwareVersion,
    createdAt: record.createdAt,
    remark: record.remark,
  });

  try {
    router.push(targetUrl);
    console.log(' 路由跳转命令已执行');
  } catch (error) {
    console.error(' 路由跳转失败:', error);
    console.log(' 使用备用方案：在新窗口打开');
    // 备用方案：在新窗口打开
    window.open(targetUrl, '_blank');
  }
}
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});
function handleAdd() {
  console.log('🔍 点击新增按钮');
  console.log('📋 当前props.productKey:', props.productKey);
  console.log('📋 当前props.productName:', props.productName);

  const drawerData = {
    update: false,
    view: false,
    productKey: props.productKey,
    productName: props.productName,
    firmwareType: props.firmwareType,
  };

  console.log('🚀 传递给edit抽屉的数据:', drawerData);
  editDrawerApi.setData(drawerData);
  editDrawerApi.open();
}
function handleEdit(row: RowType) {
  console.log('🔍 点击编辑按钮');
  console.log('📋 编辑的行数据:', row);
  console.log('📋 当前props.productKey:', props.productKey);
  console.log('📋 当前props.productName:', props.productName);

  const drawerData = {
    id: row.firmwareId,
    update: true,
    view: false,
    productKey: props.productKey,
    productName: props.productName,
    firmwareType: props.firmwareType,
  };

  console.log('🚀 传递给edit抽屉的数据:', drawerData);
  editDrawerApi.setData(drawerData);
  editDrawerApi.open();
}
async function handleDelete(row: RowType) {
  await Delete({ firmwareId: [row.firmwareId] });
  message.success("删除成功");
  await handleRefresh();
}
async function handleRefresh() {
  await gridApi.query();
}
function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const ids: string[] = [];
  for (const row of rows) {
    ids.push(row.firmwareId);
  }
  if (ids.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await Delete({ firmwareId: ids });
      message.success("删除成功");
      await handleRefresh();
    },
  });
}
async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '固件信息表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success("导出成功");
}
</script>
<template>
  <div class="p-0 m-0 ">
    <Grid table-title="固件信息表">
      <template #toolbar-tools>
        <Button class="mr-2 flex items-center " type="primary" :icon="h(MdiPlus)" @click="handleAdd"
          v-access:code="'cpm:manage:iotFirmware:edit'">
          新增
        </Button>
      </template>
      <template #action="{ row }">
        <div class="flex items-center">
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handlePreview(row)"
            v-access:code="'cpm:manage:iotFirmware:view'">
            查看
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row)"
            v-access:code="'cpm:manage:iotFirmware:edit'">
            修改
          </Button>
          <AccessControl :codes="['cpm:manage:iotFirmware:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
    <EditDrawer @reload="handleRefresh" />
  </div>
</template>