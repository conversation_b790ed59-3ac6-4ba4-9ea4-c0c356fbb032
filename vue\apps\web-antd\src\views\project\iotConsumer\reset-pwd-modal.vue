<script setup lang="ts">
import { useVbenModal, z } from '@vben/common-ui';
import { message } from 'ant-design-vue';
import { useVbenForm } from '#/adapter/form';
import { ResetPassword } from '#/api/project/iotConsumer';
import { ref } from 'vue';
import { md5 } from 'js-md5';

const title = ref('');

const [BasicModal, modalApi] = useVbenModal({
  onCancel: handleCancel,
  onConfirm: handleSubmit,
  onOpenChange: (open) => {
    if (open) {
      const { record } = modalApi.getData();
      // 动态更新标题为设备名称（假设设备名称字段为 deviceName）
      title.value = `为${record.userName}重置密码`;
    }
  },
});


const [BasicForm, formApi] = useVbenForm({
  schema: [
    {
      component: 'InputPassword',
      fieldName: 'userPassword',
      label: '请输入密码',
      rules: z
        .string({ required_error: '请输入密码' })
        .min(5, '密码长度不能少于5个字符')
        .max(20, '密码长度不能超过20个字符'),
    },
    {
      component: 'InputPassword',
      label: '再次输入密码',
      dependencies: {
        rules(values) {
          return z
            .string({ message: '请输入确认密码' })
            .min(5, '密码长度不能少于5个字符')
            .max(20, '密码长度不能超过20个字符')
            .refine(
              (value: any) => value === values.userPassword,
              '新密码和确认密码不一致',
            );
        },
        triggerFields: ['confirmPassword'],
      },
      fieldName: 'confirmPassword',
    },
  ],
  showDefaultActions: false,
  commonConfig: {
    labelWidth: 80,
  },
});

async function handleSubmit() {
  try {
    modalApi.setState({ confirmLoading: true, loading: true });
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = await formApi.getValues();
    const { record } = modalApi.getData();
    data.userPassword = md5(data.userPassword);

    console.log('修改密码', record.consumerId);
    await ResetPassword({
      consumerId: record.consumerId,
      password: data.userPassword // 使用表单中的密码
    });
    handleCancel();
    message.success('密码重置成功');
  } catch (error) {
    console.error(error);
    message.error('密码重置失败'); // 添加错误提示
  } finally {
    modalApi.setState({ confirmLoading: false, loading: false });
  }
}

async function handleCancel() {
  modalApi.close();
  await formApi.resetForm();
}
</script>

<template>
  <BasicModal :close-on-click-modal="false" :fullscreen-button="false" :title=title>
    <div class="flex flex-col gap-[12px]">
      <BasicForm />
    </div>
  </BasicModal>
</template>
