<template>
    <BasicModal :close-on-click-modal="false" title="生成公式" class="w-[600px]  min-h-[400px]">
        <div class="flex flex-col gap-2">
            <div class="flex items-center mb-2">
                <span class="w-[80px] text-right mr-2">公式预览: </span><Input 
                    :value="formulaList.map((item: any) => item.value).join(' ')" readonly class="w-3/4" />
            </div>
            <div class="border border-gray-200 rounded-md p-2 gap-2">
                <div v-for="(item, index) in formulaList" :key="index" class="flex items-center grid grid-cols-4 gap-2 p-1">
                <Select :options="typeOptions" v-model:value="item.type" class="col-span-1" />
                <Select v-show="item.type === '1'" :options="propertyOptions" v-model:value="item.value"
                    class="col-span-2" />
                <Select v-show="item.type === '2'" :options="operatorOptions" v-model:value="item.value"
                    class="col-span-2" />
                <Input v-show="item.type === '3'" type="text" v-model:value="item.value" class="col-span-2" />
                <div class="col-span-1">
                    <Button type="link" @click="handleAdd(index)" class="p-0 pl-2">+添加</Button> 
                    <Button type="link" @click="handleDelete(item.id)" class="p-2 text-red-500">删除</Button>
                </div>
            </div>
            </div>
            
        </div>
    </BasicModal>
</template>
<script setup lang="ts">
import { useVbenForm, useVbenModal } from '@vben/common-ui';
import { ref } from 'vue';
import { List } from '#/api/device/iotProductModel';
import { Button, Select, Input, Divider } from 'ant-design-vue';

const emit = defineEmits<{
    confirm: [formulaList: any];
}>();

const currentProductKey = ref('');
const formulaList = ref([{
    id: 0,
    type: '1',
    value: '',
}]);
const typeOptions = [
    {
        label: '设备属性',
        value: '1',
    },
    {
        label: '运算符',
        value: '2',
    },
    {
        label: '输入',
        value: '3',
    },
];
const propertyOptions = ref([]);
const operatorOptions = [
    {
        label: '+',
        value: '+',
    },
    {
        label: '-',
        value: '-',
    },
    {
        label: '*',
        value: '*',
    },
    {
        label: '/',
        value: '/',
    },
    {
        label: '%',
        value: '%',
    },
    {
        label: '(',
        value: '(',
    },
    {
        label: ')',
        value: ')',
    },
    
];

type ModalProps = {
    productKey: string;
};

function handleOk() {
    emit('confirm', formulaList.value.map((item: any) => item.value));
    modalApi.close();
}

function handleDelete(index: number) {
    formulaList.value = formulaList.value.filter((item: any, i: number) => item.id !== index);
}

//在index后面插入一个元素
function handleAdd(index: number) {
    const maxId = formulaList.value.reduce((max, item) => Math.max(max, item.id), 0);
    const suffix = formulaList.value.slice(index + 1);
    formulaList.value = formulaList.value.slice(0, index + 1).concat({
        id: maxId + 1,
        type: '1',
        value: '',
    });
    formulaList.value = formulaList.value.concat(suffix);
}

const [BasicModal, modalApi] = useVbenModal({
    onCancel: () => {
        formulaList.value = [];
        modalApi.close();
    },
    onConfirm: handleOk,
    async onOpenChange(isOpen) {
        // 打开的时候loading CropperImage组件加载完毕关闭loading
        if (!isOpen) {
            return null;
        }
        modalApi.setState({ confirmLoading: true, loading: true });
        const { productKey } = modalApi.getData() as ModalProps;
        currentProductKey.value = productKey;
        formulaList.value = [{
            id: 0,
            type: '1',
            value: '',
        }];
        await getPropertyOptions();
        modalApi.setState({ confirmLoading: false, loading: false });
    },
});

async function getPropertyOptions() {
    const res = await List({
        productKey: currentProductKey.value,
        type: 'property',
        page: 1,
        pageSize: 1000,
    });
    if (res.items) {
        propertyOptions.value = res.items.filter((item: any) => item.fromType !== 'cloud_calculate').map((item: any) => ({
            label: item.modelName,
            value: item.modelKey,
        })) || [];
    } else {
        propertyOptions.value = [];
    }
}
</script>
