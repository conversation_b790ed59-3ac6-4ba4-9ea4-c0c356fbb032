<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import { Button, message, Card, RangePicker, Space } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { Statistic } from '#/api/device/dashboard';
import { MsgAndAlarmCount, AlarmTypeStatistics } from '#/api/device/dashboard';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { IconifyIcon } from '@vben/icons';
import type { EchartsUIType } from '@vben/plugins/echarts';
import AlarmLog from './AlarmLog/alarmlog.vue';
import { router } from '#/router';
import { Page, VbenCountToAnimator } from '@vben/common-ui';
import TiandituMap from '../iotDevice/map/index.vue';
import { TIANDITU_CONFIG } from '#/config/tianditu';
import { List } from '#/api/device/iotDevice';

const product_total = ref(0);
const product_enabled = ref(0);
const product_disabled = ref(0);
const device_online = ref(0);
const device_enabled = ref(0);
const device_disabled = ref(0);
const msg_total = ref(0);
const msg_this_month = ref(0);
const msg_this_day = ref(0);
const alarm_total = ref(0);
const alarm_this_month = ref(0);
const alarm_this_day = ref(0);

import { emitter } from '../../_core/profile/mitt';

async function refreshAll() {
  // 设备统计
  const res = await Statistic({});
  product_total.value = res.product_total;
  product_enabled.value = res.product_enabled;
  product_disabled.value = res.product_disabled;
  device_online.value = res.device_online;
  device_enabled.value = res.device_enabled;
  device_disabled.value = res.device_disabled;

  msg_total.value = res.msg_total;
  msg_this_month.value = res.msg_this_month;
  msg_this_day.value = res.msg_this_day;

  alarm_total.value = res.alarm_total;
  alarm_this_month.value = res.alarm_this_month;
  alarm_this_day.value = res.alarm_this_day;

  // 消息/报警折线图
  const [start, end] = dateRange.value;
  await fetchChartData(start, end);
  renderChart();

  // 报警类型饼图
  await fetchAlarmTypeData();

  // 地图设备位置
  await fetchDeviceLocations();
}

onMounted(() => {
  refreshAll();
  // 监听报警推送，自动刷新首页所有数据
  emitter.on('wsDataAlarm', refreshAll);
});

import { onUnmounted } from 'vue';
onUnmounted(() => {
  emitter.off('wsDataAlarm', refreshAll);
});

//第二行的折线图和饼图

// 饼图
const pieRef = ref();
const { renderEcharts: renderPieChart } = useEcharts(pieRef);
async function fetchAlarmTypeData() {
  try {
    const res = await AlarmTypeStatistics({});
    // 假设返回结构为 { data: [{ alarmLevelName, alarmNum }, ...] }
    const chartData = (res.data || []).map((item: any) => ({
      name: item.alarmLevelName,
      value: item.alarmNum,
    }));
    renderPie(chartData);
  } catch (e) {
    message.error('获取报警类型统计失败');
  }
}
function renderPie(data: { value: number; name: string }[]) {
  renderPieChart({
    color: ['#A3D39C', '#8EC1D6', '#F18F8F'], // 提醒通知-绿色，轻微问题-蓝色，严重警告-红色
    tooltip: { trigger: 'item' },
    legend: {
      top: '5%',
      left: 'center'
    },
    series: [
      {
        name: '报警类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: { show: false, position: 'center' },
        emphasis: {
          label: {
            show: true,
            fontSize: 24,
            fontWeight: 'bold'
          }
        },
        labelLine: { show: false },
        data: data && data.length > 0 ? data : []
      }
    ]
  });
}

//折线图的
const rangeList = [
  { label: '最近24小时', value: '24h' },
  { label: '最近一周', value: '7d' },
  { label: '最近一个月', value: '30d' },
];

const activeRange = ref('24h'); // 默认选中

// 默认时间也是整点显示
const end = dayjs().set('minute', 0).set('second', 0);
const start = end.subtract(24, 'hour').set('minute', 0).set('second', 0);

const dateRange = ref<[Dayjs, Dayjs]>([start, end]);

// EchartsUI相关
const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

function getRangeByType(type: string): [Dayjs, Dayjs] {
  const end = dayjs().set('minute', 0).set('second', 0);
  let start;
  if (type === '24h') start = end.subtract(24, 'hour');
  else if (type === '7d') start = end.subtract(7, 'day');
  else if (type === '30d') start = end.subtract(30, 'day');
  else start = end;
  // 保证start和end都是整点
  start = start.set('minute', 0).set('second', 0);
  return [start, end];
}
function checkRange(val: [Dayjs, Dayjs] | undefined) {
  if (!val || val.length !== 2 || !val[0] || !val[1]) return false;
  const [start, end] = val;
  // 超过3个月
  if (end.diff(start, 'month', true) > 3) {
    // 自动修正为结束时间往前推3个月
    const newStart = end.subtract(3, 'month');
    dateRange.value = [newStart, end];
    message.warning('时间区间不能超过3个月，已自动修正！');
    return true;
  }
  return false;
}
// 按钮切换时，时间控件联动
function onRangeChange(type: string) {
  activeRange.value = type;
  dateRange.value = getRangeByType(type);
  // 这里可以触发数据刷新
}
function onDateChange(dates: string | any[]) {
  if (dates && dates.length === 2) {
    // 自动把分钟和秒设为00
    const normalizedDates: [Dayjs, Dayjs] = [
      dayjs(dates[0]).set('minute', 0).set('second', 0),
      dayjs(dates[1]).set('minute', 0).set('second', 0)
    ];
    if (!checkRange(normalizedDates)) {
      dateRange.value = normalizedDates;
    }
  }
}
function onCalendarChange(
  values: [string, string] | [Dayjs, Dayjs],
  formatString: [string, string],
  info: any
) {
  // 只处理 Dayjs 类型
  if (Array.isArray(values) && values[0] && typeof values[0] !== 'string' && values[1] && typeof values[1] !== 'string') {
    const [start, end] = values as [Dayjs, Dayjs];
    // 校验区间
    if (end.diff(start, 'month', true) > 3) {
      const newStart = end.subtract(3, 'month');
      dateRange.value = [newStart, end];
      message.warning('时间区间不能超过3个月，已自动修正！');
    }
  }
}
// 限制最大选择3个月
function disabledDate(current: dayjs.Dayjs) {
  const range = dateRange.value;
  if (!range || !range[0] || !range[1]) {
    return false;
  }
  const [start] = range;
  const max = start.add(3, 'month');
  return current.isAfter(max) || current.isBefore(start.subtract(3, 'month'));
}

// 这里可以添加折线图和饼图的配置
// 监听时间区间变化
const chartData = ref({
  x: [] as string[],
  msg: [] as number[],
  alarm: [] as number[],
});

watch(dateRange, ([start, end]) => {
  fetchChartData(start, end).then(renderChart);
});

// 获取数据
async function fetchChartData(start: Dayjs, end: Dayjs) {
  try {
    const res = await MsgAndAlarmCount({
      startTime: start.format('YYYY-MM-DD HH:mm:ss'),
      endTime: end.format('YYYY-MM-DD HH:mm:ss'),
    });
    chartData.value.x = res.timeList || [];
    chartData.value.msg = res.msgCountList || [];
    chartData.value.alarm = res.alarmCountList || [];
  } catch (e) {
    message.error('获取数据失败');
  }
}

// 渲染Echarts
function renderChart() {
  renderEcharts({
    xAxis: {
      type: 'category',
      data: chartData.value.x,
      name: '时间',
      nameLocation: 'end',
      nameGap: 30, // 增大与轴的距离
      axisLabel: {
        interval: 0,
        rotate: chartData.value.x.length > 8 ? 30 : 0,
        margin: 16, // 增大标签与轴线距离
        overflow: 'break', // 自动换行
      },
      boundaryGap: false,
    },
    yAxis: {
      type: 'value',
      name: '条数',
      splitLine: {
        show: false, // 不显示横线
      },
    },
    tooltip: { trigger: 'axis' },
    legend: { data: ['消息数'] },
    grid: {
      left: 8,
      right: 60, // 增大右边距，防止最后一个被裁切
      bottom: 24,
      containLabel: true,
    },
    series: [
      {
        name: '消息数',
        data: chartData.value.msg,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(90,177,239,0.4)' }, // 顶部颜色
              { offset: 1, color: 'rgba(90,177,239,0)' }    // 底部透明
            ]
          }
        },
        itemStyle: { color: '#5ab1ef' }
      }
    ],
  });
}
const goToMoreInfo = () => {
  router.push('/iotChannel/iotAlarmLog');
};

// 地图相关
const mapRef = ref();
const deviceLocations = ref<Array<{
  deviceId: string;
  deviceName: string;
  lng: number;
  lat: number;
  status: 'online' | 'offline' | 'alarm' | 'inactive';
  productName?: string;
}>>([]);

// 获取设备位置数据
async function fetchDeviceLocations() {
  try {
    // 调用设备列表接口，获取有经纬度的设备
    const response = await List({
      page: 1,
      pageSize: 1000, // 获取足够多的数据，确保包含所有设备
    });

    if (response && response.items && response.items.length > 0) {
      // 过滤出有经纬度的设备，并转换数据格式
      const devicesWithLocation = response.items
        .filter((device: any) => device.longitude && device.latitude)
        .map((device: any) => {
          // 根据设备状态和报警状态判断最终状态
          let status: 'online' | 'offline' | 'alarm' | 'inactive' = 'inactive';

          if (device.alarmState === 1) {
            // 有报警状态，优先显示报警 - 红色
            status = 'alarm';
          } else if (device.deviceState === 2 && device.alarmState === 0) {
            // 设备状态为禁用且无报警 - 在线状态，绿色
            status = 'online';
          } else if (device.deviceState === 3) {
            // 设备状态为离线 - 黄色
            status = 'offline';
          } else if (device.deviceState === 1) {
            // 设备状态为未激活 - 灰色
            status = 'inactive';
          }

          return {
            deviceId: device.deviceId,
            deviceName: device.deviceName,
            lng: device.longitude,
            lat: device.latitude,
            status: status,
            productName: device.productName || '未知产品'
          };
        });

      deviceLocations.value = devicesWithLocation;
      console.log(`设备位置数据加载完成: 总共${response.total}个设备，有位置信息的${devicesWithLocation.length}个设备`, deviceLocations.value);
    } else {
      console.log('没有找到设备数据');
      deviceLocations.value = [];
    }
  } catch (error) {
    console.error('获取设备位置失败:', error);
    message.error('获取设备位置失败');
    // 如果接口调用失败，设置空数组
    deviceLocations.value = [];
  }
}

// 地图就绪事件处理
const onMapReady = (map: any) => {
  console.log('天地图初始化完成:', map);
  // 地图初始化完成后，获取设备位置数据
  fetchDeviceLocations();
};

// 标记点击事件处理
const onMarkerClick = (device: any) => {
  console.log('设备标记被点击:', device);
  message.info(`设备: ${device.deviceName} (${device.status})`);
  // 这里可以添加更多逻辑，比如显示设备详情弹窗等
};

// 首次渲染
onMounted(() => {
  // 页面加载时，主动获取一次“最近24小时”数据并渲染
  const [start, end] = dateRange.value;
  fetchChartData(start, end).then(() => {
    renderChart();
  });
  fetchAlarmTypeData();
});
</script>

<template>
  <Page auto-content-height>
    <!-- 第一行展示的四个卡片 -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card class="w-full">
        <div class="card-title">产品</div>
        <div class="card-row">
          <div class="card-value">
            <VbenCountToAnimator :end-val="product_total" :start-val="1" :duration="1500" class="text-3xl font-bold"
              prefix="" />
          </div>
          <div class="icon-bg">
            <IconifyIcon icon="ant-design:deployment-unit-outlined" style="font-size:32px;color:#1890ff;" />
          </div>
        </div>
        <div class="card-status">
          <span class="dot green"></span>
          <span class="status-label">启用 {{ product_enabled }}</span>
          <span class="dot gray"></span>
          <span class="status-label">停用 {{ product_disabled }}</span>
        </div>
      </Card>
      <Card class="w-full">
        <div class="card-title">在线设备</div>
        <div class="card-row">
          <div class="card-value">
            <VbenCountToAnimator :end-val="device_online" :start-val="1" :duration="1500" class="text-3xl font-bold"
              prefix="" />
          </div>
          <div class="icon-bg">
            <IconifyIcon icon="mdi:router-wireless" style="font-size:32px;color:#1890ff;" />
          </div>
        </div>
        <div class="card-status">
          <span class="dot green"></span>
          <span class="status-label">启用 {{ device_enabled }}</span>
          <span class="dot gray"></span>
          <span class="status-label">停用 {{ device_disabled }}</span>
        </div>
      </Card>
      <Card class="w-full">
        <div class="card-title">设备消息</div>
        <div class="card-row">
          <div class="card-value">
            <VbenCountToAnimator :end-val="msg_total" :start-val="1" :duration="1500" class="text-3xl font-bold"
              prefix="" />
          </div>
          <div class="icon-bg">
            <IconifyIcon icon="ant-design:message-outlined" style="font-size:32px;color:#1890ff;" />
          </div>
        </div>
        <div class="card-status">
          <span class="dot gray"></span>
          <span class="status-label">本月 {{ msg_this_month }}</span>
          <span class="dot blue"></span>
          <span class="status-label">今日 {{ msg_this_day }}</span>
        </div>
      </Card>
      <Card class="w-full">
        <div class="card-title">设备报警</div>
        <div class="card-row">
          <div class="card-value">
            <VbenCountToAnimator :end-val="alarm_total" :start-val="1" :duration="1500" class="text-3xl font-bold"
              prefix="" />
          </div>
          <div class="icon-bg">
            <IconifyIcon icon="mdi:alarm-light-outline" style="font-size:32px;color:#1890ff;" />
          </div>
        </div>
        <div class="card-status">
          <span class="dot gray"></span>
          <span class="status-label">本月 {{ alarm_this_month }}</span>
          <span class="dot red"></span>
          <span class="status-label">今日 {{ alarm_this_day }}</span>
        </div>
      </Card>
    </div>
    <!-- 第二行展示的折线图和饼图 -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4 mt-4">
      <!-- 折线图卡片，占3/4 -->
      <Card style="height: 400px;" class="lg:col-span-3">
        <div class="flex-row-between">
          <div class="card-title">设备消息</div>
          <div class="right-controls">
            <!-- 使用Ant Design的ButtonGroup组件 -->
            <Space>
              <Button.Group>
                <Button v-for="item in rangeList" :key="item.value"
                  :type="activeRange === item.value ? 'primary' : 'default'" @click="onRangeChange(item.value)">
                  {{ item.label }}
                </Button>
              </Button.Group>

              <RangePicker v-model:value="dateRange" :show-time="{ format: 'HH:mm:ss', minuteStep: 60, secondStep: 60 }"
                :format="'YYYY-MM-DD HH:mm:ss'" :disabled="false" :disabled-date="disabledDate" @onChange="onDateChange"
                @calendarChange="onCalendarChange" style="width: 400px" />
            </Space>
          </div>
        </div>
        <div class="chart-row">
          <!-- 折线图靠左 -->
          <div class="chart-left">
            <EchartsUI ref="chartRef" style="width: 100%; height: 320px;" />
          </div>
        </div>
      </Card>
      <!-- 统计图卡片，占1/4 -->
      <Card style="height: 400px;" class="lg:col-span-1 ">
        <div class="card-title">报警类型</div>
        <EchartsUI ref="pieRef" style="width: 100%; height: 340px;" />
      </Card>
    </div>
    <!-- 第三行展示的报警信息列表 -->
    <Card style="height: auto; min-height: 0; max-height: 380px; margin-bottom: 24px; " class="mt-4">
      <div style="display: flex; align-items: center; justify-content: space-between;">
        <div class="card-title">报警信息</div>
        <Button type="link" @click="goToMoreInfo">更多信息</Button>
      </div>
      <AlarmLog />
    </Card>
    <Card style="height: auto;" class="lg:col-span-1 ">
      <div class="card-title">设备地图</div>
      <TiandituMap ref="mapRef" :width="'100%'" :height="'600px'" :center="TIANDITU_CONFIG.DEFAULT_CONFIG.center"
        :zoom="TIANDITU_CONFIG.DEFAULT_CONFIG.zoom" :show-controls="false" :markers="deviceLocations"
        :show-device-info="true" @map-ready="onMapReady" @marker-click="onMarkerClick" />
    </Card>
  </Page>
</template>

<style scoped>
.card-title {
  font-size: 24px;
  margin-bottom: 8px;
  font-weight: 600;
  /* 半粗体 */
  letter-spacing: -0.01em;
  /* 紧凑字距 */
  color: #020817;
  /* 字体颜色 */
}

.card-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
  color: #222;
}

.card-status {
  font-size: 14px;
  color: #555;
}

.card-status .status-label {
  color: #222;
  /* 单独加深数字和文字 */
  font-weight: 500;
}

.dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
  margin-left: 12px;
}

.dot.green {
  background: #52c41a;
}

.dot.gray {
  background: #bfbfbf;
}

.icon-bg {
  background: #e6f7ff;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-num {
  color: #222;
  font-weight: bold;
  margin-left: 2px;
  margin-right: 12px;
}

.dot.red {
  background: #ff4d4f;
}

.dot.blue {
  background: #1890ff;
}

/* 第二行的折线图和饼图样式 */
.button-group {
  display: flex;
  gap: 8px;
}

/* 移除旧的按钮样式 */
.range-btn-group {
  display: none;
}

.range-btn {
  display: none;
}

/* 右侧控件样式调整 */
.right-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 确保flex布局正确 */
.flex-row-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .right-controls {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  .ant-btn-group {
    width: 100%;
    display: flex;
  }

  .ant-btn-group .ant-btn {
    flex: 1;
  }

  .ant-picker {
    width: 100% !important;
    margin-top: 8px;
  }

  .ant-space {
    width: 100%;
    flex-direction: column;
  }

  .ant-space-item {
    width: 100%;
    margin-right: 0 !important;
  }
}
</style>
