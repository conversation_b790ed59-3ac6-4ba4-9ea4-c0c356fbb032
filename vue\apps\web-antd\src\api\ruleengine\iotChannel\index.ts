import { requestClient } from '#/api/request';

// 获取资源通道表列表
export function List(params: any) {
  return requestClient.get<any>('ruleengine/iotChannel/list', { params });
}
export function listNotLinked(params: any) {
  return requestClient.get<any>('ruleengine/iotChannel/listNotLinked', { params });
}
// 删除/批量删除资源通道表
export function Delete(params: any) {
  return requestClient.post<any>('ruleengine/iotChannel/delete', { ...params });
}

// 添加/编辑资源通道表
export function Edit(params: any) {
  return requestClient.post<any>('ruleengine/iotChannel/edit', { ...params });
}

// 修改资源通道表状态
export function Status(params: any) {
  return requestClient.post<any>('ruleengine/iotChannel/status', { ...params });
}

//测试资源通道表
export function TestConnect(params: any) {
  return requestClient.get<any>('ruleengine/iotChannel/testConnect', {
    params,
  });
}

// 获取资源通道表指定详情
export function View(params: any) {
  return requestClient.get<any>('ruleengine/iotChannel/view', { params });
}

// 导出资源通道表
export function Export(params: any) {
  return requestClient.post<Blob>(
    '/ruleengine/iotChannel/export',
    { ...params },
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
      responseType: 'blob',
    },
  );
}

//导出获取当前视频监控服务器类型
export function   GB28181Type(params: any) {
  return requestClient.get<any>('ruleengine/iotChannel/gb28181Type', {
    params,
  });
}

