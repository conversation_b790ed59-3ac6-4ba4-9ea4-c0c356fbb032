import { requestClient } from '#/api/request';

// 获取场景表列表
export function List(params: any) {
  return requestClient.get<any>('ruleengine/iotScene/list', { params });
}

// 删除/批量删除场景表
export function Delete(params: any) {
  return requestClient.post<any>('ruleengine/iotScene/delete', { ...params });
}

// 添加/编辑场景表
export function Edit(params: any) {
  return requestClient.post<any>('ruleengine/iotScene/edit', { ...params });
}

// 修改场景表状态
export function Status(params: any) {
  return requestClient.post<any>('ruleengine/iotScene/status', { ...params });
}

// 获取场景表指定详情
export function View(params: any) {
  return requestClient.get<any>('ruleengine/iotScene/view', { params });
}

// 导出场景表
export function Export(params: any) {
  return requestClient.post<Blob>('/ruleengine/iotScene/export', { ...params }, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}

//ModbusRTU指令预览
export function ModbusRTU(params: any) {
  return requestClient.post<any>('ruleengine/iotScene/genModbusRTUMsg', { ...params });
}

// 获取产品关联的场景列表
export function SceneListByProduct(params: any) {
  return requestClient.get<any>('ruleengine/iotScene/sceneListByProduct', { params });
}

// 根据场景获取报警配置表列表
// /api/v1/ruleengine/iotAlarmConfig/alarmConfigListByScene
export function AlarmConfigListByScene(params: any) {
  return requestClient.get<any>('ruleengine/iotAlarmConfig/alarmConfigListByScene', { params });
}
