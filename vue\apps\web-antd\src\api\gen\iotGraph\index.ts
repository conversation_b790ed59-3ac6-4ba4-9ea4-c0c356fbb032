import { requestClient } from '#/api/request';

// 获取平面图表列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotGraph/list', { params });
}

// 删除/批量删除平面图表
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotGraph/delete', { ...params });
}

// 添加/编辑平面图表
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotGraph/edit', { ...params });
}

// 获取平面图表指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotGraph/view', { params });
}

// 导出平面图表
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotGraph/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}