<script setup lang="ts">
import type { SysClient } from '#/api/system/client';
import { computed, nextTick, ref } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';
import { useVbenForm } from '#/adapter/form';
import { Description, useDescription } from '#/components/description';
import { viewSchema, type RowType } from './model';
import { View } from '#/api/manage/iotNotifyTemplate';
import type { DescItem } from '#/components/description';
import { DictEnum } from '@vben/constants';
import { renderDict } from '#/utils';
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onOpenChange: handleOpenChange,
});
const [registerDescription, { setDescProps }] = useDescription({
  column: 1,
  schema: viewSchema,
});
async function handleOpenChange(open: boolean) {
  if (!open) {
    return null;
  }
  const { record } = drawerApi.getData() as { record: RowType };
  const record2 = await View({ id: record.id });
  let currentSchema = cloneDeep(viewSchema);
  
  if (record2.channelType == '1' && record2.serviceType == '1') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'smsAli.phoneNumber',
        label: '下发手机号码',
        render(_, data) {
          return data.smsAli.phoneNumber;
        },
      },
      {
        field: 'smsAli.templateId',
        label: '模板 ID',
        render(_, data) {
          return data.smsAli.templateId;
        },
      },
      {
        field: 'smsAli.signName',
        label: '短信签名内容',
          render(_, data) {
          return data.smsAli.templateId;
        },
      },
      {
        field: 'smsAli.content',
        label: '短信内容',
        render(_, data) {
          return data.smsAli.templateId;
        },
      },
    ];
  }
  if (record2.channelType == '1' && record2.serviceType == '2') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'smsTencent.phoneNumber',
        label: '下发手机号码',
        render(_, data) {
          return data.smsTencent.phoneNumber;
        },
      },
      {
        field: 'smsTencent..sdkAppId',
        label: '短信 SdkAppId',
        render(_, data) {
          return data.smsTencent.sdkAppId;
        },
      },
      {
        field: 'smsTencent.templateId',
        label: '模板 ID',
        render(_, data) {
          return data.smsTencent.templateId;
        },
      },
      {
        field: 'smsTencent.signName',
        label: '短信签名内容',
        render(_, data) {
          return data.smsTencent.signName;
        },
      },
      {
        field: 'smsTencent.content',
        label: '短信内容',
        render(_, data) {
          return data.smsTencent.content;
        },
      },
    ];
  }

  if (record2.channelType == '2' && record2.serviceType == '1') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'wechartApp.toUserId',
        label: '用户ID',
        render(_, data) {
          return data.wechartApp.toUserId;
        },
      },
      {
        field: 'wechartApp.templateId',
        label: '模板 ID',
        render(_, data) {
          return data.wechartApp.templateId;
        },
      },
      {
        field: 'wechartApp.url',
        label: '跳转链接',
        render(_, data) {
          return data.wechartApp.url;
        },
      },
      {
        field: 'wechartApp.content',
        label: '内容',
        render(_, data) {
          return data.wechartApp.content;
        },
      },
    ];
  }

  if (record2.channelType == '2'&& record2.serviceType == '2') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'wechartCorp.msgType',
        label: '请输入配置内容',
        render(_, data) {
          return data.wechartCorp.msgType;
        },
      },
      {
        field: 'wechartCorp.toUserId',
        label: '用户ID',
        render(_, data) {
          return data.wechartCorp.toUserId;
        },
      },
      {
        field: 'wechartCorp.msgTitle',
        label: '消息标题',
        render(_, data) {
          return data.wechartCorp.msgTitle;
        },
      },
      {
        field: 'wechartCorp.msgContent',
        label: '消息内容',
        render(_, data) {
          return data.wechartCorp.msgContent;
        },
      },
      {
        field: 'wechartCorp.url',
        label: '跳转链接',
        render(_, data) {
          return data.wechartCorp.url;
        },
      },
      {
        field: 'wechartCorp.img',
        label: '图片链接',
        render(_, data) {
          return data.wechartCorp.img;
        },
      },
    ];
  }

  //tcp或udp桥接参数查看
  if (record2.channelType == '2' && record2.serviceType == '4') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'wechartOfficial.templateId',
        label: '模板 ID',
        render(_, data) {
          return data.wechartOfficial.templateId;
        },
      },
      {
        field: 'wechartOfficial.appId',
        label: '跳转小程序appID',
        render(_, data) {
          return data.wechartOfficial.appId;
        },
      },
      {
        field: 'wechartOfficial.appPath',
        label: '跳转小程序路径',
        render(_, data) {
          return data.wechartOfficial.appPath;
        },
      },
      {
        field: 'wechartOfficial.content',
        label: '模板内容',
        render(_, data) {
          return data.wechartOfficial.content;
        },
      },
      {
        field: 'wechartOfficial.msgTitle',
        label: '消息标题',
        render(_, data) {
          return data.wechartOfficial.msgTitle;
        },
      },
      {
        field: 'wechartOfficial.msgContent',
        label: '消息内容',
        render(_, data) {
          return data.wechartOfficial.msgContent;
        },
      },
      {
        field: 'wechartOfficial.url',
        label: '跳转链接',
        render(_, data) {
          return data.wechartOfficial.url;
        },
      },
      {
        field: 'wechartOfficial.img',
        label: '图片链接',
        render(_, data) {
          return data.wechartOfficial.img;
        },
      },
    ];
  }

  //Websocket桥接参数查看
  if (record2.channelType == '3'&& record2.serviceType == '1') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'voiceAli.phoneNumber',
        label: '下发手机号码',
        render(_, data) {
          return data.voiceAli.phoneNumber;
        },
      },
      {
        field: 'voiceAli.templateId',
        label: '模板 ID',
        render(_, data) {
          return data.voiceAli.templateId;
        },
      },
      {
        field: 'voiceAli.content',
        label: '模板内容',
        render(_, data) {
          return data.voiceAli.content;
        },
      },
      {
        field: 'voiceAli.palyCount',
        label: '播放次数1 - 3',
        render(_, data) {
          return data.voiceAli.content;
        },
      },
      {
        field: 'voiceAli.palyVolume',
        label: '播放音量0 - 100',
        render(_, data) {
          return data.voiceAli.content;
        },
      },
      {
        field: 'voiceAli.playRate',
        label: '语速控制-500 - 500',
        render(_, data) {
          return data.voiceAli.palyCount;
        },
      },
    ];
  }

  //数据库参数查看
  if (record2.channelType == '3'&& record2.serviceType == '2') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'voiceTencent.phoneNumber',
        label: '下发手机号码',
        render(_, data) {
          return data.voiceTencent.phoneNumber;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'voiceTencent.sdkAppId',
        label: 'SdkAppId',
        render(_, data) {
          return data.voiceTencent.sdkAppId;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'voiceTencent.templateId',
        label: '模板 ID',
        render(_, data) {
          return data.voiceTencent.templateId;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'voiceTencent.content',
        label: '模板内容',
        render(_, data) {
          return data.voiceTencent.content;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
    ];
  }
  if (record2.channelType == '4') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'email.toMail',
        label: '发送邮箱',
        render(_, data) {
          return data.email.toMail;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'email.msgTitle',
        label: '消息标题',
        render(_, data) {
          return data.email.msgTitle;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'email.msgContent',
        label: '消息内容',
        render(_, data) {
          return data.email.msgContent;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'email.file',
        label: '附件',
        render(_, data) {
          return data.email.file;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
    ];
  }
  if (record2.channelType == '5') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'ding.msgType',
        label: '消息类型',
        render(_, data) {
          return data.ding.msgType;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'ding.toDeptId',
        label: '部门ID',
        render(_, data) {
          return data.ding.toDeptId;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'ding.isAllUser',
        label: '是否所有人',
        render(_, data) {
          return data.ding.isAllUser;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'ding.toUserId',
        label: '员工ID',
        render(_, data) {
          return data.ding.toUserId;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'ding.msgTitle',
        label: '消息标题',
        render(_, data) {
          return data.ding.msgTitle;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'ding.msgContent',
        label: '消息内容',
        render(_, data) {
          return data.ding.msgContent;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'ding.img',
        label: '图片链接',
        render(_, data) {
          return data.ding.img;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'ding.url',
        label: '跳转链接',
        render(_, data) {
          return data.ding.url;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
    ];
  }
  // setDescProps({ data: record2 }, true);
  setDescProps({ data: record2, schema: currentSchema }, true);
}
</script>
<template>
  <BasicDrawer :footer="false" class="w-[600px]" title="查看">
    <Description @register="registerDescription"></Description>
  </BasicDrawer>
</template>
