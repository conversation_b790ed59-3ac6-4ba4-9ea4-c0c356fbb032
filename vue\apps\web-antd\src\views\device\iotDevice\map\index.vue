<template>
  <div class="tianditu-map-container">
    <div ref="mapContainer" :style="{ width: width, height: height }" class="tianditu-map"></div>

    <!-- 地图控制面板 -->
    <div class="map-controls" v-if="showControls">
      <div class="control-group">
        <button @click="zoomIn">放大</button>
        <button @click="zoomOut">缩小</button>
        <button @click="resetView">重置</button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="map-loading">
      <div class="loading-spinner"></div>
      <span>地图加载中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import { getTiandituJsApiUrl, TIANDITU_CONFIG } from '#/config/tianditu';
import type { DeviceLocation } from './types';

// 定义组件属性
interface Props {
  width?: string;
  height?: string;
  center?: [number, number]; // [经度, 纬度]
  zoom?: number;
  mapType?: 'vec' | 'img' | 'ter'; // 矢量、影像、地形
  showControls?: boolean;
  apiKey?: string;
  disableAutoCenter?: boolean; // 禁用自动居中
  markers?: DeviceLocation[]; // 设备标记点数据
  showDeviceInfo?: boolean; // 是否显示设备信息弹窗
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  center: () => TIANDITU_CONFIG.DEFAULT_CONFIG.center,
  zoom: TIANDITU_CONFIG.DEFAULT_CONFIG.zoom,
  mapType: TIANDITU_CONFIG.DEFAULT_CONFIG.mapType,
  showControls: true,
  apiKey: TIANDITU_CONFIG.API_KEY,
  disableAutoCenter: false,
  markers: () => [],
  showDeviceInfo: true
});

// 定义事件
const emit = defineEmits<{
  mapReady: [map: any];
  mapClick: [event: any];
  mapMoveEnd: [event: any];
  zoomEnd: [event: any];
  markerClick: [device: DeviceLocation];
  markerHover: [device: DeviceLocation];
}>();

// 响应式数据
const mapContainer = ref<HTMLDivElement>();
const loading = ref(true);
const currentMapType = ref(props.mapType);
let map: any = null;
let T: any = null; // 天地图API对象
const markers = ref<any[]>([]); // 存储地图上的标记点实例
const labels = ref<any[]>([]); // 存储地图上的文字标签实例

// 监听属性变化
watch(() => props.center, (newCenter) => {
  if (map && newCenter && !props.disableAutoCenter) {
    const point = new T.LngLat(newCenter[0], newCenter[1]);
    map.centerAndZoom(point, props.zoom);
  }
});

watch(() => props.zoom, (newZoom) => {
  if (map && newZoom) {
    map.setZoom(newZoom);
  }
});

// 监听标记点数据变化
watch(() => props.markers, (newMarkers) => {
  if (map && T && newMarkers) {
    updateMarkers(newMarkers);
  }
}, { deep: true });

// 加载天地图API
const loadTiandituAPI = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (window.T) {
      console.log('✅ 天地图API已存在，直接使用');
      T = window.T;
      resolve();
      return;
    }

    console.log('🔄 开始加载天地图API...');
    const apiUrl = getTiandituJsApiUrl();
    console.log('📡 天地图API URL:', apiUrl);

    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = apiUrl;
    script.onload = () => {
      console.log('✅ 天地图API加载成功');
      T = window.T;
      if (T) {
        console.log('✅ 天地图对象可用:', typeof T);
        resolve();
      } else {
        console.error('❌ 天地图对象未找到');
        reject(new Error('天地图对象未找到'));
      }
    };
    script.onerror = (error) => {
      console.error('❌ 天地图API加载失败:', error);
      reject(new Error('天地图API加载失败'));
    };
    document.head.appendChild(script);
  });
};

// 初始化地图
const initMap = async () => {
  try {
    console.log('🗺️ 开始初始化天地图...');
    loading.value = true;

    // 加载天地图API
    await loadTiandituAPI();

    // 等待DOM更新
    await nextTick();

    if (!mapContainer.value) {
      throw new Error('地图容器未找到');
    }

    console.log('🗺️ 创建地图实例...');
    console.log('📍 地图中心点:', props.center);
    console.log('🔍 地图缩放级别:', props.zoom);

    // 创建地图实例
    map = new T.Map(mapContainer.value);

    // 设置中心点和缩放级别
    const centerPoint = new T.LngLat(props.center[0], props.center[1]);
    map.centerAndZoom(centerPoint, props.zoom);

    // 设置地图类型
    setMapType(props.mapType);

    // 添加地图控件
    addMapControls();

    // 绑定事件
    bindMapEvents();

    loading.value = false;

    // 如果有标记点数据，添加到地图上
    if (props.markers && props.markers.length > 0) {
      updateMarkers(props.markers);
    }

    // 触发地图就绪事件
    emit('mapReady', map);

    console.log('✅ 天地图初始化成功');
  } catch (error) {
    loading.value = false;
    console.error('❌ 天地图初始化失败:', error);
  }
};

// 设置地图类型
const setMapType = (type: 'vec' | 'img' | 'ter') => {
  if (!map || !T) return;

  console.log('设置地图类型:', type);

  try {
    // 天地图API的正确调用方式
    let mapTypeId;

    switch (type) {
      case 'vec': // 矢量地图
        mapTypeId = T.MAPTYPE_VECTOR;
        break;
      case 'img': // 影像地图
        mapTypeId = T.MAPTYPE_SATELLITE;
        break;
      case 'ter': // 地形图
        mapTypeId = T.MAPTYPE_TERRAIN;
        break;
      default:
        mapTypeId = T.MAPTYPE_VECTOR;
    }

    // 设置地图类型
    map.setMapType(mapTypeId);

    currentMapType.value = type;
    console.log(`地图类型设置为: ${type}`);
  } catch (error) {
    console.error('设置地图类型失败:', error);
    // 如果失败，使用默认类型
    currentMapType.value = 'vec';
    console.log('使用默认矢量地图类型');
  }
};

// 添加地图控件
const addMapControls = () => {
  if (!map || !T) return;

  // 添加缩放控件
  const zoomControl = new T.Control.Zoom();
  map.addControl(zoomControl);

  // 添加比例尺控件
  const scaleControl = new T.Control.Scale();
  map.addControl(scaleControl);

  // 添加版权控件
  const copyrightControl = new T.Control.Copyright();
  map.addControl(copyrightControl);
};

// 绑定地图事件
const bindMapEvents = () => {
  if (!map || !T) return;

  // 地图点击事件
  map.addEventListener('click', (event: any) => {
    emit('mapClick', event);
  });

  // 地图移动结束事件
  map.addEventListener('moveend', (event: any) => {
    emit('mapMoveEnd', event);
  });

  // 缩放结束事件
  map.addEventListener('zoomend', (event: any) => {
    emit('zoomEnd', event);
  });
};

// 地图操作方法
const zoomIn = () => {
  if (map) {
    map.zoomIn();
  }
};

const zoomOut = () => {
  if (map) {
    map.zoomOut();
  }
};

const resetView = () => {
  if (map) {
    const centerPoint = new T.LngLat(props.center[0], props.center[1]);
    map.centerAndZoom(centerPoint, props.zoom);
  }
};

// 获取地图实例（供外部调用）
const getMapInstance = () => {
  return map;
};

// 获取天地图API对象（供外部调用）
const getTiandituAPI = () => {
  return T;
};



// 清除所有标记点和标签
const clearMarkers = () => {
  // 清除标记点
  markers.value.forEach(marker => {
    map.removeOverLay(marker);
  });
  markers.value = [];

  // 清除文字标签
  labels.value.forEach(label => {
    map.removeOverLay(label);
  });
  labels.value = [];
};

// 更新标记点
const updateMarkers = (deviceList: DeviceLocation[]) => {
  if (!map || !T) return;

  // 清除现有标记点
  clearMarkers();

  // 添加新的标记点
  deviceList.forEach(device => {
    if (device.lng && device.lat) {
      const point = new T.LngLat(device.lng, device.lat);

      // 根据设备状态选择对应的图标文件
      const getIconUrl = (status: string) => {
        const iconMap = {
          online: '/map-marker/map-marker-green.png',     // 在线 - 绿色
          offline: '/map-marker/map-marker-grey.png',   // 离线 - 灰色
          inactive: '/map-marker/map-marker-yellow.png',    // 未激活 - 黄色
          alarm: '/map-marker/map-marker-red.png'         // 报警 - 红色
        };
        return iconMap[status as keyof typeof iconMap] || iconMap.inactive;
      };

      // 创建使用图片文件的图标 - 调整尺寸比例
      const icon = new T.Icon({
        iconUrl: getIconUrl(device.status || 'inactive'),
        iconSize: new T.Point(28, 28), // 调整为更合适的比例
        iconAnchor: new T.Point(10, 28) // 锚点也相应调整
      });

      const marker = new T.Marker(point, { icon });

      // 固定位置：右上角显示，贴近图标
      const offsetX = 6; // 图标右侧6像素
      const offsetY = -10; // 图标上方10像素

      // 创建文字标签 - 简单文本框，紧贴图标右上角
      const label = new T.Label({
        text: `<div style="background: rgba(255,255,255,0.9); color: #333; padding: 1px 1px; font-size: 11px; border-radius: 2px; white-space: nowrap;">${device.deviceName}</div>`,
        position: point,
        offset: new T.Point(offsetX, offsetY) // 固定右上角位置
      });

      // 添加点击事件
      marker.addEventListener('click', () => {
        emit('markerClick', device);
      });

      // 添加悬停事件
      marker.addEventListener('mouseover', () => {
        emit('markerHover', device);
      });

      // 将标记点和标签添加到地图
      map.addOverLay(marker);
      map.addOverLay(label);

      // 保存引用
      markers.value.push(marker);
      labels.value.push(label);
    }
  });

  console.log(`已添加 ${markers.value.length} 个设备标记点`);
};



// 暴露方法给父组件
defineExpose({
  getMapInstance,
  getTiandituAPI,
  setMapType,
  zoomIn,
  zoomOut,
  resetView,
  updateMarkers,
  clearMarkers
});

// 生命周期
onMounted(() => {
  initMap();
});

onBeforeUnmount(() => {
  if (map) {
    // 天地图API没有destroy方法，直接清空引用
    console.log('清理地图实例');
    map = null;
    T = null;
  }
});
</script>

<style scoped>
.tianditu-map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.tianditu-map {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.control-group {
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-group button {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.control-group button:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.control-group button.active {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.map-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1001;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
