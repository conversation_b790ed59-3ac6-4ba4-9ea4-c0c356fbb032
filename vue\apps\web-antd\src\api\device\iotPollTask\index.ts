import { requestClient } from '#/api/request';

// 获取轮训任务表列表
export function List(params:any) {
  return requestClient.get<any>('device/iotPollTask/list', { params });
}

// 删除/批量删除轮训任务表
export function Delete(params:any) {
  return requestClient.post<any>('device/iotPollTask/delete', { ...params });
}

// 添加/编辑轮训任务表
export function Edit(params:any) {
  return requestClient.post<any>('device/iotPollTask/edit', { ...params });
}

// 修改轮训任务表状态
export function Status(params:any) {
  return requestClient.post<any>('device/iotPollTask/status', { ...params });
}

// 获取轮训任务表指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotPollTask/view', { params });
}

// 导出轮训任务表
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotPollTask/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}

// 生成modbus rtu 报文
export function GenModbusRTUMsg(params:any) {
  return requestClient.post<any>('/ruleengine/iotScene/genModbusRTUMsg',  { ...params } );
}

// 修改轮训任务表是否允许该设备
export function EnableDevice(params:any) {
  return requestClient.post<any>('device/iotPollTask/enableDevice', { ...params });
}