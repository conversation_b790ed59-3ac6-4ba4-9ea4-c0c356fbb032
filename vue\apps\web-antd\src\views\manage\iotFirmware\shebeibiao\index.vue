<script lang="ts" setup>
import { useVbenModal} from '@vben/common-ui';

import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';

import { columns, type RowType } from './model';
import type { DeepPartial } from '@vben/types';
import { List,} from '#/api/device/iotDevice';
import { List as CategoryList } from '#/api/device/iotProductCategory';
import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { ref, defineProps } from 'vue';

const emit = defineEmits<{
  // 点击刷新按钮的事件
  deviceSelected: [deviceKey: string, deviceName: string];
}>();

const currentProductKey = ref('');
const gridOptions: VxeTableGridOptions<RowType> = {

  checkboxConfig: {
    highlight: true,
    range: true,
    checkField: 'checked', // 可选
  },
  rowConfig: {
    keyField: 'deviceId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let result = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
          productKey: currentProductKey.value,
        });

        let categoryArray = await CategoryList({});
        let categoryMap = categoryArray.items;
        result.items.forEach((item: any) => {
          item.categoryName = categoryMap.find(
            (i: any) => item.categoryId === i.categoryId,
          )?.categoryName;
        });
        return result;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});
//确认按钮回调函数
const onConfirm = () => {

  const checkedRecords = gridApi.grid.getCheckboxRecords();
  const deviceKeys = checkedRecords.map(item => item.deviceKey).join(',');
  const deviceNames = checkedRecords.map(item => item.deviceName).join(',');
  emit('deviceSelected', deviceKeys, deviceNames);
  modalApi.close();
};

function openModal() {
  modalApi.open();
}

defineExpose({
  openModal,
});

const [Modal, modalApi] = useVbenModal({ onConfirm: onConfirm ,
  onCancel: () => {
    modalApi.close();
  },
  onOpenChange: (isOpen) => {
    const {productKey} = modalApi.getData();
    if (productKey) {
      currentProductKey.value = productKey;
    }
    if (isOpen) {
      return null;
    }
    modalApi.close();
  },
});
</script>
<template>
  <div>
    <Modal class="h-[600px] w-[1000px]" title="选择设备">
      <div class="table-content">
        <Grid table-title="设备表">
          <template #status="{ row }">
            <Tag :color="row.status === '0' ? 'green' : 'red'">
              {{ row.status === '0' ? '正常' : '停用' }}
            </Tag>
          </template>
        </Grid>
      </div>
    </Modal>
  </div>
</template>
<style scoped>
.table-content {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
