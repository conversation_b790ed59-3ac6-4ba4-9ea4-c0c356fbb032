<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Page } from '@vben/common-ui';
import { addFullName, cloneDeep, getPopupContainer, listToTree } from '@vben/utils';
import { useVbenForm } from '#/adapter/form';
import { Card, Switch, Tabs, TabPane, Button, message, Divider, Upload, } from 'ant-design-vue';
import { View, ExportModel, Publish, Edit, ImportModel } from '#/api/device/iotProduct';
import { editSchema } from '../iotProduct/model';
import IotProductModel from '../iotProductModel/index.vue';
import { useRoute } from 'vue-router';
import { List as CategoryList } from '#/api/device/iotProductCategory';
import { downloadByData } from '#/utils/file/download';
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';
import { UploadOutlined } from '@vben/icons';
import RuleScriptTable from './RuleScriptTable.vue';
import ScriptTable from './ScriptTable.vue';
import ModbusConfig from './modbusConfig/index.vue';
import Cjt188Config from './cjt188Config/index.vue';
import IotPollTask from '../iotPollTask/tab.vue';
import ExtraConfig from './extraConfig/index.vue';
import Management from './Management/index.vue';
import SubscribableEvents from './subscribableEvents/index.vue';


const loading = ref(true);
const route = useRoute();
const productId = ref(route.query.productId);
const iotProductModel = ref(IotProductModel)
const iotProductModelFuntion = ref(IotProductModel)
const iotProductModelEvent = ref(IotProductModel)
const iotProductModelTag = ref(IotProductModel)
const isUpdate = ref(false);
const isPublished = ref(false);

//const product = ref(deepClone(await View({productId: route.query.productId})));
const product = ref({
  productId: route.query.productId,
  productKey: '',
  productName: '',
  categoryId: 0,
  categoryName: '',
  deviceType: '',
  firmwareType: 0,
  networkType: '',
  isPrivate: 0,
  transport: 0,
  channelType: 0,
  vertificateMethod: 0,
  locationWay: 0,
  imgUrl: '',
  publishStatus: 0,
  tsl: {
    properties: [],
  },
});

const currentTab = ref('a');

const currentModelType = ref('property');

async function refresh() {
  isUpdate.value = false;
  const record = await View({ productId: route.query.productId });

  // 确保 firmwareType 保持为数字类型，用于传递给子组件
  product.value = {
    ...record,
    firmwareType: typeof record.firmwareType === 'string' ? parseInt(record.firmwareType) : record.firmwareType
  };

  console.log('handleRefresh')
  console.log('product', product.value);
  console.log('product.firmwareType 类型:', typeof product.value.firmwareType, '值:', product.value.firmwareType);
  isPublished.value = product.value.publishStatus == 2;
  await setupCategorySelect();
  reloadEditForm()
  refreshIotProductModel(currentModelType.value);
}

onMounted(async () => {
  refresh();
});

function switchChange(checked: any, e: any) {
  console.log('switchChange', checked);
  Publish({
    productId: route.query.productId,
    publishStatus: checked ? 2 : 1,
  }).then(res => {
    console.log('Publish', res);
    message.success(checked ? '发布成功' : '取消发布成功');
  }).finally(() => {
    refresh();
  });
}

async function setupCategorySelect() {
  // categoryArray
  const categoryArray = await CategoryList({});
  console.log('setupCategorySelect', categoryArray);

  const categoryTree = listToTree(categoryArray.items, { id: 'categoryId', pid: 'parentId' });
  const fullCategoryTree = categoryTree;
  addFullName(fullCategoryTree, 'categoryName', ' / ');


  formApi.updateSchema([
    {
      componentProps: {
        fieldNames: {
          label: 'categoryName',
          value: 'categoryId',
        },
        getPopupContainer,
        // 设置弹窗滚动高度 默认256
        listHeight: 300,
        showSearch: true,
        treeData: fullCategoryTree,
        treeDefaultExpandAll: false,
        // 默认展开的树节点
        treeLine: { showLeafIcon: false },
        // 筛选的字段
        treeNodeFilterProp: 'categoryName',
        treeNodeLabelProp: 'fullName',
      },
      fieldName: 'categoryId',
    },
  ]);
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-9 gap-x-4',
});

async function reloadEditForm() {
  const record = await View({ productId: route.query.productId });
  await formApi.setValues(record);
  await formApi.setValues({
    'deviceType': record.deviceType.toString(),
    'firmwareType': record.firmwareType.toString(),
    'networkType': record.networkType.toString(),
    'transport': record.transport.toString(),
    'channelType': record.channelType.toString(),
    'vertificateMethod': record.vertificateMethod.toString(),
    'locationWay': record.locationWay.toString(),
    'isPrivate': record.isPrivate.toString(),
    'isAuthorize': record.isAuthorize.toString(),
    'publishStatus': record.publishStatus.toString(),
  });
  if (isUpdate.value) {
    formApi.setState({
      commonConfig: {
        componentProps: {
          readonly: false,
          "only-read": false,
          disabled: false,
        }
      }
    });
  } else {
    formApi.setState({
      commonConfig: {
        componentProps: {
          readonly: true,
          "only-read": true,
          disabled: true,
        }
      }
    }
    );
  }
}

async function handleEditConfirm() {
  try {
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    data.publishStatus = 1;
    await (Edit(data));
    await handleEditCancel();
  } catch (error) {
    console.error(error);
  }
}

async function handleEditCancel() {
  isUpdate.value = false;
  reloadEditForm();
}

function handleEdit() {
  isUpdate.value = true;
  reloadEditForm();
}

async function exportProductModel() {
  const data = await ExportModel({ productId: productId.value });
  console.log('data', data);
  downloadByData(data, "物模型-" + product.value.productKey + ".json");
}
// 文件上传相关
const fileList = ref([]);
const headers = {
  authorization: 'authorization-text',
};

const fileContent = ref();
const jsonData = ref();
const customRequest = async (info: UploadRequestOption<any>) => {
  var file = info.file
  console.log('customRequest', info);

  try {
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        fileContent.value = e.target?.result;
        jsonData.value = JSON.parse(fileContent.value);
        console.log('jsonData', jsonData);
        await ImportModel({
          productId: productId.value,
          tslJson: jsonData.value,
        })
        message.success('导入成功');
      } catch (error) {
        console.error('Error parsing JSON file:', error);
        message.error('导入失败');
      }
    };
    reader.readAsText(file as Blob);

    // 获取
  } catch (error: any) {
    console.error(error);
    info.onError!(error);
  } finally {
    refresh();
  }
};

function onTabChange(key: any) {
  console.log('onTabChange', key);
  currentModelType.value = key;
  refreshIotProductModel(key);
}

function refreshIotProductModel(key: any) {
  switch (key) {
    case 'property':
      if (iotProductModel.value) {
        iotProductModel.value.reload();
      }
      break;
    case 'function':
      iotProductModelFuntion.value.reload();
      break;
    case 'event':
      iotProductModelEvent.value.reload();
      break;
    case 'tag':
      iotProductModelTag.value.reload();
      break;
  }
}

</script>

<template>
  <Page auto-content-height>
    <Card class="flex items-center justify-between">
      <label style="font-size: 24px;">产品：{{ product.productName }}</label>
      <label class="ml-6">是否发布：</label>
      <Switch v-model:checked="isPublished" @change="switchChange" />
    </Card>
    <Card class="mt-2">
      <Tabs v-model:activeKey="currentTab">
        <TabPane key="a" tab="产品信息">
          <label style="font-size: 18px;">产品信息 </label>
          <Button v-if="product.publishStatus != 2 && !isUpdate" @click="handleEdit">编辑</Button>
          <Button v-if="product.publishStatus != 2 && isUpdate" @click="handleEditConfirm">保存</Button>
          <Button v-if="product.publishStatus != 2 && isUpdate" @click="handleEditCancel">取消</Button>
          <Divider />
          <BasicForm class="gap-[8px] h-full"></BasicForm>
        </TabPane>
        <TabPane key="b" tab="物模型">
          <Tabs class="pl-2 pr-2 border-[1px] border-solid border-[#d9d9d9] rounded-[4px]"
            v-model:activeKey="currentModelType" @tabChange="onTabChange">
            <TabPane key="property" tab="&nbsp;&nbsp;属性定义" class="gap-[8px] h-[800px]">
              <IotProductModel v-if="currentModelType == 'property' && product.productKey !== ''" ref="iotProductModel"
                :product-key="product.productKey" :type="currentModelType" title="属性定义"
                :published="product.publishStatus == 2" />
            </TabPane>
            <TabPane key="function" tab="功能定义" class="gap-[8px] h-[800px]">
              <IotProductModel v-if="currentModelType == 'function' && product.productKey !== ''"
                ref="iotProductModelFuntion" :product-key="product.productKey" :type="currentModelType" title="功能定义"
                :published="product.publishStatus == 2" />
            </TabPane>
            <TabPane key="event" tab="事件定义" class="gap-[8px] h-[800px]">
              <IotProductModel v-if="currentModelType == 'event' && product.productKey !== ''"
                ref="iotProductModelEvent" :product-key="product.productKey" :type="currentModelType" title="事件定义"
                :published="product.publishStatus == 2" />
            </TabPane>
            <TabPane key="tag" tab="标签定义" class="gap-[8px] h-[800px]">
              <IotProductModel v-if="currentModelType == 'tag' && product.productKey !== ''" ref="iotProductModelTag"
                :product-key="product.productKey" :type="currentModelType" title="标签定义"
                :published="product.publishStatus == 2" />
            </TabPane>
            <template #rightExtra class="flex items-center">
              <Button @click="exportProductModel" style="display: inline-flex; align-items: center;">导出物模型</Button>
              <Upload v-model:file-list="fileList" name="file" accept=".json" :headers="headers"
                :custom-request="customRequest" :showUploadList=false
                style="display: inline-flex; align-items: center;">
                <Button v-if="product.publishStatus != 2">
                  <UploadOutlined></UploadOutlined> 导入物模型
                </Button>
              </Upload>

            </template>
          </Tabs>
        </TabPane>
        <TabPane v-if="product.transport == 2" key="b1" tab="Modbus配置" @tabChange="onTabChange">
          <ModbusConfig :product-key="product.productKey" :published="product.publishStatus == 2" />
        </TabPane>
        <TabPane v-if="product.transport == 4" key="b2" tab="CJ/T 188配置" @tabChange="onTabChange">
          <Cjt188Config :product-key="product.productKey" :published="product.publishStatus == 2" />
        </TabPane>
        <TabPane v-if="product.transport == 2 || product.transport == 4" key="b3" tab="采集任务" @tabChange="onTabChange">
          <IotPollTask targetType="2" :productKey="product.productKey" />
        </TabPane>
        <TabPane key="c" tab="设备接入">
          <ScriptTable :product-key="product.productKey" :product-name="product.productName" />
        </TabPane>
        <TabPane key="d" tab="数据解析">
          <RuleScriptTable :product-key="product.productKey" :product-name="product.productName" />

        </TabPane>
        <TabPane key="e" tab="固件管理">
          <Management :product-key="product.productKey" :product-name="product.productName"
            :firmware-type="product.firmwareType" />
        </TabPane>
        <TabPane key="f" tab="扩展配置">
          <ExtraConfig :productKey="product.productKey" :published="product.publishStatus == 2" />
        </TabPane>
        <TabPane key="g" tab="可订阅事件">
          <SubscribableEvents :productKey="product.productKey" />
        </TabPane>
      </Tabs>
    </Card>
  </Page>
</template>
