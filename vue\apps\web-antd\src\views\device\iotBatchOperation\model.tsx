import { h } from 'vue';
import { Progress } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { getDictOptions } from '#/utils/dict';
import { getPopupContainer } from '@vben/utils';

export class State {
  public batchId = 0; // ID
  public type = null; // 操作类型
  public productKey = ''; // 产品标识
  public deviceKeys = ''; // 设备标识
  public sourceDeptName = ''; // 原机构
  public targetDept = 0; // 接收机构
  public projectId = 0; // 项目ID
  public total = 0; // 设备总数
  public success = 0; // 成功数量
  public fail = 0; // 失败数量
  public batchState = null; // 任务状态
  public productionBatch = null; // 生产批次
  public packingBatch = null; // 封箱批次
  public remark = null; // 操作原因
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间
  public createdBy = ''; // 创建者
  public updatedAt = ''; // 更新时间
  public detailList: DeviceDetailType[] = []; // 设备明细列表

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'type',
    component: 'Select',
    label: '操作类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择操作类型',
      options: getDictOptions('batch_operation_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  },
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'sourceDeptName',
    component: 'InputNumber',
    label: '原机构',
    componentProps: {
      placeholder: '请输入原机构',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'batchState',
    component: 'Select',
    label: '任务状态',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择任务状态',
      options: getDictOptions('batch_state'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    formItemClass: 'col-span-1',
  },
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'datetimerange',
      clearable: true,
      valueFormat: 'FMDateRange',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    }, rules: null,
    formItemClass: 'col-span-1',
  },];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '编号',
    field: 'batchId',
    align: 'center',
    width: 60,
  },
  {
    title: '操作类型', field: 'type', align: 'left', width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.type, 'batch_operation_type');
      }
    },
  },
  {
    title: '产品',
    field: 'productKey',
    align: 'left',
    width: -1,
  },
  {
    title: '原机构',
    field: 'sourceDeptName',
    align: 'left',
    width: -1,
  },
  {
    title: '接收机构',
    field: 'targetDept',
    align: 'left',
    width: -1,
  },
  {
    title: '设备总数',
    field: 'total',
    align: 'left',
    width: -1,
  },
  {
    title: '任务状态', field: 'batchState', align: 'left', width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.batchState, 'batch_state');
      }
    },
  },
  {
    title: '失败数量',
    field: 'fail',
    align: 'left',
    width: -1,
  },
  {
    title: '成功率',
    field: 'successRate',
    align: 'center',
    width: 80,
    slots: {
      default: ({ row }) => {
        if (!row.total || row.total === 0) {
          return h(Progress, {
            type: 'circle',
            percent: 0,
            size: 40,
            strokeColor: '#ff4d4f',
            format: () => '0%'
          });
        }
        const successRate = Math.round((row.success || 0) / row.total * 100);

        // 根据成功率设置不同的颜色
        let strokeColor = '#52c41a'; // 绿色 - 成功率高
        if (successRate < 50) {
          strokeColor = '#ff4d4f'; // 红色 - 成功率低
        } else if (successRate < 80) {
          strokeColor = '#faad14'; // 橙色 - 成功率中等
        }

        return h(Progress, {
          type: 'circle',
          percent: successRate,
          size: 40,
          strokeColor: strokeColor,
          format: (percent) => `${percent}%`
        });
      }
    },
  },
  {
    title: '生产批次',
    field: 'productionBatch',
    align: 'left',
    width: -1,
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: 150,
  },
  { title: '操作', field: 'action', align: 'center', width: 80, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  batchId: number;
  type: number;
  productKey: string;
  deviceKeys: string;
  sourceDeptName: string;
  targetDept: number;
  projectId: number;
  total: number;
  success: number;
  fail: number;
  batchState: number;
  productionBatch: string;
  packingBatch: string;
  remark: string;
  tenantId: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'batchId', label: '编号' },
  {
    field: 'type',
    label: '操作类型',
    render(_, row: any) {
      return renderDict(row.type, 'batch_operation_type');
    },
  },
  {
    field: 'productKey', label: '产品标识',
    show: (formData: Record<string, any>) => formData?.type === '1',
  },

  {
    field: 'sourceDeptName', label: '原机构',
  },
  {
    field: 'targetDept', label: '接收机构',
    show: (formData: Record<string, any>) => formData?.type === '2',
  },
  {
    field: 'projectName', label: '接收项目',
    show: (formData: Record<string, any>) => formData?.type === '4',
  },
  {
    field: 'batchState',
    label: '任务状态',
    render(_, row: any) {
      return renderDict(row.batchState, 'batch_state');
    },
  },
  { field: 'total', label: '设备总数' },
  {
    field: 'successRate',
    label: '成功率',
    render(_, row: any) {
      if (!row.total || row.total === 0) {
        return '0%';
      }
      const successRate = Math.round((row.success || 0) / row.total * 100);
      return `${successRate}%`;
    }
  },
  { field: 'success', label: '成功数量' },
  { field: 'fail', label: '失败数量' },
  {
    field: 'productionBatch', label: '生产批次',
    show: (formData: Record<string, any>) => formData?.type === '1',
  },
  {
    field: 'packingBatch', label: '封箱批次',
    show: (formData: Record<string, any>) => formData?.type === '1',
  },
  { field: 'remark', label: '操作原因' },

];



// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'batchId',
    component: 'Input',
    label: 'ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'type',
    component: 'Select',
    label: '操作类型',
    defaultValue: '1',
    componentProps: {
      placeholder: '请选择操作类型',
      options: getDictOptions('batch_operation_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired',
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '产品',
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired',
    //只有在操作类型字段是 “新增”，也就是1的时候，才显示且为必填
    dependencies: {
      show: (formData: Record<string, any>) => formData.type === '1',
      triggerFields: ['type'],
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'deviceKeys',
    component: 'Textarea',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识，多个设备标识请用回车分隔',
      rows: 3,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.string()
      .min(1, '设备标识不能为空')
      .superRefine((value, ctx) => {
        if (!value || value.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: '设备标识不能为空'
          });
          return;
        }

        // 按回车分隔设备标识
        const deviceKeys = value.split('\n').filter((key: string) => key.trim() !== '');

        for (let i = 0; i < deviceKeys.length; i++) {
          const deviceKey = deviceKeys[i]?.trim();

          if (!deviceKey) continue;

          // 检查长度
          if (deviceKey.length < 1) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `第${i + 1}行：设备标识不能为空`
            });
            continue;
          }
          if (deviceKey.length > 32) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `第${i + 1}行：设备标识最大长度32个字符`
            });
            continue;
          }

          // 检查字符组成（只能是字母、数字、下划线）
          if (!/^[a-zA-Z0-9_]+$/.test(deviceKey)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `第${i + 1}行：设备标识只能包含字母、数字、下划线`
            });
            continue;
          }
        }
      }),
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'productionBatch',
    component: 'Input',
    label: '生产批次',
    componentProps: {
      placeholder: '请输入生产批次',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    //类型为新增的显示非必填
    dependencies: {
      show: (formData: Record<string, any>) => formData.type === '1',
      triggerFields: ['type'],
    },
    formItemClass: 'col-span-2',
  }, {
    fieldName: 'packingBatch',
    component: 'Input',
    label: '封箱批次',
    componentProps: {
      placeholder: '请输入封箱批次',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    //类型为新增的显示非必填
    dependencies: {
      show: (formData: Record<string, any>) => formData.type === '1',
      triggerFields: ['type'],
    },
    formItemClass: 'col-span-2',
  }, {
    fieldName: 'targetDept',
    component: 'TreeSelect',
    label: '接收机构',
    componentProps: {
      getPopupContainer,
    }, rules: 'selectRequired',
    //类型为2，机构过户时显示且必填
    dependencies: {
      show: (formData: Record<string, any>) => formData.type === '2',
      triggerFields: ['type'],
    },
    formItemClass: 'col-span-2',
  },
  //接收项目 projectId
  {
    fieldName: 'projectId',
    component: 'Select',
    label: '接收项目',
    componentProps: {
      placeholder: '请选择接收项目',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired',
    //类型为4划拨的时候显示必填
    dependencies: {
      show: (formData: Record<string, any>) => formData.type === '4',
      triggerFields: ['type'],
    },
    formItemClass: 'col-span-2',
  }, {
    fieldName: 'remark',
    component: 'Textarea',
    label: '操作原因',
    componentProps: {
      placeholder: '请输入操作原因',
      rows: 3,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    formItemClass: 'col-span-2',
  },];

// 设备明细数据结构
export interface DeviceDetailType {
  detailId: number;
  batchId: number;
  deviceKey: string;
  result: number; // 处理结果：1 成功，2 失败
  [key: string]: any;
}

