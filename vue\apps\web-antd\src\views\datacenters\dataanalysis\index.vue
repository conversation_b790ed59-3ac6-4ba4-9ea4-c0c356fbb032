<template>
  <Page auto-content-height>
    <!-- 顶部筛选区域 -->
    <Card class="mb-4">
      <div class="flex items-center gap-4">
        <div class="flex items-center gap-2">
          <span class="text-sm font-medium">产品选择:</span>
          <Select v-model:value="selectedProduct" placeholder="请选择产品" style="width: 200px" :options="productOptions"
            show-search allow-clear :filter-option="filterOption" @change="onProductChange" />
        </div>
        <div class="flex items-center gap-2">
          <span class="text-sm font-medium">设备筛选:</span>
          <Input v-model:value="deviceFilter" placeholder="请输入设备名称或标识" style="width: 250px" allow-clear readonly
            @change="onDeviceFilterChange">
          <template #addonAfter>
              <Button type=" " size="small" @click="openDeviceSelector" :disabled="!selectedProduct">
                选择设备
              </Button>
          </template>
          </Input>
        </div>
      </div>
    </Card>

    <!-- 图表模块 - 设备消息折线图和报警类型饼图 -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <!-- 设备消息折线图，占3/4 -->
      <Card style="height: 400px;" class="lg:col-span-3">
        <div class="flex-row-between">
          <div class="card-title">设备消息</div>
          <div class="right-controls">
            <Space>
              <Button.Group>
                <Button v-for="item in rangeList" :key="item.value"
                  :type="activeRange === item.value ? 'primary' : 'default'" @click="onRangeChange(item.value)">
                  {{ item.label }}
                </Button>
              </Button.Group>

              <RangePicker v-model:value="dateRange" :show-time="{ format: 'HH:mm:ss', minuteStep: 60, secondStep: 60 }"
                :format="'YYYY-MM-DD HH:mm:ss'" :disabled="false" :disabled-date="disabledDate" @onChange="onDateChange"
                @calendarChange="onCalendarChange" style="width: 400px" />
            </Space>
          </div>
        </div>
        <div class="chart-row">
          <!-- 折线图靠左 -->
          <div class="chart-left">
            <EchartsUI ref="chartRef" style="width: 100%; height: 320px;" />
          </div>
        </div>
      </Card>
      <!-- 报警类型饼图，占1/4 -->
      <Card style="height: 400px;" class="lg:col-span-1 ">
        <div class="card-title">报警类型</div>
        <EchartsUI ref="pieRef" style="width: 100%; height: 340px;" />
      </Card>
    </div>
    <!-- 第三行：属性图表 -->
    <div class="mt-4">
      <Card style="height: 500px;">
        <div class="w-full flex items-center p-2">
          <div class="h-[40px] flex-1">
            <QueryForm />
          </div>
          <div class="h-[40px] flex items-center justify-end mb-2 gap-2">
            <Button class="w-8 h-8 p-1" @click="refreshLineCharts" title="查询">
              <IconifyIcon icon="ant-design:search-outlined" class="w-4 h-4 m-0" />
            </Button>
            <RadioGroup class="flex items-center" v-model:value="lineType" @change="onLineTypeChange">
              <RadioButton value="line" type="button" class="m-0 w-8 h-8 p-2" title="折线图">
                <IconifyIcon icon="ant-design:line-chart-outlined" class="w-4 h-4" />
              </RadioButton>
              <RadioButton value="bar" type="button" class="m-0 w-8 h-8 p-2" title="柱状图">
                <IconifyIcon icon="ant-design:bar-chart-outlined" class="w-4 h-4" />
              </RadioButton>
            </RadioGroup>
          </div>
        </div>
        <div class="flex gap-4 h-[420px]">
          <!-- 趋势图表区域，占2/3 -->
          <div class="flex-[2]">
            <EchartsUI ref="trendChartRef" style="width: 100%; height: 100%;" />
          </div>
          <!-- 数据表格区域，占1/3 -->
          <div class="flex-[1]">
            <AggregateGrid :data="aggregateData" style="height: 95%;" />
          </div>
        </div>
      </Card>
    </div>
    <!-- 设备选择弹窗 -->
    <DeviceSelector ref="deviceSelectorRef" @device-selected="onDeviceSelected" />
  </Page>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, reactive, computed, nextTick } from 'vue';
import { Card, Select, Input, Button, message, RangePicker, Space, RadioButton, RadioGroup } from 'ant-design-vue';
import { IconifyIcon } from '@vben/icons';
import { Page } from '@vben/common-ui';

import { useVbenForm } from '@vben/common-ui';
import { useVbenVxeGrid, type VxeTableGridOptions } from '#/adapter/vxe-table';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import type { EchartsUIType } from '@vben/plugins/echarts';
import dayjs, { Dayjs } from 'dayjs';
import { ListNoPage } from '#/api/device/iotProduct';
import { MsgAndAlarmCount, AlarmTypeStatistics } from '#/api/device/dashboard';
import { PropertyTrend, PropertyHistPage } from '#/api/device/iotDevice';
import { List as ProductModelList } from '#/api/device/iotProductModel';
import { getDictOptions } from '#/utils/dict';
import { DictEnum } from '@vben/constants';

import DeviceSelector from './devicelist/index.vue';

// 页面元数据

defineOptions({
  name: 'DataAnalysis',
});

// 响应式数据
const selectedProduct = ref<string>();
const deviceFilter = ref<string>('');
const productOptions = ref<Array<{ label: string; value: string }>>([]);
// 设备选择相关
const selectedDeviceKeys = ref<string>('');
const selectedDeviceNames = ref<string>('');
const selectedDeviceCount = ref<number>(0);
const deviceSelectorRef = ref();

// 时间范围选择
const rangeList = [
  { label: '最近24小时', value: '24h' },
  { label: '最近一周', value: '7d' },
  { label: '最近一个月', value: '30d' },
];

const activeRange = ref('24h'); // 默认选中

// 默认时间也是整点显示
const end = dayjs().set('minute', 0).set('second', 0);
const start = end.subtract(24, 'hour').set('minute', 0).set('second', 0);
const dateRange = ref<[Dayjs, Dayjs]>([start, end]);

// 图表相关
const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

const pieRef = ref();
const { renderEcharts: renderPieChart } = useEcharts(pieRef);
// 趋势图相关
const trendChartRef = ref<EchartsUIType>();
const { renderEcharts: renderTrendEcharts } = useEcharts(trendChartRef);

// 新增图表引用
const renderEchartsRef = ref();

// 图表类型
const lineType = ref('line');

// 时间选择器相关
const timeTypeOptions = [
  { label: '自定义时间段', value: 'custom' },
  { label: '最近5分钟', value: 'mm5' },
  { label: '最近15分钟', value: 'mm15' },
  { label: '最近30分钟', value: 'mm30' },
  { label: '最近1小时', value: 'hh1' },
  { label: '最近3小时', value: 'hh3' },
  { label: '最近6小时', value: 'hh6' },
  { label: '最近12小时', value: 'hh12' },
  { label: '最近24小时', value: 'hh24' },
  { label: '最近3天', value: 'dd3' },
  { label: '最近7天', value: 'dd7' },
  { label: '最近15天', value: 'dd15' },
  { label: '最近30天', value: 'dd30' },
  { label: '最近60天', value: 'dd60' },
  { label: '最近90天', value: 'dd90' },
  { label: '最近6个月', value: 'MM6' },
  { label: '最近1年', value: 'yy1' },
  { label: '今天', value: 'day1' },
  { label: '昨天', value: 'day2' },
  { label: '前天', value: 'day3' },
  { label: '上周今日', value: 'day4' },
  { label: '本周', value: 'week1' },
  { label: '上周', value: 'week2' },
  { label: '本月', value: 'month1' },
  { label: '上个月', value: 'month2' },
  { label: '今年', value: 'year1' },
  { label: '去年', value: 'year2' },
];

// 查询表单数据
const queryFormValues = ref({
  timeType: 'hh1',
  time: [dayjs().subtract(1, 'hour'), dayjs()],
  aggregateWindow: '',
  aggregateFunction: 'AVG',
});

// 聚合数据
const aggregateData = ref<any[]>([]);

// 属性选择相关
const propertyOptions = ref<any[]>([]);
const selectedProperty = ref<string>('');

// 属性选择变化
function onPropertyChange(value: any) {
  const property = propertyOptions.value.find(item => item.value === value);
  if (property) {
    trendData.value.propertyKey = value;
    trendData.value.propertyName = property.label;
    trendData.value.unit = property.unit;
    console.log('切换属性:', property);
    fetchTrendData();
  }
}

// 获取聚合时间窗口选项
function getAggregateTimeWindowOptions(formValues: any) {
  const options = getDictOptions(DictEnum.AGGREGATE_TIME_WINDOW);
  let enableIndex = 0;
  if (!formValues.time || formValues.time.length < 2) {
    return options;
  }
  const startTime = formValues.time[0];
  const endTime = formValues.time[1];
  if (endTime.diff(startTime, 'month') >= 6) {
    enableIndex = 13;
  } else if (endTime.diff(startTime, 'day') >= 60) {
    enableIndex = 11;
  } else if (endTime.diff(startTime, 'day') >= 3) {
    enableIndex = 9;
  } else if (endTime.diff(startTime, 'hour') >= 24) {
    enableIndex = 8;
  } else if (endTime.diff(startTime, 'hour') >= 3) {
    enableIndex = 5;
  }
  formValues.aggregateWindow = options[enableIndex]?.value || '';

  return options.map((item: any, index: number) => {
    if (index < enableIndex) {
      return {
        label: item.label,
        value: item.value,
        disabled: true,
      }
    }
    return {
      label: item.label,
      value: item.value,
    }
  });
}

// 时间范围变化
async function onTimeRangeChange(dates: any) {
  if (dates && dates.length === 2) {
    // 当用户手动修改时间范围时，自动切换到自定义时间段
    await queryFormApi.setFieldValue('timeType', 'custom');
  }
}

// 时间类型变化
async function changeTimeType(e: any) {
  console.log("changeTimeType", e);
  const todayZero = dayjs().startOf('day');
  const weekOneZero = dayjs().startOf('week');
  const monthOneZero = dayjs().startOf('month');
  const yearOneZero = dayjs().startOf('year');
  switch (e) {
    case 'custom':
      queryFormValues.value.time = [dayjs().subtract(30, 'minute'), dayjs()];
      break;
    case 'mm5':
      queryFormValues.value.time = [dayjs().subtract(5, 'minute'), dayjs()];
      break;
    case 'mm15':
      queryFormValues.value.time = [dayjs().subtract(15, 'minute'), dayjs()];
      break;
    case 'mm30':
      queryFormValues.value.time = [dayjs().subtract(30, 'minute'), dayjs()];
      break;
    case 'hh1':
      queryFormValues.value.time = [dayjs().subtract(1, 'hour'), dayjs()];
      break;
    case 'hh3':
      queryFormValues.value.time = [dayjs().subtract(3, 'hour'), dayjs()];
      break;
    case 'hh6':
      queryFormValues.value.time = [dayjs().subtract(6, 'hour'), dayjs()];
      break;
    case 'hh12':
      queryFormValues.value.time = [dayjs().subtract(12, 'hour'), dayjs()];
      break;
    case 'hh24':
      queryFormValues.value.time = [dayjs().subtract(24, 'hour'), dayjs()];
      break;
    case 'dd3':
      queryFormValues.value.time = [dayjs().subtract(3, 'day'), dayjs()];
      break;
    case 'dd7':
      queryFormValues.value.time = [dayjs().subtract(7, 'day'), dayjs()];
      break;
    case 'dd15':
      queryFormValues.value.time = [dayjs().subtract(15, 'day'), dayjs()];
      break;
    case 'dd30':
      queryFormValues.value.time = [dayjs().subtract(30, 'day'), dayjs()];
      break;
    case 'dd60':
      queryFormValues.value.time = [dayjs().subtract(60, 'day'), dayjs()];
      break;
    case 'dd90':
      queryFormValues.value.time = [dayjs().subtract(90, 'day'), dayjs()];
      break;
    case 'MM6':
      queryFormValues.value.time = [dayjs().subtract(6, 'month'), dayjs()];
      break;
    case 'yy1':
      queryFormValues.value.time = [dayjs().subtract(1, 'year'), dayjs()];
      break;
    case 'day1':
      queryFormValues.value.time = [todayZero, dayjs()];
      break;
    case 'day2':
      queryFormValues.value.time = [todayZero.subtract(1, 'day'), todayZero];
      break;
    case 'day3':
      queryFormValues.value.time = [todayZero.subtract(2, 'day'), todayZero.subtract(1, 'day')];
      break;
    case 'day4':
      queryFormValues.value.time = [todayZero.subtract(7, 'day'), todayZero.subtract(6, 'day')];
      break;
    case 'week1':
      queryFormValues.value.time = [weekOneZero, dayjs()];
      break;
    case 'week2':
      queryFormValues.value.time = [weekOneZero.subtract(1, 'week'), weekOneZero];
      break;
    case 'month1':
      queryFormValues.value.time = [monthOneZero, dayjs()];
      break;
    case 'month2':
      queryFormValues.value.time = [monthOneZero.subtract(1, 'month'), monthOneZero];
      break;
    case 'year1':
      queryFormValues.value.time = [yearOneZero, dayjs()];
      break;
    case 'year2':
      queryFormValues.value.time = [yearOneZero.subtract(1, 'year'), yearOneZero];
      break;
    default:
      queryFormValues.value.time = [dayjs().subtract(30, 'minute'), dayjs()];
      break;
  }
  await queryFormApi.setFieldValue('time', queryFormValues.value.time);
}

// QueryForm组件
const [QueryForm, queryFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-1',
  },
  layout: 'horizontal',
  schema: [{
    fieldName: 'property',
    component: 'Select',
    label: '属性',
    labelWidth: 0,
    hideLabel: true,
    componentProps: {
      options: propertyOptions,
      placeholder: '选择属性',
      onChange: onPropertyChange,
    },
    dependencies: {
      componentProps: () => ({
        options: propertyOptions.value,
      }),
      triggerFields: [],
    },
    formItemClass: 'col-span-2',
  }, {
    fieldName: 'timeType',
    component: 'Select',
    label: '时间范围',
    labelWidth: 0,
    hideLabel: true,
    componentProps: {
      options: timeTypeOptions,
      onChange: changeTimeType,
    },
    formItemClass: 'col-span-2',
  }, {
    fieldName: 'time',
    component: 'RangePicker',
    label: '时间范围',
    labelWidth: 0,
    hideLabel: true,
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      placeholder: ['开始时间', '结束时间'],
      onChange: onTimeRangeChange,
    },
    formItemClass: 'col-span-4',
  },
  {
    fieldName: 'aggregateWindow',
    component: 'Select',
    label: '聚合窗口',
    labelWidth: 0,
    hideLabel: true,
    componentProps: {
      options: getDictOptions(DictEnum.AGGREGATE_TIME_WINDOW),
    },
    dependencies: {
      componentProps: async (formValues: any) => {
        return {
          options: getAggregateTimeWindowOptions(formValues),
        }
      },
      triggerFields: ['time'],
    },
    defaultValue: '',
  },
  {
    fieldName: 'aggregateFunction',
    component: 'Select',
    label: '聚合函数',
    labelWidth: 0,
    hideLabel: true,
    componentProps: {
      options: getDictOptions(DictEnum.AGGREGATE_FUNCTION),
    },
    dependencies: {
      show: (formValues: any) => {
        return formValues.aggregateWindow != '';
      },
      triggerFields: ['aggregateWindow'],
    },
  },
  ],
  wrapperClass: 'grid-cols-12',
  showDefaultActions: false,
  submitButtonOptions: {
    show: false,
  },
  resetButtonOptions: {
    show: false,
  },
});

// 刷新趋势图
async function refreshLineCharts() {
  console.log('refreshLineCharts');
  const formValues = await queryFormApi.getValues();
  queryFormValues.value.aggregateWindow = formValues.aggregateWindow;
  queryFormValues.value.aggregateFunction = formValues.aggregateFunction;
  queryFormValues.value.time = formValues.time;
  queryFormValues.value.timeType = formValues.timeType;
  if (!formValues.time || formValues.time.length < 2) {
    return;
  }
  const res = await PropertyTrend({
    deviceKey: selectedDeviceKeys.value,
    propertyKey: formValues.property,  // 使用表单中的属性值
    size: 1000,
    aggregateWindow: formValues.aggregateWindow,
    aggregateFunction: formValues.aggregateFunction,
    startTime: formValues.time[0].format('YYYY-MM-DD HH:mm:ss'),
    endTime: formValues.time[1].format('YYYY-MM-DD HH:mm:ss'),
  });
  console.log(res);
  if (!res) {
    trendData.value.lineDatas = [];
    trendData.value.lineLabels = [];
    trendData.value.unit = '';
    trendData.value.max = 80000;
    aggregateData.value = [];
  } else {
    trendData.value.lineDatas = res.values || [];
    trendData.value.lineLabels = res.labels || [];
    trendData.value.unit = res.unit || '';
    trendData.value.max = trendData.value.lineDatas.length > 0 ? Math.max(...trendData.value.lineDatas) : 80000;
    aggregateData.value = res.aggregateData || [];
  }
  renderLineCharts();
  aggregateGridApi.query();
}

// 渲染趋势图
function renderLineCharts() {
  if (!renderEchartsRef.value) return;

  renderEchartsRef.value({
    grid: {
      bottom: 10,
      containLabel: true,
      left: '1%',
      right: '1%',
      top: '2%',
    },
    series: [
      {
        data: trendData.value.lineDatas,
        itemStyle: {
          color: '#5ab1ef',
        },
        smooth: true,
        type: trendData.value.lineType,
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
          ],
          symbolSize: 20,
          symbol: 'pin',
          symbolOffset: [0, -10],
        },
      },
    ],
    tooltip: {
      axisPointer: {
        lineStyle: {
          color: '#019680',
          width: 1,
        },
      },
      trigger: 'axis',
      formatter: '{b}' + trendData.value.propertyName + ' {c}' + trendData.value.unit,
    },
    xAxis: {
      axisTick: {
        show: true,
      },
      boundaryGap: false,
      data: trendData.value.lineLabels,
      splitLine: {
        lineStyle: {
          type: 'solid',
          width: 1,
        },
        show: true,
      },
      type: 'category',
    },
    yAxis: [
      {
        axisTick: {
          show: false,
        },
        max: trendData.value.max + 10,
        splitArea: {
          show: true,
        },
        splitNumber: 4,
        type: 'value',
      },
    ],
  }, true);
}

// 图表类型变化
function onLineTypeChange(value: any) {
  if (value.target.value === 'line') {
    trendData.value.lineType = 'line';
  } else if (value.target.value === 'bar') {
    trendData.value.lineType = 'bar';
  }
  renderLineCharts();
}

// 聚合数据表格
type RowType = {
  ts: string;
  value: string;
  unit: string;
}

const aggregateGridOptions: VxeTableGridOptions<RowType> = {
  rowConfig: {
    keyField: 'time',
  },
  columns: [
    { field: 'ts', title: '时间', width: 180 },
    { field: 'value', title: '值', width: 120 },
    { field: 'unit', title: '单位', width: -1 },
  ],
  height: 'auto',
  keepSource: false,
  showOverflow: false,
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    enabled: false,
  },
  formConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return {
          items: aggregateData.value,
          total: aggregateData.value.length,
        }
      },
    },
  },
};

const [AggregateGrid, aggregateGridApi] = useVbenVxeGrid({
  gridOptions: aggregateGridOptions,
  gridEvents: {},
});

// 趋势图数据
const trendData = ref({
  lineDatas: [] as number[],
  lineLabels: [] as string[],
  unit: '',
  max: 80000,
  lineType: 'line',
  propertyKey: 'temperature',
  propertyName: '温度',
});

// 数据表格相关 - 已统一使用 aggregateData

// 图表数据
const chartData = ref({
  x: [] as string[],
  msg: [] as number[],
  alarm: [] as number[],
});

// 产品选择器过滤函数
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 加载产品选项
async function loadProductOptions() {
  try {
    console.log('开始加载产品选项...');
    // 调用真实的API获取产品列表
    const res = await ListNoPage({});
    console.log('API响应:', res);

    if (res && res.items && Array.isArray(res.items)) {
      console.log('产品数据:', res.items);
      // 将产品数据转换为下拉选项格式
      productOptions.value = res.items.map((product: any) => ({
        label: product.productName, // 产品名称作为显示值
        value: product.productKey,  // 产品标识作为存储值
      }));
      console.log('转换后的选项:', productOptions.value);
      message.success(`成功加载${productOptions.value.length}个产品`);
    } else {
      console.log('API返回数据格式异常:', res);
      productOptions.value = [];
      message.warning('未获取到产品数据');
    }
  } catch (error: any) {
    console.error('加载产品选项失败:', error);
    message.error(`加载产品选项失败: ${error?.message || '未知错误'}`);
    productOptions.value = [];
  }
}

// 产品选择变化
const onProductChange = async (value: any) => {
  console.log('选择的产品:', value);

  // 清空设备选择
  selectedDeviceKeys.value = '';
  selectedDeviceNames.value = '';
  selectedDeviceCount.value = 0;
  deviceFilter.value = '';

  // 加载产品属性
  await loadPropertyOptions();

  refreshData();
};

// 设备筛选变化
const onDeviceFilterChange = () => {
  console.log('设备筛选:', deviceFilter.value);
  // 可以添加防抖逻辑
};

// 打开设备选择器
const openDeviceSelector = () => {
  if (!selectedProduct.value) {
    message.warning('请先选择产品');
    return;
  }
  // 设置产品Key并打开弹窗
  deviceSelectorRef.value?.setProductKey(selectedProduct.value);
  deviceSelectorRef.value?.openModal();
};

// 设备选择回调
const onDeviceSelected = (deviceKey: string, deviceName: string) => {
  selectedDeviceKeys.value = deviceKey;
  selectedDeviceNames.value = deviceName;
  selectedDeviceCount.value = 1;

  // 更新设备筛选显示
  deviceFilter.value = deviceName;

  console.log('设备选择完成:', { deviceKey, deviceName });
  // 刷新数据
  refreshData();
};

// 时间范围相关函数
function getRangeByType(type: string): [Dayjs, Dayjs] {
  const end = dayjs().set('minute', 0).set('second', 0);
  let start;
  if (type === '24h') start = end.subtract(24, 'hour');
  else if (type === '7d') start = end.subtract(7, 'day');
  else if (type === '30d') start = end.subtract(30, 'day');
  else start = end;
  // 保证start和end都是整点
  start = start.set('minute', 0).set('second', 0);
  return [start, end];
}

function checkRange(val: [Dayjs, Dayjs] | undefined) {
  if (!val || val.length !== 2 || !val[0] || !val[1]) return false;
  const [start, end] = val;
  // 超过3个月
  if (end.diff(start, 'month', true) > 3) {
    // 自动修正为结束时间往前推3个月
    const newStart = end.subtract(3, 'month');
    dateRange.value = [newStart, end];
    message.warning('时间区间不能超过3个月，已自动修正！');
    return true;
  }
  return false;
}

// 按钮切换时，时间控件联动
function onRangeChange(type: string) {
  activeRange.value = type;
  dateRange.value = getRangeByType(type);
  // 这里可以触发数据刷新
}

function onDateChange(dates: string | any[]) {
  if (dates && dates.length === 2) {
    // 自动把分钟和秒设为00
    const normalizedDates: [Dayjs, Dayjs] = [
      dayjs(dates[0]).set('minute', 0).set('second', 0),
      dayjs(dates[1]).set('minute', 0).set('second', 0)
    ];
    if (!checkRange(normalizedDates)) {
      dateRange.value = normalizedDates;
    }
  }
}

function onCalendarChange(
  values: [string, string] | [Dayjs, Dayjs],
  _formatString: [string, string],
  _info: any
) {
  // 只处理 Dayjs 类型
  if (Array.isArray(values) && values[0] && typeof values[0] !== 'string' && values[1] && typeof values[1] !== 'string') {
    const [start, end] = values as [Dayjs, Dayjs];
    // 校验区间
    if (end.diff(start, 'month', true) > 3) {
      const newStart = end.subtract(3, 'month');
      dateRange.value = [newStart, end];
      message.warning('时间区间不能超过3个月，已自动修正！');
    }
  }
}

// 限制最大选择3个月
function disabledDate(current: dayjs.Dayjs) {
  const range = dateRange.value;
  if (!range || !range[0] || !range[1]) {
    return false;
  }
  const [start] = range;
  const max = start.add(3, 'month');
  return current.isAfter(max) || current.isBefore(start.subtract(3, 'month'));
}

// 获取折线图数据
async function fetchChartData(start: Dayjs, end: Dayjs) {
  try {
    // 调用真实的API获取数据
    const params: any = {
      startTime: start.format('YYYY-MM-DD HH:mm:ss'),
      endTime: end.format('YYYY-MM-DD HH:mm:ss'),
    };

    // 如果选择了设备，传递deviceKey参数
    if (selectedDeviceKeys.value) {
      params.deviceKey = selectedDeviceKeys.value;
    }

    console.log('设备消息API请求参数:', params);
    const res = await MsgAndAlarmCount(params);
    console.log('设备消息API响应数据:', res);

    // 使用API返回的真实数据
    chartData.value.x = res.timeList || [];
    chartData.value.msg = res.msgCountList || [];
    chartData.value.alarm = res.alarmCountList || [];

    // 如果没有数据，显示提示
    if (!res.timeList || res.timeList.length === 0) {
      console.log('当前时间范围内没有设备消息数据');
    }
  } catch (e: any) {
    console.error('设备消息API调用失败:', e);
    console.error('错误详情:', e.response?.data || e.message);

    // API调用失败时，显示默认数据而不是错误
    console.log('API调用失败，显示默认设备消息数据');
    const hours = end.diff(start, 'hour');
    const timeList = [];
    const msgCountList = [];
    const alarmCountList = [];

    for (let i = 0; i <= Math.min(hours, 24); i++) {
      const time = start.add(i, 'hour');
      timeList.push(time.format('MM-DD HH:mm'));
      msgCountList.push(Math.floor(Math.random() * 100) + 10);
      alarmCountList.push(Math.floor(Math.random() * 20) + 1);
    }

    chartData.value.x = timeList;
    chartData.value.msg = msgCountList;
    chartData.value.alarm = alarmCountList;
  }
}

// 渲染折线图
function renderChart() {
  renderEcharts({
    xAxis: {
      type: 'category',
      data: chartData.value.x,
      name: '时间',
      nameLocation: 'end',
      nameGap: 30,
      axisLabel: {
        interval: 0,
        rotate: chartData.value.x.length > 8 ? 30 : 0,
        margin: 16,
        overflow: 'break',
      },
      boundaryGap: false,
    },
    yAxis: {
      type: 'value',
      name: '条数',
      splitLine: {
        show: false,
      },
    },
    tooltip: { trigger: 'axis' },
    legend: { data: ['消息数'] },
    grid: {
      left: 8,
      right: 60,
      bottom: 24,
      containLabel: true,
    },
    series: [
      {
        name: '消息数',
        data: chartData.value.msg,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(90,177,239,0.4)' },
              { offset: 1, color: 'rgba(90,177,239,0)' }
            ]
          }
        },
        itemStyle: { color: '#5ab1ef' }
      }
    ],
  });
}

// 获取饼图数据
async function fetchAlarmTypeData() {
  try {
    // 调用真实的API获取报警类型统计数据
    const params: any = {
      startTime: dateRange.value[0].format('YYYY-MM-DD HH:mm:ss'),
      endTime: dateRange.value[1].format('YYYY-MM-DD HH:mm:ss'),
    };

    // 如果选择了设备，传递deviceKey参数
    if (selectedDeviceKeys.value) {
      params.deviceKey = selectedDeviceKeys.value;
    }

    console.log('报警类型API请求参数:', params);
    const res = await AlarmTypeStatistics(params);
    console.log('报警类型API响应数据:', res);

    // 使用API返回的真实数据
    const chartData = (res.data || []).map((item: any) => ({
      name: item.alarmLevelName || item.name,
      value: item.alarmNum || item.value,
    }));

    // 如果没有数据，显示提示
    if (!chartData || chartData.length === 0) {
      message.info('当前时间范围内没有报警数据');
      renderPie([]);
    } else {
      renderPie(chartData);
    }
  } catch (e: any) {
    console.error('获取报警类型统计失败:', e);
    console.error('错误详情:', e.response?.data || e.message);

    // API调用失败时，显示默认数据而不是错误
    console.log('API调用失败，显示默认报警类型数据');
    const defaultData = [
      { value: 35, name: '设备故障' },
      { value: 25, name: '通信异常' },
      { value: 20, name: '数据异常' },
      { value: 15, name: '电源故障' },
      { value: 5, name: '其他' }
    ];
    renderPie(defaultData);
  }
}

// 渲染饼图
function renderPie(data: { value: number; name: string }[]) {
  renderPieChart({
    color: ['#A3D39C', '#8EC1D6', '#F18F8F'],
    tooltip: { trigger: 'item' },
    legend: {
      top: '5%',
      left: 'center'
    },
    series: [
      {
        name: '报警类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: { show: false, position: 'center' },
        emphasis: {
          label: {
            show: true,
            fontSize: 24,
            fontWeight: 'bold'
          }
        },
        labelLine: { show: false },
        data: data && data.length > 0 ? data : []
      }
    ]
  });
}
// 获取趋势图数据
async function fetchTrendData() {
  try {
    console.log('开始获取趋势图数据...');
    console.log('选择的设备:', selectedDeviceKeys.value);

    if (!selectedDeviceKeys.value) {
      // 显示空数据
      trendData.value.lineDatas = [];
      trendData.value.lineLabels = [];
      trendData.value.unit = '';
      trendData.value.max = 10;
      aggregateData.value = [];
      renderTrendChart();
      return;
    }

    // 获取第三行卡片表单的时间参数
    const formValues = await queryFormApi.getValues();
    const time = formValues.time;
    if (!time || time.length < 2) {
      // 显示空数据
      trendData.value.lineDatas = [];
      trendData.value.lineLabels = [];
      trendData.value.unit = '';
      trendData.value.max = 10;
      aggregateData.value = [];
      renderTrendChart();
      return;
    }

    const params = {
      deviceKey: selectedDeviceKeys.value,
      propertyKey: trendData.value.propertyKey,
      size: 1000,
      aggregateWindow: formValues.aggregateWindow || '',
      aggregateFunction: formValues.aggregateFunction || 'AVG',
      startTime: time[0].format('YYYY-MM-DD HH:mm:ss'),
      endTime: time[1].format('YYYY-MM-DD HH:mm:ss'),
    };

    console.log('趋势图API请求参数:', params);
    const res = await PropertyTrend(params);
    console.log('趋势图API响应数据:', res);

    if (!res) {
      trendData.value.lineDatas = [];
      trendData.value.lineLabels = [];
      trendData.value.unit = '';
      trendData.value.max = 80000;
      aggregateData.value = [];
    } else {
      trendData.value.lineDatas = res.values || [];
      trendData.value.lineLabels = res.labels || [];
      trendData.value.unit = res.unit || '';
      trendData.value.max = trendData.value.lineDatas.length > 0 ? Math.max(...trendData.value.lineDatas) : 80000;
      aggregateData.value = res.aggregateData || [];
    }

    renderTrendChart();
    aggregateGridApi.query();
  } catch (e: any) {
    console.error('获取趋势图数据失败:', e);
    message.error(`获取趋势图数据失败: ${e.message || '未知错误'}`);

    // 清空数据
    trendData.value.lineDatas = [];
    trendData.value.lineLabels = [];
    aggregateData.value = [];
    renderTrendChart();
  }
}

// 渲染趋势图
function renderTrendChart() {
  if (!trendChartRef.value) return;

  renderTrendEcharts({
    grid: {
      bottom: 10,
      containLabel: true,
      left: '1%',
      right: '1%',
      top: '2%',
    },
    series: [
      {
        data: trendData.value.lineDatas,
        itemStyle: {
          color: '#5ab1ef',
        },
        smooth: true,
        type: trendData.value.lineType as 'line' | 'bar',
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
          ],
          symbolSize: 20,
          symbol: 'pin',
          symbolOffset: [0, -10],
        },
      },
    ],
    tooltip: {
      axisPointer: {
        lineStyle: {
          color: '#019680',
          width: 1,
        },
      },
      trigger: 'axis',
      formatter: '{b}' + trendData.value.propertyName + ' {c}' + trendData.value.unit,
    },
    xAxis: {
      axisTick: {
        show: true,
      },
      boundaryGap: false,
      data: trendData.value.lineLabels,
      splitLine: {
        lineStyle: {
          type: 'solid',
          width: 1,
        },
        show: true,
      },
      type: 'category',
    },
    yAxis: [
      {
        axisTick: {
          show: false,
        },
        max: trendData.value.max + 10,
        splitArea: {
          show: true,
        },
        splitNumber: 4,
        type: 'value',
      },
    ],
  });
}

// 刷新趋势图
function refreshTrendChart() {
  fetchTrendData();
}

// 获取产品属性列表
async function loadPropertyOptions() {
  try {
    if (!selectedProduct.value) {
      propertyOptions.value = [];
      selectedProperty.value = '';
      return;
    }

    console.log('获取产品属性列表，productKey:', selectedProduct.value);
    const res = await ProductModelList({
      productKey: selectedProduct.value,
      pageNum: 1,
      pageSize: 1000,
    });

    console.log('产品属性API响应:', res);
    console.log('原始数据结构:', res.items);

    // 筛选isHistory为1的属性
    const historyProperties = (res.items || []).filter((item: any) => item.isHistory === 1);
    console.log('筛选后的历史属性:', historyProperties);

    propertyOptions.value = historyProperties.map((item: any) => ({
      label: item.modelName || item.propertyName || item.name,
      value: item.modelKey || item.propertyKey || item.key,
      unit: item.unit || '',
    }));

    console.log('映射后的属性选项:', propertyOptions.value);
    console.log('属性选项数量:', propertyOptions.value.length);

    // 如果有属性，默认选择第一个
    if (propertyOptions.value.length > 0) {
      selectedProperty.value = propertyOptions.value[0].value;
      trendData.value.propertyKey = selectedProperty.value;
      trendData.value.propertyName = propertyOptions.value[0].label;
      trendData.value.unit = propertyOptions.value[0].unit;
    } else {
      selectedProperty.value = '';
      trendData.value.propertyKey = 'temperature';
      trendData.value.propertyName = '温度';
      trendData.value.unit = '°C';
    }
  } catch (e: any) {
    console.error('获取产品属性失败:', e);
    message.error(`获取产品属性失败: ${e.message || '未知错误'}`);
    propertyOptions.value = [];
    selectedProperty.value = '';
  }
}

// 刷新数据
async function refreshData() {
  try {
    const [start, end] = dateRange.value;
    await fetchChartData(start, end);
    renderChart();
    await fetchAlarmTypeData();
    await fetchTrendData();
    message.success('数据刷新成功');
  } catch (error) {
    console.error('刷新数据失败:', error);
    message.error('刷新数据失败');
  }
}

// 监听时间区间变化
watch(dateRange, ([start, end]) => {
  fetchChartData(start, end).then(renderChart);
});

// 页面初始化
onMounted(async () => {
  await loadProductOptions();
  await loadPropertyOptions();
  await refreshData();

  // 初始化趋势图
  const { renderEcharts: renderTrendChart } = useEcharts(trendChartRef);
  renderEchartsRef.value = renderTrendChart;

  // 设置查询表单初始值
  await queryFormApi.setValues({
    ...queryFormValues.value,
    property: selectedProperty.value,
  });
});
</script>

<style scoped>
.card-title {
  font-size: 24px;
  margin-bottom: 8px;
  font-weight: 600;
  letter-spacing: -0.01em;
  color: #020817;
}

.flex-row-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.right-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chart-left {
  width: 100%;
}


</style>