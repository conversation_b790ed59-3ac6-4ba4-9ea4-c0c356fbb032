<script lang="ts" setup>
import { h, ref, onMounted, reactive, computed } from 'vue';
import { cloneDeep } from 'lodash-es';
import {
  Button, RadioGroup, RadioButton, message, Tag, Modal, Popconfirm,
  Switch, Card, Tooltip, Dropdown, Menu as AntdMenu, QRCode,
} from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import { useVbenForm, z } from '#/adapter/form';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer, getPopupContainer, addFullName } from '@vben/utils';
import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, Delete, Status } from '#/api/device/iotDevice';
import { Edit } from '#/api/device/iotBatchOperation';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import editDrawer from './edit.vue';
import viewDrawer from './view.vue';
import { ListNoPage as ListProduct } from '#/api/device/iotProduct';
import { router } from '#/router';
import { useRoute } from 'vue-router';
import { IconifyIcon } from '@vben/icons';
import { getSysDeptTreeApi } from '#/api/system/dept';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { processImageUrl, handleImageError } from '#/utils/image';
import { List as ProjectList } from '#/api/project/iotProject';
import viewModal from './qrCode.vue'

const deviceRoute = useRoute();
const transportOptions = getDictOptions(DictEnum.TRANSPORT);
// 设备状态颜色映射
const deviceStateColorMap: { [key: string]: { color: string; bg: string; border: string } } = {
  '1': { color: '#faad14', bg: '#fffbe6', border: '1px solid #ffe58f' }, // 未激活（黄色）
  '2': { color: '#52c41a', bg: '#f6ffed', border: '1px solid #b7eb8f' }, // 在线（绿色）
  '3': { color: '#909399', bg: '#f4f6fa', border: '1px solid #d9d9d9' }  // 离线/禁用（灰色）
};
const deviceStateOptions = getDictOptions(DictEnum.DEVICE_STATE); // 设备状态字典
function getDeviceStateStyle(value: string | number) {
  return deviceStateColorMap[String(value)] || deviceStateColorMap['3'];
}
function getDeviceStateLabel(value: string | number) {
  const found = deviceStateOptions.find(opt => String(opt.value) === String(value));
  return found ? found.label : value;
}
function getOptionLabel(value: string | number) {
  const found = transportOptions.find(opt => opt.value == value);
  return found ? found.label : value;
}
const channelTypeOptions = getDictOptions(DictEnum.CHANNEL_TYPE);

function getChannelTypeLabel(value: string | number) {
  const found = channelTypeOptions.find(opt => String(opt.value) === String(value));
  return found ? found.label : value;
}

type Option = {
  label: string;
  value: string;
  deviceType?: number | string; // 添加deviceType字段
};

const productOptions = ref<Option[]>([]);
const projectOptions = ref<Option[]>([]);
const deptTreeData = ref<any[]>([]);
async function loadProductOptions() {
  const res = await ListProduct({
    page: 1,
    pageSize: 1000,
  });
  if (!res || !res.items) {
    productOptions.value = [];
  } else {
    productOptions.value = res.items.map((item: any) => ({
      label: item.productName,
      value: item.productKey,
      deviceType: item.deviceType, // 保存deviceType信息
    }));
  }
  console.log('产品选项加载完成，包含deviceType:', productOptions.value);
}

async function loadProjectOptions() {
  const res = await ProjectList({
    page: 1,
    pageSize: 1000,
  });
  if (!res || !res.items) {
    projectOptions.value = [];
  } else {
    projectOptions.value = res.items.map((item: any) => ({
      label: item.projectName,
      value: item.projectId,
    }));
  }
  console.log('项目选项加载完成:', projectOptions.value);

}

function updateSearchFormOptions() {
  console.log('更新搜索表单选项', deptTreeData.value);
  gridApi.formApi.updateSchema([
    {
      fieldName: 'productKey',
      component: 'Select',
      label: '所属产品',
      componentProps: {
        placeholder: '请选择产品',
        showSearch: true,
        allowClear: true,
        options: productOptions.value,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        onUpdateValue: (e: any) => {
          console.log('产品选择:', e);
        },
      },
    },
    {
      fieldName: 'projectId',
      component: 'Select',
      label: '所属项目',
      componentProps: {
        placeholder: '请选择项目',
        showSearch: true,
        allowClear: true,
        options: projectOptions.value,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        onUpdateValue: (e: any) => {
          console.log('项目选择:', e);
        },
      },
    },
    {
      componentProps: {
        fieldNames: { label: 'deptName', value: 'deptId' },
        showSearch: true,
        treeData: deptTreeData.value,
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        treeNodeLabelProp: 'fullName',
        // 选中后显示在输入框的值
        displayRender: (label: any, selected: any, node: any) => {
          return node.props.dataRef.fullName || label;
        },
        // 模糊搜索
        filterTreeNode: (input: string, treeNode: any) => {
          const label = treeNode.deptName || treeNode.fullName || '';
          return label.toLowerCase().includes(input.toLowerCase());
        },
      },
      fieldName: 'deptId',
    },
  ]);
}

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'deviceId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let res = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
        if (gridType.value == '2') {
          let newItems = [{ list: res.items }];
          res.items = newItems;
        }
        return res;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: true,
    search: true,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});
const [ViewDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: viewDrawer,
});
function handlePreview(record: RowType) {
  drawerApi.setData({ record });
  drawerApi.open();
}
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});

// 批量操作相关函数定义
async function handleBatchAddCancel() {
  batchAddModalApi.close();
  await batchAddFormApi.resetForm();
}

async function handleBatchAddConfirm() {
  try {
    batchAddModalApi.setState({ confirmLoading: true, loading: true });
    const { valid } = await batchAddFormApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await batchAddFormApi.getValues());

    // 处理设备标识字段：将多行文本转换为数组，并映射字段名
    if (data.deviceKeys && typeof data.deviceKeys === 'string') {
      // 按回车分割，过滤空行，去除首尾空格
      data.deviceKeyList = data.deviceKeys
        .split('\n')
        .map(item => item.trim())
        .filter(item => item.length > 0);

      // 删除原字段，因为后端期望的是 deviceKeyList
      delete data.deviceKeys;
    } else if (data.deviceKeys && Array.isArray(data.deviceKeys)) {
      // 如果已经是数组，直接映射字段名
      data.deviceKeyList = data.deviceKeys;
      delete data.deviceKeys;
    }

    console.log('提交的数据:', data);

    // 调用批量操作API
    try {
      console.log('开始调用批量操作API...');
      await Edit(data);
      console.log('批量操作API调用成功');
    } catch (apiError) {
      console.error('批量操作API调用失败:', apiError);
      throw apiError; // 重新抛出错误，让外层catch处理
    }

    // 根据操作类型显示不同的成功消息
    let successMessage = '';
    switch (data.type) {
      case '1':
        successMessage = '批量新增操作成功';
        break;
      case '2':
        successMessage = '机构过户操作成功';
        break;
      case '3':
        successMessage = '批量删除操作成功';
        break;
      case '4':
        successMessage = '项目划拨操作成功';
        break;
      default:
        successMessage = '操作成功';
    }

    message.success(successMessage);
    await handleRefresh(); // 刷新表格数据
    await handleBatchAddCancel();
  } catch (error) {
    console.error(error);
    // 根据操作类型显示不同的错误消息
    const formData = await batchAddFormApi.getValues();
    let errorMessage = '';
    switch (formData.type) {
      case '1':
        errorMessage = '批量新增操作失败';
        break;
      case '2':
        errorMessage = '机构过户操作失败';
        break;
      case '3':
        errorMessage = '批量删除操作失败';
        break;
      case '4':
        errorMessage = '项目划拨操作失败';
        break;
      default:
        errorMessage = '操作失败';
    }
    message.error(errorMessage);
  } finally {
    batchAddModalApi.setState({ confirmLoading: false, loading: false });
  }
}

// 批量操作弹窗标题
const batchModalTitle = ref('批量操作');

// 批量新增弹窗 - 在函数定义之后
const [BatchAddModal, batchAddModalApi] = useVbenModal({
  title: '批量操作',
  onCancel: handleBatchAddCancel,
  onConfirm: handleBatchAddConfirm,
});

// 项目划拨弹窗 - 复用批量新增弹窗和表单
async function handleProjectTransfer() {
  console.log('开始处理项目划拨...');

  // 获取表格中选中的设备行
  const selectedRows = gridApi.grid.getCheckboxRecords();
  console.log('选中的设备行:', selectedRows);

  // 设置弹窗标题
  batchModalTitle.value = '项目划拨';

  // 先打开弹窗，提升响应速度
  console.log('准备打开项目划拨弹窗...');
  batchAddModalApi.open();
  console.log('项目划拨弹窗已打开');

  // 然后异步加载数据
  try {
    // 设置操作类型为项目划拨（类型4）
    await batchAddFormApi.setFieldValue('type', '4');

    // 如果有选中的设备，自动填充设备标识；如果没有选中，用户可以手动输入
    if (selectedRows && selectedRows.length > 0) {
      // 提取选中设备的设备标识
      const deviceKeys = selectedRows.map((row: any) => row.deviceKey).filter(key => key);
      console.log('提取的设备标识:', deviceKeys);

      if (deviceKeys.length > 0) {
        // 将选中的设备标识填充到设备标识字段（每个设备标识占一行）
        const deviceKeysText = deviceKeys.join('\n');
        await batchAddFormApi.setFieldValue('deviceKeys', deviceKeysText);
        console.log('设备标识已自动填充:', deviceKeysText);
      } else {
        console.log('选中的设备中没有有效的设备标识，用户需手动输入');
      }
    } else {
      console.log('没有选中设备，用户需手动输入设备标识');
    }

    // 加载所有下拉选项数据
    await loadAllBatchAddOptions();

    // 更新批量新增表单schema中的下拉选项
    updateBatchAddSchemaOptions();

    console.log('项目划拨数据加载完成');
  } catch (error) {
    console.error('加载项目划拨选项数据失败:', error);
    message.error('加载数据失败，请稍后重试');
  }
}

// 批量删除弹窗 - 复用批量新增弹窗和表单
async function handleBatchDelete() {
  console.log('开始处理批量删除...');

  // 获取表格中选中的设备行
  const selectedRows = gridApi.grid.getCheckboxRecords();
  console.log('选中的设备行:', selectedRows);

  // 设置弹窗标题
  batchModalTitle.value = '批量删除';

  // 先打开弹窗，提升响应速度
  console.log('准备打开批量删除弹窗...');
  batchAddModalApi.open();
  console.log('批量删除弹窗已打开');

  // 然后异步加载数据
  try {
    // 设置操作类型为批量删除（类型3）
    await batchAddFormApi.setFieldValue('type', '3');

    // 如果有选中的设备，自动填充设备标识；如果没有选中，用户可以手动输入
    if (selectedRows && selectedRows.length > 0) {
      // 提取选中设备的设备标识
      const deviceKeys = selectedRows.map((row: any) => row.deviceKey).filter(key => key);
      console.log('提取的设备标识:', deviceKeys);

      if (deviceKeys.length > 0) {
        // 将选中的设备标识填充到设备标识字段（每个设备标识占一行）
        const deviceKeysText = deviceKeys.join('\n');
        await batchAddFormApi.setFieldValue('deviceKeys', deviceKeysText);
        console.log('设备标识已自动填充:', deviceKeysText);
      } else {
        console.log('选中的设备中没有有效的设备标识，用户需手动输入');
      }
    } else {
      console.log('没有选中设备，用户需手动输入设备标识');
    }

    // 加载所有下拉选项数据
    await loadAllBatchAddOptions();

    // 更新批量新增表单schema中的下拉选项
    updateBatchAddSchemaOptions();

    console.log('批量删除数据加载完成');
  } catch (error) {
    console.error('加载批量删除选项数据失败:', error);
    message.error('加载数据失败，请稍后重试');
  }
}

// 机构过户弹窗 - 复用批量新增弹窗和表单
async function handleDeptTransfer() {
  console.log('开始处理机构过户...');

  // 获取表格中选中的设备行
  const selectedRows = gridApi.grid.getCheckboxRecords();
  console.log('选中的设备行:', selectedRows);

  // 设置弹窗标题
  batchModalTitle.value = '机构过户';

  // 先打开弹窗，提升响应速度
  console.log('准备打开机构过户弹窗...');
  batchAddModalApi.open();
  console.log('机构过户弹窗已打开');

  // 然后异步加载数据
  try {
    // 设置操作类型为机构过户（类型2）
    await batchAddFormApi.setFieldValue('type', '2');

    // 如果有选中的设备，自动填充设备标识；如果没有选中，用户可以手动输入
    if (selectedRows && selectedRows.length > 0) {
      // 提取选中设备的设备标识
      const deviceKeys = selectedRows.map((row: any) => row.deviceKey).filter(key => key);
      console.log('提取的设备标识:', deviceKeys);

      if (deviceKeys.length > 0) {
        // 将选中的设备标识填充到设备标识字段（每个设备标识占一行）
        const deviceKeysText = deviceKeys.join('\n');
        await batchAddFormApi.setFieldValue('deviceKeys', deviceKeysText);
        console.log('设备标识已自动填充:', deviceKeysText);
      } else {
        console.log('选中的设备中没有有效的设备标识，用户需手动输入');
      }
    } else {
      console.log('没有选中设备，用户需手动输入设备标识');
    }

    // 加载所有下拉选项数据
    await loadAllBatchAddOptions();

    // 更新批量新增表单schema中的下拉选项
    updateBatchAddSchemaOptions();

    console.log('机构过户数据加载完成');
  } catch (error) {
    console.error('加载机构过户选项数据失败:', error);
    message.error('加载数据失败，请稍后重试');
  }
}



// 统一管理批量新增表单的所有选项数据
const batchAddOptions = reactive({
  product: [] as Array<{ label: string; value: string }>,
  targetDept: [] as Array<{ label: string; value: string }>,
  project: [] as Array<{ label: string; value: string }>,
});

// 加载批量操作选项数据
async function loadBatchAddOptions() {
  try {
    console.log('开始加载批量新增选项...');

    // 导入批量操作的API并调用
    const { List: BatchOperationList } = await import('#/api/device/iotBatchOperation');
    const response = await BatchOperationList({ page: 1, pageSize: 1000 });

    console.log('批量操作API响应:', response);

    if (response?.items && response.items.length > 0) {
      // 从所有记录中提取唯一值
      const extractUniqueValues = (field: string, prefix: string = '') => {
        const uniqueValues = [...new Set(
          response.items
            .map((item: any) => item[field])
            .filter((value: any) => value !== null && value !== undefined && value !== '')
        )];
        return uniqueValues.map((value: any) => ({
          label: prefix ? `${prefix} ${value}` : String(value),
          value: value,
        }));
      };

      const result = {
        targetDept: extractUniqueValues('targetDept'),
        project: extractUniqueValues('projectId'),
      };
      console.log('提取的批量新增选项数据:', result);
      return result;
    }

    console.log('批量新增API响应中没有items数据或数据为空');
    return { targetDept: [], project: [] };
  } catch (error) {
    console.error('加载批量新增选项失败:', error);
    // 返回默认的空数组，不阻塞功能
    return { targetDept: [], project: [] };
  }
}
async function getDeptTree() {
  const treeRes = await getSysDeptTreeApi();
  const treeData = treeRes.items;
  addFullName(treeData, 'deptName', ' / ');
  return treeData;
}
//加载机构选项
async function loadDeptOptions() {
  const treeData = await getDeptTree();
  batchAddFormApi.updateSchema([
    {
      componentProps: {
        fieldNames: { label: 'deptName', value: 'deptId' },
        showSearch: true,
        treeData: treeData,
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        treeNodeLabelProp: 'fullName',
        // 选中后显示在输入框的值
        displayRender: (label: any, selected: any, node: any) => {
          return node.props.dataRef.fullName || label;
        },
        // 模糊搜索
        filterTreeNode: (input: string, treeNode: any) => {
          const label = treeNode.deptName || treeNode.fullName || '';
          return label.toLowerCase().includes(input.toLowerCase());
        },
      },
      fieldName: 'targetDept',
    },
  ]);
}


// 加载批量操作的项目选项
async function loadBatchProjectOptions() {
  const response = await ProjectList({ page: 1, pageSize: 1000 });
  if (response && response.items) {
    batchAddOptions.project = response.items.map((item: any) => ({
      label: item.projectName,
      value: item.projectId,
    }));
  } else {
    batchAddOptions.project = [];
  }
}

// 加载批量新增表单的所有选项数据
async function loadAllBatchAddOptions() {
  try {
    console.log('开始加载批量新增表单所有选项数据...');
    await Promise.all([
      Promise.resolve().then(() => {
        batchAddOptions.product = productOptions.value;
      }),
      loadBatchProjectOptions(),
      loadDeptOptions(), // 确保机构树数据加载
      loadBatchAddOptions().then(result => {
        batchAddOptions.targetDept = result.targetDept;
      })
    ]);
    console.log('批量新增表单所有选项数据加载完成');
  } catch (error) {
    console.error('加载批量新增选项数据时发生错误:', error);
  }
}

// 更新批量新增表单schema中的下拉选项（targetDept 只用 TreeSelect，projectId 用动态项目列表）
function updateBatchAddSchemaOptions() {
  batchAddFormApi.updateSchema([
    {
      fieldName: 'productKey',
      component: 'Select',
      label: '所属产品',
      componentProps: {
        placeholder: '请选择产品',
        options: batchAddOptions.product,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        onUpdateValue: (e: any) => {
          console.log('批量新增-产品选择:', e);
        },
      },
    },
    // targetDept 只用 TreeSelect，不用 options
    {
      fieldName: 'projectId',
      component: 'Select',
      label: '接收项目',
      componentProps: {
        placeholder: '请选择接收项目',
        options: batchAddOptions.project,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        onUpdateValue: (e: any) => {
          console.log('批量新增-接收项目选择:', e);
        },
      },
    },
  ]);
}

// 批量新增表单
const batchAddSchema = [
  {
    fieldName: 'batchId',
    component: 'Input',
    label: 'ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'type',
    component: 'Select',
    label: '操作类型',
    defaultValue: '1',
    componentProps: {
      disabled: true,
      placeholder: '请选择操作类型',
      options: getDictOptions('batch_operation_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired',
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
    componentProps: {
      placeholder: '请选择产品',
      options: [], // 初始为空，动态更新
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      onUpdateValue: (e: any) => {
        console.log('产品选择:', e);
      },
    },
    rules: 'selectRequired',
    //只有在操作类型字段是 “新增”，也就是1的时候，才显示且为必填
    dependencies: {
      show: (formData: Record<string, any>) => formData.type === '1',
      triggerFields: ['type'],
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'deviceKeys',
    component: 'Textarea',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识，多个设备标识请用回车分隔',
      rows: 3,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.string()
      .min(1, '设备标识不能为空')
      .superRefine((value, ctx) => {
        if (!value || value.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: '设备标识不能为空'
          });
          return;
        }

        // 按回车分隔设备标识
        const deviceKeys = value.split('\n').filter((key: string) => key.trim() !== '');

        for (let i = 0; i < deviceKeys.length; i++) {
          const deviceKey = deviceKeys[i]?.trim();

          if (!deviceKey) continue;

          // 检查长度
          if (deviceKey.length < 1) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `第${i + 1}行：设备标识不能为空`
            });
            continue;
          }
          if (deviceKey.length > 32) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `第${i + 1}行：设备标识最大长度32个字符`
            });
            continue;
          }

          // 检查字符组成（只能是字母、数字、下划线）
          if (!/^[a-zA-Z0-9_]+$/.test(deviceKey)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `第${i + 1}行：设备标识只能包含字母、数字、下划线`
            });
            continue;
          }
        }
      }),
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'productionBatch',
    component: 'Input',
    label: '生产批次',
    componentProps: {
      placeholder: '请输入生产批次',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    //类型为新增的显示非必填
    dependencies: {
      show: (formData: Record<string, any>) => formData.type === '1',
      triggerFields: ['type'],
    },
    formItemClass: 'col-span-2',
  }, {
    fieldName: 'packingBatch',
    component: 'Input',
    label: '封箱批次',
    componentProps: {
      placeholder: '请输入封箱批次',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    //类型为新增的显示非必填
    dependencies: {
      show: (formData: Record<string, any>) => formData.type === '1',
      triggerFields: ['type'],
    },
    formItemClass: 'col-span-2',
  }, {
    fieldName: 'targetDept',
    component: 'TreeSelect',
    label: '接收机构',
    componentProps: {
      getPopupContainer,
    }, rules: 'selectRequired',
    //类型为2，机构过户时显示且必填
    dependencies: {
      show: (formData: Record<string, any>) => formData.type === '2',
      triggerFields: ['type'],
    },
    formItemClass: 'col-span-2',
  },
  //接收项目 projectId
  {
    fieldName: 'projectId',
    component: 'Select',
    label: '接收项目',
    componentProps: {
      placeholder: '请选择接收项目',
      options: [], // 初始为空，动态更新
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      onUpdateValue: (e: any) => {
        console.log('接收项目选择:', e);
      },
    },
    rules: 'selectRequired',
    //类型为4划拨的时候显示必填
    dependencies: {
      show: (formData: Record<string, any>) => formData.type === '4',
      triggerFields: ['type'],
    },
    formItemClass: 'col-span-2',
  }, {
    fieldName: 'remark',
    component: 'Textarea',
    label: '操作原因',
    componentProps: {
      placeholder: '请输入操作原因',
      rows: 3,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    formItemClass: 'col-span-2',
  },
];

const [BatchAddForm, batchAddFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
  },
  layout: 'vertical',
  schema: batchAddSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

async function handleBatchAdd() {
  try {
    batchModalTitle.value = '批量新增';
    batchAddModalApi.open();
    setTimeout(async () => {
      try {
        await loadAllBatchAddOptions();
        await loadDeptOptions();
        await batchAddFormApi.setFieldValue('type', '1');
        updateBatchAddSchemaOptions();
      } catch (error) {
        console.error('数据加载失败:', error);
      }
    }, 100);
  } catch (error) {
    console.error('打开弹窗失败:', error);
    message.error('打开弹窗失败，请稍后重试');
  }
}
function handleAdd() {
  editDrawerApi.setData({
    update: false,
    view: false,
    productOptions: productOptions.value,
  });
  editDrawerApi.open();
}
function handleEdit(row: RowType) {
  editDrawerApi.setData({
    id: row.deviceId,
    update: true,
    view: false,
    productOptions: productOptions.value,
  });
  editDrawerApi.open();
}
async function handleDelete(row: RowType) {
  await Delete({ deviceId: [row.deviceId] });
  message.success('删除成功');
  await handleRefresh();
}
async function handleRefresh() {
  await gridApi.query();
}
function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const ids: string[] = [];
  for (const row of rows) {
    ids.push(row.deviceId);
  }
  if (ids.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await Delete({ deviceId: ids });
      message.success('删除成功');
      await handleRefresh();
    },
  });
}
async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '设备表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success('导出成功');
}
async function handleStatusChange(row: RowType) {
  await Status({ deviceId: row.deviceId, status: row.status });
  await message.success('操作成功');
  await handleRefresh();
}
const cardListColumns = [{
  field: 'list',
  slots: {
    default: 'list',
  },
}];

const cardListPager = {
  pageSizes: [12, 24, 36, 48, 60],
  pageSize: 12,
};
const gridType = ref('1');
function handleChangeGrid(value: string) {
  gridType.value = value;
  configGridOptions();
}

function configGridOptions() {
  gridApi.setGridOptions({
    checkboxConfig: {
      highlight: true,
      labelField: 'productId',
    },
    border: gridType.value == '2' ? 'none' : 'default',
    columns: gridType.value == '2' ? cardListColumns : columns,  //columns,
    showHeader: gridType.value == '2' ? false : true,
    pagerConfig: gridType.value == '2' ? cardListPager : {},
  });
  gridApi.reload();
}

const handleDetail = (deviceId: number, tabKey?: string) => {
  // 通过deviceId在表格数据中查找对应的设备信息
  const deviceData = gridApi.grid.getData();

  /** 表格类型(列表-卡片)不同时,gridOptions会对结果进行不同处理,在此处处理不同结果,使结果统一 */
  const currentDevice = gridType.value == "1" ? deviceData.find((item: any) => item.deviceId === deviceId) : deviceData[0].list.find((item: any) => item.deviceId === deviceId);
  console.log("currentDevice", currentDevice)
  if (currentDevice) {
    // 通过productKey查找对应的产品信息
    const productInfo = productOptions.value.find((p: any) => p.value === currentDevice.productKey);

    console.log("productInfo", productInfo)
    // 判断是否为监控设备（deviceType = 4）
    const isMonitorDevice = productInfo && (
      productInfo.deviceType === 4 ||
      productInfo.deviceType === '4'
    );

    console.log('设备详情跳转判断:', {
      deviceId,
      productKey: currentDevice.productKey,
      productInfo,
      isMonitorDevice
    });

    let url = '';
    if (isMonitorDevice) {
      // 监控设备跳转到监控详情页，传递deviceKey参数
      url = `/device/monitorDetail?deviceKey=${currentDevice.deviceKey}`;
    } else {
      // 普通设备跳转到普通详情页
      url = `/device/deviceDetail?deviceId=${deviceId}`;
    }

    if (tabKey) {
      if (productInfo!.deviceType == '4') {
        url += `&tab=preview`
      } else {
        url += `&tab=${tabKey}`;
      }
    }
    router.push(url);

  } else {
    console.error('未找到设备信息，使用默认详情页');
    let url = `/device/deviceDetail?deviceId=${deviceId}`;
    if (tabKey) {
      url += `&tab=${tabKey}`;
    }
    router.push(url);
  };
}
// 二维码操作

const [ViewModal, modalApi] = useVbenModal({
  connectedComponent: viewModal,
});
const handleShowQrcode = (row: RowType) => {
  modalApi.setData({ row });
  modalApi.open();
};


onMounted(async () => {
  await loadProductOptions();
  await loadProjectOptions();
  const treeData = await getDeptTree();
  deptTreeData.value = treeData;
  updateSearchFormOptions();
  // 1. 获取路由参数
  const { productName, productKey } = deviceRoute.query;
  if (productKey) {
    // 2. 设置表单字段
    gridApi.formApi.setFieldValue('productKey', productKey); // 你的下拉框字段名是 productKey
    // 3. 自动触发一次搜索
    console.log('自动触发搜索', productName);
    // gridApi.query();
  }
});


const label = computed(() => (item: any) => {
  if (item.product != undefined && item.product.deviceType != undefined && item.product.deviceType == 4) {
    return "实时预览"
  }
  return "设备状态";
})

</script>
<template>
  <Page auto-content-height>
    <Grid table-title="设备列表">
      <template #toolbar-tools>

        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiPlus)" @click="handleAdd"
          v-access:code="'cpm:device:iotDevice:edit'">
          新增
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiExport)" @click="handleExport"
          v-access:code="'cpm:device:iotDevice:export'">
          导出
        </Button>
        <Dropdown>
          <Button class="mr-2 flex items-center" type="default">
            批量操作
            <IconifyIcon icon="ant-design:down-outlined" class="ml-1" />
          </Button>
          <template #overlay>
            <AntdMenu>
              <AntdMenu.Item @click="handleBatchAdd" v-access:code="'cpm:device:iotDevice:edit'">
                批量新增
              </AntdMenu.Item>
              <AntdMenu.Item @click="handleDeptTransfer" v-access:code="'cpm:device:iotDevice:edit'">
                机构过户
              </AntdMenu.Item>
              <AntdMenu.Item @click="handleProjectTransfer" v-access:code="'cpm:device:iotDevice:edit'">
                项目划拨
              </AntdMenu.Item>
              <AntdMenu.Item @click="handleBatchDelete" v-access:code="'cpm:device:iotDevice:delete'">
                批量删除
              </AntdMenu.Item>
            </AntdMenu>
          </template>
        </Dropdown>
        <RadioGroup class="mr-2 flex items-center" @change="handleChangeGrid($event.target.value)"
          v-model:value="gridType">
          <RadioButton value="1"
            style="display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0;">
            <IconifyIcon icon="ant-design:menu-outlined" />
          </RadioButton>
          <RadioButton value="2"
            style="display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0;">
            <IconifyIcon icon="mdi:view-grid" />
          </RadioButton>
        </RadioGroup>
      </template>
      <template #list="{ row }">
        <div class="grid grid-cols-1  lg:grid-cols-2  xl:grid-cols-3 2xl:grid-cols-4  gap-4">
          <Card v-for="item in row.list" :key="item.deviceId" class="w-full"
            :bodyStyle="{ padding: '14px 14px 0 14px' }">
            <div class="flex items-center text-left w-full ">
              <!-- 左侧图片 -->
              <img :src="processImageUrl(item.imgUrl)" alt="产品图片"
                style="width: 62px; height: 62px; border-radius: 8px; object-fit: cover; margin-right: 12px;"
                @error="handleImageError" />
              <div class="flex-1 text-left ">
                <div class="flex  justify-between">
                  <div class="flex-1 text-lg">
                    <div class="overflow-hidden text-overflow-ellipsis h-[30px]">{{ item.deviceName }}</div>
                  </div>
                  <!-- 信号强度 -->
                  <div class="signal-indicator" style="margin-left: 8px; display: inline-block; position: relative;">
                    <svg width="25" height="25" viewBox="0 0 18 18">
                      <!-- 信号条背景（灰色） ，先用蓝色看了下效果-->
                      <rect x="2" y="13" width="2" height="3" rx="1" fill="#d9d9d9" />
                      <rect x="6" y="10" width="2" height="6" rx="1" fill="#d9d9d9" />
                      <rect x="10" y="7" width="2" height="9" rx="1" fill="#d9d9d9" />
                      <rect x="14" y="4" width="2" height="12" rx="1" fill="#d9d9d9" />
                      <!-- 信号条前景（蓝色，根据信号强度显示） -->
                      <rect v-if="(item.rssi || 0) >= 1" x="2" y="13" width="2" height="3" rx="1" fill="#1890ff" />
                      <rect v-if="(item.rssi || 0) >= 2" x="6" y="10" width="2" height="6" rx="1" fill="#1890ff" />
                      <rect v-if="(item.rssi || 0) >= 3" x="10" y="7" width="2" height="9" rx="1" fill="#1890ff" />
                      <rect v-if="(item.rssi || 0) >= 4" x="14" y="4" width="2" height="12" rx="1" fill="#1890ff" />
                    </svg>
                  </div>
                </div>
                <div style="margin-top: 4px; text-align: left; display: flex; align-items: center; gap: 4px;">
                  <!-- 设备状态Tag -->
                  <Tag :style="{
                    borderRadius: '6px',
                    color: (getDeviceStateStyle(item.deviceState)?.color) ?? '#909399',
                    background: (getDeviceStateStyle(item.deviceState)?.bg) ?? '#f4f6fa',
                    border: (getDeviceStateStyle(item.deviceState)?.border) ?? '1px solid #d9d9d9',
                    flexShrink: 0
                  }">
                    {{ getDeviceStateLabel(item.deviceState) }}
                  </Tag>
                  <!-- 传输方式Tag -->
                  <Tooltip>
                    <template #title>{{ getOptionLabel(item.product?.transport) }}</template>
                    <Tag style="border-radius: 4px; color: #909399; background: #f5f5f5;max-width: 88px; "
                      class="truncate inline-block">
                      {{ getOptionLabel(item.product?.transport) }}
                    </Tag>
                  </Tooltip>
                  <Tooltip>
                    <template #title>{{ getChannelTypeLabel(item.product?.channelType) }}</template>
                    <Tag style=" border-radius: 4px; color: #909399; background: #f5f5f5;max-width: 88px;"
                      class="truncate inline-block">
                      {{ getChannelTypeLabel(item.product?.channelType) }}
                    </Tag>
                  </Tooltip>
                </div>
              </div>
            </div>
            <!-- 下面内容 -->
            <div class="mt-4 mb-4">
              <div style="display: flex;">
                <div style="flex: 1; text-align: left;">
                  <div class="flex items-center">
                    <span class="inline-block w-[42px]">编号：</span>
                    <Tooltip>
                      <template #title>{{ item.deviceKey }}</template>
                      <span class="text-[#3b82f6] truncate inline-block" style="max-width: 105px;">
                        {{ item.deviceKey }}
                      </span>
                    </Tooltip>
                  </div>
                  <!-- 产品名称超出隐藏 -->
                  <div class="flex items-center mt-2">
                    <span class="inline-block w-[42px]">产品：</span>
                    <Tooltip>
                      <template #title>{{ item.productName }}</template>
                      <span class="truncate inline-block" style="max-width: 105px;">
                        {{ item.productName }}
                      </span>
                    </Tooltip>
                  </div>
                </div>
                <div style="flex: 1; text-align: left;">
                  <div class="flex items-center">
                    <span class="inline-block w-[70px]">机构名称：</span>
                    <Tooltip>
                      <template #title>{{ item.deptName }}</template>
                      <span class="truncate inline-block" style="max-width: 80px;">
                        {{ item.deptName }}
                      </span>
                    </Tooltip>
                  </div>
                  <div class="flex items-center mt-2">
                    <span class="inline-block w-[70px]">激活时间：</span>
                    <Tooltip>
                      <template #title>{{ item.activeTime ? item.activeTime : '未激活' }}</template>
                      <span class="truncate inline-block" style="max-width: 80px;">
                        {{ item.activeTime ? item.activeTime : '未激活' }}
                      </span>
                    </Tooltip>
                  </div>
                </div>
              </div>
            </div>
            <template #actions>
              <Button type="link" class="h-6 custom-link-button" @click="handleDetail(item.deviceId)">
                查看详情
              </Button>
              <Button type="link" class="h-6 custom-link-button" @click="handleDetail(item.deviceId, 'a1')">
                {{ label(item) }}
              </Button>
            </template>

          </Card>
        </div>
      </template>
      <template #status="{ row }">
        <Switch v-model:checked="row.status" :checkedValue="'0'" :unCheckedValue="'1'" @change="handleStatusChange(row)"
          :disabled="!hasAccessByCodes(['cpm:device:iotDevice:status'])" />
      </template>
      <template #action="{ row }">
        <div class="flex items-center">
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleDetail(row.deviceId)"
            v-access:code="'cpm:device:iotDevice:view'">
            详情
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row)"
            v-access:code="'cpm:device:iotDevice:edit'">
            修改
          </Button>
          <AccessControl :codes="['cpm:device:iotDevice:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
      <template #deviceState="{ row }">
        <Tag :style="{
          borderRadius: '4px',
          minWidth: '48px',
          textAlign: 'center',
          color: row.deviceState === 2 ? '#52c41a' : row.deviceState === 1 ? '#faad14' : '#909399',
          background: row.deviceState === 2 ? '#f6ffed' : row.deviceState === 1 ? '#fffbe6' : '#f4f6fa',
          border: row.deviceState === 2
            ? '1px solid #b7eb8f'
            : row.deviceState === 1
              ? '1px solid #ffe58f'
              : '1px solid #d9d9d9'
        }">
          {{ getDeviceStateLabel(row.deviceState) }}
        </Tag>
      </template>
      <template #deviceKey="{ row }">
        <div class="flex items-center">
          <!-- 设备标识文本 -->
          <span>{{ row.deviceKey }}</span>

          <!-- 二维码按钮 -->
          <Button type="link" size="small" style="padding: 0 4px; margin-left: 8px" @click="handleShowQrcode(row)">
            <iconify-icon icon="ant-design:qrcode-outlined" style="color: #409eff; width: 18px; height: 18px" />
          </Button>
        </div>
      </template>
    </Grid>
    <EditDrawer @reload="handleRefresh" />
    <ViewDrawer />
    <ViewModal />

    <!-- 批量操作弹窗（批量新增、机构过户、项目划拨共用） -->
    <BatchAddModal :title="batchModalTitle" class="w-[800px]">
      <template #default>
        <div class="p-4">
          <BatchAddForm />
        </div>
      </template>
    </BatchAddModal>
  </Page>
</template>

<style scoped>
.custom-link-button {
  color: #666666 !important;
}

.custom-link-button:hover {
  color: #333333 !important;
}

.custom-link-button:focus {
  color: #666666 !important;
}
</style>
