<template>
  <div class="video-player-container">
    <!-- 视频播放区域 -->
    <div class="video-player-content">
      <div class="video-wrapper">
        <!-- Jessibuca视频播放器 - 总是显示，不依赖在线状态 -->
        <div class="video-container">
          <!-- 播放器主体 -->
          <div class="player-main">
            <jessibucaPlayer ref="jessibucaRef" :video-url="videoUrl" :has-audio="true" :autoplay="true" :live="true"
              :visible="true" @error="onVideoError" @play="onVideoPlay" @pause="onVideoPause" class="video-player" />
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading && !currentDevice" class="loading-state">
          <Spin size="large" />
          <p>正在连接设备...</p>
        </div>

        <!-- 错误状态显示 -->
        <div v-if="streamError && !isStreaming && !isConnecting" class="error-state">
          <div class="error-icon">❌</div>
          <h3>视频流获取失败</h3>
          <p>{{ streamError }}</p>
          <div class="error-actions">
            <Button type="primary" @click="retryStream">重试</Button>
            <Button @click="resetState">重置</Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 控制按钮区域 - 移到视频播放器外部 -->
    <div class="control-panel">
      <div class="control-group">
        <Button type="primary" @click="startStream" :disabled="isStreaming" :loading="isConnecting">
          {{ isStreaming ? '直播中' : '开始直播' }}
        </Button>
        <Button @click="stopStream" :disabled="!isStreaming">
          停止直播
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { Button, Spin, message } from 'ant-design-vue';
import { GB28181StartReview } from '#/api/device/iotDevice';
import jessibucaPlayer from '../components/jessibucaPlayer.vue';

const props = defineProps<{
  deviceId: string;
  videoUrl?: string;
}>();

// 定义事件
const emit = defineEmits<{
  playFailed: [deviceId: string, error: any];
  playSuccess: [deviceId: string];
  playStarted: [deviceId: string]; // 新增：播放开始事件
}>();
//   backToList: [];
// }>();

// 设备信息（简化版，仅用于显示）
const currentDevice = ref<any>({
  deviceId: '',
  cameraKey: '',
  deviceName: '监控设备',
  deviceState: 1
});
const isLoading = ref(false);

// 视频相关状态
const jessibucaRef = ref();
const videoUrl = ref<string>('');
const isStreaming = ref(false);
const isConnecting = ref(false);
const streamError = ref<string>('');

// 获取视频流URL - 调用GB28181StartReview接口
const getVideoStreamUrl = async (deviceKey: string) => {
  try {
    console.log('🔗 调用GB28181StartReview接口获取流地址, deviceKey:', deviceKey);

    const response = await GB28181StartReview({
      deviceKey: deviceKey  // 使用deviceKey参数调用接口
    });
    console.log('🔗 GB28181StartReview接口完整响应:', JSON.stringify(response, null, 2));

    // 根据实际接口响应结构解析流地址
    let streamUrl = '';
    let streamData = null;

    // 尝试从不同层级获取流数据
    if (response && response.data) {
      streamData = response.data;
    } else if (response) {
      streamData = response;
    }

    if (streamData) {
      console.log('📡 解析到的流数据:', streamData);

      // 优先使用FLV格式，因为Jessibuca对FLV支持最好
      if (streamData.flv) {
        streamUrl = streamData.flv;
        console.log('📡 使用FLV流地址:', streamUrl);
      }
      // 备用方案1: HLS格式
      else if (streamData.hls) {
        streamUrl = streamData.hls;
        console.log('📡 使用HLS流地址:', streamUrl);
      }
      // 备用方案2: RTMP格式
      else if (streamData.rtmp) {
        streamUrl = streamData.rtmp;
        console.log('📡 使用RTMP流地址:', streamUrl);
      }
      // 备用方案3: 通用播放地址
      else if (streamData.publishUrl) {
        streamUrl = streamData.publishUrl;
        console.log('📡 使用推流地址:', streamUrl);
      }

      // 记录流相关信息
      if (streamData.streamId) {
        console.log('📡 流ID:', streamData.streamId);
      }
      if (streamData.publishUrlExpired) {
        console.log('📡 流地址过期时间:', streamData.publishUrlExpired);
      }
    }

    console.log('📡 最终解析得到的流地址:', streamUrl);

    if (!streamUrl) {
      console.warn('⚠️ 接口未返回有效的流地址');
      throw new Error('接口未返回有效的流地址');
    }

    return streamUrl;
  } catch (error) {
    console.error('❌ 调用GB28181StartReview接口失败:', error);
    throw error;
  }
};

// 开始直播
const startStream = async () => {
  console.log('🎬 手动开始直播流程');

  try {
    isConnecting.value = true;
    streamError.value = '';

    // 如果已经有视频URL，直接通过Jessibuca播放
    if (videoUrl.value) {
      console.log('🎥 使用已有的视频流URL:', videoUrl.value);
      try {
        if (jessibucaRef.value) {
          jessibucaRef.value.play(videoUrl.value);
          console.log('✅ 手动播放调用成功');
        } else {
          throw new Error('播放器组件未就绪');
        }
        return;
      } catch (playError) {
        console.error('❌ 手动播放失败:', playError);
        emit('playFailed', props.deviceId, playError);
        return;
      }
    }

    // 如果没有视频URL，则获取新的
    const cameraKey = props.deviceId;
    if (!cameraKey) {
      const error = new Error('监控设备标识为空，无法开始直播');
      console.error('❌', error.message);
      message.error(error.message);
      emit('playFailed', props.deviceId, error);
      return;
    }

    console.log('🎥 开始获取监控设备视频流, cameraKey:', cameraKey);

    // 显示加载提示
    const loadingMsg = message.loading('正在获取视频流地址...', 0);

    try {
      // 调用实际接口获取视频流URL
      const streamUrl = await getVideoStreamUrl(String(cameraKey));
      console.log('📡 获取到的视频流URL:', streamUrl);

      // 关闭加载提示
      loadingMsg();

      if (!streamUrl) {
        const error = new Error('获取视频流地址失败');
        console.error('❌', error.message);
        message.error(error.message);
        emit('playFailed', props.deviceId, error);
        return;
      }

      // 设置视频URL，让Jessibuca自动播放
      videoUrl.value = streamUrl;

    } catch (apiError: any) {
      // 关闭加载提示
      loadingMsg();
      console.error('❌ 获取流地址失败:', apiError);

      // 提取错误信息
      let errorMessage = '获取视频流地址失败';
      if (apiError?.response?.data?.message) {
        errorMessage = apiError.response.data.message;
      } else if (apiError?.message) {
        errorMessage = apiError.message;
      }

      streamError.value = errorMessage;
      message.error(errorMessage);
      emit('playFailed', props.deviceId, apiError);
    }
  } catch (error: any) {
    console.error('❌ 开始直播失败:', error);
    message.error('开始直播失败: ' + (error?.message || '未知错误'));
    streamError.value = `直播启动失败: ${error?.message || '未知错误'}`;
    emit('playFailed', props.deviceId, error);
  } finally {
    isConnecting.value = false;
  }
};

// 停止直播
const stopStream = () => {
  try {
    console.log('⏹️ 停止直播');

    if (jessibucaRef.value) {
      // 先清理画面，再停止播放器
      if (jessibucaRef.value.clearView) {
        jessibucaRef.value.clearView();
        console.log('🖥️ 画面已清理');
      }

      jessibucaRef.value.stop();
      console.log('✅ 播放器已停止');
    }

    // 重置状态
    isStreaming.value = false;
    isConnecting.value = false;
    videoUrl.value = '';
    streamError.value = '';

    console.log('✅ 播放状态已重置');
    message.info('直播已停止');
  } catch (error) {
    console.error('❌ 停止直播失败:', error);
    // 即使停止失败，也要重置状态
    isStreaming.value = false;
    isConnecting.value = false;
    videoUrl.value = '';
    streamError.value = '';
    message.error('停止直播失败');
  }
};

// 暴露停止播放方法给父组件使用
const stopVideoStream = () => {
  console.log('🛑 外部调用停止视频播放');
  stopStream();

  // 额外的清理操作，确保画面彻底清空
  setTimeout(() => {
    if (jessibucaRef.value && jessibucaRef.value.clearView) {
      jessibucaRef.value.clearView();
      console.log('🖥️ 延迟清理画面完成');
    }
  }, 100);
};

// 重试获取流
const retryStream = async () => {
  console.log('🔄 重试获取视频流');

  // 重置错误状态
  streamError.value = '';

  // 重新开始直播
  await startStream();
};

// 重置状态
const resetState = () => {
  console.log('🔄 重置播放器状态');

  // 停止当前播放
  stopStream();

  // 清空错误信息
  streamError.value = '';

  // 重置所有状态
  isStreaming.value = false;
  isConnecting.value = false;
  videoUrl.value = '';

  console.log('✅ 播放器状态已重置');
};

// 彻底重置播放器（包括重新初始化内部播放器）
const forceReset = async () => {
  console.log('💥 强制重置播放器');

  // 重置状态
  resetState();

  // 重新初始化内部播放器
  if (jessibucaRef.value && jessibucaRef.value.reinitializePlayer) {
    try {
      await jessibucaRef.value.reinitializePlayer();
      console.log('✅ 内部播放器重新初始化成功');
    } catch (error) {
      console.error('❌ 内部播放器重新初始化失败:', error);
    }
  }
};

// 强制清理状态并触发失败事件（用于处理各种异常情况）
const forceCleanupAndFail = (reason: string) => {
  console.log('💥 强制清理状态并触发失败事件:', reason);

  // 停止播放
  try {
    stopStream();
  } catch (error) {
    console.error('❌ 停止播放失败:', error);
  }

  // 重置所有状态
  isStreaming.value = false;
  isConnecting.value = false;
  videoUrl.value = '';
  streamError.value = reason;

  // 触发失败事件
  const error = new Error(reason);
  emit('playFailed', props.deviceId, error);

  console.log('✅ 强制清理完成');
};

// 暴露方法给父组件
defineExpose({
  stopVideoStream,
  stopStream,
  resetState,
  retryStream,
  forceReset,
  forceCleanupAndFail
});

// Jessibuca播放器事件处理
const onVideoPlay = () => {
  console.log('🎬 视频开始播放 - Jessibuca播放器事件');
  isStreaming.value = true;
  isConnecting.value = false; // 重置连接状态
  streamError.value = '';
  message.success('视频播放成功');

  // 发出播放成功事件
  console.log('✅ 发出播放成功事件, deviceId:', props.deviceId);
  emit('playSuccess', props.deviceId);
};

const onVideoPause = () => {
  console.log('⏸️ 视频已暂停');
};

const onVideoError = (error: any) => {
  console.error('❌ 视频播放错误 - Jessibuca播放器事件:', error);
  isStreaming.value = false;
  isConnecting.value = false; // 重置连接状态

  // 根据错误类型显示不同的提示信息
  let errorMessage = '视频播放出现错误';

  if (typeof error === 'string') {
    if (error.includes('network') || error.includes('连接')) {
      errorMessage = '网络连接错误，请检查视频流地址是否正确';
    } else if (error.includes('decode') || error.includes('解码')) {
      errorMessage = '视频解码错误，可能视频格式不支持';
    } else if (error.includes('timeout') || error.includes('超时')) {
      errorMessage = '连接超时，请检查网络状态';
    }
  }

  streamError.value = errorMessage;
  message.error(errorMessage);

  // 发出播放失败事件
  console.log('❌ 发出播放失败事件, deviceId:', props.deviceId, 'error:', error);
  emit('playFailed', props.deviceId, error);
};

// 返回设备列表 - 已移除顶部导航栏，此功能暂时不需要
// const goBack = () => {
//   // 停止视频播放
//   if (isStreaming.value) {
//     stopStream();
//   }
//   emit('backToList');
// };

onMounted(() => {
  console.log('📺 视频播放器组件挂载, cameraKey:', props.deviceId, 'videoUrl:', props.videoUrl);

  // 初始化基本设备信息用于显示
  currentDevice.value = {
    deviceId: props.deviceId,
    cameraKey: props.deviceId,
    deviceName: `监控设备-${props.deviceId}`,
    deviceState: 1
  };

  // 如果传递了视频URL，设置videoUrl并发出开始事件
  if (props.videoUrl) {
    console.log('📺 检测到视频URL，设置播放地址');
    videoUrl.value = props.videoUrl;

    // 发出播放开始事件
    emit('playStarted', props.deviceId);

    console.log('📺 视频URL已设置，等待Jessibuca自动播放');
  } else {
    console.log('📺 没有视频URL，等待手动播放');
    // 即使没有URL，也发出开始事件，让父组件知道组件已准备好
    emit('playStarted', props.deviceId);
  }

  console.log('📺 视频播放器初始化完成');
});

onUnmounted(() => {
  console.log('📺 视频播放器组件卸载');
  // 停止直播
  if (isStreaming.value) {
    stopStream();
  }
});
</script>

<style scoped>
.video-player-container {
  width: 100%;
  height: 800px;
  display: flex;
  flex-direction: column;
  background: #000;
}

.video-player-content {
  flex: 1;
  display: flex;
  align-items: stretch;
  justify-content: center;
  background: #000;
  padding: 0;
  min-height: 0;
}

.video-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: stretch;
  justify-content: center;
  position: relative;
}

.video-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 0;
}

.player-main {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 播放器容器的比例保持 */
.video-player {
  width: 100% !important;
  height: 100% !important;
}

/* 控制面板样式 - 简化样式 */
.control-panel {
  background: #fff;
  border-top: 1px solid #e8e8e8;
  padding: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  flex-shrink: 0;
}

.control-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.loading-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
  padding: 40px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  border-radius: 16px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 100;
}

.loading-state p {
  color: rgba(255, 255, 255, 0.8);
  margin: 20px 0 0 0;
  font-size: 14px;
  line-height: 1.5;
}

.error-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
  padding: 40px;
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.9) 0%, rgba(255, 77, 79, 0.7) 100%);
  border-radius: 16px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 77, 79, 0.3);
  z-index: 100;
  max-width: 400px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.9;
}

.error-state h3 {
  color: #fff;
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 500;
}

.error-state p {
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 24px 0;
  font-size: 14px;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
