import { requestClient } from '#/api/request';

// 获取设备安装点列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotProjectPoint/list', { params });
}

// 删除/批量删除设备安装点
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotProjectPoint/delete', { ...params });
}

// 添加/编辑设备安装点
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotProjectPoint/edit', { ...params });
}

// 获取设备安装点指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotProjectPoint/view', { params });
}

// 导出设备安装点
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotProjectPoint/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}