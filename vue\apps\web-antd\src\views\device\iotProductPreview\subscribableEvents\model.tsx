import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils';

export class State {
  public seId = 0; // ID
  public seName = ''; // 订阅事件名称
  public productKey = ''; // 产品标识
  public alarmConfigId = 0; // 报警配置ID
  public sceneId = 0; // 场景ID
  public orderNum = 0; // 排序
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'seName',
    component: 'Input',
    label: '订阅事件名称',
    componentProps: {
      placeholder: '请输入订阅事件名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  },];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    // title: 'ID',
    field: 'seId',
    align: 'center',
    width: 100,
    type: 'checkbox',
  },
  {
    title: '订阅事件名称',
    field: 'seName',
    align: 'center',
    width: -1,
  },
  {
    title: '产品标识',
    field: 'productKey',
    align: 'center',
    width: -1,
  },
  // {
  //   title: '场景',
  //   field: 'sceneId',
  //   align: 'center',
  //   width: -1,
  // },
  {
    title: '场景名称',
    field: 'sceneName',
    align: 'center',
    width: -1,
  },
  // {
  //   title: '报警配置',
  //   field: 'alarmConfigId',
  //   align: 'center',
  //   width: -1,
  // },
  {
    title: '报警配置名称',
    field: 'alarmConfigName',
    align: 'center',
    width: -1,
  },
  {
    title: '产品名称',
    field: 'productName',
    align: 'center',
    width: -1,
  },
  {
    title: '排序',
    field: 'orderNum',
    align: 'center',
    width: -1,
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: -1,
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  seId: number;
  seName: string;
  productKey: string;
  alarmConfigId: number;
  sceneId: number | string;
  orderNum: number;
  tenantId: string;
  createdAt: string;
  sceneName: string;
  alarmConfigName: string;
  productName: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'seId', label: 'ID' },
  { field: 'seName', label: '订阅事件名称' },
  { field: 'productKey', label: '产品标识' },
  { field: 'alarmConfigId', label: '报警配置ID' },
  { field: 'sceneId', label: '场景ID' },
  { field: 'orderNum', label: '排序' },
  { field: 'tenantId', label: '租户ID' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'sceneName', label: '场景名称' },
  { field: 'alarmConfigName', label: '报警配置名称' },
  { field: 'productName', label: '产品名称' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'seId',
    component: 'Input',
    label: 'ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'seName',
    component: 'Input',
    label: '订阅事件名称',
    componentProps: {
      placeholder: '请输入订阅事件名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品',
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'sceneId',
    component: 'Input',
    label: '场景',
    componentProps: {
      placeholder: '请输入场景',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-2',
  }, {
    fieldName: 'alarmConfigId',
    component: 'Input',
    label: '报警配置',
    componentProps: {
      placeholder: '请输入报警配置ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-2',
  }, {
    fieldName: 'orderNum',
    component: 'InputNumber',
    label: '排序',
    componentProps: {
      placeholder: '请输入排序',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    }, rules: z.number({ required_error: '请输入排序', invalid_type_error: '无效数字' }),
    formItemClass: 'col-span-1',
  },];

// 报警配置表格列表格列接口
export interface AlarmRowType {
  alarmConfigId: number;
  alarmName: string;
  alarmLevel: number;
  status: string;
  notifyUserType: string;
  remark: string;
  createdAt: string;
  updatedAt: string;
};
// 报警配置表格列 alarmConfigGridColumns
export const alarmConfigGridColumns: VxeGridProps['columns'] = [
  {
    title: '',
    align: 'left',
    width: 100,
    type: 'checkbox',
  },
  {
    title: '报警配置名称',
    field: 'alarmName',
    align: 'left',
    width: -1,
  },
  {
    title: '报警级别',
    field: 'alarmLevel',
    align: 'left',
    width: 180,
    slots: {
      default: ({ row }) => {
        return renderDict(row.alarmLevel, 'alarm_level');
      }
    },
  },
  {
    title: '报警状态',
    field: 'status',
    align: 'left',
    width: 180,
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, 'alarm_state');
      }
    },
  },
  {
    title: '创建时间',
    field: 'createAt',
    align: 'center',
    width: 160,
  },
];

// 场景表格列 sceneGridColumns
export const sceneGridColumns: VxeGridProps['columns'] = [
  {
    title: '',
    align: 'left',
    width: 40,
    type: 'checkbox',
  },
  {
    title: 'ID',
    field: 'sceneId',
    align: 'center',
    width: 60,
  },
  {
    title: '场景名称',
    field: 'sceneName',
    align: 'left',
    width: -1,
  },
  {
    title: '触发类型',
    field: 'triggerType',
    align: 'left',
    width: -1,
    formatter: ({ cellValue }) => {
      const dict = typeof getDictOptions === 'function' ? getDictOptions(DictEnum.SCENE_TRIGGER_TYPE) : [];
      const found = dict.find(opt => opt.value == cellValue);
      return found ? found.label : cellValue ?? '';
    },
  },
  {
    title: '场景状态',
    field: 'status',
    align: 'left',
    width: -1,
    formatter: ({ cellValue }) => {
      const dict = typeof getDictOptions === 'function' ? getDictOptions(DictEnum.SYS_NORMAL_DISABLE) : [];
      const found = dict.find(opt => opt.value == cellValue);
      return found ? found.label : cellValue ?? '';
    },
  },
  {
    title: '是否触发报警',
    field: 'hasAlarm',
    align: 'left',
    width: -1,
    formatter: ({ cellValue }) => cellValue === 1 ? '是' : '否',
  },
  {
    title: '执行方式',
    field: 'executeMode',
    align: 'left',
    width: -1,
    formatter: ({ cellValue }) => {
      if (cellValue === 1) return '串行(顺序执行)';
      if (cellValue === 2) return '并行(同时执行)';
      return cellValue ?? '';
    },
  },
  {
    title: '创建人',
    field: 'createdBySumma.userName',
    align: 'left',
    width: -1,
    formatter: ({ row }) => row.createdBySumma?.userName || '',
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: 150,
  },
];
