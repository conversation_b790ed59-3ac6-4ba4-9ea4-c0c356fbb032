<script lang="ts" setup>
  import { h, reactive, ref, computed, defineProps, watch, onMounted } from 'vue';
  import { Button, message,Tag, Modal, Popconfirm,Switch } from 'ant-design-vue';
  import type { VbenFormProps } from '#/adapter/form';
  import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
  import type { DeepPartial } from '@vben/types';
  import { getVxePopupContainer } from '@vben/utils';
  import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
  import { useVbenVxeGrid } from '#/adapter/vxe-table';
  import { AccessControl, useAccess } from '@vben/access';
  const { hasAccessByCodes } = useAccess();
  import { commonDownloadExcel } from '#/utils/file/download';
  import { List, Export, Delete } from '#/api/device/iotPollTaskLog';
  import { MdiPlus, MdiExport, MdiDelete }  from '@vben/icons';
  import { columns, querySchema, type RowType } from './model';
  import editDrawer from './edit.vue';
  import viewDrawer from './view.vue';

  // 定义props，用于接收任务ID
  interface Props {
    taskId?: number;
    embedded?: boolean; // 是否为嵌入模式
  }

  const props = withDefaults(defineProps<Props>(), {
    taskId: undefined,
    embedded: false
  });
  // 根据是否嵌入模式来调整表单配置
  const formOptions: VbenFormProps = {
    // 默认展开
    collapsed: false,
    fieldMappingTime: [['date', ['start', 'end']]],
    schema: props.embedded ?
      // 嵌入模式只显示时间选择器
      [
        {
          fieldName: 'createdAt',
          component: 'RangePicker',
          label: '创建时间',
          componentProps: {
            type: 'datetimerange',
            clearable: true,
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
            onUpdateValue: (e: any) => {
              console.log(e);
            },
          },
          rules: null,
          formItemClass: 'col-span-1',
        }
      ] : querySchema,
    // 控制表单是否显示折叠按钮
    showCollapseButton: !props.embedded,
    // 是否在字段值改变时提交表单
    submitOnChange: true,
    // 按下回车时是否提交表单
    submitOnEnter: false,
    // 嵌入模式不显示默认操作按钮（重置和搜索）
    showDefaultActions: !props.embedded,
  };

  // 创建过滤后的列配置（移除指定字段）
  const getFilteredColumns = () => {
    if (!props.embedded) {
      return columns;
    }

    // 嵌入模式下需要隐藏的字段
    const hiddenFields = ['logId', 'taskId', 'message', 'tenantId'];

    return columns?.filter(col => {
      // 保留操作列，但移除指定的字段列
      if (col.title === '操作') {
        return false; // 嵌入模式下不显示操作列
      }
      return !hiddenFields.includes(col.field as string);
    }) || [];
  };

  const gridOptions: VxeTableGridOptions<RowType> = {
    checkboxConfig: {
      highlight: true,
      labelField: 'logId',
    },
    rowConfig: {
      keyField: 'logId',
    },
    columns: getFilteredColumns(),
    exportConfig: {},
    height: props.embedded ? 400 : 'auto', // 嵌入模式限制高度为400px
    keepSource: true,
    showOverflow: false,
    pagerConfig: props.embedded ? { pageSize: 10 } : {}, // 嵌入模式每页显示10条
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          // 如果是嵌入模式且有taskId，则添加taskId过滤条件
          const queryParams = {
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };

          if (props.embedded && props.taskId) {
            queryParams.taskId = props.taskId;
          }

          return await List(queryParams);
        },
      },
    },
    toolbarConfig: {
      custom: !props.embedded, // 嵌入模式不显示自定义工具栏
      export: false,
      refresh: !props.embedded, // 嵌入模式不显示刷新按钮
      resizable: true,
      search: !props.embedded, // 嵌入模式不显示搜索
      zoom: !props.embedded, // 嵌入模式不显示缩放
    },
  };
  const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
  };
  const CheckboxChecked = ref(false);
  function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
  }
  const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
  });
  const [ViewDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: viewDrawer,
  });
  function handlePreview(record: RowType) {
  drawerApi.setData({ record });
  drawerApi.open();
  }
  const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
  });
  function handleAdd() {
  editDrawerApi.setData({ update: false, view: false });
  editDrawerApi.open();
  }
  function handleEdit(row: RowType) {
  editDrawerApi.setData({ id: row.logId, update: true, view: false });
  editDrawerApi.open();
  }
  async function handleDelete(row: RowType) {
  await Delete({ logId: [row.logId] });
  message.success("删除成功");
  await handleRefresh();
  }
  async function handleRefresh() {
  await gridApi.query();
  }
  function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const ids: string[] = [];
  for (const row of rows) {
  ids.push(row.logId);
  }
  if (ids.length === 0){
  message.error('请至少选择一项要删除的数据');
  return;
  }
  Modal.confirm({
  title: '提示',
  okType: 'danger',
  content: `确认删除选中的${ids.length}条记录吗？`,
  onOk: async () => {
  await Delete({ logId: ids });
  message.success("删除成功");
  await handleRefresh();
  },
  });
  }
  async function handleExport() {
    const formValues = gridApi.formApi.form.values;
    await commonDownloadExcel(Export, '采集任务流水表',{
      ...formValues,
      page: 1,
      pageSize: 2000,
    });
    message.success("导出成功");
  }

  // 监听taskId变化，重新查询数据
  watch(() => props.taskId, (newTaskId) => {
    if (props.embedded && newTaskId) {
      handleRefresh();
    }
  }, { immediate: true });

  // 组件挂载时，如果是嵌入模式且有taskId，则自动查询
  onMounted(() => {
    if (props.embedded && props.taskId) {
      handleRefresh();
    }
  });
</script>
<template>
  <Page v-if="!props.embedded" auto-content-height>
    <Grid table-title="采集任务流水表">
      <template #toolbar-tools>
        <Button class="mr-2 flex items-center " type="primary" :icon="h(MdiPlus)" @click="handleAdd" v-access:code="'cpm:device:iotPollTaskLog:edit'">
          新增
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :disabled="!CheckboxChecked" :icon="h(MdiDelete)" @click="handleMultiDelete" v-access:code="'cpm:device:iotPollTaskLog:delete'">
          删除
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiExport)" @click="handleExport" v-access:code="'cpm:device:iotPollTaskLog:export'">
          导出
        </Button>
      </template>
      <template #action="{ row }">
        <div class="flex items-center">
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handlePreview(row)" v-access:code="'cpm:device:iotPollTaskLog:view'">
            查看
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row)" v-access:code="'cpm:device:iotPollTaskLog:edit'">
            修改
          </Button>
          <AccessControl :codes="['cpm:device:iotPollTaskLog:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"  @confirm="handleDelete(row)" >
              <Button class="mr-2 border-none p-0" :block="false" type="link"  danger >
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
    <EditDrawer @reload="handleRefresh" />
    <ViewDrawer />
  </Page>

  <!-- 嵌入模式的简化版本 -->
  <Grid v-else>
  </Grid>
</template>