import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:database',
      keepAlive: true,
      order: 500,
      title: $t('menu.datacenters.root'),
    },
    name: 'Datacenters',
    path: '/datacenters',
    children: [
      {
        meta: {
          title: $t('menu.datacenters.dataanalysis'),
          icon: 'lucide:bar-chart-3',
        },
        name: 'DataAnalysis',
        path: '/datacenters/dataanalysis',
        component: () => import('#/views/datacenters/dataanalysis/index.vue'),
      },
    ],
  },
];

export default routes;
