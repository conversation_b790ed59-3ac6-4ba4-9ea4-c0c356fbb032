import { requestClient } from '#/api/request';

// 获取设备废弃数据日志列表
export function List(params:any) {
  return requestClient.get<any>('device/iotDeviceAbandonLog/list', { params });
}

// 删除/批量删除设备废弃数据日志
export function Delete(params:any) {
  return requestClient.post<any>('device/iotDeviceAbandonLog/delete', { ...params });
}
export function deleteAll(params:any) {
  return requestClient.post<any>('device/iotDeviceAbandonLog/deleteAll', { ...params });
}

// 添加/编辑设备废弃数据日志
export function Edit(params:any) {
  return requestClient.post<any>('device/iotDeviceAbandonLog/edit', { ...params });
}

// 获取设备废弃数据日志指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotDeviceAbandonLog/view', { params });
}

// 导出设备废弃数据日志
export function Export(params:any) {
  return requestClient.post<Blob>('device/iotDeviceAbandonLog/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}