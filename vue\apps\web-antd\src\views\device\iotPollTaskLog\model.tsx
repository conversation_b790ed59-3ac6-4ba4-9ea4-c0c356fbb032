import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';

export class State {
  public logId = 0; // ID
  public productKey = null; // 产品标识
  public deviceKey = null; // 设备标识
  public taskId = 0; // 任务ID
  public traceId = null; // 任务批次
  public code = 0; // 任务状态
  public message = ''; // 错误信息
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
				fieldName: 'taskId',
				component: 'InputNumber',
				label: '任务ID',
				componentProps: {
					placeholder: '请输入任务ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入任务ID', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'code',
				component: 'InputNumber',
				label: '任务状态',
				componentProps: {
					placeholder: '请输入任务状态',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入任务状态', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'createdAt',
				component: 'RangePicker',
				label: '创建时间',
				componentProps: {
					type: 'datetimerange',
					clearable: true,
					valueFormat: 'YYYY-MM-DD HH:mm:ss',
					onUpdateValue: (e: any) => {
						console.log(e);
					},
				},  rules:null,
				formItemClass: 'col-span-1',
			},];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: 'ID',
    field: 'logId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '产品标识',
    field: 'productKey',
    align: 'left',
    width: -1,
 },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
 },
  {
    title: '任务ID',
    field: 'taskId',
    align: 'left',
    width: -1,
 },
  {
    title: '任务批次',
    field: 'traceId',
    align: 'left',
    width: -1,
 },
  {
    title: '任务状态',
    field: 'code',
    align: 'left',
    width: -1,
 },
  {
    title: '错误信息',
    field: 'message',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  logId: number;
  productKey: string;
  deviceKey: string;
  taskId: number;
  traceId: string;
  code: number;
  message: string;
  tenantId: string;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'logId',  label: 'ID'},
  {  field: 'productKey',  label: '产品标识'},
  {  field: 'deviceKey',  label: '设备标识'},
  {  field: 'taskId',  label: '任务ID'},
  {  field: 'traceId',  label: '任务批次'},
  {  field: 'code',  label: '任务状态'},
  {  field: 'message',  label: '错误信息'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdAt',  label: '创建时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
 {
					fieldName: 'logId',
					component: 'Input',
					label: 'ID',
					dependencies: {   show: () => false,    triggerFields: [''],   },
					componentProps: {
						placeholder: '',
						onUpdateValue: (e: any) => {
							console.log(e);
						},   
					},
					formItemClass: 'col-span-1',
				},  {
				fieldName: 'productKey',
				component: 'Input',
				label: '产品标识',
				componentProps: {
					placeholder: '请输入产品标识',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'deviceKey',
				component: 'Input',
				label: '设备标识',
				componentProps: {
					placeholder: '请输入设备标识',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'taskId',
				component: 'InputNumber',
				label: '任务ID',
				componentProps: {
					placeholder: '请输入任务ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入任务ID', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'traceId',
				component: 'Input',
				label: '任务批次',
				componentProps: {
					placeholder: '请输入任务批次',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'code',
				component: 'InputNumber',
				label: '任务状态',
				componentProps: {
					placeholder: '请输入任务状态',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入任务状态', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'message',
				component: 'Input',
				label: '错误信息',
				componentProps: {
					placeholder: '请输入错误信息',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},];