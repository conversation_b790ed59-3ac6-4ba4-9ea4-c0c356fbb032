<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { type RowType } from './model';
import QRCode from 'qrcode';

const [BasicModal, modalApi] = useVbenModal({
    onOpenChange: handleOpenChange,
});

// 用于存储生成的二维码 base64 图片地址
const qrCodeUrl = ref<string>('');
// 设备标识
const deviceKey = ref<string>('');

function handleOpenChange(open: boolean) {
    if (!open) {
        qrCodeUrl.value = '';
        return;
    }

    const { row } = modalApi.getData() as { row: RowType };
    if (row?.deviceKey) {
        deviceKey.value = row.deviceKey;
        generateQRCode(row.deviceKey);
    }
}

async function generateQRCode(text: string) {
    try {
        const url = await QRCode.toDataURL(text);
        qrCodeUrl.value = url;
    } catch (error) {
        console.error('生成二维码失败:', error);
    }
}
</script>

<template>
    <BasicModal :footer="false" class="w-[600px]" title="查看二维码">
        <div class="flex flex-col items-center justify-center p-6">
            <p class="mb-4">设备标识：{{ deviceKey }}</p>
            <div v-if="qrCodeUrl" class="border border-gray-300 p-2 rounded">
                <img :src="qrCodeUrl" alt="二维码" class="w-[200px] h-[200px]" />
            </div>
            <p v-else class="text-gray-500">正在生成二维码...</p>
        </div>
    </BasicModal>
</template>