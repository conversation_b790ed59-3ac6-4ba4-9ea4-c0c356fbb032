<script setup lang="ts">
import type { SysClient } from '#/api/system/client';
import { computed, nextTick, ref } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';
import { useVbenForm } from '#/adapter/form';
import { Description, useDescription } from '#/components/description';
import { viewSchema, type RowType } from './model';
import { View } from '#/api/manage/iotNotifyChannel';
import type { DescItem } from '#/components/description';
import { DictEnum } from '@vben/constants';
import { renderDict } from '#/utils';
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onOpenChange: handleOpenChange,
});
const [registerDescription, { setDescProps }] = useDescription({
  column: 1,
  schema: viewSchema,
});
async function handleOpenChange(open: boolean) {
  if (!open) {
    return null;
  }
  const { record } = drawerApi.getData() as { record: RowType };
  const record2 = await View({ channelId: record.channelId });
  let currentSchema = cloneDeep(viewSchema);
  //Http桥接出口参数
  if (record2.channelType == '1' || record2.channelType == '3') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'sms.accessKeyId',
        label: 'accessKeyId',
        render(_, data) {
          return data.sms.accessKeyId;
        },
      },
      {
        field: 'sms.accessKeySecret',
        label: 'accessKeySecret',
        render(_, data) {
          return data.sms.accessKeySecret;
        },
      },
      
    ];
  }
  if (record2.channelType == '2' && record2.serviceType == '1' ||record2.channelType == '2' && record2.serviceType == '4') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'wechat.appId',
        label: 'appId',
          render(_, data) {
          return data.smsAli.templateId;
        },
      },
      {
        field: 'wechat.appSecret',
        label: 'appSecret',
        render(_, data) {
          return data.wechat.appSecret;
        },
      },
     
    ];
  }

  if (record2.channelType == '2'&& record2.serviceType == '2') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'wechat.corpId',
        label: '企业ID',
        render(_, data) {
          return data.wechat.corpId;
        },
      },
      {
        field: 'wechat.agentId',
        label: '应用agentId',
        render(_, data) {
          return data.wechat.agentId;
        },
      },
      ];
  }
  if (record2.channelType == '2'&& record2.serviceType == '3') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'wechat.webHook',
        label: 'webHook',
        render(_, data) {
          return data.wechat.webHook;
        },
      },
      ];
  }
  if (record2.channelType == '4' ) {
    currentSchema = [
      ...currentSchema,
      {
        field: 'email.smtpServer',
        label: '服务器地址',
        render(_, data) {
          return data.email.smtpServer;
        },
      },
      {
        field: 'email.port',
        label: '端口号',
        render(_, data) {
          return data.email.port;
        },
      },
      {
        field: 'email.username',
        label: '发件人账号',
        render(_, data) {
          return data.email.username;
        },
      },
      {
        field: 'email.password',
        label: '发件秘钥',
        render(_, data) {
          return data.email.password;
        },
      },
      {
        field: 'email.sslEnable',
        label: '是否启动ssl',
        render(_, data) {
          return data.email.sslEnable;
        },
      },
      {
        field: 'email.authEnable',
        label: '开启验证',
        render(_, data) {
          return data.email.authEnable;
        },
      },
      {
        field: 'email.retryInterval',
        label: '重试间隔',
        render(_, data) {
          return data.email.retryInterval;
        },
      },
      {
        field: 'email.maxRetries',
        label: '重试次数',
        render(_, data) {
          return data.email.maxRetries;
        },
      },
    ];
  }

  
  if (record2.channelType == '5'&& record2.serviceType == '1') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'ding.appKey',
        label: 'appKey',
        render(_, data) {
          return data.ding.appKey;
        },
      },
      {
        field: 'ding.appSecret',
        label: 'appSecret',
        render(_, data) {
          return data.ding.appSecret;
        },
      },
      {
        field: 'ding.agentId',
        label: 'agentIdt',
        render(_, data) {
          return data.ding.agentId;
        },
      },
      
    ];
  }

  //tcp或udp桥接参数查看
 
  if (record2.channelType == '5'&& record2.serviceType == '2') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'ding.webHook',
        label: 'webHook',
        render(_, data) {
          return data.ding.webHook;
        },
      },

      ];
  }
  // setDescProps({ data: record2 }, true);
  setDescProps({ data: record2, schema: currentSchema }, true);
}
</script>
<template>
  <BasicDrawer :footer="false" class="w-[600px]" title="查看">
    <Description @register="registerDescription"></Description>
  </BasicDrawer>
</template>
