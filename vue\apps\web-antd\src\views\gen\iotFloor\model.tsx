import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';

export class State {
  public floorId = 0; // 楼层ID
  public buildId = 0; // 建筑物ID
  public projectId = 0; // 项目ID
  public floorName = ''; // 楼层
  public tenantId = ''; // 租户ID
  public deptId = 0; // 所属机构
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'floorId',
    component: 'InputNumber',
    label: '楼层ID',
    componentProps: {
      placeholder: '请输入楼层ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '楼层ID',
    field: 'floorId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '建筑物ID',
    field: 'buildId',
    align: 'left',
    width: -1,
 },
  {
    title: '项目ID',
    field: 'projectId',
    align: 'left',
    width: -1,
 },
  {
    title: '楼层',
    field: 'floorName',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '所属机构',
    field: 'deptId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建部门',
    field: 'createdDept',
    align: 'left',
    width: -1,
 },
  {
    title: '创建者',
    field: 'createdBy',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) =>  {
      return renderPopoverMemberSumma(row.createdBySumma);
    },
 },
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  {
    title: '更新者',
    field: 'updatedBy',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) =>  {
      return renderPopoverMemberSumma(row.updatedBySumma);
    },
 },
 },
  {
    title: '更新时间',
    field: 'updatedAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  floorId: number;
  buildId: number;
  projectId: number;
  floorName: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'floorId',  label: '楼层ID'},
  {  field: 'buildId',  label: '建筑物ID'},
  {  field: 'projectId',  label: '项目ID'},
  {  field: 'floorName',  label: '楼层'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'deptId',  label: '所属机构'},
  {  field: 'createdDept',  label: '创建部门'},
  {  field: 'createdBy',  label: '创建者'},
  {  field: 'createdAt',  label: '创建时间'},
  {  field: 'updatedBy',  label: '更新者'},
  {  field: 'updatedAt',  label: '更新时间'},
  {  field: 'remark',  label: '备注'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'floorId',
    component: 'Input',
    label: '楼层ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'buildId',
    component: 'InputNumber',
    label: '建筑物ID',
    componentProps: {
      placeholder: '请输入建筑物ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入建筑物ID', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'projectId',
    component: 'InputNumber',
    label: '项目ID',
    componentProps: {
      placeholder: '请输入项目ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入项目ID', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'floorName',
    component: 'Input',
    label: '楼层',
    componentProps: {
      placeholder: '请输入楼层',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'tenantId',
    component: 'Input',
    label: '租户ID',
    componentProps: {
      placeholder: '请输入租户ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'deptId',
    component: 'InputNumber',
    label: '所属机构',
    componentProps: {
      placeholder: '请输入所属机构',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入所属机构', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];