import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';

import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';

import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';


export class State {
  public ppId = 0; // 项目产品关联ID
  public projectId = 0; // 项目ID
  public productKey = ''; // 产品标识
  public productAlias = ''; // 产品别名
  public imgUrl = ''; // 图片URL
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma = null; // 创建者信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma = null; // 更新者信息
  public updatedAt = ''; // 更新时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
 
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: 'ppId',
    field: 'ppId',
    align: 'center',
    width: 0,
    visible: false, // 隐藏列，但确保数据能传递
  },
  {
    title: '产品名称',
    field: 'productAlias',
    align: 'center',
    width: 150,
  },
  {
    title: '产品标识',
    field: 'productKey',
    align: 'center',
    width: -1,
  },
  {
    title: '创建部门',
    field: 'createdDept',
    align: 'center',
    width: 80,
   
  },
  {
    title: '创建者',
    field: 'createdBy',
    align: 'center',
   
    width: -1,
  },
 
  {
    title: '关联时间',
    field: 'createdAt',
    align: 'center',
    width: 150,
  },
  {
    title: '操作',
    width: 190,
    align: 'center',
    slots: { default: 'action' }
  },
];

// 表格列接口
export interface RowType {
    ppId: number;
  projectProductId: number;
  projectId: number;
  productId: number;
  productKey: string;
  productName: string;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
  // 关联的产品信息
  product?: {
    productId: number;
    productKey: string;
    productName: string;
    categoryId: number;
    categoryName: string;
    deviceType: number;
    firmwareType: number;
    networkType: number;
    isPrivate: number;
    transport: number;
    channelType: number;
    vertificateMethod: number;
    locationWay: number;
    imgUrl: string;
    thingsModelsJson: string;
    isAuthorize: number;
    publishStatus: number;
    onlineTimeout: number;
    deviceModel: string;
    remark: string;
  };
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'projectProductId', label: '关联ID' },
  { field: 'productKey', label: '产品标识' },
  { field: 'productName', label: '产品名称' },
  { field: 'product.categoryName', label: '产品分类' },
  {
    field: 'product.deviceType', label: '设备类型',
    render(val, _) {
      return renderDict(val, DictEnum.DEVICE_TYPE);
    },
  },
  {
    field: 'product.firmwareType', label: '固件类型',
    render(val, _) {
      return renderDict(val, DictEnum.FIRMWARE_TYPE);
    },
  },
  {
    field: 'product.networkType', label: '连网方式',
    render(val, _) {
      return renderDict(val, DictEnum.NETWORK_TYPE);
    },
  },
  {
    field: 'product.transport', label: '通讯协议',
    render(val, _) {
      return renderDict(val, DictEnum.TRANSPORT);
    },
  },
  {
    field: 'product.channelType', label: '通道类型',
    render(val, _) {
      return renderDict(val, DictEnum.CHANNEL_TYPE);
    },
  },
  {
    field: 'product.publishStatus',
    label: '产品状态',
    render(val, _) {
      return renderDict(val, DictEnum.PUBLISH_STATUS);
    },
  },
  { field: 'createdAt', label: '关联时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'ppId',
    component: 'Input',
    label: '自增ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
    },
  },
  {
    fieldName: 'projectId',
    component: 'Input',
    label: '项目ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
    },
  },
  {
    fieldName: 'productId',
    component: 'Select',
    label: '选择产品',
    componentProps: {
      placeholder: '请选择要关联的产品',
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option.label.toLowerCase().includes(input.toLowerCase());
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
   {
    fieldName: 'productAlias',
    component: 'Input',
    label: '产品别名',
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    
  },
  {
    fieldName: 'createdDept',
    component: 'Input',
    label: '创建部门',
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
   
  },
   {
    fieldName: 'createdBy',
    component: 'Input',
    label: '创建者',
    componentProps: {
      placeholder: '请输入创建者',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    
  },
];
