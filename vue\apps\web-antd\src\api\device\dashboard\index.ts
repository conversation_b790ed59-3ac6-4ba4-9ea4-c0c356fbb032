import { requestClient } from '#/api/request';

// 获取统计数据
export function Statistic(params: any) {
  return requestClient.get<any>('device/dashboard/statistic', { params });
}

// 获取报警类型统计数据
export function AlarmTypeStatistics(params: any) {
  return requestClient.get<any>('device/dashboard/alarmTypeStatistic', { params });
}

// 获取消息和报警趋势数据
export function MsgAndAlarmCount(params: any) {
  return requestClient.get<any>('device/dashboard/msgAlarmCountHour', { params });
}

