<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <template #Option01="slotProps">
        <div v-if="slotProps.value != undefined">
          <Input type="text" v-model:value="slotProps.value.url">
            <template #addonBefore>
              <Select
                v-model:value="slotProps.value.protocol"
                style="width: 90px"
              >
                <SelectOption value="Http://">Http://</SelectOption>
                <SelectOption value="Https://">Https://</SelectOption>
              </Select>
            </template>
          </Input>
        </div>
      </template>

      <template #httpOutput01="slotProps">
        <div>
          <div
            v-for="(item, index) in slotProps.value"
            style="display: flex; align-items: center"
            class="mb-2"
          >
            <Input
              type="text"
              v-model:value="item.key"
              placeholder="请输入参数"
              addon-before="键:"
            />

            <Input
              type="text"
              v-model:value="item.value"
              placeholder="请输入参数"
              addon-before="值:"
              class="ml-7"
            />

            <Button
              type="primary"
              danger
              class="ml-4"
              @click="handleDelete(index, 'httpOutput01')"
              >删除</Button
            >
          </div>
          <Button @click="handleAdd('httpOutput01')">添加请求头</Button>
        </div>
      </template>
      <template #httpOutput02="slotProps">
        <div>
          <div
            v-for="(item, index) in slotProps.value"
            style="display: flex; align-items: center"
            class="mb-2"
          >
            <Input
              type="text"
              v-model:value="item.key"
              placeholder="请输入参数"
              addon-before="键:"
            />

            <Input
              type="text"
              v-model:value="item.value"
              placeholder="请输入参数"
              addon-before="值:"
              class="ml-7"
            />

            <Button
              type="primary"
              danger
              class="ml-4"
              @click="handleDelete(index, 'httpOutput02')"
              >删除</Button
            >
          </div>
          <Button @click="handleAdd('httpOutput02')">添加参数</Button>
        </div>
      </template>
      <template #httpOutput03="slotProps">
        <div>
          <div
            v-for="(item, index) in slotProps.value"
            style="display: flex; align-items: center"
            class="mb-2"
          >
            <Input
              type="text"
              v-model:value="item.key"
              placeholder="请输入参数"
              addon-before="键:"
            />

            <Input
              type="text"
              v-model:value="item.value"
              placeholder="请输入参数"
              addon-before="值:"
              class="ml-7"
            />

            <Button
              type="primary"
              danger
              class="ml-4"
              @click="handleDelete(index, 'httpOutput03')"
              >删除</Button
            >
          </div>
          <Button @click="handleAdd('httpOutput03')">添加请求配置</Button>
        </div>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Alert, Input, Select, SelectOption, Button } from 'ant-design-vue';
import { Edit, View } from '#/api/ruleengine/iotChannel';
import { editSchema } from './model';

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
}

const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

async function handleAdd(param: string) {
  console.log(param);

  let datas = await formApi.getValues();
  console.log('handleAdd', datas[param]);
  let latestindex = 0;
  datas[param].forEach((item: any) => {
    if (item.index > latestindex) {
      latestindex = item.index;
    }
  });

  await formApi.setValues({
    [param]: [...datas[param], { index: latestindex + 1, key: '', value: '' }],
  });
}

async function handleDelete(index: any, param: string) {
  console.log('handleDeleteEnum', index);
  let datas = await formApi.getValues();
  datas[param] = datas[param].filter(
    (item: any, i: any) => item.index !== index,
  );
  await formApi.setValues({ [param]: datas[param] });
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: [...editSchema],
  // schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true });
    const { id, update, view } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;
    if (isUpdate.value || isView.value) {
      const record = await View({ channelId: id });
      record.type = record.type.toString();
      record.direction = record.direction.toString();
      record.status = record.status == '0' ? true: false;

      if(record.type == '1'){
        record.mqtt.deviceMatchType = record.mqtt.deviceMatchType.toString();
      }

      if (record.type == '4' && record.direction == '2') {
        record.httpOutput.headers.forEach((obj: any, index: number) => {
          obj.index = index;
        });
        record.httpOutput.params.forEach((obj: any, index: number) => {
          obj.index = index;
        });
        record.httpOutput.config.forEach((obj: any, index: number) => {
          obj.index = index;
        });
        await formApi.setValues({ httpOutput01: record.httpOutput.headers });
        await formApi.setValues({ httpOutput02: record.httpOutput.params });
        await formApi.setValues({ httpOutput03: record.httpOutput.config });
      }

      await formApi.setValues(record);
    }
    drawerApi.setState({ confirmLoading: false, loading: false });

    await formApi.setValues({ Option01: { url: 'aa', protocol: 'Http://' } });

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            'only-read': true,
          },
        },
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            'only-read': false,
          },
        },
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true });
    const { valid } = await formApi.validate();

    if (!valid) {
      return;
    }
    let data = cloneDeep(await formApi.getValues());
    data.status = data.status == true? '0': '1';
    if (data.type == '4' && data.direction == '2') {
      data.httpOutput.headers = data.httpOutput01.map(
        ({ index, ...rest }: { index: number }) => rest,
      );
      data.httpOutput.params = data.httpOutput02.map(
        ({ index, ...rest }: { index: number }) => rest,
      );
      data.httpOutput.config = data.httpOutput03.map(
        ({ index, ...rest }: { index: number }) => rest,
      );
    }

    await Edit(data);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false });
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
</script>
