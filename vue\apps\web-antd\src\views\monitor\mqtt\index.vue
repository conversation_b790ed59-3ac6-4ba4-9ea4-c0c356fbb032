<template>
  <Page auto-content-height>
    <div class="flex flex-col gap-4">
      <!-- 第一栏 消息速率 连接数 订阅数 -->
      <div class="grid lg:grid-cols-5 grid-cols-2 gap-4">
        <!-- 消息速率 -->
        <Card class="w-full lg:col-span-1 col-span-2">
          <div class="flex flex-col gap-4 ">
            <div class="flex gap-2 items-center">信息流出速率:<p class="text-xl font-bold">{{ pointData.outPerSec }}</p>
            </div>
            <div class="flex gap-2 items-center">信息流入速率:<p class="text-xl font-bold">{{ pointData.inPerSec }}</p>
            </div>
          </div>
        </Card>
        <!-- 连接数 -->
        <Card class="w-full col-span-2">
          <div class="grid grid-cols-2 gap-4">
            <div class="px-8">
              <div class="text-gray-800 flex gap-2 items-center">
                <IconifyIcon class="text-blue-800 text-base" icon="ant-design:deployment-unit-outlined" />
                总连接数

              </div>
              <div class="text-2xl font-bold">
                <VbenCountToAnimator :duration="1500" :end-val="pointData.connectAll" :start-val="0" />
              </div>
            </div>
            <div class="px-8">
              <div class="text-gray-800 flex gap-2 items-center">
                <IconifyIcon class="text-blue-800 text-base" icon="ant-design:deployment-unit-outlined" />
                在线连接数
              </div>
              <div class="text-2xl font-bold">
                <VbenCountToAnimator :duration="1500" :end-val="pointData.connectOnline" :start-val="0" />
              </div>
            </div>
          </div>
        </Card>
        <!-- 订阅数 -->
        <Card class="w-full col-span-2">
          <div class="grid grid-cols-2 gap-4">
            <div class="px-8">
              <div class="text-gray-800 flex gap-2 items-center">
                <IconifyIcon class="text-blue-800 text-base" icon="ant-design:deployment-unit-outlined" />
                主题数
              </div>
              <div class="text-2xl font-bold">
                <VbenCountToAnimator :duration="1500" :end-val="pointData.topicAll" :start-val="0" />
              </div>
            </div>
            <div class="px-8">
              <div class="text-gray-800 flex gap-2 items-center">
                <IconifyIcon class="text-blue-800 text-base" icon="ant-design:deployment-unit-outlined" />
                订阅数
              </div>
              <div class="text-2xl font-bold">
                <VbenCountToAnimator :duration="1500" :end-val="pointData.subscribeAll" :start-val="0" />
              </div>
            </div>
          </div>
        </Card>
      </div>
      <!-- 第二栏 节点状态 -->
      <div>
        <Card>
          <div class="grid lg:grid-cols-4 grid-cols-2 gap-4">
            <div class="flex flex-col gap-2">
              <Select v-model:value="channelSelect" :options="channelOption" @change="getMqttData"
                :field-names="{ label: 'channelName', value: 'channelId' }" class="w-32" placeholder="请选择通道"></Select>
              <Image :width="125" :height="125" src='http://localhost:5666/ProductPictures.png'></Image>
            </div>
            <div class="flex flex-col gap-2 justify-center">
              <p class="flex items-center gap-1">节点名称:
                <span class="text-lg font-bold">{{ pointInfo.nodeName }}</span>
              </p>
              <p class="flex items-center gap-1">运行时长:
                <span class="text-lg font-bold">
                  <VbenCountToAnimator :duration="1500" :end-val="parseInt(timeComputed.split('-')[0])" :start-val="0" />天
                  <VbenCountToAnimator :duration="1500" :end-val="parseInt(timeComputed.split('-')[1])" :start-val="0" />小时
                  <VbenCountToAnimator :duration="1500" :end-val="parseInt(timeComputed.split('-')[2])" :start-val="0" />分钟
                </span>
              </p>
            </div>
            <div class="flex flex-col gap-2 justify-center">
              <p class="flex items-center gap-1">连接数:
                <span class="text-lg font-bold">
                  <VbenCountToAnimator :duration="1500" :end-val="pointInfo.currentConnected" :start-val="0" />
                </span>
              </p>
              <p class="flex items-center gap-1">主题数:
                <span class="text-lg font-bold">
                  <VbenCountToAnimator :duration="1500" :end-val="pointInfo.currentTopic" :start-val="0" />
                </span>
              </p>
              <p class="flex items-center gap-1">订阅数:
                <span class="text-lg font-bold">
                  <VbenCountToAnimator :duration="1500" :end-val="pointInfo.currentSubscribe" :start-val="0" />
                </span>
              </p>
            </div>
            <div class="flex flex-col gap-2 justify-center">
              <div class="grid grid-cols-[auto_1fr] grid-rows-3 gap-2">
                <span>操作系统CPU负载: </span>
                <Progress :format="v => `${v}%`" :size="20" :percent="pointInfo.sysCpuLoad">
                </Progress>
                <span>操作系统内存: </span>
                <Progress :size="20" :percent="pointInfo.sysMemUse / pointInfo.sysMemTotal * 100"
                  :format="v => `${v}%`">
                </Progress>
                <span>系统硬盘: </span>
                <Progress :format="v => `${v}%`" :size="20"
                  :percent="pointInfo.sysDiskUse / pointInfo.sysDiskTotal * 100">
                </Progress>
              </div>
            </div>
          </div>
        </Card>
      </div>
      <!-- 第三栏 各种状态 -->
      <div class="flex flex-col gap-4">
        <div class="flex gap-4">
          <Select v-model:value="timeSelect" :options="timeOption" class="w-48" placeholder="请选择时间数据"></Select>
          <!-- <Select class="w-48" placeholder="Ciallo"></Select> -->
          <Button @click="getEchartsData" type="primary">查询</Button>
        </div>
        <div class="grid lg:grid-cols-2 grid-cols-1  gap-4">
          <Card v-for="(chartRef, index) in chartRefs" :key="index">
            <EchartsUI :ref="(el) => chartRef.value = el" style="width: 100%; height: 320px;" />
          </Card>
        </div>
      </div>
    </div>
  </Page>
</template>

<script setup lang='ts' name=''>
import { IconifyIcon } from '@vben/icons';
import { Page } from '@vben/common-ui';
import { Card, Select, Button, Image, Progress, message } from 'ant-design-vue';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import type { EchartsUIType } from '@vben/plugins/echarts';
import { computed, onMounted, ref } from 'vue';
import { PointData, PointInfo } from './model';
import { getAllInOutConnectTopic, postChannelCurrentInfo, postChannelHistory } from '#/api/monitor/mqtt';
import { List } from '#/api/ruleengine/iotChannel';
import { getDictOptions } from '#/utils/dict';
import { DictEnum } from '@vben/constants';
import { VbenCountToAnimator } from '@vben/common-ui';
/** ----折线图配置---- */
/** 折线图列表 */
const chartRefs = [
  ref<EchartsUIType>(),
  ref<EchartsUIType>(),
  ref<EchartsUIType>(),
  ref<EchartsUIType>()
]
/** 折线图配置列表 */
const chartOptionsList = ref<any>([

  /** 折线图 1 流入流出数据列表 */
  {
    title: {
      text: '流入流出'
    },
    xAxis: {
      /** x轴声明 */
      type: 'category',
      /** x轴刻度集 */
      data: [],
      /** x轴显示字段 */
      name: '时间',
      /** x轴字段显示位置 */
      nameLocation: 'end',
      /** x轴字段显示位置间距 */
      nameGap: 0,
      axisLabel: {
        interval: 0,
        rotate: 30,
        margin: 16, // 增大标签与轴线距离
        overflow: 'break', // 自动换行
      },
      /** 坐标轴留白策略 */
      boundaryGap: true,
    },
    yAxis: {
      /** y轴声明 */
      type: 'value',
      /** y轴显示字段 */
      name: '数量',
      /** x轴分割线 */
      splitLine: {
        /** x轴横线 */
        show: true,
      },
    },
    /** 提示框触发 */
    tooltip: {
      /** 坐标轴触发 */
      trigger: 'axis'
    },
    legend: {
      data: ['流入', "流出"]
    },
    /** 绘图区域 */
    grid: {
      left: 10,
      right: 60, // 增大右边距，防止最后一个被裁切
      bottom: 10,
      containLabel: true,
    },
    /** 折线数据列表 */
    series: [{
      /** 显示字段 */
      name: '流入',
      /** 数据 */
      data: [],
      /** 图表类型 */
      type: 'line',
      /** 平滑曲线 */
      smooth: true,
      /** 是否显示面积区域 */
      itemStyle: { color: '#39b54a' }
    }, {
      /** 显示字段 */
      name: '流出',
      /** 数据 */
      data: [],
      /** 图表类型 */
      type: 'line',
      /** 平滑曲线 */
      smooth: true,
      /** 是否显示面积区域 */
      itemStyle: { color: '#e54d42' }
    }],
  },
  /** 折线图 2 连接数列表 */
  {
    title: {
      text: '连接数'
    },
    xAxis: {
      type: 'category',
      data: [],
      name: '时间',
      nameLocation: 'end',
      nameGap: 0,
      axisLabel: {
        interval: 0,
        rotate: 30,
        margin: 16,
        overflow: 'break',
      },
      boundaryGap: true,
    },
    yAxis: {
      type: 'value',
      name: '数量',
      splitLine: {
        show: true,
      },
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['连接数']
    },
    grid: {
      left: 10,
      right: 60,
      bottom: 10,
      containLabel: true,
    },
    series: [{
      name: '连接数',
      data: [],
      type: 'line',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#f37b1d55' }, // 顶部颜色
            { offset: 1, color: '#f37b1d00' }    // 底部透明
          ]
        }
      },
      itemStyle: { color: '#f37b1d' }
    }],
  },
  /** 折线图 3 主题数列表 */
  {
    title: {
      text: '主题数'
    },
    xAxis: {
      type: 'category',
      data: [],
      name: '时间',
      nameLocation: 'end',
      nameGap: 0,
      axisLabel: {
        interval: 0,
        rotate: 30,
        margin: 16,
        overflow: 'break',
      },
      boundaryGap: true,
    },
    yAxis: {
      type: 'value',
      name: '数量',
      splitLine: {
        show: true,
      },
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['主题数']
    },
    grid: {
      left: 10,
      right: 60,
      bottom: 10,
      containLabel: true,
    },
    series: [{
      name: '主题数',
      data: [],
      type: 'line',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#6739b655' }, // 顶部颜色
            { offset: 1, color: '#6739b600' }    // 底部透明
          ]
        }
      },
      itemStyle: { color: '#6739b6' }
    }],
  },  /** 折线图 4 订阅数列表 */
  {
    title: {
      text: '订阅数'
    },
    xAxis: {
      type: 'category',
      data: [],
      name: '时间',
      nameLocation: 'end',
      nameGap: 0,
      axisLabel: {
        interval: 0,
        rotate: 30,
        margin: 16,
        overflow: 'break',
      },
      boundaryGap: true,
    },
    yAxis: {
      type: 'value',
      name: '数量',
      splitLine: {
        show: true,
      },
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['订阅数']
    },
    grid: {
      left: 10,
      right: 60,
      bottom: 10,
      containLabel: true,
    },
    series: [{
      name: '订阅数',
      data: [],
      type: 'line',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#e0399755' }, // 顶部颜色
            { offset: 1, color: '#e0399700' }    // 底部透明
          ]
        }
      },
      itemStyle: { color: '#e03997' }
    }],
  },
])
/** ----展示数据配置---- */
/** 第一列 节点数据 获取Mqtt统计所有节点的流入流出连接和主题 */
const pointData = ref(new PointData())
/** 第二列 节点状态 统计MQTT指定通道下的当前运行状态信息 */
const pointInfo = ref(new PointInfo())
/** MQTT选择通道数据 */
const channelSelect = ref()
const channelOption = ref([])
/** 折线图选择配置 */
const timeSelect = ref("6")
const timeOption = ref()
/** 节点数据 异步请求 获取Mqtt统计所有节点的流入流出连接和主题 */
async function getPointData() {
  const response = await getAllInOutConnectTopic()
  pointData.value = response
}
/** 节点状态 异步请求 统计MQTT指定通道下的当前运行状态信息 */
async function getPointInfo() {
  const response = await postChannelCurrentInfo({ channelId: channelSelect.value })
  pointInfo.value = response
  // console.log("统计MQTT指定通道下的当前运行状态信息", response)
}
/** 折线图数据 异步请求 统计MQTT指定通道下的历史统计信息 */
async function getEchartsData() {
  const response = await postChannelHistory({ channelId: channelSelect.value, timeType: timeSelect.value })
  console.log("统计MQTT指定通道下的历史统计信息", response)
  /** 重置数据 */
  chartOptionsList.value.forEach((item: any) => {
    item.xAxis.data = []
    item.series.forEach((seriesItem: any) => {
      seriesItem.data = []
    })
  })

  /** 折线图 1 流入流出数据列表 */
  response.inOut.forEach((item: any) => {
    chartOptionsList.value[0].xAxis.data.push(item.reportTime);
    chartOptionsList.value[0].series[0].data.push(item.dataChgValIn)
    chartOptionsList.value[0].series[1].data.push(item.dataChgValOut)

  });
  /** 折线图 2 连接数列表 */
  response.connect.forEach((item: any) => {
    chartOptionsList.value[1]?.xAxis.data?.push(item.reportTime)
    chartOptionsList.value[1]?.series[0].data?.push(item.dataChgVal)

  })
  /** 折线图 3 主题数列表 */
  response.connect.forEach((item: any) => {
    chartOptionsList.value[2]?.xAxis.data?.push(item.reportTime)
    chartOptionsList.value[2]?.series[0].data?.push(item.dataChgVal)

  })
  /** 折线图 4 订阅数列表 */
  response.connect.forEach((item: any) => {
    chartOptionsList.value[3]?.xAxis.data?.push(item.reportTime)
    chartOptionsList.value[3]?.series[0].data?.push(item.dataChgVal)

  })
  /** 刷新数据 */
  chartRefs.forEach((chartRef, index) => {
    const { renderEcharts } = useEcharts(chartRef)
    renderEcharts(chartOptionsList.value[index])
  })
}

/** 折线图数据 本地请求 时间选择 */
async function getTimeSelect() {
  const res = await getDictOptions(DictEnum.CHANNEL_MONITOR_WINDOW)
  // console.log("时间选择", res)
  timeOption.value = res
}

/** 获取通道数据 */
async function getChannelData() {
  const response = await List({ type: 1 })
  // console.log("获取通道数据", response.items)
  channelOption.value = response.items.reverse()
  if (response.items.length > 0) {
    channelSelect.value = response.items[0].channelId
    getMqttData()
  } else {
    message.warning('不存在通道数据')
  }
}


/** 获取节点数据 */
async function getMqttData() {
  /** 折线图数据 时间选择 */
  await getTimeSelect()
  /** 节点数据 获取Mqtt统计所有节点的流入流出连接和主题 */
  getPointData()
  /** 节点状态 统计MQTT指定通道下的当前运行状态信息 */
  getPointInfo()
  /** 折线图数据 统计MQTT指定通道下的历史统计信息 */
  getEchartsData()
}

onMounted(async () => {
  /** 折线图渲染 */
  chartRefs.forEach((chartRef, index) => {
    const { renderEcharts } = useEcharts(chartRef)
    renderEcharts(chartOptionsList.value[index])
  })

  /** 获取通道数据 */
  await getChannelData()
})


const timeComputed = computed(() => {    // 原始时间数据
  const timeData = {
    currentSendTime: pointInfo.value.currentSendTime?pointInfo.value.currentSendTime:"",
    startTime: pointInfo.value.startTime? pointInfo.value.startTime:""
  };
  if (!timeData.currentSendTime || !timeData.startTime) {
    return '0-0-0' // 数据未加载时显示默认值
  }
  // 解析时间字符串为Date对象
  const parseTime = (timeStr: any) => {
    const [datePart, timePart] = timeStr.split(' ');
    const [year, month, day] = datePart.split('-').map(Number);
    const [hours, minutes, seconds] = timePart.split(':').map(Number);
    return new Date(year, month - 1, day, hours, minutes, seconds);
  };

  // 计算时间差（毫秒）
  const start = parseTime(timeData.startTime);
  const current = parseTime(timeData.currentSendTime);
  let diffMs = current - start; // 可能为负值

  // 处理负值情况（如果currentSendTime在startTime之前）
  const isNegative = diffMs < 0;
  diffMs = Math.abs(diffMs);

  // 计算时间单位
  const msPerMinute = 60 * 1000;
  const msPerHour = msPerMinute * 60;
  const msPerDay = msPerHour * 24;

  const days = Math.floor(diffMs / msPerDay);
  const hours = Math.floor((diffMs % msPerDay) / msPerHour);
  const minutes = Math.floor((diffMs % msPerHour) / msPerMinute);

  // 格式化为字符串并添加负号标记
  return `${isNegative ? '-' : ''}${days}-${hours}-${minutes}`;
})
</script>

<style scoped></style>
