import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

export class State {
  public fieldId = 0; // 属性ID
  public unionKey = ''; // 组合设备标识
  public fieldType = 1; // 属性类型（1-录入型变量 3-运算型变量）
  public modelKey = ''; // 属性标识符，设备下唯一
  public fieldName = ''; // 属性名称
  public fieldUnit = null; // 属性单位
  public fieldValueType = 1; // 属性值类型（1-数值 2-字符串）
  public fieldDefaultValue = null; // 属性默认值
  public deviceKey = null; // 设备标识
  public computeType = 0; // 计算周期类型（1-周期循环 2-自定义时间段）
  public interval = 0; // 统计周期时长（分钟）
  public cron = null; // 执行Cron
  public computeExpression = null; // 计算公式（支持+ - * / 运算 A+B）
  public computeItemsJson = null; // 计算元素JSON
  public isSave = 0; // 是否存储（0-否 1-是）
  public status = 0; // 状态：0=正常，1=停用
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'fieldId',
    component: 'InputNumber',
    label: '属性ID',
    componentProps: {
      placeholder: '请输入属性ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '属性ID',
    field: 'fieldId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '组合设备标识',
    field: 'unionKey',
    align: 'left',
    width: -1,
 },
  {
    title: '属性类型（1-录入型变量 3-运算型变量）',
    field: 'fieldType',
    align: 'left',
    width: -1,
 },
  {
    title: '属性标识符，设备下唯一',
    field: 'modelKey',
    align: 'left',
    width: -1,
 },
  {
    title: '属性名称',
    field: 'fieldName',
    align: 'left',
    width: -1,
 },
  {
    title: '属性单位',
    field: 'fieldUnit',
    align: 'left',
    width: -1,
 },
  {
    title: '属性值类型（1-数值 2-字符串）',
    field: 'fieldValueType',
    align: 'left',
    width: -1,
 },
  {
    title: '属性默认值',
    field: 'fieldDefaultValue',
    align: 'left',
    width: -1,
 },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
 },
  {
    title: '计算周期类型（1-周期循环 2-自定义时间段）',
    field: 'computeType',
    align: 'left',
    width: -1,
 },
  {
    title: '统计周期时长（分钟）',
    field: 'interval',
    align: 'left',
    width: -1,
 },
  {
    title: '执行Cron',
    field: 'cron',
    align: 'left',
    width: -1,
 },
  {
    title: '计算公式（支持+ - * / 运算 A+B）',
    field: 'computeExpression',
    align: 'left',
    width: -1,
 },
  {
    title: '是否存储（0-否 1-是）',
    field: 'isSave',
    align: 'left',
    width: -1,
 },
  {
    title: '状态：0=正常，1=停用',
    field: 'status',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  fieldId: number;
  unionKey: string;
  fieldType: number;
  modelKey: string;
  fieldName: string;
  fieldUnit: string;
  fieldValueType: number;
  fieldDefaultValue: string;
  deviceKey: string;
  computeType: number;
  interval: number;
  cron: string;
  computeExpression: string;
  computeItemsJson: string;
  isSave: number;
  status: string;
  tenantId: string;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'fieldId',  label: '属性ID'},
  {  field: 'unionKey',  label: '组合设备标识'},
  {  field: 'fieldType',  label: '属性类型（1-录入型变量 3-运算型变量）'},
  {  field: 'modelKey',  label: '属性标识符，设备下唯一'},
  {  field: 'fieldName',  label: '属性名称'},
  {  field: 'fieldUnit',  label: '属性单位'},
  {  field: 'fieldValueType',  label: '属性值类型（1-数值 2-字符串）'},
  {  field: 'fieldDefaultValue',  label: '属性默认值'},
  {  field: 'deviceKey',  label: '设备标识'},
  {  field: 'computeType',  label: '计算周期类型（1-周期循环 2-自定义时间段）'},
  {  field: 'interval',  label: '统计周期时长（分钟）'},
  {  field: 'cron',  label: '执行Cron'},
  {  field: 'computeExpression',  label: '计算公式（支持+ - * / 运算 A+B）'},
  {  field: 'computeItemsJson',  label: '计算元素JSON'},
  {  field: 'isSave',  label: '是否存储（0-否 1-是）'},
  {  field: 'status',  label: '状态：0=正常，1=停用'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdAt',  label: '创建时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'fieldId',
    component: 'Input',
    label: '属性ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'unionKey',
    component: 'Input',
    label: '组合设备标识',
    componentProps: {
      placeholder: '请输入组合设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'fieldType',
    component: 'InputNumber',
    label: '属性类型（1-录入型变量 3-运算型变量）',
    componentProps: {
      placeholder: '请输入属性类型（1-录入型变量 3-运算型变量）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入属性类型（1-录入型变量 3-运算型变量）', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'modelKey',
    component: 'Input',
    label: '属性标识符，设备下唯一',
    componentProps: {
      placeholder: '请输入属性标识符，设备下唯一',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'fieldName',
    component: 'Input',
    label: '属性名称',
    componentProps: {
      placeholder: '请输入属性名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'fieldUnit',
    component: 'Input',
    label: '属性单位',
    componentProps: {
      placeholder: '请输入属性单位',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'fieldValueType',
    component: 'InputNumber',
    label: '属性值类型（1-数值 2-字符串）',
    componentProps: {
      placeholder: '请输入属性值类型（1-数值 2-字符串）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'fieldDefaultValue',
    component: 'Input',
    label: '属性默认值',
    componentProps: {
      placeholder: '请输入属性默认值',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'computeType',
    component: 'InputNumber',
    label: '计算周期类型（1-周期循环 2-自定义时间段）',
    componentProps: {
      placeholder: '请输入计算周期类型（1-周期循环 2-自定义时间段）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'interval',
    component: 'InputNumber',
    label: '统计周期时长（分钟）',
    componentProps: {
      placeholder: '请输入统计周期时长（分钟）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'cron',
    component: 'Input',
    label: '执行Cron',
    componentProps: {
      placeholder: '请输入执行Cron',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'computeExpression',
    component: 'Input',
    label: '计算公式（支持+ - * / 运算 A+B）',
    componentProps: {
      placeholder: '请输入计算公式（支持+ - * / 运算 A+B）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'computeItemsJson',
    component: 'Input',
    label: '计算元素JSON',
    componentProps: {
      placeholder: '请输入计算元素JSON',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'isSave',
    component: 'InputNumber',
    label: '是否存储（0-否 1-是）',
    componentProps: {
      placeholder: '请输入是否存储（0-否 1-是）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入是否存储（0-否 1-是）', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'status',
    component: 'Input',
    label: '状态：0=正常，1=停用',
    componentProps: {
      placeholder: '请输入状态：0=正常，1=停用',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'tenantId',
    component: 'Input',
    label: '租户ID',
    componentProps: {
      placeholder: '请输入租户ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
];