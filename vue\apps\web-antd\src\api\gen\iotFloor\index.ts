import { requestClient } from '#/api/request';

// 获取楼层表列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotFloor/list', { params });
}

// 删除/批量删除楼层表
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotFloor/delete', { ...params });
}

// 添加/编辑楼层表
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotFloor/edit', { ...params });
}

// 获取楼层表指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotFloor/view', { params });
}

// 导出楼层表
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotFloor/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}