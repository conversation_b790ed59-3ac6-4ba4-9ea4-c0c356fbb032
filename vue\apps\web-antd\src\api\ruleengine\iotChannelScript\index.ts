import { requestClient } from '#/api/request';

// 获取规则脚本列表
export function List(params: any) {
  return requestClient.get<any>('ruleengine/iotChannelScript/list', { params });
}

// 删除/批量删除规则脚本
export function Delete(params: any) {
  return requestClient.post<any>('ruleengine/iotChannelScript/delete', {
    ...params,
  });
}

// 添加/编辑规则脚本
export function Edit(params: any) {
  return requestClient.post<any>('ruleengine/iotChannelScript/edit', {
    ...params,
  });
}

// 修改规则脚本状态
export function Status(params: any) {
  return requestClient.post<any>('ruleengine/iotChannelScript/status', {
    ...params,
  });
}

// 获取规则脚本指定详情
export function View(params: any) {
  return requestClient.get<any>('ruleengine/iotChannelScript/view', { params });
}
//验证规则脚本
export function Validate(params: any) {
  return requestClient.post<any>('ruleengine/iotChannelScript/validate', {
    ...params,
  });
}
// 导出规则脚本
export function Export(params: any) {
  return requestClient.post<Blob>(
    '/ruleengine/iotChannelScript/export',
    { ...params },
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
      responseType: 'blob',
    },
  );
}
