import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import type { DescItem } from '#/components/description';
import { renderDict } from '#/utils';
export class State {
	public consumerId = 0; // 终端用户ID
	public userName = ''; // 用户名
	public userPassword = ''; // 登录密码
	public userSalt = ''; // 加密盐
	public nickName = ''; // 用户昵称
	public realName = null; // 真实姓名
	public sex = null; // 性别（0男 1女 2未知）
	public phone = ''; // 手机
	public email = null; // 邮箱
	public status = 0; // 状态：0=正常，1=停用
	public projectId = 0; // 项目ID
	public tenantId = ''; // 租户ID
	public deptId = 0; // 所属机构
	public createdDept = 0; // 创建部门
	public createdBy = 0; // 创建者
	// public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
	public createdAt = ''; // 创建时间
	public updatedBy = 0; // 更新者
	// public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
	public updatedAt = ''; // 更新时间
	public deletedBy = 0; // 删除人
	// public deletedBySumma?: null | MemberSumma = null; // 删除人摘要信息
	public deletedAt = ''; // 删除时间
	public remark = null; // 备注

	constructor(state?: Partial<State>) {
		if (state) {
			Object.assign(this, state);
		}
	}
}

// 用户信息
export const schema: VbenFormSchema[] = [
	// 用户名
	{
		fieldName: 'userName',
		component: 'Input',
		label: '用户名',
		componentProps: { placeholder: '用户名', readonly: true, },
		formItemClass: 'col-span-4', // 每行3个，占4列（24栅格）
	},
	// 昵称
	{
		fieldName: 'nickName',
		component: 'Input',
		label: '昵称',
		componentProps: { placeholder: '昵称', readonly: true, },
		formItemClass: 'col-span-4',
	},
	// 手机
	{
		fieldName: 'phone',
		component: 'Input',
		label: '手机',
		componentProps: { placeholder: '手机', readonly: true, },
		formItemClass: 'col-span-4',
	},

	// 真实姓名
	{
		fieldName: 'realName',
		component: 'Input',
		label: '真实姓名',
		componentProps: { placeholder: '真实姓名', readonly: true, },
		formItemClass: 'col-span-4',
	},
	// 性别
	{
		fieldName: 'sex',
		component: 'Input',
		label: '性别',
		componentProps: { placeholder: '性别', readonly: true, },
		formItemClass: 'col-span-4',
	},
	// 邮箱
	{
		fieldName: 'email',
		component: 'Input',
		label: '邮箱',
		componentProps: { placeholder: '邮箱', readonly: true, },
		formItemClass: 'col-span-4',
	},

	// 所在项目
	{
		fieldName: 'projectName',
		component: 'Input',
		label: '所在项目',
		componentProps: { placeholder: '所在项目', readonly: true, },
		formItemClass: 'col-span-4',
	},
	// 所属机构
	{
		fieldName: 'deptName',
		component: 'Input',
		label: '所属机构',
		componentProps: { placeholder: '所属机构', readonly: true, },
		formItemClass: 'col-span-4',
	},
	{
		fieldName: 'ip',
		component: 'Input',
		label: '登录地址',
		componentProps: { placeholder: '登录地址', readonly: true, },
		formItemClass: 'col-span-4',
	},
	{
		fieldName: 'remark',
		component: 'Input',
		label: '备注',
		componentProps: { placeholder: '备注', readonly: true, },
		formItemClass: 'col-span-12',
	},
];

// 第三方授权信息
export const columns: VxeGridProps['columns'] = [
	{
		title: '编号',
		field: 'id',
		align: 'center',
		width: -1,
	},
	{
		title: '第三方类型', field: 'oauthType', align: 'center', width: -1,
		slots: {
			default: ({ row }) => {
				return renderDict(row.oauthType, 'oauth_type');
			}
		},
	},
	{
		title: '第三方AppID',
		field: 'appid',
		align: 'center',
		width: -1,
	},
	{
		title: '访问令牌',
		field: 'accessToken',
		align: 'center',
		width: -1,
	},
	{
		title: '访问令牌过期时间',
		field: 'accessExpires',
		align: 'center',
		width: -1,
	},
	{
		title: '刷新令牌',
		field: 'refreshToken',
		align: 'center',
		width: -1,
	},
	{
		title: '刷新令牌过期时间',
		field: 'refreshExpires',
		align: 'center',
		width: -1,
	},
	{
		title: '第三方OpenID',
		field: 'openid',
		align: 'center',
		width: -1,
	},
	{
		title: '第三方UnionID',
		field: 'unionid',
		align: 'center',
		width: -1,
	},
	{
		title: '关注状态', field: 'status', align: 'center', width: -1,
		slots: {
			default: ({ row }) => {
				return renderDict(row.status, 'follow_type');
			}
		},
	},
	{
		title: '数据更新时间',
		field: 'updatedAt',
		align: 'center',
		width: -1,
	},
];

// 表格列接口
export interface RowType {
	id: number;
	consumerId: number;
	oauthType: string;
	appid: string;
	accessToken: string;
	accessExpires: string;
	refreshToken: string;
	refreshExpires: string;
	openid: string;
	unionid: string;
	status: number;
	createdBy: number;
	updatedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
	{ field: 'id', label: '编号' },
	{ field: 'consumerId', label: '消费用户ID' },
	{
		field: 'oauthType',
		label: '第三方类型',
		render(row: any) {
			return renderDict(row.oauthType, 'oauth_type');
		},
	},
	{ field: 'appid', label: '第三方AppID' },
	{ field: 'accessToken', label: '访问令牌' },
	{ field: 'accessExpires', label: '访问令牌过期时间' },
	{ field: 'refreshToken', label: '刷新令牌' },
	{ field: 'refreshExpires', label: '刷新令牌过期时间' },
	{ field: 'openid', label: '第三方OpenID' },
	{ field: 'unionid', label: '第三方UnionID' },
	{
		field: 'status',
		label: '关注状态',
		render(row: any) {
			return renderDict(row.status, 'follow_type');
		},
	},
	{ field: 'createdBy', label: '创建者' },
	{ field: 'updatedAt', label: '更新时间' },
];
