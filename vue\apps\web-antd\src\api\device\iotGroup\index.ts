import { requestClient } from '#/api/request';

// 获取分组信息列表
export function List(params:any) {
  return requestClient.get<any>('device/iotGroup/list', { params });
}

// 删除/批量删除分组信息
export function Delete(params:any) {
  return requestClient.post<any>('device/iotGroup/delete', { ...params });
}

// 添加/编辑分组信息
export function Edit(params:any) {
  return requestClient.post<any>('device/iotGroup/edit', { ...params });
}

// 获取分组信息指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotGroup/view', { params });
}

// 导出分组信息
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotGroup/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}

// 获取分组信息列表
export function ListNoPage(params:any) {
  return requestClient.get<any>('device/iotGroup/listNoPage', { params });
}