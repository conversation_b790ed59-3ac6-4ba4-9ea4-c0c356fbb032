
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import { DictEnum } from '@vben/constants';
import { renderDict } from '#/utils/render';
import { renderDictTags } from '#/utils';
import { getDictOptions } from '#/utils/dict';


export class State {
  public alarmLogId = 0; // 报警记录ID
  public alarmConfigId = 0; // 报警配置ID
  public sceneId = 0; // 场景ID
  public productName = ''; // 报警产品
  public deviceName = ''; // 报警设备

  public key = '';
  public value = '';
  public alarmConfigName = '';
  public alarmLevel = 0; // 报警级别
  public startTime = ''; // 开始时间
  public endTime = ''; // 结束时间
  public confirmState = 0; // 处理状态（1=确认报警 2=躁扰报警 3=测试报警）
  public confirmContent = ''; // 处理结果
  public confirmTime = ''; // 处理时间
  public confirmUser = 0; // 处理用户ID
  public confirmUserType = ''; // 报警处理用户（1=设备报警用户 2=终端绑定用户 3=项目接警用户 4=后台用户）
  public sceneConditions = ''; // 场景条件JSON
  public tenantId = ''; // 租户ID（报警配置所属租户）
  public deptId = 0; // 所属机构
  public createdAt = ''; // 创建时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '编号',
    field: 'alarmLogId',
    align: 'center',
    width: 80,
  },
  {
    title: '报警名称',
    field: 'alarmConfigName',
    align: 'center',
    width: -1,
    //设置最小宽度
    minWidth: 100,
  },
    {
      title: '报警产品',
      field: 'productName',
      align: 'center',
      width: -1,
      minWidth: 100,
   },
    {
      title: '报警设备',
      field: 'deviceName',
      align: 'center',
      width: -1,
      minWidth: 100,
   },

   {
     title: '报警级别',
     field: 'items.alarmLevel',
     align: 'center',
     width: 150,
     slots: {
       default: ({ row }) => {
         // 转成类型再查字典
         return renderDictTags(
           [String(row.alarmLevel)],
           getDictOptions('alarm_level'),
           true,
           4
        );
       },
    },
  },
  {
    title: '处理状态',
    field: 'confirmState',
    align: 'center',
    width: 120,
    slots: {
      default: ({ row }) => {
        return renderDict(row.confirmState, DictEnum.ALARM_CONFIRM_STATE);
      },
    },
  },
  {
    title: '开始时间',
    field: 'startTime',
    align: 'center',
    width: 180,
  },
  {
    title: '结束时间',
    field: 'endTime',
    align: 'center',
    width: 180,
  },

];

// 表格列接口
export interface RowType {
  alarmLogId: number;
  alarmConfigId: number;
  sceneId: number;
  productName: string;
  alarmConfigName: string;
  deviceName: string;
  alarmLevel: number;
  startTime: string;
  endTime: string;
  confirmState: number;
  confirmContent: string;
  confirmTime: string;
  confirmUser: number;
  confirmUserType: string;
  sceneConditions: string;
  tenantId: string;
  deptId: number;
  key: string;
  value: string;
  createdAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'alarmLogId', label: '报警记录ID' },
  { field: 'items.alarmConfigId', label: '报警配置ID' },
  { field: 'sceneId', label: '场景ID' },
  { field: 'productName', label: '报警产品' },
  { field: 'deviceName', label: '报警设备' },
  { field: 'items.alarmConfigName', label: '报警名称' },

  { field: 'items.alarmLevel', label: '报警级别' },
  { field: 'startTime', label: '开始时间' },
  { field: 'endTime', label: '结束时间' },
  { field: 'confirmState', label: '处理状态' },
  { field: 'confirmContent', label: '处理结果' },
  { field: 'confirmTime', label: '处理时间' },
  { field: 'confirmUser', label: '处理用户ID' },
  { field: 'confirmUserType', label: '报警处理用户' },
  { field: 'sceneConditions', label: '场景条件JSON' },
  { field: 'deptId', label: '所属机构' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'remark', label: '备注' },
];

