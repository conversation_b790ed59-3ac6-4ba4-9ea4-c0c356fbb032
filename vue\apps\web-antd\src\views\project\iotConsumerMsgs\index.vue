<script lang="ts" setup>
import { h, reactive, ref, computed, onMounted } from 'vue';
import { Button, message, Tag, Modal, Popconfirm, Switch } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, Delete, Status } from '#/api/project/iotConsumerMsgs';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import viewDrawer from '#/views/project/iotConsumer/msgs/view.vue';
import { List as ListConsumer } from '#/api/project/iotConsumer';


type Option = {
  label: string;
  value: string;
};
const consumerOptions = ref<Option[]>([]);
async function loadProductOptions() {
  const res = await ListConsumer({
    page: 1,
    pageSize: 1000,
  });
  if (!res || !res.items) {
    consumerOptions.value = [];
  } else {
    consumerOptions.value = res.items.map((item: any) => ({
      label: item.userName,
      value: item.consumerId,
    }));
  }
  gridApi.formApi.updateSchema([
    {
      fieldName: 'consumerId',
      component: 'Select',
      label: '接受者',
      componentProps: {
        placeholder: '请选择状态',
        onUpdateValue: (e: any) => {
          console.log(e);
        },
        options: consumerOptions.value,
        showSearch: true,
        filterOption: (input: any, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
      },
    },
  ]);
}
loadProductOptions()
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'id',
  },
  rowConfig: {
    keyField: 'id',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});
const [ViewDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: viewDrawer,
});
function handlePreview(record: RowType) {
  drawerApi.setData(record);
  drawerApi.open();
}
async function handleRefresh() {
  await gridApi.query();
}
async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '终端用户消息表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success("导出成功");
}
async function handleStatusChange(row: RowType) {
  await Status({ id: row.msgId, status: row.status });
  await message.success("操作成功")
  await handleRefresh();
}
</script>
<template>
  <Page auto-content-height>
    <Grid table-title="终端用户消息表">
      <template #toolbar-tools>
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiExport)" @click="handleExport"
          v-access:code="'cpm:project:iotConsumerMsgs:export'">
          导出
        </Button>
      </template>
      <template #status="{ row }">
        <Switch v-model:checked="row.status" :checkedValue="'0'" :unCheckedValue="'1'" @change="handleStatusChange(row)"
          :disabled="!hasAccessByCodes(['cpm:project:iotConsumerMsgs:status'])" />
      </template>
      <template #action="{ row }">
        <div class="flex items-center justify-center">
          <Button class="border-none p-0" :block="false" type="link" @click="handlePreview(row)"
            v-access:code="'cpm:project:iotConsumerMsgs:view'">
            查看
          </Button>
        </div>
      </template>
    </Grid>
    <EditDrawer @reload="handleRefresh" />
    <ViewDrawer />
  </Page>
</template>