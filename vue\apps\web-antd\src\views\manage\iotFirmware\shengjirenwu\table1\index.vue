<script lang="ts" setup>
import { h, reactive, ref, computed, watch } from 'vue';
import { Button, message, Tag, Modal, Popconfirm, Switch, Radio, Input } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, Delete } from '#/api/manage/iotUpgradeTaskDetail';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import { getDictOptions } from '#/utils/dict';
// import editDrawer from './edit-drawer.vue';
// import viewDrawer from './view-drawer.vue';

// 定义props
interface Props {
  taskId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  taskId: '',
});

const formOptions: VbenFormProps = {
  // 隐藏搜索表单，因为我们使用自定义的状态筛选按钮
  schema: [],
  showCollapseButton: false,
  showDefaultActions: false,
  wrapperClass: 'hidden',
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'detailId',
  },
  rowConfig: {
    keyField: 'detailId',
  },
  columns: columns,
  exportConfig: {},
  height: '450', // 自动高度，让表格完全展示所有数据
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  emptyRender: {
    name: 'AEmpty',
    props: {
      description: computed(() => {
        if (props.taskId) {
          if (searchDeviceKey.value && searchDeviceKey.value.trim()) {
            return `任务 ${props.taskId} 中没有找到包含 "${searchDeviceKey.value}" 的设备`;
          }
          return `任务 ${props.taskId} 暂无升级明细数据`;
        }
        if (searchDeviceKey.value && searchDeviceKey.value.trim()) {
          return `没有找到包含 "${searchDeviceKey.value}" 的设备`;
        }
        return '暂无升级明细数据';
      })
    }
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const queryParams = {
          page: 1, // 获取所有数据用于前端筛选
          pageSize: 1000, // 获取足够多的数据
          ...formValues,
        };

        // 添加任务ID作为默认筛选参数
        if (props.taskId) {
          queryParams.taskId = props.taskId;
          console.log('🔍 任务ID筛选:', props.taskId);
        }

        // 注意：不在后端进行状态筛选，始终获取所有数据
        console.log('🔍 查询全部数据用于前端筛选和统计');

        try {
          const result = await List(queryParams);

          if (result.items) {
            // 先计算状态统计（基于所有数据）
            calculateStatusCounts(result.items);

            let filteredItems = result.items;

            // 前端状态筛选
            if (selectedStatus.value && selectedStatus.value !== 'all') {
              const statusButton = statusButtons.value.find(btn => btn.key === selectedStatus.value);
              if (statusButton && statusButton.value) {
                filteredItems = filteredItems.filter((item: any) =>
                  String(item.upgradeStatus) === statusButton.value
                );
                console.log('🔍 前端状态筛选:', statusButton.label, '筛选后数量:', filteredItems.length);
              }
            }

            // 前端设备标识搜索筛选
            if (searchDeviceKey.value && searchDeviceKey.value.trim()) {
              const searchTerm = searchDeviceKey.value.trim().toLowerCase();
              filteredItems = filteredItems.filter((item: any) =>
                item.deviceKey && item.deviceKey.toLowerCase().includes(searchTerm)
              );
              console.log('🔍 前端设备标识筛选:', searchTerm, '筛选后数量:', filteredItems.length);
            }

            // 前端分页处理
            const startIndex = (page.currentPage - 1) * page.pageSize;
            const endIndex = startIndex + page.pageSize;
            const paginatedItems = filteredItems.slice(startIndex, endIndex);

            return {
              items: paginatedItems,
              total: filteredItems.length,
              page: page.currentPage,
              pageSize: page.pageSize
            };
          }

          return result;
        } catch (error: any) {
          console.error('❌ 查询失败:', error);
          return { items: [], total: 0 };
        }
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: false, // 隐藏搜索功能
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}

// 搜索相关
const searchDeviceKey = ref(''); // 设备标识搜索关键词

// 状态筛选相关
const selectedStatus = ref('all'); // 当前选中的状态
const statusCounts = ref({
  all: 0,
  pending: 0,    // 待推送
  upgrading: 0,  // 升级中
  success: 0,    // 升级成功
  failed: 0,     // 升级失败
 // stopped: 0,    // 停止
});

// 状态筛选按钮配置
const statusButtons = computed(() => {
  return [
    { key: 'all', label: '全部', value: null, count: statusCounts.value.all },
    { key: 'pending', label: '待升级', value: '1', count: statusCounts.value.pending },
    { key: 'upgrading', label: '升级中', value: '2', count: statusCounts.value.upgrading },
    { key: 'success', label: '升级成功', value: '3', count: statusCounts.value.success },
    { key: 'failed', label: '升级失败', value: '4', count: statusCounts.value.failed },

  ];
});

// 设备标识搜索处理函数
function handleDeviceSearch() {
  console.log('🔍 设备标识搜索:', searchDeviceKey.value);

  // 触发表格查询
  if (gridApi && gridApi.query) {
    gridApi.query();
  }
}

// 清空搜索
function handleClearSearch() {
  searchDeviceKey.value = '';
  console.log('🔍 清空设备标识搜索');

  // 触发表格查询
  if (gridApi && gridApi.query) {
    gridApi.query();
  }
}

// 状态筛选处理函数
function handleStatusFilter(e: any) {
  const status = e.target.value;
  selectedStatus.value = status;
  console.log('🔍 状态筛选:', status);

  // 触发表格查询
  if (gridApi && gridApi.query) {
    gridApi.query();
  }
}

// 计算各状态的数量
function calculateStatusCounts(data: RowType[]) {
  const counts = {
    all: data.length,
    pending: 0,
    upgrading: 0,
    success: 0,
    failed: 0,
    stopped: 0,
  };

  data.forEach((item) => {
    const status = String(item.upgradeStatus);
    switch (status) {
      case '1':
        counts.pending++;
        break;
      case '2':
        counts.upgrading++;
        break;
      case '3':
        counts.success++;
        break;
      case '4':
        counts.failed++;
        break;
      case '5':
        counts.stopped++;
        break;
    }
  });

  statusCounts.value = counts;
  console.log('📊 状态统计:', counts);
}
const [Grid, gridApi] = useVbenVxeGrid({
  //formOptions,
  gridOptions,
  gridEvents,
});

// const [ViewDrawer, drawerApi] = useVbenDrawer({
//   connectedComponent: viewDrawer,
// });

// const [EditDrawer, editDrawerApi] = useVbenDrawer({
//   connectedComponent: editDrawer,
// });


async function handleDelete(row: RowType) {
  await Delete({ detailId: [row.detailId] });
  message.success("删除成功");
  await handleRefresh();
}

function handlePreview(row: RowType) {
  message.info(`查看明细ID: ${row.detailId}`);
  // drawerApi.setData({ record: row });
  // drawerApi.open();
}

function handleEdit(row: RowType) {
  message.info(`编辑明细ID: ${row.detailId}`);
  // editDrawerApi.setData({ id: row.detailId, update: true, view: false });
  // editDrawerApi.open();
}
async function handleRefresh() {
  await gridApi.query();
}

// 刷新表格数据的方法，供父组件调用
function refreshTable() {
  console.log('🔄 刷新表格 - taskId:', props.taskId, '状态:', selectedStatus.value);

  try {
    if (gridApi && gridApi.query) {
      gridApi.query();
    } else {
      console.warn('⚠️ 表格API未准备好，延迟重试');
      setTimeout(() => {
        if (gridApi && gridApi.query) {
          gridApi.query();
        }
      }, 100);
    }
  } catch (error) {
    console.error('❌ 刷新表格失败:', error);
  }
}

// 防抖搜索功能
let searchTimer: NodeJS.Timeout | null = null;

// 监听搜索关键词变化，实现防抖搜索
watch(searchDeviceKey, (newValue) => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 设置新的定时器，500ms后执行搜索
  searchTimer = setTimeout(() => {
    console.log('🔍 防抖搜索触发:', newValue);
    if (gridApi && gridApi.query) {
      gridApi.query();
    }
  }, 500);
});

// 移除自动刷新逻辑，只保留手动刷新
// 组件挂载后不自动刷新，让表格自然加载

// 暴露方法给父组件
defineExpose({
  refreshTable,
});
function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const ids: string[] = [];
  for (const row of rows) {
    ids.push(row.detailId);
  }
  if (ids.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await Delete({ detailId: ids });
      message.success("删除成功");
      await handleRefresh();
    },
  });
}
async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '升级任务明细表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success("导出成功");
}
</script>
<template>
  <Page auto-content-height>
    <Grid >
      <template #toolbar-tools>
        <!-- 搜索框和状态筛选按钮组 -->
        <div class="toolbar-container">
          <!-- 设备标识搜索框 -->
          <div class="search-container">
            <Input.Search
              v-model:value="searchDeviceKey"
              placeholder="请输入设备标识进行搜索"
              allow-clear
              
              size="small"
              style="width: 250px;"
              @search="handleDeviceSearch"
              @clear="handleClearSearch"
            />
          </div>

          <!-- 状态筛选按钮组 -->
          <div class="status-filter-container">
            <span style="margin-right: 12px; color: #666;">状态筛选:</span>
            <Radio.Group
              v-model:value="selectedStatus"
              @change="handleStatusFilter"
              button-style="solid"
              size="small"
            >
              <Radio.Button
                v-for="button in statusButtons"
                :key="button.key"
                :value="button.key"
                class="status-filter-button"
              >
                {{ button.label }}({{ button.count }})
              </Radio.Button>
            </Radio.Group>
          </div>
        </div>
      </template>
      <template #action="{ row }">
        <div class="flex items-center">
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handlePreview(row)"
            v-access:code="'cpm:manage:iotUpgradeTaskDetail:view'">
            查看
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row)"
            v-access:code="'cpm:manage:iotUpgradeTaskDetail:edit'">
            修改
          </Button>
          <AccessControl :codes="['cpm:manage:iotUpgradeTaskDetail:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
    <!-- <EditDrawer @reload="handleRefresh" /> -->
    <!-- <ViewDrawer /> -->
  </Page>
</template>

<style scoped>
/* 工具栏容器样式 */
.toolbar-container {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 20px;
}

/* 搜索容器样式 */
.search-container {
  display: flex;
  align-items: center;
}

/* 状态筛选容器样式 */
.status-filter-container {
  display: flex;
  align-items: center;
  flex: 1;
}

/* 状态筛选按钮样式 */
.status-filter-button {
  margin-right: 8px !important;
}

.status-filter-button:last-child {
  margin-right: 0 !important;
}

/* 按钮组容器样式 */
:deep(.ant-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 按钮样式调整 */
:deep(.ant-radio-button-wrapper) {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

:deep(.ant-radio-button-wrapper:hover) {
  border-color: #1890ff;
  color: #1890ff;
}

:deep(.ant-radio-button-wrapper-checked) {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
}

:deep(.ant-radio-button-wrapper-checked:hover) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}
</style>