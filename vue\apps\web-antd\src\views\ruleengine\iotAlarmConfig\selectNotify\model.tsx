import type { VxeGridProps } from '#/adapter/vxe-table';
import { cloneDeep, } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public id = 0; // 通知模板ID
  public templateName = ''; // 通知模板名称
  public businessType = null; // 业务类型
  public channelType = null; // 渠道类型
  public serviceType = null; // 服务商
  public channelId = 0; // 渠道ID

  public templateParam = null; // 模板参数JSON
  public status = 0; // 状态
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'templateName',
    component: 'Input',
    label: '通知模板名称',
    componentProps: {
      placeholder: '请输入通知模板名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'channelType',
    component: 'Select',
    label: '渠道类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择渠道类型',
      options: getDictOptions('notify_channel_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    type: 'radio',
    width: 40,
  },
  {
    title: '编号',
    field: 'id',
    align: 'center',
    width: 80,
  },
  {
    title: '通知模板名称',
    field: 'templateName',
    align: 'center',
    width: -1,
  },
  {
    title: '业务类型', field: 'businessType', align: 'center', width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.businessType, 'notify_business_type');
      }
    },
  },
  {
    title: '渠道类型', field: 'channelType', align: 'center', width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.channelType, 'notify_channel_type');
      }
    },
  },
  {
    title: '服务商',
    field: 'serviceType',
    align: 'center',
    width: -1,
    slots: {
      default: ({ row }) => {
        // 根据渠道类型动态选择服务商字典
        let dictType = DictEnum.NOTIFY_PROVIDER_SMS;
        if (row.channelType) {
          switch (row.channelType) {
            case 1: // 短信
            case 3: // 语音
              dictType = DictEnum.NOTIFY_PROVIDER_SMS;
              break;
            case 2: // 微信
              dictType = DictEnum.NOTIFY_PROVIDER_WECHAT;
              break;
            case 4: // 邮箱
              dictType = DictEnum.NOTIFY_PROVIDER_EMAIL;
              break;
            case 5: // 钉钉
              dictType = DictEnum.NOTIFY_PROVIDER_DING;
              break;
            case 6: // MQTT
              dictType = DictEnum.NOTIFY_PROVIDER_MQTT;
              break;
          }
        }
        return renderDict(row.serviceType, dictType);
      }
    },
  },
  {
    title: '渠道编号',
    field: 'channelId',
    align: 'center',
    width: 100,
  },
  {
    title: '状态', field: 'status', align: 'center', width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
        if (found) {
          return found;
        }
        return row.status;
      },
    },
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: 180,
  },
];

// 表格列接口
export interface RowType {
  id: number;
  templateName: string;
  businessType: number;
  channelType: number;
  serviceType: number;
  channelId: number;
  templateParam: string;
  status: string;
  imgUrl: string;
  FileUpload: string;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
};
