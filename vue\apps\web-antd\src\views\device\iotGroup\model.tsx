import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';

export class State {
  public groupId = 0; // 分组ID
  public groupName = ''; // 分组名称
  public groupOrder = 0; // 排序
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedBySumma?: null | MemberSumma = null; // 删除人摘要信息
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'groupName',
    component: 'Input',
    label: '分组名称',
    componentProps: {
      placeholder: '请输入分组名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  },];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '',
    align: 'left',
    width: 50,
    type: 'checkbox',
  },
  {
    title: '编号',
    field: 'groupId',
    align: 'center',
    width: 200,
  },
  {
    title: '分组名称',
    field: 'groupName',
    align: 'left',
    width: -1,
  },
  {
    title: '排序',
    field: 'groupOrder',
    align: 'center',
    width: 200,
  },
  {
    title: '备注',
    field: 'remark',
    align: 'left',
    width: -1,
  },
  { title: '操作', width: 160, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  groupId: number;
  groupName: string;
  groupOrder: number;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'groupId', label: '编号' },
  { field: 'groupName', label: '分组名称' },
  { field: 'groupOrder', label: '排序' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'groupId',
    component: 'Input',
    label: '编号',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'groupName',
    component: 'Input',
    label: '分组名称',
    componentProps: {
      placeholder: '请输入分组名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'groupOrder',
    component: 'InputNumber',
    label: '排序',
    defaultValue: 0,
    componentProps: {
      placeholder: '请输入排序',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      min: 0,
      max: 9999,
    }, rules: null,
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    formItemClass: 'col-span-1',
  },];

  // 表格列
export const deviceColumns: VxeGridProps['columns'] = [
  {
    title: '',
    field: '',
    align: 'left',
    width: 50,
    type: 'checkbox',
  },
  {
    title: '所属产品',
    field: 'productName',
    align: 'left',
    width: -1,
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
  },
  {
    title: '设备名称',
    field: 'deviceName',
    align: 'left',
    width: -1,
  },
  { title: '操作', width: 80, slots: { default: 'action' } },
];

// 表格搜索表单
export const deviceGroupQuerySchema: VbenFormSchema[] = [
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
    componentProps: {
      placeholder: '请选择产品Key',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  },
];