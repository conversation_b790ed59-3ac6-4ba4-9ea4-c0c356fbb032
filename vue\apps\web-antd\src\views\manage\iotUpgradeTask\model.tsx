import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';
import { renderDict, renderPopoverMemberSumma, type MemberSumma } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public taskId = 0; // 任务ID
  public taskName = ''; // 任务名称
  public taskType = null; // 任务范围
  public upgradeType = 0; // 升级类型
  public firmwareId = 0; // 固件ID
  public firmwareVersion = ''; // 固件版本
  public hardwareVersion = ''; // 硬件版本
  public subPackageSize = 0; // 分包字节大小
  public fileUrl = ''; // 文件地址
  public taskTime = ''; // 预定执行时间
  public taskStatus = null; // 任务状态
  public productKey = ''; // 产品标识
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 更新时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'taskName',
    component: 'Input',
    label: '任务名称',
    componentProps: {
      placeholder: '请输入任务名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'firmwareId',
    component: 'InputNumber',
    label: '固件ID',
    componentProps: {
      placeholder: '请输入固件ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '任务ID',
    field: 'taskId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '任务名称',
    field: 'taskName',
    align: 'left',
    width: -1,
  },
  {
    title: '任务范围', field: 'taskType', align: 'left', width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.taskType, 'upgrade_task_type');
      }
    },
  },

  {
    title: '固件ID',
    field: 'firmwareId',
    align: 'left',
    width: -1,
  },
  {
    title: '固件版本',
    field: 'firmwareVersion',
    align: 'left',
    width: -1,
  },
  {
    title: '硬件版本',
    field: 'hardwareVersion',
    align: 'left',
    width: -1,
  },
  {
    title: '分包字节大小',
    field: 'subPackageSize',
    align: 'left',
    width: -1,
  },
  {
    title: '文件地址',
    field: 'fileUrl',
    align: 'left',
    width: -1,
  },
  {
    title: '预定执行时间',
    field: 'taskTime',
    align: 'left',
    width: -1,
  },
  {
    title: '任务状态', field: 'taskStatus', align: 'left', width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.taskStatus, 'sys_common_status');
      }
    },
  },
  {
    title: '产品标识',
    field: 'productKey',
    align: 'left',
    width: -1,
  },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
  },
  {
    title: '创建部门',
    field: 'createdDept',
    align: 'left',
    width: -1,
  },
  {
    title: '创建者',
    field: 'createdBy',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderPopoverMemberSumma(row.createdBySumma);
      },
    },
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
  },
  {
    title: '更新时间',
    field: 'updatedAt',
    align: 'left',
    width: -1,
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  taskId: number;
  taskName: string;
  taskType: number;
  upgradeType: number;
  firmwareId: number;
  firmwareVersion: string;
  hardwareVersion: string;
  subPackageSize: number;
  fileUrl: string;
  taskTime: string;
  taskStatus: number;
  productKey: string;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'taskId', label: '任务ID' },
  { field: 'taskName', label: '任务名称' },
  {
    field: 'taskType',
    label: '任务范围',
    render(row: any) {
      return renderDict(row.taskType, 'upgrade_task_type');
    },
  },
  { field: 'upgradeType', label: '升级类型' },
  { field: 'firmwareId', label: '固件ID' },
  { field: 'firmwareVersion', label: '固件版本' },
  { field: 'hardwareVersion', label: '硬件版本' },
  { field: 'subPackageSize', label: '分包字节大小' },
  { field: 'fileUrl', label: '文件地址' },
  { field: 'taskTime', label: '预定执行时间' },
  {
    field: 'taskStatus',
    label: '任务状态',
    render(row: any) {
      return renderDict(row.taskStatus, 'sys_common_status');
    },
  },
  { field: 'productKey', label: '产品标识' },
  { field: 'tenantId', label: '租户ID' },
  { field: 'createdDept', label: '创建部门' },
  { field: 'createdBy', label: '创建者' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'updatedAt', label: '更新时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'taskId',
    component: 'Input',
    label: '任务ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'taskName',
    component: 'Input',
    label: '任务名称',
    componentProps: {
      placeholder: '请输入任务名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'taskType',
    component: 'Select',
    label: '任务范围',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择任务范围',
      options: getDictOptions('upgrade_task_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired'
  },
  {
    fieldName: 'firmwareId',
    component: 'InputNumber',
    label: '固件ID',
    componentProps: {
      placeholder: '请输入固件ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({ required_error: '请输入固件ID', invalid_type_error: '无效数字' })
  },
  
  {
    fieldName: 'taskTime',
    component: 'DatePicker',
    label: '预定执行时间',
    componentProps: {
      type: 'datetime',
      clearable: true,
      shortcuts: 'FMTime',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];