import type { VbenFormSchema } from "@vben/common-ui";
import type { VxeGridProps } from "@vben/plugins/vxe-table";

/** 表格搜索 */
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'fieldName',
    component: 'Input',
    label: '属性名称',
    componentProps: {
      placeholder: '请输入属性名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  }, {
    fieldName: 'modelKey',
    component: 'Input',
    label: '属性标识符',
    componentProps: {
      placeholder: '请输入属性标识符',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
]
/** 表格列配置 */
export const columns: VxeGridProps['columns'] = [
  // 编号
  {
    title: '编号',
    field: 'fieldId',
    align: 'center',
    width: 80,
  },
  // 属性名称
  {
    title: '属性名称',
    field: 'fieldName',
    align: 'center',
    // width: -1,
  },
  //  属性标识
  {
    title: '属性标识',
    field: 'modelKey',
    align: 'center',
    // width: -1,
  },
  // 是否启用
  {
    title: '是否历史存储',
    field: 'isSave',
    align: 'center',
    width: 80,
    slots: { default: 'isSave' },
  },
  //  计算周期
  {
    title: '计算周期',
    field: 'computeType',
    align: 'center',
    slots: { default: 'computeType' },
    // width: -1,
  },//  计算公式
  {
    title: '计算公式',
    field: 'computeExpression',
    align: 'center',
    // width: -1,
  },
  // 是否启用
  {
    title: '是否启用',
    field: 'status',
    align: 'center',
    width: 80,
    slots: { default: 'status' },
  },
  { title: '操作', slots: { default: 'action' } },
];


/** 新增/编辑表单 */
export const editSchema: VbenFormSchema[] = [
  /** 属性ID */
  {
    fieldName: 'fieldId',
    component: 'Input',
    label: '属性ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  /** 属性名称 */
  {
    fieldName: 'fieldName',
    component: 'Input',
    label: '属性名称',
    componentProps: {
      placeholder: '请输入属性名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  /** 属性标识 */
  {
    fieldName: 'modelKey',
    component: 'Input',
    label: '属性标识',
    componentProps: {
      placeholder: '请输入属性标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  /** 属性单位 */
  {
    fieldName: 'fieldUnit',
    component: 'Input',
    label: '属性单位',
    componentProps: {
      placeholder: '请输入属性单位',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  /** 计算周期 */
  {
    fieldName: 'computed',
    component: 'Input',
    label: '计算周期',
    rules: 'required'
  },
  /** 变量及计算公式 */
  {
    fieldName: 'exp',
    component: 'Input',
    label: '变量及计算公式',
    rules: 'required'
  },
  /** 是否历史存储 */
  {
    fieldName: 'isSave',
    component: 'Switch',
    label: '是否历史存储',
    defaultValue:0,
    componentProps: {
      style: {
        width: '20px',
      },
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      checkedValue: 1,
      unCheckedValue:0
    },
    rules: 'required',
  },
  /** 是否启用 */
  {
    fieldName: 'status',
    component: 'Switch',
    label: '是否启用',
    defaultValue:"1",
    componentProps: {
      style: {
        width: '20px',
      },
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      checkedValue: "0",
      unCheckedValue:"1"
    },
    rules: 'required',
  },
];




