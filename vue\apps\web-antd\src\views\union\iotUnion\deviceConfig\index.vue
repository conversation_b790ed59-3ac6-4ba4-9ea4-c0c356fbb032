<template>
  <Page auto-content-height>
    <!-- 表格顶部功能按钮 -->
    <Space>
      <Button @click="editData" v-if="!isEdit" type="primary" block>编辑</Button>
      <Button @click="saveData" v-else type="primary" block>保存</Button>
      <Button @click="reload" type="primary" block>刷新</Button>
    </Space>
    <div class="flex flex-row width-full ">
      <!-- 子设备列表 -->
      <ChildGrid style="width: 30%;" table-title="子设备列表">
        <!-- 子设备列表 工具栏 -->
        <template #toolbar-tools>
          <Button :disabled="!isEdit" @click="openModal" type="primary" size="small">添加</Button>
        </template>
        <!-- 子设备列表 删除按钮 -->
        <template #action="{ row }">
          <Button type="link" :disabled="!isEdit" @click="childDelete(row.id)">删除</Button>
        </template>
      </ChildGrid>

      <!-- 属性列表 -->
      <PropGrid style="width: 70%;" class="" :table-title="title">
        <!-- 子设备列表 工具栏 -->
        <template #toolbar-tools>
          <label class="mr-2">全部启用</label>
          <Switch v-model:checked="allChange" :disabled="!isEdit" @click="changeStatus" :checkedValue="true"
            :unCheckedValue="false"></Switch>
        </template>
        <!-- 属性列表 状态开关 -->
        <template #action="{ row }">
          <Switch v-model:checked="row.isEnable" :checkedValue="1" :unCheckedValue="0" :disabled="!isEdit"></Switch>
        </template>
      </PropGrid>
    </div>
    <EditModal @confirm="modalConfirm"></EditModal>
  </Page>
</template>

<script setup lang='ts' name=''>
import { computed, onMounted, ref, defineExpose } from 'vue';
import { Page } from '@vben/common-ui';
import { Space, Button, Switch, Modal, message } from 'ant-design-vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid, type VxeGridListeners, type VxeTableGridOptions } from '@vben/plugins/vxe-table';
import { childColumns, type ChildRowType, EditListItemType, FieldListItemType, propColumns, type PropRowType } from './model';
import editModal from './edit.vue';
import type { DeepPartial } from '@vben/types';
import { List as PropsList } from '#/api/device/iotProductModel';
import { List as ChildList, EditList as ChildEdit } from '#/api/union/iotUnionItem';

/** 编辑状态flag */
const isEdit = ref(false)
/** 全部启用Switch状态 */
const allChange = ref(false)
/** props,接收核心unionKey */
const props = defineProps(['unionKey'])
const emit=defineEmits(['undateCalculate'])
/** 标题 */
const title = computed(() => {
  if (activeProduct.value && "deviceName" in activeProduct.value)
    return activeProduct.value.deviceName + '-属性列表'
  else
    return '属性列表'
})

/** 子设备列表数据(全部) */
const childTabelData = ref<any>([])
/** 属性列表数据(全部) */
const propsTabelData = ref<any>([])
/** 当前激活实体(单条) */
const activeProduct = ref<any>({})


/** 子设备列表 表格配置 */
const childGridOptions: VxeTableGridOptions<ChildRowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'id',
    /** 高亮点击行 */
    isCurrent: true,
  },
  columns: childColumns,
  exportConfig: {},
  // 如果设置为 auto，则必须确保存在父节点且不允许存在相邻元素，否则会出现高度闪动问题
  height: '600px',
  // height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: { enabled: false },
  proxyConfig: {
    ajax: { query: async () => { return childTabelData.value } },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  }
}
/** 子设备列表 点击事件配置 */
const gridEvents: DeepPartial<VxeGridListeners> = {
  /** 点击事件 */
  cellClick: childClick,
}
/** 子设备列表 表格实例 */
const [ChildGrid, childGridApi] = useVbenVxeGrid({
  gridOptions: childGridOptions,
  gridEvents: gridEvents
})
/** 属性列表 表格配置 */
const propGridOptions: VxeTableGridOptions<PropRowType> = {
  checkboxConfig: {
    highlight: true,
  },
  columns: propColumns,
  exportConfig: {},
  // 如果设置为 auto，则必须确保存在父节点且不允许存在相邻元素，否则会出现高度闪动问题
  height: '600px',
  // height: '100%',
  keepSource: true,
  showOverflow: false,
  pagerConfig: { enabled: false },
  proxyConfig: {
    ajax: { query: async () => { return propsTabelData.value }, },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  }
}
/** 属性列表 表格实例*/
const [PropGrid, propGridApi] = useVbenVxeGrid({
  gridOptions: propGridOptions
})
/** 模态框实例 */
const [EditModal, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: editModal,
});
// 事件处理
/** 模态框打开 */
function openModal() {
  modalApi.setData({ update: false, view: false });
  modalApi.open();
}
/** 模态框确认事件 */
async function modalConfirm(data: any) {
  /** 遍历选中的数据 */
  data.forEach((item: any) => {
    /** 创建模板数据 */
    const childItem: any = new EditListItemType()
    /** 将输入载入至模板 */
    for (const key in childItem) {
      if (item[key] != undefined) {
        childItem[key] = item[key]
      }
      if (key == "itemId")
        childItem[key] = item[key]
    }
    childItem.unionKey = props.unionKey

    /** 判断数据是否已经存在 */
    const isExist = childTabelData.value.items.find((item: any) => {
      const addItem = {
        deviceKey: childItem.deviceKey,
        deviceName: childItem.deviceName,
        productKey: childItem.productKey,
      }
      const oldItem = {
        deviceKey: item.deviceKey,
        deviceName: item.deviceName,
        productKey: item.productKey,
      }
      // console.log("对比数据:", addItem, oldItem)
      return JSON.stringify(addItem) == JSON.stringify(oldItem)
    })
    /** 数据存在,跳过添加,提示 */
    if (isExist == undefined) {
      childTabelData.value.items.unshift(childItem)
    } else { message.warn("设备名称:  " + childItem.deviceName + "  (设备标识:" + childItem.productKey + ") " + "已存在") }
  })
  activeProduct.value = childTabelData.value.items[0]
  // console.log("activeProduct:", activeProduct.value)
  await refreshPropsData()

  childGridApi.query()
}
// 按钮事件
/** 编辑按钮 */
async function editData() {
  isEdit.value = !isEdit.value
  /** 载入数据 */
  await refreshPropsData()
  /** 将本地数据与请求数据合并 */
  propsTabelData.value.items = [...propsTabelData.value.items]
}
/** 保存按钮 */
async function saveData() {
  /** fieldList 数据 */
  const data: any = propGridApi.grid.getData()
  /** 将最新fieldList数据保存到子设备列表中 */
  childTabelData.value.items.forEach((item: any) => {
    if (item.itemId == activeProduct.value.itemId) {
      item.fieldList = data
      return
    }
  })
  /**发起请求 */
  const res = await ChildEdit({
    unionKey: props.unionKey,
    items: childTabelData.value.items
  })
  /** 刷新数据 */
  reload()
  emit("undateCalculate")
  /** 关闭全部启用Switch */
  allChange.value = false
}
/** 刷新按钮 */
async function reload() {
  isEdit.value = false
  propGridApi.setLoading(true)
  /** 等待子设备列表加载 完成 */
  await refreshChildData()
  /** 默认取第一个加载 */
  activeProduct.value = childTabelData.value.items[0]
  /** 属性列表默认取第一个值为准 */
  propsTabelData.value = {
    "page": 1,
    "pageSize": 1000,
    "total": 2,
    "items": childTabelData.value.items.length > 0 ? childTabelData.value.items[0].fieldList : []
  }
  propGridApi.query()
  propGridApi.setLoading(false)
}
/** 属性列表 右侧状态切换 */
function changeStatus() {
  // console.log("属性列表开关变化")
  // console.log(allChange.value)
  if (allChange.value) {
    /** 启用所有 */
    propsTabelData.value.items.forEach((item: any) => {
      item.isEnable = 1
    })
  } else {
    /** 禁用所有 */
    propsTabelData.value.items.forEach((item: any) => {
      item.isEnable = 0
    })
  }
}
/** 子设备列表 点击数据事件 */
async function childClick(data: any) {
  activeProduct.value = data.row
  /** 编辑状态下 */
  if (isEdit.value) {
    /** 加载数据 */
    await refreshPropsData()
  }
  /** 预览状态下 */
  else {
    // console.log("非编辑状态:", data.row.fieldList)
    /** 属性列表默认取第一个值为准 */
    propsTabelData.value = {
      "page": 1,
      "pageSize": 1000,
      "total": 2,
      "items": data.row.fieldList
    }
  }
  await propGridApi.query()

}
/** 子设备列表 删除事件 */
function childDelete(id: any) {
  // console.log("删除目标ID:", itemId)
  if ("items" in childTabelData.value) {
    // console.log('表格所有数据:', childTabelData.value.items);
    /** 过滤目标 */
    childTabelData.value.items = childTabelData.value.items.filter((item: any) => item.id != id)
    // console.log("删除后数据:")
    // console.log(childTabelData.value.items)
    /** 重载数据 */
    childGridApi.query()
  }
}

// 数据获取
/** 子设备列表获取/刷新数据函数 */
async function refreshChildData() {
  childGridApi.setLoading(true)
  const childRes = await ChildList({
    page: 1,
    pageSize: 1000,
    unionKey: props.unionKey,
  });
  childTabelData.value = childRes
  childGridApi.query()
  childGridApi.setLoading(false)
  /** 设置第一行为高亮行 */
  childGridApi.grid.setCurrentRow(childTabelData.value.items[0]);
}
/** 属性列表获取/刷新数据函数 */
async function refreshPropsData() {
  propGridApi.setLoading(true)
  if (activeProduct.value) {
    const propRes = await PropsList({
      page: 1,
      pageSize: 1000,
      productKey: activeProduct.value.productKey,
      type: "property"
    });
    /** 当前激活的fieldList,匹配使用 */
    // console.log(activeProduct.value.fieldList)
    const activeFieldList = activeProduct.value.fieldList
    const filterList = propRes.items.map((mapItem: any) => {
      const filterItem = ref<Record<string, any>[]>([])
      if (activeFieldList.length > 0) {
        /** 从子设备类别fieldList中筛出同modelKey的数据 */
        filterItem.value = activeFieldList.filter((element: any) => {
          return mapItem.modelKey == element.modelKey
        })
      }
      /** 筛选结果为空,则返回当前实例 */
      if (filterItem.value.length == 0) {
        /** 设置isEnable值为1 (启用) */
        mapItem.isEnable = 1
        /** 创建新fieldList实例 */
        const retItem: any = new FieldListItemType()
        /** 载入值 */
        for (const key in retItem) { retItem[key] = mapItem[key] }
        return { ...retItem }
      } else {
        /** 筛选结果不为空,则返回筛选结果 */
        return { ...filterItem.value[0] }
      }
    })
    propRes.items = filterList
    propsTabelData.value = propRes
    propGridApi.query()
  }
  propGridApi.setLoading(false)
}

/** 挂载事件 */
onMounted(async () => {
  await reload()
  // console.log(childTabelData.value)
})

/** 切换页面检查函数 */
async function checkValue() {
  if (isEdit.value) {
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: '请确认是否保存数据',
        content: '请确认是否保存数据',
        okText: '保存',
        cancelText: '取消',
        async onOk() {
          try {
            await saveData();   // 等待保存完成
            resolve(true);      // 保存成功
          } catch (error) {
            reject(error);      // 保存失败时 reject
          }
        },
        async onCancel() {
          try {
            await reload();     // 等待重置完成
            resolve(false);     // 取消操作
          } catch (error) {
            reject(error);      // 重置失败时 reject
          }
        },
      });
    });
  }
  return Promise.resolve(false); // 非编辑状态直接返回 resolved
}
defineExpose({ checkValue })
</script>

<style scoped></style>
