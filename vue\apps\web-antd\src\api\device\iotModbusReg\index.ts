import { requestClient } from '#/api/request';

// 获取数据寄存器设置表列表
export function List(params:any) {
  return requestClient.get<any>('device/iotModbusReg/list', { params });
}

// 删除/批量删除数据寄存器设置表
export function Delete(params:any) {
  return requestClient.post<any>('device/iotModbusReg/delete', { ...params });
}

// 添加/编辑数据寄存器设置表
export function Edit(params:any) {
  return requestClient.post<any>('device/iotModbusReg/edit', { ...params });
}

// 获取数据寄存器设置表指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotModbusReg/view', { params });
}

// 导出数据寄存器设置表
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotModbusReg/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}