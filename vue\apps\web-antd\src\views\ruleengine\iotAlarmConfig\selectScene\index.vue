<script lang="ts" setup>
import { useVbenModal, } from '@vben/common-ui';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import type { VbenFormProps } from '#/adapter/form';
import { columns, type RowType, querySchema } from './model';
import type { DeepPartial } from '@vben/types';
import { List, } from '#/api/ruleengine/iotScene';
import { List as CategoryList } from '#/api/device/iotProductCategory';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { ref, defineProps } from 'vue';
import type { SceneRowType } from '../model';

// 添加 alarmLevel 状态
const currentAlarmLevel = ref<number | null>(null);

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const props = defineProps<{ getSceneKey: Function; editRow?: SceneRowType }>();
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'sceneId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        // 构建查询参数
        const queryParams: any = {
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        };

        // 根据 alarmLevel 决定是否添加 hasAlarm 筛选
        // 如果 alarmLevel 为 1（提醒通知），则显示所有场景，不添加 hasAlarm 筛选
        // 其他情况下添加 hasAlarm: 1 筛选
        if (currentAlarmLevel.value !== null && currentAlarmLevel.value != 1) {
          queryParams.hasAlarm = 1;
        }

        let result = await List(queryParams);

        let categoryArray = await CategoryList({});
        let categoryMap = categoryArray.items;
        result.items.forEach((item: any) => {
          item.categoryName = categoryMap.find(
            (i: any) => item.categoryId === i.categoryId,
          )?.categoryName;
        });
        return result;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});
//确认按钮回调函数
const onConfirm = () => {
  const row = gridApi.grid.getRadioRecord();
  if (row) {
    props.getSceneKey(row); // 直接传递整行数据
  }
  modalApi.close();
};

function openModal() {
  modalApi.open();
}

defineExpose({
  openModal,
});
const [Modal, modalApi] = useVbenModal({
  onConfirm: onConfirm,
  onCancel: () => {
    modalApi.close();
  },
  onOpenChange: (isOpen) => {
    if (isOpen) {
      // 当模态框打开时，获取传递的 alarmLevel 数据
      const modalData = modalApi.getData();
      if (modalData && modalData.alarmLevel !== undefined) {
        // 确保转换为数字类型
        currentAlarmLevel.value = Number(modalData.alarmLevel);
      } else {
        // 如果没有传递 alarmLevel，默认设置为 1（显示所有场景）
        currentAlarmLevel.value = 1;
      }
      // 重新加载表格数据以应用新的筛选条件
      setTimeout(() => {
        gridApi.query();
      }, 100);
      return null;
    }
    modalApi.close();
  },
});
</script>
<template>
  <div>
    <Modal class="h-[800px] w-[1000px]" title="选择场景">
      <div class="table-content">
        <Grid table-title="场景表"> </Grid>
      </div>
    </Modal>
  </div>
</template>
<style scoped>
.table-content {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
