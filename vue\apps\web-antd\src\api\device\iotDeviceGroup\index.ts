import { requestClient } from '#/api/request';

// 获取设备分组列表
export function List(params:any) {
  return requestClient.get<any>('device/iotDeviceGroup/list', { params });
}

// 删除/批量删除设备分组
export function Delete(params:any) {
  return requestClient.post<any>('device/iotDeviceGroup/delete', { ...params });
}

// 添加/编辑设备分组
export function Edit(params:any) {
  return requestClient.post<any>('device/iotDeviceGroup/edit', { ...params });
}

// 获取设备分组指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotDeviceGroup/view', { params });
}

// 导出设备分组
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotDeviceGroup/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}