import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict, renderPopoverMemberSumma, type MemberSumma } from '#/utils';
import { Switch } from '#/api/manage/iotFirmware';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';


export class State {
  public firmwareId = 0; // 固件ID
  public firmwareName = ''; // 固件名称
  public firmwareType = null; // 固件类型
  public firmwareVersion = ''; // 固件版本
  public hardwareVersion = ''; // 硬件版本
  public productKey = ''; // 产品标识
  public isLatest = 0; // 是否最新版本
  public fileUrl = ''; // 文件地址
  public subPackageSize = 0; // 分包字节大小
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedBySumma?: null | MemberSumma = null; // 删除人摘要信息
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'firmwareName',
    component: 'Input',
    label: '固件名称',
    componentProps: {
      placeholder: '请输入固件名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '产品名称',
    componentProps: {
      placeholder: '请选择产品名称',
      allowClear: true,
      showSearch: true, // 支持搜索
      filterOption: true, // 启用本地过滤
      options: [], // 初始为空，通过动态更新设置
      onUpdateValue: (value: any) => {
        console.log('选择的产品Key:', value);
      },
    },
  },
  
];

// 表格列
export const columns: VxeGridProps['columns'] = [

  {
    title: '固件名称',
    field: 'firmwareName',
    align: 'center',
    width: -1,
  },
  {
    title: '固件类型', field: 'firmwareType', align: 'center', width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.firmwareType, DictEnum.FIRMWARE_TYPE);
      }
    },
  },
  {
    title: '固件版本',
    field: 'firmwareVersion',
    align: 'center',
    width: -1,
  },

  {
    title: '产品标识',
    field: 'productKey',
    align: 'center',
    width: -1,
     visible: false,
  },
  {
    title: '产品名称',
    field: 'productName',
    align: 'center',
    width: -1,
  },
  {
    title: '下载地址',
    field: 'fileUrl',
    align: 'center',
    width: -1,
  },
  {
    title: '是否最新固件',
    field: 'isLatest',
    align: 'center',
    width: 120,
    slots: {
      default: ({ row }) => {
        const isLatest = row.isLatest === 1 || row.isLatest === true;
        return h(Tag, {
          color: isLatest ? 'green' : 'default',
        }, () => isLatest ? '最新' : '默认');
      }
    },
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: -1,
  },


  {
    title: '固件描述',
    field: 'remark',
    align: 'center',
    width: -1,

  },
  { title: '操作', width: 120, align: 'center', slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  firmwareId: number;
  firmwareName: string;
  firmwareType: number;
  firmwareVersion: string;
  hardwareVersion: string;
  productKey: string;
  productName?: string; // 添加产品名称字段
  isLatest: number;
  fileUrl: string;
  subPackageSize: number;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'firmwareId', label: '固件ID' },
  { field: 'firmwareName', label: '固件名称' },
  {
    field: 'firmwareType',
    label: '固件类型',
    render(row: any) {
      return renderDict(row.firmwareType, DictEnum.FIRMWARE_TYPE);
    },
  },
  { field: 'firmwareVersion', label: '固件版本' },
  { field: 'hardwareVersion', label: '硬件版本' },
  { field: 'productKey', label: '产品标识' },
  {
    field: 'isLatest',
    label: '是否最新固件',
    render(row: any) {
      return renderDict(row.isLatest, 'iot_yes_no');
    },
  },
  { field: 'fileUrl', label: '文件地址' },
  { field: 'subPackageSize', label: '分包字节大小' },
  { field: 'tenantId', label: '租户ID' },
  { field: 'createdDept', label: '创建部门' },
  { field: 'createdBy', label: '创建者' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'updatedBy', label: '更新者' },
  { field: 'updatedAt', label: '更新时间' },
  { field: 'deletedBy', label: '删除人' },
  { field: 'deletedAt', label: '删除时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'firmwareId',
    component: 'Input',
    label: '固件ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'firmwareName',
    component: 'Input',
    label: '固件名称',
    componentProps: {
      placeholder: '请输入固件名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
    {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
     dependencies: { show: () => false, triggerFields: [''], },
     componentProps: {
      showSearch: true,
      allowClear: true,
      style: {
        width: '50%',
      },
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-3',
    disabled: false,
    rules: 'required',
  }, {
    fieldName: 'productName',
    component: 'Input',
    label: '产品名称',
    componentProps: {
      placeholder: '产品名称',
      readonly: true,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  // {
  //   fieldName: 'firmwareType',
  //   component: 'RadioGroup',
  //   label: '固件类型',
  //   componentProps: {
  //     options: getDictOptions('firmware_type'),
  //     onUpdateChecked: (e: any) => {
  //       console.log(e);
  //     },
  //   },
  //   rules: 'required'
  // },
   {
          fieldName: 'firmwareType',
          component: 'Select',
          label: '固件类型',
          componentProps: {
            
            disabled: true, // 设置为禁用，不可选择
            options: [], // 
            onUpdateValue: (e: any) => {
              console.log(e);
            },
          },
          rules: 'selectRequired',
        },
  {
    fieldName: 'firmwareVersion',
    component: 'Input',
    label: '固件版本号',
    componentProps: {
      placeholder: '请输入固件版本',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },

  // {
  //   fieldName: 'isLatest',
  //   component: 'status',
  //   label: '是否最新版本',
  //   componentProps: {
  //     options: getDictOptions('iot_yes_no'),
  //     onUpdateChecked: (e: any) => {
  //       console.log(e);
  //     },
  //   },
  //   rules: 'required'
  // },
  {
  fieldName: 'isLatest',
  component: 'Switch',
  label: '是否最新固件',
  defaultValue: false,
  componentProps: {
    checkedChildren: '最新',
    unCheckedChildren: '默认',
    style: {
      width: 'auto',
    },
    placeholder: '请选择状态',
    onUpdateValue: (e: any) => {
      console.log('Switch值变化:', e);
    },
  },
  rules: 'selectRequired',
},
  {
    fieldName: 'fileUrl',
    component: 'Input',
    label: '固件文件',
    componentProps: {
      placeholder: '请上传固件文件',
      readonly: true,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  
  {
    fieldName: 'remark',
    component: 'Textarea',
    label: '固件描述',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];