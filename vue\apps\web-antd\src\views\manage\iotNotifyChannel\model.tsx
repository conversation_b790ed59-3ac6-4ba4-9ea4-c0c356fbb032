import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { Status } from '#/api/device/iotDevice';
import { Textarea } from 'ant-design-vue';

export class State {
  public channelId = 0; // 渠道ID
  public channelName = ''; // 渠道名称
  public channelType = null; // 渠道类型
  public serviceType = null; // 服务商
  public param = ''; // 参数
  public status = 0; // 状态
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注
  public sms = { accessKeyId: null };

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state!== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'channelName',
    component: 'Input',
    label: '渠道名称',
    componentProps: {
      placeholder: '请输入渠道名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'channelType',
    component: 'Select',
    label: '渠道类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择渠道类型',
      options: getDictOptions('notify_channel_type'),
      onUpdateValue: (e: any) => {
        console.log('渠道类型变更:', e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '渠道编号',
    field: 'channelId',
    align: 'center',
    width: 80,
  },
  {
    title: '渠道名称',
    field: 'channelName',
    align: 'left',
    width: -1,
  },
  {
    title: '渠道类型',
    field: 'channelType',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.channelType, DictEnum.NOTIFY_CHANNEL_TYPE);
      }
    },
  },
  {
    title: '服务商',
    field: 'serviceType',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        // 根据渠道类型动态选择服务商字典
        let dictType = DictEnum.NOTIFY_PROVIDER_SMS;
        if (row.channelType) {
          switch (row.channelType) {
            case 1: // 短信
            case 3: // 语音
              dictType = DictEnum.NOTIFY_PROVIDER_SMS;
              break;
            case 2: // 微信
              dictType = DictEnum.NOTIFY_PROVIDER_WECHAT;
              break;
            case 4: // 邮箱
              dictType = DictEnum.NOTIFY_PROVIDER_EMAIL;
              break;
            case 5: // 钉钉
              dictType = DictEnum.NOTIFY_PROVIDER_DING;
              break;
            case 6: // MQTT
              dictType = DictEnum.NOTIFY_PROVIDER_MQTT;
              break;
          }
        }
        return renderDict(row.serviceType, dictType);
      }
    },
  },
  {
    title: '状态',
    field: 'status',
    align: 'center',
    width: 100,
    slots: { default: 'status' },
 
   },

  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: 180,
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  id: any;
  channelId: number;
  channelName: string;
  channelType: number;
  serviceType: number;
  param: string;
  status: string;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
}

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'channelId', label: '渠道ID' },
  { field: 'channelName', label: '渠道名称' },
 
  {
    field: 'channelTypen',
    label: '渠道类型',
    render: (_, row: any) => {
      return renderDict(row.channelType, DictEnum.NOTIFY_CHANNEL_TYPE);
    },
  },

  {
    field: 'serviceType',
    label: '服务商',
    render: (_, row: any) => {
      // 根据渠道类型动态选择服务商字典
      let dictType = DictEnum.NOTIFY_PROVIDER_SMS;
      if (row.channelType) {
        switch (row.channelType) {
          case 1: // 短信
          case 3: // 语音
            dictType = DictEnum.NOTIFY_PROVIDER_SMS;
            break;
          case 2: // 微信
            dictType = DictEnum.NOTIFY_PROVIDER_WECHAT;
            break;
          case 4: // 邮箱
            dictType = DictEnum.NOTIFY_PROVIDER_EMAIL;
            break;
          case 5: // 钉钉
            dictType = DictEnum.NOTIFY_PROVIDER_DING;
            break;
          case 6: // MQTT
            dictType = DictEnum.NOTIFY_PROVIDER_MQTT;
            break;
          default:
            dictType = DictEnum.NOTIFY_PROVIDER_SMS;
        }
      }
      return renderDict(row.serviceType, dictType);
    },
  },
  { field: 'param', label: '参数' },
  {
    field: 'status',
    label: '状态',
    render: (_, row: any) => {
      return renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
    },
  },
  { field: 'createdAt', label: '创建时间' },
  { field:'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'channelId',
    component: 'Input',
    label: '渠道ID',
    dependencies: { show: () => false, triggerFields: [''] },
    componentProps: {
        placeholder: '',
        onUpdateValue: (e: any) => {
            console.log(e);
        },
    },
},
{
    fieldName: 'channelName',
    component: 'Input',
    label: '渠道名称',
    componentProps: {
        placeholder: '请输入渠道名称',
        onUpdateValue: (e: any) => {
            console.log(e);
        },
    },
    rules:'required'
},
{
    fieldName: 'channelType',
    component: 'Select',
    label: '渠道类型',
    defaultValue: null,
    componentProps: {
        placeholder: '请选择渠道类型',
        options: getDictOptions('notify_channel_type'),
        onUpdateValue: (e: any) => {
            console.log('渠道类型变更:', e);
        },
    },
    rules:'selectRequired'
},
{
    fieldName:'serviceType',
    component: 'Select',
    label: '服务商',
    defaultValue: null,
    componentProps: ( formModel ) => {
        let dictType = DictEnum.NOTIFY_PROVIDER_SMS;

        // let dictType ;
        if (formModel) {
     
          
            if (formModel.channelType == '1' || formModel.channelType == '3') {
                dictType = DictEnum.NOTIFY_PROVIDER_SMS;
            } else if (formModel.channelType == 2) {
                dictType = DictEnum.NOTIFY_PROVIDER_WECHAT;
            } else if (formModel.channelType == '4') {
                dictType = DictEnum.NOTIFY_PROVIDER_EMAIL;
            } else if (formModel.channelType == '5') {
                dictType = DictEnum.NOTIFY_PROVIDER_DING;
            } else if (formModel.channelType == '6') {
                dictType = DictEnum.NOTIFY_PROVIDER_MQTT;
            }
        }
        return {
            placeholder: '请选择服务商',
            options: getDictOptions(dictType),
            onUpdateValue: (e: any) => {
                console.log('服务商变更:', e);
            },
      };
    },
    rules:'selectRequired'
  },
 

  //11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111
  {
    fieldName: 'sms.accessKeyId',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: 'accessKeyId',
    componentProps: {
      placeholder: '请输入配置内容',
      onUpdateValue: (e: any) => {
        console.log(e);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 1 && values.serviceType == 1 || values.channelType == 3 && values.serviceType == 1
           || values.channelType == 1 && values.serviceType == 2  || values.channelType == 3 && values.serviceType == 2) {
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] // 只需要监听服务商值的变化
    },
    rules: null
  },
  {
    fieldName: 'sms.accessKeySecret',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: 'accessKeySecret',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 1 && values.serviceType == 1 || values.channelType == 3 && values.serviceType == 1
          || values.channelType == 1 && values.serviceType == 2  || values.channelType == 3 && values.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'ding.appKey',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: 'appKey',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 5 && values.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName: 'ding.appSecret',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: 'appSecret',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if ( values.channelType == 5 && values.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName: 'wechat.appId',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: 'appId',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 2 && values.serviceType == 1 ||values.channelType == 2 && values.serviceType == 4){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechat.appSecret',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: 'appSecret',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 2 && values.serviceType == 1 ||values.channelType == 2 && values.serviceType == 4 ){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechat.corpId',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: '企业ID',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 2 && values.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  // {
  //   fieldName: 'wechat.',
  //   component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
  //   label: '应用Secret',
  //   componentProps: {
  //     placeholder: '请输入配置内容',
  //     onChange: (e: Event) => {
  //       const value = (e.target as HTMLTextAreaElement).value;
  //       console.log('配置内容变更:', value);
  //       // 这里可以添加值更新逻辑
  //     },
  //     allowClear: true, // 保留原组件的允许清空功能
  //     autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
  //   },
  //   dependencies: {
  //     show: (values, formApi) => {
  //       if (values.channelType == 2 && values.serviceType == 2){
  //         return true;
  //       }
  //       return false;
  //     },
  //     triggerFields: ['serviceType', 'channelType'] 
  //   },
  //   rules: null
  // },
  {
    fieldName: 'wechat.agentId',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: '应用agentId',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 2 && values.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'wechat.webHook',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: 'webHook',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 2 && values.serviceType == 3 ){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'email.smtpServer',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: '服务器地址',
    rules: z.string().default('smtp.qq.com').optional(),
    componentProps: {
      
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 4 && values.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
  },
  {
    fieldName: 'email.smtpServer',
    component: 'Input', // 对应 Ant Design Vue 的 <a-textarea>
    label: '服务器地址',
    rules: z.string().default('smtp.163.com').optional(),
    componentProps: {
      
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 4 && values.serviceType == 2){
          return true;
        }
        return false;
      },
      rules :(values, formApi) => {
        return z.string().default('smtp.163.com').optional();
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    
  },
  {
    fieldName: 'email.port',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: '端口号',
    rules: z.string().default('456').optional(),
    componentProps: {
      
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 4 && values.serviceType == 2 || values.channelType == 4 && values.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
  },{
    fieldName: 'email.username',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: '发件人账号',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 4 && values.serviceType == 2 || values.channelType == 4 && values.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'email.password',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: '发件秘钥',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 4 && values.serviceType == 2 || values.channelType == 4 && values.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },{
    fieldName: 'ding.agentId',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: 'agentId',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 5 && values.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName:'email.sslEnable',
    component: 'Switch',
    label: '是否启动ssl',
    componentProps: {
      checkedValue: true,
      uncheckedValue: false,
      style: "width:40px;",
      options: getDictOptions('sys_normal_disable'),
      onUpdateChecked: (e: any) => {
        console.log(e);
      },
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 4 && values.serviceType == 2 || values.channelType == 4 && values.serviceType == 1){
          return true;
        }
        return false;
      },
      componentProps: ()=>{
        return {
          checkedValue: true,
          uncheckedValue: false,
          style: "width:40px;",
          options: getDictOptions('sys_normal_disable'),
          onUpdateChecked: (e: any) => {
            console.log(e);
          },

          defaultValue: false
        }
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    defaultValue:false,
    rules:'required'
  },{
    fieldName:'email.authEnable',
    component: 'Switch',
    label: '开启验证',
    componentProps: {
      checkedValue: '0',
      uncheckedValue: '1',
      style: "width:40px;",
      options: getDictOptions('sys_normal_disable'),
      onUpdateChecked: (e: any) => {
        console.log(e);
      },
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 4 && values.serviceType == 2 || values.channelType == 4 && values.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    defaultValue:'1',
    rules:'required'
  },
  {
    fieldName:'email.retryInterval',
    component: 'Input',
    label: '重试间隔',
    rules: z.string().default('5').optional(),
    componentProps: {
      // placeholder: '5',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 4 && values.serviceType == 2 || values.channelType == 4 && values.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
  },
  {
    fieldName: 'ding.webHook',
    component: 'Textarea', // 对应 Ant Design Vue 的 <a-textarea>
    label: 'webHook',
    componentProps: {
      placeholder: '请输入配置内容',
      onChange: (e: Event) => {
        const value = (e.target as HTMLTextAreaElement).value;
        console.log('配置内容变更:', value);
        // 这里可以添加值更新逻辑
      },
      allowClear: true, // 保留原组件的允许清空功能
      autoSize: { minRows: 2, maxRows: 6 } // 可选：自动调整文本域高度
    },
    dependencies: {
      show: (values, formApi) => {
        if ( values.channelType == 5 && values.serviceType == 2){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
    rules: null
  },
  {
    fieldName:'email.maxRetries',
    component: 'Input',
    label: '重试次数',
    rules: z.string().default('1').optional(),
    componentProps: {
      // placeholder: '1',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: {
      show: (values, formApi) => {
        if (values.channelType == 4 && values.serviceType == 2 || values.channelType == 4 && values.serviceType == 1){
          return true;
        }
        return false;
      },
      triggerFields: ['serviceType', 'channelType'] 
    },
  },
//11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111
{
  fieldName: 'status',
  component: 'Switch',
  label: '状态',
  defaultValue: false,
  componentProps: {
    style: {
      width: '20px',
    },
    placeholder: '请选择状态',
    onUpdateValue: (e: any) => {
      console.log(e);
    },
  },
  rules: 'selectRequired',
},
  {
    fieldName:'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];