import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';

export class State {
  public tagId = 0; // 标签ID
  public tagName = ''; // 标签名称
  public tagKey = ''; // 标签键名
  public tagValue = ''; // 标签值
  public productKey = ''; // 产品标识
  public deviceKey = ''; // 设备标识
  public tenantId = ''; // 租户ID
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'tagId',
    component: 'InputNumber',
    label: '标签ID',
    componentProps: {
      placeholder: '请输入标签ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '标签ID',
    field: 'tagId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '标签名称',
    field: 'tagName',
    align: 'left',
    width: -1,
 },
  {
    title: '标签键名',
    field: 'tagKey',
    align: 'left',
    width: -1,
 },
  {
    title: '标签值',
    field: 'tagValue',
    align: 'left',
    width: -1,
 },
  {
    title: '产品标识',
    field: 'productKey',
    align: 'left',
    width: -1,
 },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建者',
    field: 'createdBy',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) =>  {
      return renderPopoverMemberSumma(row.createdBySumma);
    },
 },
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  tagId: number;
  tagName: string;
  tagKey: string;
  tagValue: string;
  productKey: string;
  deviceKey: string;
  tenantId: string;
  createdBy: number;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'tagId',  label: '标签ID'},
  {  field: 'tagName',  label: '标签名称'},
  {  field: 'tagKey',  label: '标签键名'},
  {  field: 'tagValue',  label: '标签值'},
  {  field: 'productKey',  label: '产品标识'},
  {  field: 'deviceKey',  label: '设备标识'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdBy',  label: '创建者'},
  {  field: 'createdAt',  label: '创建时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'tagId',
    component: 'Input',
    label: '标签ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'tagName',
    component: 'Input',
    label: '标签名称',
    componentProps: {
      placeholder: '请输入标签名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'tagKey',
    component: 'Input',
    label: '标签键名',
    componentProps: {
      placeholder: '请输入标签键名',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'tagValue',
    component: 'Input',
    label: '标签值',
    componentProps: {
      placeholder: '请输入标签值',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
  fieldName: 'productKey',
  component: 'Input',
  label: '产品标识',
  dependencies: {   show: () => false,    triggerFields: [''],   },
  componentProps: {
    placeholder: '请输入产品标识',
    onUpdateValue: (e: any) => {
      console.log(e);
    },
  }
},
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  }
];