import { h, readonly, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep, keyBy, split, values } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';
export interface EnumType {
  index: number;
  value: string;
  text: string;
}

export interface SpecsType {
  type?: string;
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
  decimals?: number;
  maxLength?: number;
  itemType?: string;
  arrayCount?: number;
  itemKeys?: string[];
  enumList?: EnumType[];
  items?: SpecsType[];
  trueText?: string;
  falseText?: string;
}

export class State {
  public modelId = 0; // 模型ID
  public modelName = ''; // 模型名称
  public modelKey = ''; // 模型标识
  public modelOrder = 0; // 模型排序
  public type = ''; // 模型类别（property-属性，function-功能，event-事件，tag-标签）
  public props = '';
  public isChart = 0; // 是否图表展示（0-否，1-是）
  public isMonitor = 0; // 是否实时监测（0-否，1-是）
  public isReadonly = 0; // 是否只读数据(0-否，1-是)
  public isHistory = 0; // 是否历史存储 (0-否，1-是）
  public isSharePerm = 0; // 是否设备分享权限(0-否，1-是)
  public dataType = ''; // 数据类型（integer、decimal、string、bool、array、enum、object）
  public specs = ''; // 数据定义
  public tenantId = ''; // 租户ID
  public deptId = 0; // 所属机构
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'modelName',
    component: 'Input',
    label: '模型名称',
    componentProps: {
      placeholder: '请输入模型名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'modelKey',
    component: 'Input',
    label: '模型标识',
    componentProps: {
      placeholder: '请输入模型标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  // {
  //   title: '',
  //   field: '',
  //   align: 'left',
  //   width: 60,
  //   type: 'checkbox',
  // },
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '模型名称',
    field: 'modelName',
    align: 'left',
    width: -1,

  },
  {
    title: '模型标识',
    field: 'modelKey',
    align: 'left',
    width: -1,
  },
  {
    title: '模型排序',
    field: 'modelOrder',
    align: 'center',
    width: 100,
  },
  // {
  //   title: '模型类别',
  //   field: 'type',
  //   align: 'left',
  //   width: -1,
  //   slots: {
  //     default: ({ row }) => {
  //       let found = renderDict(row.type, DictEnum.DEVICE_MODEL_TYPE);
  //       if (found) {
  //         return found;
  //       }
  //       return row.type;
  //     },
  //   },
  // },
  {
    title: '是否图表展示',
    field: 'isChart',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.isChart, DictEnum.IOT_YES_NO);
        if (found) {
          return found;
        }
        return row.isChart;
      },
    },
  },
  {
    title: '是否历史存储',
    field: 'isHistory',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.isHistory, DictEnum.IOT_YES_NO);
        if (found) {
          return found;
        }
        return row.isHistory;
      },
    },
  },
  {
    title: '数据类型',
    field: 'dataType',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.dataType, DictEnum.DEVICE_MODEL_DATA_TYPE);
        if (found) {
          return found;
        }
        return row.dataType;
      },
    },
  },
  {
    title: '数据定义',
    field: 'specs',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        if (!row.specs) {
          return row.specs;
        }
        if (row.dataType === 'integer' || row.dataType === 'decimal') {
          return (
            '最大值: ' +
            (row.max !== undefined ? row.max : '') +
            ', 最小值: ' +
            (row.min !== undefined ? row.min : '') +
            ', 步长: ' +
            (row.step !== undefined ? row.step : '') +
            ', 单位: ' +
            (row.unit !== undefined ? row.unit : '')
          );
        } else if (row.dataType === 'string') {
          return '最大长度: ' + (row.maxLength !== undefined ? row.maxLength : '');
        } else if (row.dataType === 'bool') {
          return '0: ' + (row.falseText !== undefined ? row.falseText : '') + ', 1: ' + (row.trueText !== undefined ? row.trueText : '');
        } else if (row.dataType === 'enum' && row.enumList) {
          return row.enumList
            .map((item: any) => {
              return item.value + ': ' + (item.text !== undefined ? item.text : '');
            })
            .join(', ');
        } else if (row.dataType === 'object' && row.items) {
          return row.items
            .map((item: any) => {
              return item.modelName + ': ' + (item.dataType !== undefined ? item.dataType : '');
            })
            .join(', ');
        } else if (row.dataType === 'array') {
          return (
            ' 数组元素类型: ' + (row.itemType !== undefined ? row.itemType : '') + ', 元素个数: ' + (row.arrayCount !== undefined ? row.arrayCount : '')
          );
        }
      },
    },
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: 200,
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  modelId: number;
  modelName: string;
  modelKey: string;
  modelOrder: number;
  type: string;
  isChart: number;
  isMonitor: number;
  isReadonly: number;
  isHistory: number;
  isSharePerm: number;
  dataType: string;
  specs: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
}

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'modelId', label: '模型ID' },
  { field: 'modelName', label: '模型名称' },
  { field: 'modelKey', label: '模型标识' },
  { field: 'modelOrder', label: '模型排序' },
  {
    field: 'type',
    label: '模型类别',
    render(_, data) {
      return renderDict(data.type, DictEnum.DEVICE_MODEL_TYPE);
    },
  },
  {
    field: 'props',
    label: '模型特性',
    render(val, data) {
      return (
        (data.isChart === 1 ? '图表展示, ' : '非图表展示, ') +
        (data.isMonitor === 1 ? '实时监测, ' : '非实时监测, ') +
        (data.isReadonly === 1 ? '只读数据, ' : '非只读数据, ') +
        (data.isHistory === 1 ? '历史存储, ' : '非历史存储, ') +
        (data.isSharePerm === 1 ? '设备分享权限' : '非设备分享权限')
      );
    },
  },
  {
    field: 'dataType',
    label: '数据类型',
    render(_, data) {
      return renderDict(data.dataType, DictEnum.DEVICE_MODEL_DATA_TYPE);
    },
  },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'modelId',
    component: 'Input',
    label: '模型ID',
    dependencies: { show: () => false, triggerFields: [''] },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'modelName',
    component: 'Input',
    label: '模型名称',
    componentProps: {
      placeholder: '请输入模型名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'type',
    component: 'Input',
    label: '模型类别',
    dependencies: {
      show: () => false,
      triggerFields: ['']
    },
  },
  {
    fieldName: 'modelKey',
    component: 'Input',
    label: '模型标识',
    componentProps: {
      placeholder: '请输入模型标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'modelOrder',
    component: 'InputNumber',
    label: '模型排序',
    componentProps: {
      placeholder: '请输入模型排序',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({
      required_error: '请输入模型排序',
      invalid_type_error: '无效数字',
    }),
    formItemClass: 'col-span-2',
  },
  {
    component: 'CheckboxGroup',
    fieldName: 'props',
    formItemClass: 'col-span-4',
    dependencies: {
      componentProps(values, formApi) {
        if (values.type === 'function') {
          return {
            options: [
              {
                label: '只读数据',
                value: 'isReadonly',
              },
              {
                label: '历史存储',
                value: 'isHistory',
              },
              {
                label: '分享权限',
                value: 'isSharePerm',
              },
            ],
          };
        } else if (values.type === 'event') {
          let props = values.props || [];
          props.push('isReadonly');
          formApi.setFieldValue('props', props);
          return {
            options: [
              {
                label: '只读数据',
                value: 'isReadonly',
                disabled: true,
                //默认选中
              },
              {
                label: '历史存储',
                value: 'isHistory',
              },
              {
                label: '分享权限',
                value: 'isSharePerm',
              },
            ],
          };
        }
        return {
          options: [
            {
              label: '图表展示',
              value: 'isChart',
            },
            {
              label: '实时监测',
              value: 'isMonitor',
            },
            {
              label: '只读数据',
              value: 'isReadonly',
            },
            {
              label: '历史存储',
              value: 'isHistory',
            },
            {
              label: '分享权限',
              value: 'isSharePerm',
            },
          ],
        };
      },

      triggerFields: ['type', 'props'],
    },
    label: '模型特性',
  },
  {
    fieldName: 'dataType',
    component: 'Select',
    label: '数据类型',
    componentProps: {
      placeholder: '请选择',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: getDictOptions(DictEnum.DEVICE_MODEL_DATA_TYPE),
    },
    rules: 'required',
  },
  {
    fieldName: 'min',
    component: 'InputNumber',
    componentProps: {
      placeholder: '最小值',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'decimal' || values.dataType === 'integer';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '最小值',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
    label: '取值范围',
    suffix: '到',
  },
  {
    fieldName: 'max',
    component: 'InputNumber',
    componentProps: {
      placeholder: '最大值',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2  gap-y-6',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'decimal' || values.dataType === 'integer';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '最大值',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
  },

  {
    fieldName: 'unit',
    component: 'Input',
    label: '单位',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'decimal' || values.dataType === 'integer';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '单位',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'step',
    component: 'InputNumber',
    label: '步长',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'decimal' || values.dataType === 'integer';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '步长',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'decimals',
    component: 'InputNumber',
    label: '小数位数',
    defaultValue: 0,
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'decimal';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '小数位数',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
  },
  {
    fieldName: 'maxLength',
    component: 'InputNumber',
    label: '最大长度',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'string';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '最大长度',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
  },
  {
    fieldName: 'trueText',
    component: 'Input',
    label: '布尔值',
    componentProps: {
      placeholder: '1 值对应文本',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'bool';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '1 值对应文本',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
  },
  {
    fieldName: 'falseText',
    component: 'Input',
    componentProps: {
      placeholder: '0 值对应文本',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2 gap-y-6',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'bool';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '0 值对应文本',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
  },
  {
    fieldName: 'enumList',
    component: 'Input',
    label: '枚举项',
    defaultValue: [],
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'enum';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {};
      },
      triggerFields: ['dataType'], // Required property added
    },
  },
  {
    fieldName: 'arrayCount',
    component: 'InputNumber',
    label: '元素个数',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'array';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '例如：500',
        };
      },
      required(values: Partial<Record<string, any>>) {
        return values.dataType === 'array';
      },
      triggerFields: ['dataType'], // Required property added
    },
  },
  {
    fieldName: 'itemKeys',
    component: 'Input',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'array';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        if (values.arrayCount > 0 ) {
          if (!values.itemKeys || values.itemKeys.length != values.arrayCount) {
            let param = Array.from({ length: values.arrayCount }, (_, i) => String(i));
            formApi.setFieldValue('itemKeys', param);
          }
        }
        return {};
      },
      required(values: Partial<Record<string, any>>) {
        return values.dataType === 'array';
      },
      triggerFields: ['dataType', 'arrayCount', 'itemKeys'], // Required property added
    },
    label: '元素标识',
  },
  {
    fieldName: 'itemType',
    component: 'Select',
    label: '元素类型',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'array';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          onUpdateValue: (e: any) => {
            console.log(e);
          },
          options: [
            {
              label: '字符串',
              value: 'string',
            },
            {
              label: '整型',
              value: 'integer',
            },
            {
              label: '小数',
              value: 'decimal',
            },
            {
              label: '对象',
              value: 'object',
            },
          ],
        };
      },
      required(values: Partial<Record<string, any>>) {
        return values.dataType === 'array';
      },
      triggerFields: ['dataType'], // Required property added
    },
  },

  {
    fieldName: 'items',
    component: 'Input',
    label: '对象参数',
    defaultValue: [],
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'object' || values.itemType === 'object';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {};
      },
      triggerFields: ['dataType', 'itemType'], // Required property added
    },
  },
  {
    fieldName: 'fromType',
    component: 'Select',
    label: '属性来源类型',
    defaultValue: 'device',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.type === 'property';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          options: getDictOptions(DictEnum.FROM_TYPE),
          rules: 'required',
        };
      },
      triggerFields: ['type'], // Required property added
    },
  },
  {
    fieldName: 'formulaList',
    component: 'Input',
    label: '公式',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.type === 'property' && values.fromType === 'cloud_calculate';
      },
      triggerFields: ['type', 'fromType'], // Required property added
    },
  },
  {
    fieldName: 'defalutValue',
    component: 'Input',
    label: '默认值',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.type === 'property' && values.fromType === 'cloud_input';
      },
      triggerFields: ['type', 'fromType'], // Required property added
    },
  },
];

// 表格搜索表单
export const modelQuerySchema: VbenFormSchema[] = [
  {
    fieldName: 'modelName',
    component: 'Input',
    label: '模型名称',
    hideLabel: true,
    componentProps: {
      placeholder: '请输入模型名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const modelColumns: VxeGridProps['columns'] = [
  {
    title: '模型名称',
    field: 'modelName',
    align: 'left',
    width: -1,
    type: 'radio',
  },
  {
    title: '模型标识',
    field: 'modelKey',
    align: 'left',
    width: -1,
  },
  {
    title: '数据类型',
    field: 'dataType',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.dataType, DictEnum.DEVICE_MODEL_DATA_TYPE);
        if (found) {
          return found;
        }
        return row.dataType;
      },
    },
  },
];

// 编辑字段列表
export const objectEditSchema: VbenFormSchema[] = [
  {
    fieldName: 'modelId',
    component: 'Input',
    label: '模型ID',
    dependencies: { show: () => false, triggerFields: [''] },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'modelName',
    component: 'Input',
    label: '模型名称',
    componentProps: {
      placeholder: '请输入模型名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'modelKey',
    component: 'Input',
    label: '模型标识',
    componentProps: {
      placeholder: '请输入模型标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'modelOrder',
    component: 'InputNumber',
    label: '模型排序',
    componentProps: {
      placeholder: '请输入模型排序',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({
      required_error: '请输入模型排序',
      invalid_type_error: '无效数字',
    }),
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'type',
    component: 'Select',
    label: '模型类别',
    componentProps: {
      placeholder: '请选择',
      disabled: true,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: getDictOptions(DictEnum.DEVICE_MODEL_TYPE),
    },
    //dependencies: { show: () => false, triggerFields: [''], },
    // defaultValue: 'property',
    rules: 'required',
    formItemClass: 'col-span-2',
  },
  {
    component: 'CheckboxGroup',
    fieldName: 'props',
    formItemClass: 'col-span-4',
    componentProps: {
      options: [
        {
          label: '图表展示',
          value: 'isChart',
        },
        {
          label: '实时监测',
          value: 'isMonitor',
        },
        {
          label: '只读数据',
          value: 'isReadonly',
        },
        {
          label: '历史存储',
          value: 'isHistory',
        },
        {
          label: '分享权限',
          value: 'isSharePerm',
        },
      ],
    },
    label: '模型特性',
  },
  {
    fieldName: 'dataType',
    component: 'Select',
    label: '数据类型',
    rules: 'required',
    dependencies: {
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        let options = getDictOptions(DictEnum.DEVICE_MODEL_DATA_TYPE);
        if (values.type === 'property') {
          options = options.filter((item) => {
            if (item.value === 'array' || item.value === 'object') {
              return false;
            }
            return true;
          });
        }
        return {
          options: options,
        };
      },
      triggerFields: ['type'],
    },
  },
  {
    fieldName: 'min',
    component: 'InputNumber',
    componentProps: {
      placeholder: '最小值',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'decimal' || values.dataType === 'integer';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '最小值',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
    label: '取值范围',
    suffix: '到',
  },
  {
    fieldName: 'max',
    component: 'InputNumber',
    componentProps: {
      placeholder: '最大值',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2  gap-y-6',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'decimal' || values.dataType === 'integer';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '最大值',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
  },

  {
    fieldName: 'unit',
    component: 'Input',
    label: '单位',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'decimal' || values.dataType === 'integer';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '单位',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'step',
    component: 'InputNumber',
    label: '步长',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'decimal' || values.dataType === 'integer';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '步长',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'decimals',
    component: 'InputNumber',
    label: '小数位数',
    defaultValue: 0,
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'decimal';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '小数位数',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
  },
  {
    fieldName: 'maxLength',
    component: 'InputNumber',
    label: '最大长度',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'string';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '最大长度',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
  },
  {
    fieldName: 'trueText',
    component: 'Input',
    label: '布尔值',
    componentProps: {
      placeholder: '1 值对应文本',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'bool';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '1 值对应文本',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
  },
  {
    fieldName: 'falseText',
    component: 'Input',
    componentProps: {
      placeholder: '0 值对应文本',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2 gap-y-6',
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'bool';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {
          placeholder: '0 值对应文本',
        };
      },
      triggerFields: ['dataType'], // Required property added
    },
  },
  {
    fieldName: 'enumList',
    component: 'Input',
    label: '枚举项',
    defaultValue: [{ text: '', value: '' }],
    dependencies: {
      show: (values: Partial<Record<string, any>>) => {
        return values.dataType === 'enum';
      },
      componentProps: (values: Partial<Record<string, any>>, formApi: any) => {
        return {};
      },
      triggerFields: ['dataType'], // Required property added
    },
  },
];
