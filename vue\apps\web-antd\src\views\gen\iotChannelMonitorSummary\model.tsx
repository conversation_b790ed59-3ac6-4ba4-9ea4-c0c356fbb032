import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public imssId = 0; // 汇总ID
  public channelType = null; // 通道类型
  public channelId = 0; // 资源ID
  public shardId = 0; // 分片ID
  public historyConnected = 0; // 历史链接总数
  public historyTopic = 0; // 历史主题数
  public historySubscribe = 0; // 历史订阅数
  public historySharedSubscribe = 0; // 历史共享订阅数
  public historyRecvSize = 0; // 历史接收数据大小字节
  public historyRecvCount = 0; // 历史接收数据数量
  public historySendSize = 0; // 历史发送数据大小字节
  public historySendCount = 0; // 历史发送数据数量
  public historyAuthTotal = ''; // 历史认证数量
  public historyAuthSucceed = ''; // 历史认证成功数量
  public historyAuthFailed = ''; // 历史认证失败数量
  public currentClients = 0; // 当前在线数
  public currentConnected = 0; // 当前连接数
  public currentDisconnected = 0; // 当前断开连接数
  public currentTopic = 0; // 当前主题数
  public currentSubscribe = 0; // 当前订阅数
  public currentSharedSubscribe = 0; // 当前共享订阅数
  public currentRecvSize = 0; // 当前接收数据大小字节
  public currentRecvCount = 0; // 当前接收数据数量
  public currentRecvTime = ''; // 当前最后接收时间
  public currentSendSize = 0; // 当前发送数据大小字节
  public currentSendCount = 0; // 当前发送数据数量
  public currentSendTime = ''; // 当前最后发送时间
  public currentAuthTotal = ''; // 当前认证数量
  public currentAuthSucceed = ''; // 当前认证成功数量
  public currentAuthFailed = ''; // 当前认证失败数量
  public nodeName = ''; // 当前节点名称
  public startTime = ''; // 当前节点启动时间
  public processMemSize = 0; // 当前节点进程内存大小字节
  public processThreadSize = 0; // 当前节点线程或协程数量
  public sysCpuNum = 0; // 系统CPU数量
  public sysCpuCores = 0; // 系统CPU核心数量
  public sysCpuUse = 0; // 系统CPU使用百分比
  public sysCpuLoad = ''; // 系统CPU负载
  public sysMemTotal = 0; // 系统内存总数
  public sysMemUse = 0; // 系统内存已使用
  public sysDiskTotal = 0; // 系统硬盘总数
  public sysDiskUse = 0; // 系统硬盘已使用
  public sysIpLan = ''; // 服务器IP内网
  public sysIpWan = ''; // 服务器IP外网
  public appVersion = ''; // 程序版本
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 更新时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {    
			fieldName: 'channelType',    
			component: 'Select',    
			label: '通道类型',    
			defaultValue: null,    
			componentProps: {    
				placeholder: '请选择通道类型',    
				options: getDictOptions('channel_type'),    
				onUpdateValue: (e: any) => {    
					console.log(e);    
				},  
			},
			rules:'selectRequired',
			formItemClass: 'col-span-1',
		},
		  {
				fieldName: 'channelId',
				component: 'InputNumber',
				label: '资源ID',
				componentProps: {
					placeholder: '请输入资源ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入资源ID', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '汇总ID',
    field: 'imssId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
 {    
				title: '通道类型',    field: 'channelType',    align: 'left',    width: -1, 
				slots: {
      				default: ({ row }) => {
						return renderDict(row.channelType, 'channel_type');
					}
				},
			},
			  {
    title: '资源ID',
    field: 'channelId',
    align: 'left',
    width: -1,
 },
  {
    title: '分片ID',
    field: 'shardId',
    align: 'left',
    width: -1,
 },
  {
    title: '历史链接总数',
    field: 'historyConnected',
    align: 'left',
    width: -1,
 },
  {
    title: '历史主题数',
    field: 'historyTopic',
    align: 'left',
    width: -1,
 },
  {
    title: '历史订阅数',
    field: 'historySubscribe',
    align: 'left',
    width: -1,
 },
  {
    title: '历史共享订阅数',
    field: 'historySharedSubscribe',
    align: 'left',
    width: -1,
 },
  {
    title: '历史接收数据大小字节',
    field: 'historyRecvSize',
    align: 'left',
    width: -1,
 },
  {
    title: '历史接收数据数量',
    field: 'historyRecvCount',
    align: 'left',
    width: -1,
 },
  {
    title: '历史发送数据大小字节',
    field: 'historySendSize',
    align: 'left',
    width: -1,
 },
  {
    title: '历史发送数据数量',
    field: 'historySendCount',
    align: 'left',
    width: -1,
 },
  {
    title: '历史认证数量',
    field: 'historyAuthTotal',
    align: 'left',
    width: -1,
 },
  {
    title: '历史认证成功数量',
    field: 'historyAuthSucceed',
    align: 'left',
    width: -1,
 },
  {
    title: '历史认证失败数量',
    field: 'historyAuthFailed',
    align: 'left',
    width: -1,
 },
  {
    title: '当前在线数',
    field: 'currentClients',
    align: 'left',
    width: -1,
 },
  {
    title: '当前连接数',
    field: 'currentConnected',
    align: 'left',
    width: -1,
 },
  {
    title: '当前断开连接数',
    field: 'currentDisconnected',
    align: 'left',
    width: -1,
 },
  {
    title: '当前主题数',
    field: 'currentTopic',
    align: 'left',
    width: -1,
 },
  {
    title: '当前订阅数',
    field: 'currentSubscribe',
    align: 'left',
    width: -1,
 },
  {
    title: '当前共享订阅数',
    field: 'currentSharedSubscribe',
    align: 'left',
    width: -1,
 },
  {
    title: '当前接收数据大小字节',
    field: 'currentRecvSize',
    align: 'left',
    width: -1,
 },
  {
    title: '当前接收数据数量',
    field: 'currentRecvCount',
    align: 'left',
    width: -1,
 },
  {
    title: '当前最后接收时间',
    field: 'currentRecvTime',
    align: 'left',
    width: -1,
 },
  {
    title: '当前发送数据大小字节',
    field: 'currentSendSize',
    align: 'left',
    width: -1,
 },
  {
    title: '当前发送数据数量',
    field: 'currentSendCount',
    align: 'left',
    width: -1,
 },
  {
    title: '当前最后发送时间',
    field: 'currentSendTime',
    align: 'left',
    width: -1,
 },
  {
    title: '当前认证数量',
    field: 'currentAuthTotal',
    align: 'left',
    width: -1,
 },
  {
    title: '当前认证成功数量',
    field: 'currentAuthSucceed',
    align: 'left',
    width: -1,
 },
  {
    title: '当前认证失败数量',
    field: 'currentAuthFailed',
    align: 'left',
    width: -1,
 },
  {
    title: '当前节点名称',
    field: 'nodeName',
    align: 'left',
    width: -1,
 },
  {
    title: '当前节点启动时间',
    field: 'startTime',
    align: 'left',
    width: -1,
 },
  {
    title: '当前节点进程内存大小字节',
    field: 'processMemSize',
    align: 'left',
    width: -1,
 },
  {
    title: '当前节点线程或协程数量',
    field: 'processThreadSize',
    align: 'left',
    width: -1,
 },
  {
    title: '系统CPU数量',
    field: 'sysCpuNum',
    align: 'left',
    width: -1,
 },
  {
    title: '系统CPU核心数量',
    field: 'sysCpuCores',
    align: 'left',
    width: -1,
 },
  {
    title: '系统CPU使用百分比',
    field: 'sysCpuUse',
    align: 'left',
    width: -1,
 },
  {
    title: '系统CPU负载',
    field: 'sysCpuLoad',
    align: 'left',
    width: -1,
 },
  {
    title: '系统内存总数',
    field: 'sysMemTotal',
    align: 'left',
    width: -1,
 },
  {
    title: '系统内存已使用',
    field: 'sysMemUse',
    align: 'left',
    width: -1,
 },
  {
    title: '系统硬盘总数',
    field: 'sysDiskTotal',
    align: 'left',
    width: -1,
 },
  {
    title: '系统硬盘已使用',
    field: 'sysDiskUse',
    align: 'left',
    width: -1,
 },
  {
    title: '服务器IP内网',
    field: 'sysIpLan',
    align: 'left',
    width: -1,
 },
  {
    title: '服务器IP外网',
    field: 'sysIpWan',
    align: 'left',
    width: -1,
 },
  {
    title: '程序版本',
    field: 'appVersion',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  {
    title: '更新时间',
    field: 'updatedAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  imssId: number;
  channelType: number;
  channelId: number;
  shardId: number;
  historyConnected: number;
  historyTopic: number;
  historySubscribe: number;
  historySharedSubscribe: number;
  historyRecvSize: number;
  historyRecvCount: number;
  historySendSize: number;
  historySendCount: number;
  historyAuthTotal: string;
  historyAuthSucceed: string;
  historyAuthFailed: string;
  currentClients: number;
  currentConnected: number;
  currentDisconnected: number;
  currentTopic: number;
  currentSubscribe: number;
  currentSharedSubscribe: number;
  currentRecvSize: number;
  currentRecvCount: number;
  currentRecvTime: string;
  currentSendSize: number;
  currentSendCount: number;
  currentSendTime: string;
  currentAuthTotal: string;
  currentAuthSucceed: string;
  currentAuthFailed: string;
  nodeName: string;
  startTime: string;
  processMemSize: number;
  processThreadSize: number;
  sysCpuNum: number;
  sysCpuCores: number;
  sysCpuUse: number;
  sysCpuLoad: string;
  sysMemTotal: number;
  sysMemUse: number;
  sysDiskTotal: number;
  sysDiskUse: number;
  sysIpLan: string;
  sysIpWan: string;
  appVersion: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'imssId',  label: '汇总ID'},
  {
				field: 'channelType',
				label: '通道类型',
				render(row: any) {
					return renderDict(row.channelType, 'channel_type');
				},
			},
			  {  field: 'channelId',  label: '资源ID'},
  {  field: 'shardId',  label: '分片ID'},
  {  field: 'historyConnected',  label: '历史链接总数'},
  {  field: 'historyTopic',  label: '历史主题数'},
  {  field: 'historySubscribe',  label: '历史订阅数'},
  {  field: 'historySharedSubscribe',  label: '历史共享订阅数'},
  {  field: 'historyRecvSize',  label: '历史接收数据大小字节'},
  {  field: 'historyRecvCount',  label: '历史接收数据数量'},
  {  field: 'historySendSize',  label: '历史发送数据大小字节'},
  {  field: 'historySendCount',  label: '历史发送数据数量'},
  {  field: 'historyAuthTotal',  label: '历史认证数量'},
  {  field: 'historyAuthSucceed',  label: '历史认证成功数量'},
  {  field: 'historyAuthFailed',  label: '历史认证失败数量'},
  {  field: 'currentClients',  label: '当前在线数'},
  {  field: 'currentConnected',  label: '当前连接数'},
  {  field: 'currentDisconnected',  label: '当前断开连接数'},
  {  field: 'currentTopic',  label: '当前主题数'},
  {  field: 'currentSubscribe',  label: '当前订阅数'},
  {  field: 'currentSharedSubscribe',  label: '当前共享订阅数'},
  {  field: 'currentRecvSize',  label: '当前接收数据大小字节'},
  {  field: 'currentRecvCount',  label: '当前接收数据数量'},
  {  field: 'currentRecvTime',  label: '当前最后接收时间'},
  {  field: 'currentSendSize',  label: '当前发送数据大小字节'},
  {  field: 'currentSendCount',  label: '当前发送数据数量'},
  {  field: 'currentSendTime',  label: '当前最后发送时间'},
  {  field: 'currentAuthTotal',  label: '当前认证数量'},
  {  field: 'currentAuthSucceed',  label: '当前认证成功数量'},
  {  field: 'currentAuthFailed',  label: '当前认证失败数量'},
  {  field: 'nodeName',  label: '当前节点名称'},
  {  field: 'startTime',  label: '当前节点启动时间'},
  {  field: 'processMemSize',  label: '当前节点进程内存大小字节'},
  {  field: 'processThreadSize',  label: '当前节点线程或协程数量'},
  {  field: 'sysCpuNum',  label: '系统CPU数量'},
  {  field: 'sysCpuCores',  label: '系统CPU核心数量'},
  {  field: 'sysCpuUse',  label: '系统CPU使用百分比'},
  {  field: 'sysCpuLoad',  label: '系统CPU负载'},
  {  field: 'sysMemTotal',  label: '系统内存总数'},
  {  field: 'sysMemUse',  label: '系统内存已使用'},
  {  field: 'sysDiskTotal',  label: '系统硬盘总数'},
  {  field: 'sysDiskUse',  label: '系统硬盘已使用'},
  {  field: 'sysIpLan',  label: '服务器IP内网'},
  {  field: 'sysIpWan',  label: '服务器IP外网'},
  {  field: 'appVersion',  label: '程序版本'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdAt',  label: '创建时间'},
  {  field: 'updatedAt',  label: '更新时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
 {
					fieldName: 'imssId',
					component: 'Input',
					label: '汇总ID',
					dependencies: {   show: () => false,    triggerFields: [''],   },
					componentProps: {
						placeholder: '',
						onUpdateValue: (e: any) => {
							console.log(e);
						},   
					},
					formItemClass: 'col-span-1',
				},  {    
			fieldName: 'channelType',    
			component: 'Select',    
			label: '通道类型',    
			defaultValue: null,    
			componentProps: {    
				placeholder: '请选择通道类型',    
				options: getDictOptions('channel_type'),    
				onUpdateValue: (e: any) => {    
					console.log(e);    
				},  
			},
			rules:'selectRequired',
			formItemClass: 'col-span-1',
		},
		  {
				fieldName: 'channelId',
				component: 'InputNumber',
				label: '资源ID',
				componentProps: {
					placeholder: '请输入资源ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入资源ID', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'shardId',
				component: 'InputNumber',
				label: '分片ID',
				componentProps: {
					placeholder: '请输入分片ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入分片ID', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'historyConnected',
				component: 'InputNumber',
				label: '历史链接总数',
				componentProps: {
					placeholder: '请输入历史链接总数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入历史链接总数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'historyTopic',
				component: 'InputNumber',
				label: '历史主题数',
				componentProps: {
					placeholder: '请输入历史主题数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入历史主题数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'historySubscribe',
				component: 'InputNumber',
				label: '历史订阅数',
				componentProps: {
					placeholder: '请输入历史订阅数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入历史订阅数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'historySharedSubscribe',
				component: 'InputNumber',
				label: '历史共享订阅数',
				componentProps: {
					placeholder: '请输入历史共享订阅数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入历史共享订阅数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'historyRecvSize',
				component: 'InputNumber',
				label: '历史接收数据大小字节',
				componentProps: {
					placeholder: '请输入历史接收数据大小字节',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入历史接收数据大小字节', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'historyRecvCount',
				component: 'InputNumber',
				label: '历史接收数据数量',
				componentProps: {
					placeholder: '请输入历史接收数据数量',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入历史接收数据数量', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'historySendSize',
				component: 'InputNumber',
				label: '历史发送数据大小字节',
				componentProps: {
					placeholder: '请输入历史发送数据大小字节',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入历史发送数据大小字节', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'historySendCount',
				component: 'InputNumber',
				label: '历史发送数据数量',
				componentProps: {
					placeholder: '请输入历史发送数据数量',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入历史发送数据数量', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'historyAuthTotal',
				component: 'DatePicker',
				label: '历史认证数量',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'historyAuthSucceed',
				component: 'DatePicker',
				label: '历史认证成功数量',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'historyAuthFailed',
				component: 'DatePicker',
				label: '历史认证失败数量',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentClients',
				component: 'InputNumber',
				label: '当前在线数',
				componentProps: {
					placeholder: '请输入当前在线数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前在线数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentConnected',
				component: 'InputNumber',
				label: '当前连接数',
				componentProps: {
					placeholder: '请输入当前连接数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前连接数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentDisconnected',
				component: 'InputNumber',
				label: '当前断开连接数',
				componentProps: {
					placeholder: '请输入当前断开连接数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前断开连接数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentTopic',
				component: 'InputNumber',
				label: '当前主题数',
				componentProps: {
					placeholder: '请输入当前主题数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前主题数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentSubscribe',
				component: 'InputNumber',
				label: '当前订阅数',
				componentProps: {
					placeholder: '请输入当前订阅数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前订阅数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentSharedSubscribe',
				component: 'InputNumber',
				label: '当前共享订阅数',
				componentProps: {
					placeholder: '请输入当前共享订阅数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前共享订阅数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentRecvSize',
				component: 'InputNumber',
				label: '当前接收数据大小字节',
				componentProps: {
					placeholder: '请输入当前接收数据大小字节',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前接收数据大小字节', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentRecvCount',
				component: 'InputNumber',
				label: '当前接收数据数量',
				componentProps: {
					placeholder: '请输入当前接收数据数量',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前接收数据数量', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentRecvTime',
				component: 'DatePicker',
				label: '当前最后接收时间',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentSendSize',
				component: 'InputNumber',
				label: '当前发送数据大小字节',
				componentProps: {
					placeholder: '请输入当前发送数据大小字节',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前发送数据大小字节', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentSendCount',
				component: 'InputNumber',
				label: '当前发送数据数量',
				componentProps: {
					placeholder: '请输入当前发送数据数量',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前发送数据数量', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentSendTime',
				component: 'DatePicker',
				label: '当前最后发送时间',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentAuthTotal',
				component: 'DatePicker',
				label: '当前认证数量',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentAuthSucceed',
				component: 'DatePicker',
				label: '当前认证成功数量',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'currentAuthFailed',
				component: 'DatePicker',
				label: '当前认证失败数量',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'nodeName',
				component: 'Input',
				label: '当前节点名称',
				componentProps: {
					placeholder: '请输入当前节点名称',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'startTime',
				component: 'DatePicker',
				label: '当前节点启动时间',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'processMemSize',
				component: 'InputNumber',
				label: '当前节点进程内存大小字节',
				componentProps: {
					placeholder: '请输入当前节点进程内存大小字节',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前节点进程内存大小字节', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'processThreadSize',
				component: 'InputNumber',
				label: '当前节点线程或协程数量',
				componentProps: {
					placeholder: '请输入当前节点线程或协程数量',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入当前节点线程或协程数量', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'sysCpuNum',
				component: 'InputNumber',
				label: '系统CPU数量',
				componentProps: {
					placeholder: '请输入系统CPU数量',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入系统CPU数量', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'sysCpuCores',
				component: 'InputNumber',
				label: '系统CPU核心数量',
				componentProps: {
					placeholder: '请输入系统CPU核心数量',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入系统CPU核心数量', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'sysCpuUse',
				component: 'InputNumber',
				label: '系统CPU使用百分比',
				componentProps: {
					placeholder: '请输入系统CPU使用百分比',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入系统CPU使用百分比', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'sysCpuLoad',
				component: 'Input',
				label: '系统CPU负载',
				componentProps: {
					placeholder: '请输入系统CPU负载',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'sysMemTotal',
				component: 'InputNumber',
				label: '系统内存总数',
				componentProps: {
					placeholder: '请输入系统内存总数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入系统内存总数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'sysMemUse',
				component: 'InputNumber',
				label: '系统内存已使用',
				componentProps: {
					placeholder: '请输入系统内存已使用',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入系统内存已使用', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'sysDiskTotal',
				component: 'InputNumber',
				label: '系统硬盘总数',
				componentProps: {
					placeholder: '请输入系统硬盘总数',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入系统硬盘总数', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'sysDiskUse',
				component: 'InputNumber',
				label: '系统硬盘已使用',
				componentProps: {
					placeholder: '请输入系统硬盘已使用',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入系统硬盘已使用', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'sysIpLan',
				component: 'Input',
				label: '服务器IP内网',
				componentProps: {
					placeholder: '请输入服务器IP内网',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'sysIpWan',
				component: 'Input',
				label: '服务器IP外网',
				componentProps: {
					placeholder: '请输入服务器IP外网',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'appVersion',
				component: 'Input',
				label: '程序版本',
				componentProps: {
					placeholder: '请输入程序版本',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'tenantId',
				component: 'Input',
				label: '租户ID',
				componentProps: {
					placeholder: '请输入租户ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},];