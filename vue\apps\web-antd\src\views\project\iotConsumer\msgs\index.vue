<script lang="ts" setup>
import { ref } from 'vue';
import { Button } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { List } from '#/api/project/iotConsumerMsgs';
import { columns, querySchema, type RowType } from './model';
import viewDrawer from './view.vue';

// 定义接收的属性类型
const props = defineProps({
  consumer: {
    type: Object,
    required: true,
    default: () => ({}),
  },
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  rowConfig: {
    keyField: 'id',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          consumerId: props.consumer.consumerId,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  gridClass: 'p-0 mt-0',
  formOptions: formOptions,
  gridOptions: gridOptions,
  gridEvents: gridEvents,
  hideSeparator: true,
});

const [ViewDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: viewDrawer,
});
function handlePreview(record: RowType) {
  drawerApi.setData(record);
  drawerApi.open();
}

async function handleRefresh() {
  await gridApi.query();
}
</script>
<template class="p-0 m-0">
  <div class="p-0 m-0 h-[800px]" >
    <Grid>
      <template #action="{ row }">
        <div class="flex items-center justify-center">
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handlePreview(row)"
            v-access:code="'cpm:project:iotConsumer:view'">
            详情
          </Button>
        </div>
      </template>
    </Grid>
    <ViewDrawer />
    <EditDrawer @reload="handleRefresh" />
  </div>
</template>