<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import type { VbenFormProps } from '#/adapter/form';
import { columns, formSchema, type RowType } from './model';
import type { DeepPartial } from '@vben/types';
import { List } from '#/api/device/iotDevice';
import { List as CategoryList } from '#/api/device/iotProductCategory';
import { Tag } from 'ant-design-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { ref } from 'vue';

const emit = defineEmits<{
  // 设备选择事件
  deviceSelected: [deviceKey: string, deviceName: string];
}>();

// 当前产品Key
const currentProductKey = ref<string>('');

// 设置产品Key的方法
function setProductKey(productKey: string) {
  currentProductKey.value = productKey;
  console.log('设置产品Key:', productKey);
}

// 表单配置
const formOptions: VbenFormProps = {
  schema: formSchema,
  showDefaultActions: false,
  layout: 'horizontal',
  actionWrapperClass: 'grid-cols-1',
  wrapperClass: 'grid-cols-4',
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
};

// 表格配置
const gridOptions: VxeTableGridOptions<RowType> = {
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        console.log('查询设备列表，产品Key:', currentProductKey.value);
        console.log('查询参数:', formValues);
        
        let result = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
          productKey: currentProductKey.value, // 根据选择的产品过滤设备
        });

        // 获取产品分类信息
        let categoryArray = await CategoryList({});
        let categoryMap = categoryArray.items;
        result.items.forEach((item: any) => {
          item.categoryName = categoryMap.find(
            (i: any) => item.categoryId === i.categoryId,
          )?.categoryName;
        });
        
        console.log('设备列表查询结果:', result);
        return result;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};

// 表格事件
const gridEvents: DeepPartial<VxeGridListeners> = {
  radioChange: handleRadioChange,
};

const RadioChecked = ref(false);
function handleRadioChange() {
  RadioChecked.value = !!gridApi.grid.getRadioRecord();
}

const [Grid, gridApi] = useVbenVxeGrid({
 // formOptions,
  gridOptions,
  gridEvents,
});

// 确认按钮回调函数
const onConfirm = () => {
  const selectedRecord = gridApi.grid.getRadioRecord();

  if (!selectedRecord) {
    console.warn('请选择一个设备');
    return;
  }

  const deviceKey = selectedRecord.deviceKey;
  const deviceName = selectedRecord.deviceName;

  console.log('选中的设备:', { deviceKey, deviceName });
  emit('deviceSelected', deviceKey, deviceName);
  modalApi.close();
};

// 打开弹窗的方法
function openModal() {
  modalApi.open();
}

// 暴露方法给父组件
defineExpose({
  openModal,
  setProductKey,
});

// 模态框配置
const [Modal, modalApi] = useVbenModal({
  onConfirm: onConfirm,
  onCancel: () => {
    modalApi.close();
  },
  onOpenChange: (isOpen) => {
    if (isOpen) {
      console.log('🔍 设备选择弹窗打开，当前产品Key:', currentProductKey.value);
      // 弹窗打开时，表格会自动加载数据，不需要手动调用查询
      return null;
    }
    modalApi.close();
  },
});
</script>

<template>
  <div>
    <Modal class="w-[1200px] h-[600px]" title="选择设备">
      <div class="h-full overflow-hidden">
        <Grid table-title="设备列表" class="h-full">
          <template #status="{ row }">
            <Tag :color="row.status === '0' ? 'green' : 'red'">
              {{ row.status === '0' ? '在线' : '离线' }}
            </Tag>
          </template>
          <template #action="{ row }">
            <div class="flex items-center justify-center">
              <span class="text-gray-400">-</span>
            </div>
          </template>
        </Grid>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
/* 隐藏VxeGrid表格的滚动条 */
:deep(.vxe-table--body-wrapper) {
  overflow: hidden !important;
}

/* 表格容器样式 */
:deep(.vxe-grid) {
  border: none;
  height: 100%;
}

/* 表格主体样式 */
:deep(.vxe-table) {
  height: 100%;
}
</style>
