export enum DictEnum {
  SYS_COMMON_STATUS = 'sys_common_status',
  SYS_DEVICE_TYPE = 'sys_device_type', // 设备类型
  SYS_GRANT_TYPE = 'sys_grant_type', // 授权类型
  SYS_NORMAL_DISABLE = 'sys_normal_disable',
  SYS_NOTICE_STATUS = 'sys_notice_status', // 通知状态
  SYS_NOTICE_TYPE = 'sys_notice_type', // 通知类型
  SYS_OPER_TYPE = 'sys_oper_type', // 操作类型
  SYS_OSS_ACCESS_POLICY = 'oss_access_policy', // oss权限桶类型
  SYS_SHOW_HIDE = 'sys_show_hide', // 显示状态
  SYS_USER_SEX = 'sys_user_sex', // 性别
  SYS_YES_NO = 'sys_yes_no', // 是否
  SYS_YES_NO_NUM = 'sys_yes_no_num', // 是否数字
  SYS_GEN_STATUS = 'sys_gen_status', // 生成状态
  WF_BUSINESS_STATUS = 'wf_business_status', // 业务状态
  WF_FORM_TYPE = 'wf_form_type', // 表单类型
  WF_TASK_STATUS = 'wf_task_status', // 任务状态
  SYS_JOB_GROUP = 'sys_job_group', // 任务分组
  SYS_MISSFIRE_POLICY = 'sys_missfire_policy', // 任务计划执行策略\
  SYS_JOB_CONCURRENT = 'sys_job_concurrent', // 任务是否并发
  DEVICE_MODEL_TYPE = 'model_type', // 物模型类型
  IOT_YES_NO = 'iot_yes_no', // 是否
  DEVICE_MODEL_DATA_TYPE = 'model_data_type', //物模型数据类型
  DEVICE_TYPE = 'device_type', //设备类型（1-直连设备、2-网关设备、3-子设备、4-监控设备）
  FIRMWARE_TYPE = 'firmware_type', //固件类型（1-分包、2-http）
  NETWORK_TYPE = 'network_type', //连网方式（1-以太网、2-蜂窝、3-WIFI、4-NB、5-其他）
  TRANSPORT = 'transport', //通讯协议（1-内置MQTT协议）
  CHANNEL_TYPE = 'channel_type', //通道类型（1-MQTT服务器、2-TCP服务器、3-UDP服务器、4-HTTP服务、5-WebSocket服务、6-数据库服务）
  VERTIFICATE_METHOD = 'vertificate_method', //认证方式（1-简单认证、2-加密认证、3-简单+加密）
  LOCATION_WAY = 'location_way', //定位方式(1=ip自动定位，2=设备定位，3=项目定位，4=自定义)
  PUBLISH_STATUS = 'publish_status', //产品状态（1-未发布，2-已发布）]
  DEVICE_STATE = 'device_state', //设备状态（1-在线、2-离线、3-禁用）
  ALARM_STATE = 'alarm_state', //设备告警状态（1-告警、2-正常）
  DEVICE_LOG_MSG_TYPE = 'devicelog_msg_type', //设备日志类型
  CHANNEL_DIRECTION = 'channel_direction', //桥接方向（1-输入、2-输出）
  CHANNEL_CONNECT = 'channel_connect', //资源通道连接状态（0-未连接 1-已连接）
  DEVICE_MATCH_TYPE = 'device_match_type', //设备匹配类型（1-设备ID、2-设备名称）
  SCRIPT_INPUT_TYPE = 'script_input_type', //规则脚本事件 1=设备上报 2=平台下发 3=设备上线 4=设备离线 5=HTTP接入 6=MQTT接入
  SCRIPT_OUTPUT_TYPE = 'script_output_type', //规则脚本-输出动作 1=消息重发 2=HTTP接入 3=MQTT接入
  SCRIPT_STATE = 'script_state', //规则脚本-状态 0=正常  1=停用
  ALARM_USER_LEVEL = 'alarm_user_level',// 告警用户级别接警级别（1-一级、2-二级、3-三级、4-四级）
  NOTIFY_CHANNEL_TYPE = 'notify_channel_type',
  NOTIFY_PROVIDER_SMS = 'notify_provider_sms',
  NOTIFY_PROVIDER_WECHAT = 'notify_provider_wechat',
  NOTIFY_PROVIDER_EMAIL = 'notify_provider_email',
  NOTIFY_PROVIDER_DING = 'notify_provider_ding',
  NOTIFY_PROVIDER_MQTT = 'notify_provider_mqtt',
  NOTIFY_BUSINESS_TYPE = 'notify_business_type',
  NOTIFY_USER_TYPE = 'notify_user_type',
  ALARM_LEVEL = 'alarm_level',
  ALARM_CONFIRM_STATE = 'alarm_confirm_state',
  ALARM_CONFIRM_USER_TYPE = 'confirm_user_type',

  SCENE_RELATION_OP = 'scene_relation_op', // 场景关联操作
  SCENE_LOGIC_OP = 'scene_logic_op',
  SCENE_SCRIPT_SOURCE = 'scene_script_source', // 动作范围
  DOWN_DATA_TYPE = 'down_data_type', // 动作类型


  REG_DATA_TYPE = 'reg_data_type',    //寄存器数据类型
  REG_BYTE_ORDER_16 = 'reg_byte_order_16',//寄存器16位字节序
  REG_BYTE_ORDER_32 = 'reg_byte_order_32',//寄存器32位字节序

  CJT188_METER_TYPE = 'cjt188_meter_type', // 仪表类型
  CJT188_DATA_TYPE = 'cjt188_data_type', // 数据对象类型
  CJT188_WATER_GAS_DATA1_KEYS = 'cjt188_water_gas_data1_keys', // 水表、燃气表的读数据1对象属性
  CJT188_HEAT_DATA1_KEYS = 'cjt188_heat_data1_keys', // 热量表的读数据1对象
  CJT188_WATER_GAS_DATA2_KEYS = 'cjt188_water_gas_data2_keys', // 水表、燃气表的读数据2对象属性
  CJT188_HEAT_DATA2_KEYS = 'cjt188_heat_data2_keys', // 热量表读数据2对象  TOPIC_TYPE = 'topic_type', // 数据流类型
  TOPIC_DATA_TYPE = 'topic_data_type', // 消息类型

  DATA_CHECK_TYPE = 'data_check_type', // 数据校验方式
  MODBUS_FUNCTION = 'modbus_function', // Modbus功能码

  SCENE_EXECUTE_MODE = 'scene_execute_mode', //执行方式;
  SCENE_TRIGGER_TYPE = 'scene_trigger_type', //触发类型

  TOPIC_TYPE = 'topic_type', // 数据流类型
  AGGREGATE_TIME_WINDOW = 'aggregate_time_window', // 聚合窗口
  AGGREGATE_FUNCTION = 'aggregate_function', // 聚合函数
  FROM_TYPE = 'from_type', // 属性来源类型
  STORE_POLICY = 'store_policy', // 存储策略
  UPGRADE_TASK_TYPE = 'upgrade_task_type', // 升级任务类型 1==http升级，2=二进制包升级
  BATCH_DETAIL_RESULT = 'batch_detail_result', // 批量操作结果
  FRAME_TYPE = 'frame_type', //分包类型
  FRAME_BYTE_ORDER = 'frame_byte_order', //分包字节顺序
  GB28181_SERVER_TYPE = 'GB28181_server_type', //GB28181服务类型

  NOTICE_RANGE = 'notice_range',//通知公告通知范围

  CHANNEL_MONITOR_WINDOW = "channel_monitor_window",//资源监控统计时间窗口
  DEVICE_SHARE_STATUS="device_share_status", //设备分享状态

}
