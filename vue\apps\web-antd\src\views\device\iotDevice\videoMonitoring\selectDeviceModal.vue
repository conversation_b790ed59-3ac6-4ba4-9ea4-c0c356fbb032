<template>
  <Modal v-model:open="modalOpen" title="选择设备" :width="modalWidth" :footer="null">
    <div class="select-device-modal">
      <!-- 使用VxeGrid替换原有的搜索栏和表格 -->
      <div class="device-grid">
        <Grid>
          <template #deviceState="{ row }">
            <Tag :style="{
              borderRadius: '4px',
              minWidth: '48px',
              textAlign: 'center',
              color: row.deviceState === 2 ? '#52c41a' : row.deviceState === 1 ? '#faad14' : '#909399',
              background: row.deviceState === 2 ? '#f6ffed' : row.deviceState === 1 ? '#fffbe6' : '#f4f6fa',
              border: row.deviceState === 2
                ? '1px solid #b7eb8f'
                : row.deviceState === 1
                  ? '1px solid #ffe58f'
                  : '1px solid #d9d9d9'
            }">
              {{ getDeviceStateLabel(row.deviceState) }}
            </Tag>
          </template>
          <template #status="{ row }">
            <Tag :style="{
              borderRadius: '4px',
              minWidth: '48px',
              textAlign: 'center',
              color: row.status === '0' ? '#52c41a' : '#909399',
              background: row.status === '0' ? '#f6ffed' : '#f4f6fa',
              border: row.status === '0' ? '1px solid #b7eb8f' : '1px solid #d9d9d9'
            }">
              {{ row.status === '0' ? '启用' : '禁用' }}
            </Tag>
          </template>
          <template #deviceType>
            <Tag color="blue">监控设备</Tag>
          </template>
        </Grid>
      </div>

      <!-- 底部按钮 -->
      <div class="modal-footer">
        <Space>
          <Button @click="handleCancel">取消</Button>
          <Button type="primary" @click="handleConfirm" :disabled="!selectedDevice">
            确定
          </Button>
        </Space>
      </div>
    </div>
  </Modal>

  <!-- 设备位置输入弹窗 -->
  <Modal v-model:open="locationModalVisible" title="设置设备位置" :width="400" :maskClosable="false" :footer="null">
    <Form ref="locationFormRef" :model="locationForm" :rules="locationFormRules" layout="vertical">
      <FormItem label="设备位置" name="deviceLocation">
        <Input v-model:value="locationForm.deviceLocation" placeholder="请输入设备位置" :maxlength="100" show-count />
      </FormItem>
    </Form>


    <div class="modal-footer">
      <Space>
        <Button @click="handleLocationCancel">取消</Button>
        <Button type="primary" @click="handleLocationConfirm">
          确定
        </Button>
      </Space>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Modal, Button, Space, Tag, Input, Form, FormItem, message } from 'ant-design-vue';
import type { FormInstance, Rule } from 'ant-design-vue/es/form';
import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { List as getDeviceList } from '#/api/device/iotDevice';
import { ListNoPage as getProductList } from '#/api/device/iotProduct';
import { getDictOptions } from '#/utils/dict';
import { DictEnum } from '@vben/constants';
import type { VbenFormSchema } from '@vben/common-ui';

const props = defineProps<{
  open: boolean;
}>();

const emit = defineEmits<{
  'update:open': [value: boolean];
  confirm: [selectedDevice: any];
}>();

// 模态框状态
const modalOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value),
});

// 数据状态
const monitorProductOptions = ref<any[]>([]);
const selectedDevice = ref<any>(null);

// 位置输入弹窗相关状态
const locationModalVisible = ref(false);
const locationForm = ref({
  deviceLocation: ''
});
const locationFormRef = ref<FormInstance>();
const locationFormRules: Record<string, Rule[]> = {
  deviceLocation: [
    { required: true, message: '请输入设备位置', trigger: 'blur' }
  ]
};

// 弹窗宽度为屏幕的55%，最小800px
const modalWidth = computed(() => {
  const screenWidth = window.innerWidth;
  const targetWidth = Math.floor(screenWidth * 0.55);
  return Math.max(targetWidth, 800) + 'px';
});

// 获取设备状态选项
const deviceStateOptions = getDictOptions(DictEnum.DEVICE_STATE);

// 获取设备状态标签
const getDeviceStateLabel = (value: string | number) => {
  const found = deviceStateOptions.find(opt => String(opt.value) === String(value));
  return found ? found.label : value;
};

// 查询表单Schema - 针对监控设备选择，只保留所属产品和设备标识
const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
    componentProps: {
      placeholder: '请选择产品',
      options: [],
      showSearch: true,
      filterOption: (input: any, option: any) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
    },
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
    },
  },
];

// 表格列配置 - 去掉地址字段，增加是否启用字段
const columns = [
  {
    type: 'radio' as const,
    width: 50,
  },
  {
    title: '设备名称',
    field: 'deviceName',
    align: 'left' as const,
    width: -1,
    showOverflow: 'tooltip' as const,
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left' as const,
    width: -1,
    showOverflow: 'tooltip' as const,
  },
  {
    title: '所属产品',
    field: 'productName',
    align: 'left' as const,
    width: -1,
    showOverflow: 'tooltip' as const,
  },
  {
    title: '设备类型',
    field: 'deviceType',
    align: 'center' as const,
    width: 100,
    slots: { default: 'deviceType' },
  },
  {
    title: '在线状态',
    field: 'deviceState',
    align: 'center' as const,
    width: 100,
    slots: { default: 'deviceState' },
  },
  {
    title: '是否启用',
    field: 'status',
    align: 'center' as const,
    width: 100,
    slots: { default: 'status' },
  },
];

// 表单配置
const formOptions: VbenFormProps = {
  collapsed: false,
  schema: querySchema,
  showCollapseButton: false,
  submitOnChange: false,
  submitOnEnter: true,
};

// VxeGrid配置
const gridOptions: VxeTableGridOptions = {
  height: Math.floor(window.innerHeight * 0.6),
  showOverflow: false,
  keepSource: true,
  rowConfig: {
    keyField: 'deviceId',
  },
  radioConfig: {
    highlight: true,
  },
  columns: columns,
  pagerConfig: {
    enabled: true,
    pageSize: 10,
    pageSizes: [10, 20, 50],
    layouts: ['PrevPage', 'JumpNumber', 'NextPage', 'Sizes', 'Total'],
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        console.log('🔍 查询监控设备，参数:', { page, formValues });

        // 构建查询参数，只查询监控设备类型
        const queryParams = {
          page: page.currentPage,
          pageSize: page.pageSize,
          deviceType: '4', // 监控设备类型，尝试使用字符串
          ...formValues,
        };

        console.log('📡 实际请求参数:', queryParams);

        try {
          const res = await getDeviceList(queryParams);
          console.log('📡 接口返回原始数据:', res);

          if (res && res.items) {
            // 添加产品名称
            let processedDevices = res.items.map((device: any) => {
              const product = monitorProductOptions.value.find(p => p.value === device.productKey);
              return {
                ...device,
                productName: product?.label || device.productKey
              };
            });

            // 如果后端没有正确过滤，我们在前端手动过滤监控设备
            if (processedDevices.length > 0) {
              console.log('📡 设备数据示例:', processedDevices[0]);
              
              // 前端过滤：只保留监控设备类型的设备
              const filteredDevices = processedDevices.filter((device: any) => {
                // 检查多种可能的字段名和值
                return device.deviceType === 4 || 
                       device.deviceType === '4' || 
                       device.type === 4 || 
                       device.type === '4' ||
                       device.productType === 4 ||
                       device.productType === '4';
              });

              console.log('📡 过滤前设备数量:', processedDevices.length);
              console.log('📡 过滤后设备数量:', filteredDevices.length);

              if (filteredDevices.length > 0) {
                processedDevices = filteredDevices;
              } else {
                console.warn('⚠️ 前端过滤后没有找到监控设备，可能字段名不正确');
                console.log('📡 设备数据字段:', Object.keys(processedDevices[0]));
              }
            }

            console.log('✅ 设备数据加载成功，共', processedDevices.length, '条记录');

            return {
              items: processedDevices,
              total: res.total || processedDevices.length,
            };
          }

          console.warn('⚠️ 接口返回数据为空');
          return { items: [], total: 0 };
        } catch (error) {
          console.error('❌ 查询设备列表失败:', error);
          return { items: [], total: 0 };
        }
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: false,
  },
};

// VxeGrid事件
const gridEvents: DeepPartial<VxeGridListeners> = {
  radioChange: ({ row }: any) => {
    selectedDevice.value = row;
    console.log('✅ 选择设备:', selectedDevice.value);
  },
};

// 创建Grid实例
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

// 加载监控设备产品列表
const loadMonitorProducts = async () => {
  try {
    console.log('📡 开始加载产品列表');
    const res = await getProductList({
      page: 1,
      pageSize: 1000,
    });

    if (res && res.items) {
      console.log('📡 获取到产品数据:', res.items.length, '条');

      // 只显示监控设备类型的产品
      const monitorProducts = res.items.filter((product: any) =>
        product.deviceType === 4 || product.deviceType === '4'
      );

      console.log('📡 过滤后的监控设备产品:', monitorProducts.length, '条');

      monitorProductOptions.value = monitorProducts.map((product: any) => ({
        label: product.productName,
        value: product.productKey,
        ...product
      }));

      console.log('✅ 产品选项列表生成完成:', monitorProductOptions.value.length, '个产品');

      // 更新表单中的产品选项
      if (gridApi && gridApi.formApi) {
        gridApi.formApi.updateSchema([
          {
            fieldName: 'productKey',
            component: 'Select',
            label: '所属产品',
            componentProps: {
              placeholder: '请选择产品',
              options: monitorProductOptions.value,
              showSearch: true,
              filterOption: (input: any, option: any) => {
                return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              },
            },
          },
        ]);
        console.log('✅ 产品选择下拉框已更新');
      }
    } else {
      console.warn('⚠️ 产品列表接口返回数据为空');
    }
  } catch (error) {
    console.error('❌ 加载产品列表失败:', error);
    // 即使产品列表加载失败，设备选择功能仍然可用
  }
};

// 确认选择 - 先关闭选择设备弹窗，再弹出位置输入弹窗
const handleConfirm = () => {
  if (!selectedDevice.value) {
    console.warn('请选择一个设备');
    return;
  }

  console.log('✅ 确认选择设备，先关闭选择设备弹窗:', selectedDevice.value);

  // 临时保存设备信息，防止在弹窗切换过程中丢失
  const tempDevice = { ...selectedDevice.value };

  // 先关闭选择设备弹窗
  modalOpen.value = false;

  // 延迟显示位置输入弹窗，确保第一个弹窗完全关闭
  setTimeout(() => {
    // 恢复设备信息
    selectedDevice.value = tempDevice;
    console.log('🔄 恢复设备信息:', selectedDevice.value);

    // 重置位置表单
    locationForm.value.deviceLocation = '';
    // 显示位置输入弹窗
    locationModalVisible.value = true;
    console.log('✅ 显示设备位置输入弹窗');
  }, 150);
};

// 位置输入弹窗确认
const handleLocationConfirm = async () => {
  try {
    // 表单验证
    await locationFormRef.value?.validate();

    // 在重置之前先保存设备信息
    const currentSelectedDevice = selectedDevice.value;

    console.log('✅ 最终确认选择设备和位置:', {
      device: currentSelectedDevice,
      location: locationForm.value.deviceLocation
    });

    // 检查设备信息是否还存在
    if (!currentSelectedDevice) {
      console.error('❌ 设备信息丢失，无法继续操作');
      message.error('设备信息丢失，请重新选择');
      return;
    }

    // 将设备信息和位置信息一起传递给父组件
    const deviceWithLocation = {
      ...currentSelectedDevice,
      deviceLocation: locationForm.value.deviceLocation
    };

    emit('confirm', deviceWithLocation);

    // 关闭所有弹窗
    locationModalVisible.value = false;
    modalOpen.value = false;

    // 重置状态
    selectedDevice.value = null;
    locationForm.value.deviceLocation = '';

    message.success('设备选择成功');
  } catch (error) {
    console.error('位置表单验证失败:', error);
  }
};

// 位置输入弹窗取消 - 重新打开选择设备弹窗
const handleLocationCancel = () => {
  console.log('🔙 取消位置输入，准备重新打开设备选择弹窗');

  // 保存当前设备信息
  const tempDevice = selectedDevice.value;

  locationModalVisible.value = false;
  locationForm.value.deviceLocation = '';

  // 延迟重新打开选择设备弹窗
  setTimeout(() => {
    // 恢复设备信息
    selectedDevice.value = tempDevice;
    modalOpen.value = true;
    console.log('✅ 重新打开选择设备弹窗，设备信息:', selectedDevice.value);
  }, 100);
};

// 取消选择
const handleCancel = () => {
  selectedDevice.value = null;
  locationForm.value.deviceLocation = '';
  locationModalVisible.value = false;
  modalOpen.value = false;
};

// 刷新表格数据
const handleRefresh = async () => {
  await gridApi.grid.commitProxy('query');
};

// 监听模态框打开状态
watch(
  () => props.open,
  async (open) => {
    if (open) {
      // 先加载产品列表，再刷新表格数据
      await loadMonitorProducts();
      await handleRefresh();
    } else {
      // 只有在真正关闭整个流程时才清理数据（不是弹窗切换时）
      if (!locationModalVisible.value) {
        console.log('🧹 清理设备选择数据');
        selectedDevice.value = null;
        locationForm.value.deviceLocation = '';
      }
    }
  }
);

onMounted(async () => {
  if (props.open) {
    await loadMonitorProducts();
    await handleRefresh();
  }
});
</script>

<style scoped>
.select-device-modal {
  max-height: 700px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.device-grid {
  flex: 1;
  overflow: auto;
  min-height: 450px;
}

.device-grid :deep(.vxe-grid) {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.device-grid :deep(.vxe-grid .vxe-table--border-line) {
  border-color: #f0f0f0;
}

.device-grid :deep(.vxe-grid .vxe-table--body-wrapper) {
  background: #fff;
}

.device-grid :deep(.vxe-grid .vxe-body--row:hover) {
  background-color: #f5f5f5;
}

.device-grid :deep(.vxe-grid .vxe-header--row) {
  background-color: #fafafa;
  font-weight: 500;
}

.device-grid :deep(.vxe-grid .vxe-cell) {
  padding: 8px 12px;
  line-height: 1.5;
}

.modal-footer {
  margin-top: 16px;
  text-align: right;
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;
}

.location-modal-content {
  padding: 8px 0;
}

.location-hint {
  color: #666;
  font-size: 14px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f6f8fa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}
</style>
