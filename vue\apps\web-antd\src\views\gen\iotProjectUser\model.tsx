import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';

export class State {
  public projectUserId = 0; // ID
  public projectId = 0; // 项目ID
  public name = ''; // 用户名称
  public sex = '0'; // 用户性别（0男 1女 2未知）
  public phone = ''; // 手机号
  public email = null; // 邮箱
  public workTitle = null; // 头衔
  public isPhone = 0; // 是否电话通知 （0 否 1是）
  public isSms = 0; // 是否短信通知 （0 否 1是）
  public isWechat = 0; // 是否公众号通知 （0 否 1是）
  public isEmail = 0; // 是否邮箱通知 （0 否 1是）
  public recvAlarmTime = null; // 接警时段
  public tenantId = ''; // 租户ID
  public deptId = 0; // 所属机构
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedBySumma?: null | MemberSumma = null; // 删除人摘要信息
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'projectUserId',
    component: 'InputNumber',
    label: 'ID',
    componentProps: {
      placeholder: '请输入ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: 'ID',
    field: 'projectUserId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '项目ID',
    field: 'projectId',
    align: 'left',
    width: -1,
 },
  {
    title: '用户名称',
    field: 'name',
    align: 'left',
    width: -1,
 },
  {
    title: '用户性别（0男 1女 2未知）',
    field: 'sex',
    align: 'left',
    width: -1,
 },
  {
    title: '手机号',
    field: 'phone',
    align: 'left',
    width: -1,
 },
  {
    title: '邮箱',
    field: 'email',
    align: 'left',
    width: -1,
 },
  {
    title: '头衔',
    field: 'workTitle',
    align: 'left',
    width: -1,
 },
  {
    title: '是否电话通知 （0 否 1是）',
    field: 'isPhone',
    align: 'left',
    width: -1,
 },
  {
    title: '是否短信通知 （0 否 1是）',
    field: 'isSms',
    align: 'left',
    width: -1,
 },
  {
    title: '是否公众号通知 （0 否 1是）',
    field: 'isWechat',
    align: 'left',
    width: -1,
 },
  {
    title: '是否邮箱通知 （0 否 1是）',
    field: 'isEmail',
    align: 'left',
    width: -1,
 },
  {
    title: '接警时段',
    field: 'recvAlarmTime',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '所属机构',
    field: 'deptId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建部门',
    field: 'createdDept',
    align: 'left',
    width: -1,
 },
  {
    title: '创建者',
    field: 'createdBy',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) =>  {
      return renderPopoverMemberSumma(row.createdBySumma);
    },
 },
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  {
    title: '更新者',
    field: 'updatedBy',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) =>  {
      return renderPopoverMemberSumma(row.updatedBySumma);
    },
 },
 },
  {
    title: '更新时间',
    field: 'updatedAt',
    align: 'left',
    width: -1,
 },
  {
    title: '删除人',
    field: 'deletedBy',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) =>  {
      return renderPopoverMemberSumma(row.deletedBySumma);
    },
 },
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  projectUserId: number;
  projectId: number;
  name: string;
  sex: string;
  phone: string;
  email: string;
  workTitle: string;
  isPhone: number;
  isSms: number;
  isWechat: number;
  isEmail: number;
  recvAlarmTime: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'projectUserId',  label: 'ID'},
  {  field: 'projectId',  label: '项目ID'},
  {  field: 'name',  label: '用户名称'},
  {  field: 'sex',  label: '用户性别（0男 1女 2未知）'},
  {  field: 'phone',  label: '手机号'},
  {  field: 'email',  label: '邮箱'},
  {  field: 'workTitle',  label: '头衔'},
  {  field: 'isPhone',  label: '是否电话通知 （0 否 1是）'},
  {  field: 'isSms',  label: '是否短信通知 （0 否 1是）'},
  {  field: 'isWechat',  label: '是否公众号通知 （0 否 1是）'},
  {  field: 'isEmail',  label: '是否邮箱通知 （0 否 1是）'},
  {  field: 'recvAlarmTime',  label: '接警时段'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'deptId',  label: '所属机构'},
  {  field: 'createdDept',  label: '创建部门'},
  {  field: 'createdBy',  label: '创建者'},
  {  field: 'createdAt',  label: '创建时间'},
  {  field: 'updatedBy',  label: '更新者'},
  {  field: 'updatedAt',  label: '更新时间'},
  {  field: 'deletedBy',  label: '删除人'},
  {  field: 'deletedAt',  label: '删除时间'},
  {  field: 'remark',  label: '备注'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'projectUserId',
    component: 'Input',
    label: 'ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'projectId',
    component: 'InputNumber',
    label: '项目ID',
    componentProps: {
      placeholder: '请输入项目ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入项目ID', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'name',
    component: 'Input',
    label: '用户名称',
    componentProps: {
      placeholder: '请输入用户名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'sex',
    component: 'Input',
    label: '用户性别（0男 1女 2未知）',
    componentProps: {
      placeholder: '请输入用户性别（0男 1女 2未知）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'phone',
    component: 'Input',
    label: '手机号',
    componentProps: {
      placeholder: '请输入手机号',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'email',
    component: 'Input',
    label: '邮箱',
    componentProps: {
      placeholder: '请输入邮箱',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.string().email('无效邮箱格式')
},
  {
    fieldName: 'workTitle',
    component: 'Input',
    label: '头衔',
    componentProps: {
      placeholder: '请输入头衔',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'isPhone',
    component: 'InputNumber',
    label: '是否电话通知 （0 否 1是）',
    componentProps: {
      placeholder: '请输入是否电话通知 （0 否 1是）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入是否电话通知 （0 否 1是）', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'isSms',
    component: 'InputNumber',
    label: '是否短信通知 （0 否 1是）',
    componentProps: {
      placeholder: '请输入是否短信通知 （0 否 1是）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入是否短信通知 （0 否 1是）', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'isWechat',
    component: 'InputNumber',
    label: '是否公众号通知 （0 否 1是）',
    componentProps: {
      placeholder: '请输入是否公众号通知 （0 否 1是）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入是否公众号通知 （0 否 1是）', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'isEmail',
    component: 'InputNumber',
    label: '是否邮箱通知 （0 否 1是）',
    componentProps: {
      placeholder: '请输入是否邮箱通知 （0 否 1是）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入是否邮箱通知 （0 否 1是）', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'recvAlarmTime',
    component: 'Input',
    label: '接警时段',
    componentProps: {
      placeholder: '请输入接警时段',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'tenantId',
    component: 'Input',
    label: '租户ID',
    componentProps: {
      placeholder: '请输入租户ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'deptId',
    component: 'InputNumber',
    label: '所属机构',
    componentProps: {
      placeholder: '请输入所属机构',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入所属机构', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];