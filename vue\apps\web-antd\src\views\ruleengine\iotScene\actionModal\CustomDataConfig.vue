<script setup lang="ts">
import { watch, ref, computed, } from 'vue';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { Input, Select, Radio, } from 'ant-design-vue';
import hljs from 'highlight.js/lib/core';
import go from 'highlight.js/lib/languages/go';
import typescript from 'highlight.js/lib/languages/typescript';
import javascript from 'highlight.js/lib/languages/javascript';
import xml from 'highlight.js/lib/languages/xml';
import sql from 'highlight.js/lib/languages/sql';
import json from 'highlight.js/lib/languages/json';
import 'highlight.js/styles/atom-one-dark.css';
import 'highlight.js/lib/common';
hljs.registerLanguage('ts', typescript);
hljs.registerLanguage('js', javascript);
hljs.registerLanguage('go', go);
hljs.registerLanguage('xml', xml);
hljs.registerLanguage('sql', sql);
hljs.registerLanguage('json', json);
import highlight from '#/adapter/highlightedit';

hljs.registerLanguage('ts', typescript);
hljs.registerLanguage('js', javascript);

// 接收父组件传入的 v-model
const props = defineProps<{
  customTopic: string;
  topicType: string;
  dataType: string;
  dataMsg: string;
}>();

const emit = defineEmits([
  'update:customTopic',
  'update:topicType',
  'update:dataType',
  'update:dataMsg',
]);

// 内部状态绑定 v-model
const innerCustomTopic = ref(props.customTopic);
const innerTopicType = ref(props.topicType);
const innerDataType = ref(props.dataType);
const innerDataMsg = ref(props.dataMsg);

// 双向绑定更新父组件
watch(innerCustomTopic, val => emit('update:customTopic', val));
watch(innerTopicType, val => emit('update:topicType', val));
watch(innerDataType, val => emit('update:dataType', val));
watch(innerDataMsg, val => emit('update:dataMsg', val));

// 初始化监听父组件变化
watch(() => props.customTopic, val => innerCustomTopic.value = val);
watch(() => props.topicType, val => innerTopicType.value = val);
watch(() => props.dataType, val => innerDataType.value = val);
watch(() => props.dataMsg, val => innerDataMsg.value = val);

const topicTypeOptions = getDictOptions(DictEnum.TOPIC_TYPE); // 数据流类型
const dataTypeOptions = getDictOptions(DictEnum.TOPIC_DATA_TYPE); //消息格式，取前三个
// 是否显示“自定义数据流”输入框
const showCustomTopicInput = computed(() => props.topicType === '3');

</script>

<template>
  <div style="border:1px solid #d9e5f6;padding:12px;border-radius:8px;width: 800px;">
    <!-- 标题 -->
    <h2 style="margin-bottom: 20px;">动作脚本配置</h2>

    <!-- 第二行：数据流类型 + 自定义数据流（条件显示） -->
    <div class="form-row">
      <div class="form-item">
        <label>数据流类型：</label>
        <Select v-model:value="innerTopicType" :options="topicTypeOptions" placeholder="请选择数据流类型" style="width: 200px"
          @change="val => emit('update:topicType', val)" />
      </div>

      <div class="form-item" v-if="showCustomTopicInput" style="margin-left: 32px;">
        <label>自定义数据流：</label>
        <Input v-model:value="innerCustomTopic" placeholder="请输入" style="width: 200px" />
      </div>
    </div>

    <!-- 第三行：消息格式 -->
    <div class="form-row" style="margin-bottom: 16px;">
      <label>消息格式：</label>
      <Radio.Group v-model:value="innerDataType" size="small" class="custom-radio-group">
        <Radio v-for="item in dataTypeOptions.slice(0, 3)" :key="item.value" :value="item.value" class="custom-radio">
          {{ item.label }}
        </Radio>
      </Radio.Group>
    </div>

    <!-- 第四行：下发消息 -->
    <div style="margin-bottom: 16px;">
      <label>下发消息：</label>
      <highlight class="w-full h500 code-json" :code="innerDataMsg" language="json" :autodetect="false"
        @update:code="(d: any) => innerDataMsg = d" style="margin-top: 18px;" />
    </div>
  </div>
</template>

<style >
.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.form-item {
  display: flex;
  align-items: center;
}

 .h500 code {
  height: 300px;
}

.custom-radio-group .custom-radio {
  margin-right: 32px;
  /* 控制选项间距 */
  font-size: 18px;
  /* 控制字体大小 */
}
</style>
