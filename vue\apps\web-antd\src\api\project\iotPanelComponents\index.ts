import { requestClient } from '#/api/request';

// 获取面板配置组件表列表
export function List(params:any) {
  return requestClient.get<any>('project/iotPanelComponents/list', { params });
}

// 删除/批量删除面板配置组件表
export function Delete(params:any) {
  return requestClient.post<any>('project/iotPanelComponents/delete', { ...params });
}

// 添加/编辑面板配置组件表
export function Edit(params:any) {
  return requestClient.post<any>('project/iotPanelComponents/edit', { ...params });
}

// 获取面板配置组件表指定详情
export function View(params:any) {
  return requestClient.get<any>('project/iotPanelComponents/view', { params });
}

// 导出面板配置组件表
export function Export(params:any) {
  return requestClient.post<Blob>('/project/iotPanelComponents/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}