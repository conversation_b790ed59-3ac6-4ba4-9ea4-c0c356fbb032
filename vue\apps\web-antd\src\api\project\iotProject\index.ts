import { requestClient } from '#/api/request';

// 获取项目表列表
export function List(params: any) {
  return requestClient.get<any>('project/iotProject/list', { params });
}

// 删除/批量删除项目表
export function Delete(params: any) {
  return requestClient.post<any>('project/iotProject/delete', { ...params });
}

// 添加/编辑项目表
export function Edit(params: any) {
  return requestClient.post<any>('project/iotProject/edit', { ...params });
}

// 修改项目表状态
export function Status(params: any) {
  return requestClient.post<any>('project/iotProject/status', { ...params });
}

// 获取项目表指定详情
export function View(params: any) {
  return requestClient.get<any>('project/iotProject/view', { params });
}

// 导出项目表
export function Export(params: any) {
  return requestClient.post<Blob>('/project/iotProject/export', { ...params }, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}

// 获取绑定项目的设备列表
export function DeviceList(params: any) {
  return requestClient.get<any>('project/iotProject/deviceList', { params });
}

// 获取未绑定项目的设备列表
export function DeviceSelectList(params: any) {
  return requestClient.get<any>('project/iotProject/deviceSelectList', { params });
} 

// 绑定设备
export function DeviceBind(params: any) {
  return requestClient.post<any>('project/iotProject/deviceBind', { ...params });
}

// 解绑设备
export function DeviceUnbind(params: any) {
  return requestClient.post<any>('project/iotProject/deviceUnbind', { ...params });
}