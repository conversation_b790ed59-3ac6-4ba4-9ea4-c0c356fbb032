<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[900px]">
    <BasicForm></BasicForm>
    <!-- Tabs和新增按钮同一行 -->
    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
      <Tabs v-model:activeKey="activeTab" style="flex:1;">
        <TabPane key="scene" tab="关联场景"></TabPane>
        <TabPane key="notify" tab="消息通知"></TabPane>
      </Tabs>
      <Button type="primary" @click="activeTab === 'scene' ? openSceneModal() : openNotifyModal()"
        style="margin-left: 16px;margin-right: 20px;">
        <template #icon>
          <PlusOutlined />
        </template>
        新增
      </Button>
    </div>
    <!-- Tab内容区域 关联场景列表的 -->
    <div v-if="activeTab === 'scene'">
      <div class="table-content h-[400px]">
        <SceneGrid table-title="关联场景列表">
          <!-- 添加动作按钮弹窗 ，这个弹窗的内容最后放到一个表格里面-->
          <template #Newaction="{ row }">
            <AccessControl :codes="['cpm:ruleengine:iotScene:delete']" type="code">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger @click="handleDeleteScene(row)">
                删除
              </Button>
            </AccessControl>
          </template>
        </SceneGrid>
      </div>
    </div>
    <!-- Tab内容区域 消息通知列表的 -->
    <div v-else>
      <div class="table-content h-[400px]">
        <NotifyGrid table-title="消息通知列表">
          <!-- 添加动作按钮弹窗 ，这个弹窗的内容最后放到一个表格里面-->
          <template #Newaction="{ row }">
            <AccessControl :codes="['cpm:ruleengine:iotScene:delete']" type="code">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger @click="handleDeleteNotify(row)">
                删除
              </Button>
            </AccessControl>
          </template>
        </NotifyGrid>
      </div>
    </div>

    <!-- 关联场景弹窗 -->
    <SelectScene ref="refselectScene" :getSceneKey="getSceneKey" />
    <!-- 消息通知弹窗 -->
    <SelectNotify ref="refselectNotify" :getNotifyKey="getNotifyKey" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, nextTick, ref, toRaw, h } from 'vue';
import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';
import { Button, message, Tabs, TabPane } from 'ant-design-vue';
import { useVbenForm } from '#/adapter/form';
import { Edit, View } from '#/api/gen/iotAlarmConfig';
import { editSchema, type SceneRowType, scenecolumns, type NotifyRowType, notifycolumns } from './model';
import { MdiPlus, PlusOutlined } from '@vben/icons';
import type { VxeTableGridOptions, VxeGridListeners, } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import selectSceneModal from './selectScene/index.vue';
import selectNotifyModal from './selectNotify/index.vue';
import { AccessControl } from '@vben/access';


const activeTab = ref('scene');
const sceneList = ref<SceneRowType[]>([]);  //接收关联场景列表弹窗的数据
const notifyList = ref<NotifyRowType[]>([]); //接收消息通知列表弹窗的数据
//场景选择
const [SelectScene, selectSceneModalApi] = useVbenModal({
  zIndex: 2001,
  connectedComponent: selectSceneModal,
});
const refselectScene = ref();
// 关联场景
async function openSceneModal() {
  // 获取当前表单的 alarmLevel 值
  const formValues = await formApi.getValues();
  const alarmLevel = Number(formValues.alarmLevel); // 确保转换为数字

  nextTick(() => {
    // 将 alarmLevel 传递给子组件
    selectSceneModalApi.setData({ alarmLevel });
    selectSceneModalApi.open();
  });
}
function getSceneKey(row: SceneRowType) {
  // 避免重复添加
  console.log(row);
  if (!sceneList.value.some(item => item.sceneId === row.sceneId)) {
    sceneList.value.push({ ...row });
    scenegridApi.query(); // 刷新表格
  } else {
    message.warning('该场景已添加');
  }
}
//关联场景下的表单-----------------------
const sceneGridOptions: VxeTableGridOptions<SceneRowType> = {
  rowConfig: {
    keyField: 'sceneId',
  },
  columns: scenecolumns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return {
          items: toRaw(sceneList.value),
          total: sceneList.value.length,
        };
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: true,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const sceneGridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleSceneCheckboxChange,
  checkboxAll: handleSceneCheckboxChange,
};
const sceneCheckboxChecked = ref(false);
function handleSceneCheckboxChange() {
  sceneCheckboxChecked.value = scenegridApi.grid.getCheckboxRecords().length > 0;
}
const [SceneGrid, scenegridApi] = useVbenVxeGrid({
  gridOptions: sceneGridOptions,
  gridEvents: sceneGridEvents,
});

async function handleRefresh() {
  await nextTick();

  if (activeTab.value === 'scene' && scenegridApi && scenegridApi.query) {
    await scenegridApi.query();
  }
  if (activeTab.value != 'scene' && notifygridApi && notifygridApi.query) {
    await notifygridApi.query();
  }
}

async function handleDeleteScene(row: SceneRowType) {
  // 删除指定行
  sceneList.value = sceneList.value.filter(item => item.sceneId !== row.sceneId);
  message.success("删除成功");
  await scenegridApi.query();
}

// 消息通知-------------------------------------------------------
// 消息通知-------------------------------------------------------

// 获取消息通知数据

const [SelectNotify, selectNotifyModalApi] = useVbenModal({
  zIndex: 2001,
  connectedComponent: selectNotifyModal,
});
const refselectNotify = ref();
// 关联场景
function openNotifyModal() {
  nextTick(() => {
    selectNotifyModalApi.open();
  });
}
function getNotifyKey(row: NotifyRowType) {
  // 避免重复添加
  if (!notifyList.value.some(item => item.id === row.id)) {
    notifyList.value.push({ ...row });
    notifygridApi.query(); // 刷新表格
  } else {
    message.warning('该场景已添加');
  }
}


//消息通知下的表单-----------------------
const notifyGridOptions: VxeTableGridOptions<NotifyRowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'id',
  },
  rowConfig: {
    keyField: 'id',
  },
  columns: notifycolumns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return {
          items: toRaw(notifyList.value),
          total: notifyList.value.length,
        };
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: true,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const notifyGridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleNotifyCheckboxChange,
  checkboxAll: handleNotifyCheckboxChange,
};
const notifyCheckboxChecked = ref(false);
function handleNotifyCheckboxChange() {
  notifyCheckboxChecked.value = notifygridApi.grid.getCheckboxRecords().length > 0;
}
const [NotifyGrid, notifygridApi] = useVbenVxeGrid({
  gridOptions: notifyGridOptions,
  gridEvents: notifyGridEvents,
});

async function handleDeleteNotify(row: NotifyRowType) {
  // 删除指定行
  notifyList.value = notifyList.value.filter((item) => item.id !== row.id);
  message.success("删除成功");
  await notifygridApi.query();
}

//原来的代码位置-----------------------------------
const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
}

const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'horizontal',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, update, view, } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;
    if (isUpdate.value || isView.value) {
      const record = await View({ alarmConfigId: id });
      // 这里打印后端返回的数据
      console.log('修改接口返回数据record:', record);
      await formApi.setValues(record);
      sceneList.value = record.alarmScenes || [];
      notifyList.value = record.alarmTemplates || [];
    } else {
      // 新增时清空tab列表内容
      sceneList.value = [];
      notifyList.value = [];
    }
    handleRefresh();
    drawerApi.setState({ confirmLoading: false, loading: false })

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    data.sceneIds = sceneList.value.map(item => item.sceneId);
    data.templateIds = notifyList.value.map(item => item.id);
    if(data.ipcScreenshotOpen==undefined){
      data.ipcScreenshotOpen = 0;
    }else{
      data.ipcScreenshotOpen = 1;
    }
    // console.log('Edit提交数据:', data);
    await (Edit(data));
    emit('reload');
    console.log('Edit提交数据:', data);
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

</script>
