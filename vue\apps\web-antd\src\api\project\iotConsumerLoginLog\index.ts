import { requestClient } from '#/api/request';

// 获取终端用户登录记录列表
export function List(params:any) {
  return requestClient.get<any>('project/iotConsumerLoginLog/list', { params });
}

// 删除/批量删除终端用户登录记录
export function Delete(params:any) {
  return requestClient.post<any>('project/iotConsumerLoginLog/delete', { ...params });
}

// 添加/编辑终端用户登录记录
export function Edit(params:any) {
  return requestClient.post<any>('project/iotConsumerLoginLog/edit', { ...params });
}

// 修改终端用户登录记录状态
export function Status(params:any) {
  return requestClient.post<any>('project/iotConsumerLoginLog/status', { ...params });
}

// 获取终端用户登录记录指定详情
export function View(params:any) {
  return requestClient.get<any>('project/iotConsumerLoginLog/view', { params });
}

// 导出终端用户登录记录
export function Export(params:any) {
  return requestClient.post<Blob>('/project/iotConsumerLoginLog/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}