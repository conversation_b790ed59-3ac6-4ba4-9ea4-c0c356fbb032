import { requestClient } from '#/api/request';

// 获取报警关联场景列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotAlarmScene/list', { params });
}

// 删除/批量删除报警关联场景
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotAlarmScene/delete', { ...params });
}

// 添加/编辑报警关联场景
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotAlarmScene/edit', { ...params });
}

// 获取报警关联场景指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotAlarmScene/view', { params });
}

// 导出报警关联场景
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotAlarmScene/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}