import { requestClient } from '#/api/request';

// 获取采集任务流水表列表
export function List(params:any) {
  return requestClient.get<any>('device/iotPollTaskLog/list', { params });
}

// 删除/批量删除采集任务流水表
export function Delete(params:any) {
  return requestClient.post<any>('device/iotPollTaskLog/delete', { ...params });
}

// 添加/编辑采集任务流水表
export function Edit(params:any) {
  return requestClient.post<any>('device/iotPollTaskLog/edit', { ...params });
}

// 获取采集任务流水表指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotPollTaskLog/view', { params });
}

// 导出采集任务流水表
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotPollTaskLog/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}