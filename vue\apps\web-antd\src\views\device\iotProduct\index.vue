<script lang="ts" setup>
import { h, ref, onMounted } from 'vue';
import {
  Button, message, Modal, Popconfirm, Card, RadioGroup,
  RadioButton, Tag, Tooltip,
} from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl } from '@vben/access';
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, Delete } from '#/api/device/iotProduct';
import { List as CategoryList } from '#/api/device/iotProductCategory';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import editDrawer from './edit.vue';
import { router } from '#/router';
import { IconifyIcon } from '@vben/icons';
import { useRouter } from 'vue-router';
import { DictEnum } from '@vben/constants';
import { getDictOptions, getDictLabel } from '#/utils/dict';
import { processImageUrl, handleImageError } from '#/utils/image';


const deviceRouter = useRouter()
function onDevice(item: RowType) {
  deviceRouter.push({
    path: '/device/iotDevice',
    query: {
      productKey: item.productKey,
      productName: item.productName
    }
  })
}

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const cardListColumns = [{
  field: 'list',
  slots: {
    default: 'list',
  },
}];

const cardListPager = {
  pageSizes: [12, 24, 36, 48, 60],
  pageSize: 12,
};

const gridType = ref('2');
function handleChangeGrid(value: string) {
  gridType.value = value;
  configGridOptions();
}

function configGridOptions() {
  gridApi.setGridOptions({
    checkboxConfig: {
      highlight: true,
      labelField: 'productId',
    },
    border: gridType.value == '2' ? 'none' : 'default',
    columns: gridType.value == '2' ? cardListColumns : columns,  //columns,
    showHeader: gridType.value == '2' ? false : true,
    pagerConfig: gridType.value == '2' ? cardListPager : {},
  });
  gridApi.reload();
}

const gridOptions: VxeTableGridOptions<RowType> = {
  rowConfig: {
    keyField: 'productId',
  },
  columns: columns,  //columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let result = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
        let categoryArray = await CategoryList({});
        let categoryMap = categoryArray.items;
        result.items.forEach((item: any) => {
          item.categoryName = categoryMap.find(
            (i: any) => item.categoryId === i.categoryId,
          )?.categoryName;
        });
        if (gridType.value == '2') {
          let newItems = [{ list: result.items }];
          result.items = newItems;
        }
        return result;

      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: true,
    search: true,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});
// const [ViewModal, modalApi] = useVbenModal({
//   connectedComponent: detailModal,
// });
// function handlePreview(record: RowType) {
//   modalApi.setData(record);
//   modalApi.open();
// }

const handleDetail = (productId: number) => {
  router.push(`/device/productDetail?productId=${productId}`);
};

const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});

function handleAdd() {
  editDrawerApi.setData({ update: false, view: false });
  editDrawerApi.open();
}
function handleEdit(row: RowType) {
  editDrawerApi.setData({ id: row.productId, update: true, view: false });
  editDrawerApi.open();
}
async function handleDelete(row: RowType) {
  await Delete({ productId: [row.productId] });
  message.success('删除成功');
  await handleRefresh();
}
async function handleRefresh() {
  await gridApi.query();
}
function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const ids: string[] = [];
  for (const row of rows) {
    ids.push(row.productId);
  }
  if (ids.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await Delete({ productId: ids });
      message.success('删除成功');
      await handleRefresh();
    },
  });
}
async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '产品表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success('导出成功');
};
function getNetworkTypeLabel(value: string | number) {
  const option = networkTypeOptions.find(opt => opt.value == value);
  return option ? option.label : value;
}
const networkTypeOptions = getDictOptions(DictEnum.NETWORK_TYPE);

const deviceTypeOptions = getDictOptions(DictEnum.DEVICE_TYPE);
function getDeviceTypeLabel(value: string | number) {
  const option = deviceTypeOptions.find(opt => opt.value == value);
  return option ? option.label : value;
}

onMounted(() => {
  configGridOptions();
});
</script>

<template>
  <Page auto-content-height>
    <Grid table-title="产品列表">
      <template #toolbar-tools>
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiPlus)" @click="handleAdd"
          v-access:code="'cpm:device:iotProduct:edit'">
          新增
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :disabled="!CheckboxChecked" :icon="h(MdiDelete)"
          @click="handleMultiDelete" v-access:code="'cpm:device:iotProduct:delete'">
          删除
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiExport)" @click="handleExport"
          v-access:code="'cpm:device:iotProduct:export'">
          导出
        </Button>
        <RadioGroup class="mr-2 flex items-center" @change="handleChangeGrid($event.target.value)"
          v-model:value="gridType">
          <RadioButton value="1"
            style="display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0;">
            <IconifyIcon icon="ant-design:menu-outlined" />
          </RadioButton>
          <RadioButton value="2"
            style="display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0;">
            <IconifyIcon icon="mdi:view-grid" />
          </RadioButton>
        </RadioGroup>
      </template>

      <template #list="{ row }">
        <div class="grid grid-cols-1  lg:grid-cols-2  xl:grid-cols-3 2xl:grid-cols-4  gap-4">
          <Card v-for="item in row.list" :key="item.productId" class="w-full "
            :bodyStyle="{ padding: '14px 14px 0 14px' }">
            <!-- 卡片头部：图片+名称+状态 -->
            <div class="flex items-center text-left w-full ">
              <!-- 左侧图片 -->
              <img :src="processImageUrl(item.imgUrl)" alt="产品图片"
                style="width: 62px; height: 62px; border-radius: 8px; object-fit: cover; margin-right: 12px;"
                @error="handleImageError" />
              <!-- 中间：星星+产品名+管理员 -->
              <div class="flex-1 text-left ">
                <div class="flex  justify-between">
                  <div class="flex-1 text-lg  ">
                    <div class="overflow-hidden text-overflow-ellipsis h-[30px]">
                      {{ item.productName }}
                    </div>
                  </div>
                  <div class="w-[70px] ">
                    <Tag :color="item.publishStatus === 2 ? 'green' : 'default'" style="margin-top: 4px;">
                      <template v-if="item.publishStatus === 2">
                        <IconifyIcon icon="ant-design:check-circle-filled"
                          style="color: #52c41a; margin-right: 2px; font-size: 16px; margin-bottom: 1px;" />已发布
                      </template>
                      <template v-else>
                        <IconifyIcon icon="ant-design:exclamation-circle-filled"
                          style="color: #bfbfbf; margin-right: 2px; font-size: 16px; margin-bottom: 1px;" />未发布
                      </template>
                    </Tag>
                  </div>
                </div>
                <div>
                  <Tag color="default">
                    {{ item.deptName || '未知机构' }}
                  </Tag>
                </div>
              </div>
            </div>

            <!-- 下面内容 -->
            <div class="mt-4 mb-4">
              <div style="display: flex;">
                <!-- 左侧：所属分类、产品类型， -->
                <div style="flex: 1; text-align: left;">
                  <div>
                    所属分类：<span style="color: #3b82f6;">{{ item.categoryName }}</span>
                  </div>
                  <div class="mt-2">
                    设备类型：{{ getDeviceTypeLabel(item.deviceType) }}
                  </div>
                </div>
                <!-- 右侧：联网方式、设备授权， -->
                <div style="flex: 1; text-align: left;">
                  <div>
                    联网方式：{{ getNetworkTypeLabel(item.networkType) }}
                  </div>
                  <Tooltip>
                    <template #title>{{ getDictLabel(DictEnum.TRANSPORT, item.transport) }}</template>
                    <div class="overflow-hidden text-overflow-ellipsis h-[20px] mt-2">
                      通讯协议：{{ getDictLabel(DictEnum.TRANSPORT, item.transport) }}
                    </div>
                  </Tooltip>

                </div>
              </div>
            </div>
            <template #actions>
              <Button type="link" class="h-6 custom-link-button" @click="handleDetail(item.productId)">
                查看详情
              </Button>
              <Button type="link" class="h-6 custom-link-button" @click="onDevice(item)">
                查看设备
              </Button>
            </template>
          </Card>
        </div>
      </template>

      <template #action="{ row }">
        <div class="flex items-center">
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleDetail(row.productId)"
            v-access:code="'cpm:device:iotProduct:view'">
            详情
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row)"
            v-access:code="'cpm:device:iotProduct:edit'" :disabled="row.publishStatus === 2">
            修改
          </Button>
          <AccessControl :codes="['cpm:device:iotProduct:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
      <template #publishStatus="{ row }">
        <Tag :color="row.publishStatus === 2 ? 'green' : 'default'"
          style="border-radius: 4px; min-width: 48px; text-align: center;">
          {{ row.publishStatus === 2 ? '已发布' : '未发布' }}
        </Tag>
      </template>
    </Grid>
    <EditDrawer @reload="handleRefresh" />
  </Page>
</template>
<style scoped>
.custom-link-button {
  color: #666666 !important;
}

.custom-link-button:hover {
  color: #333333 !important;
}

.custom-link-button:focus {
  color: #666666 !important;
}
</style>
