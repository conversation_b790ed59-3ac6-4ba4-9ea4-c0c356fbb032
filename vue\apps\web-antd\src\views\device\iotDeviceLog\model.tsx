import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils';
import dayjs from 'dayjs';
import { IconifyIcon } from '@vben/icons';

export class State {
  public ts = ''; // 创建时间
  public deviceKey = ''; // 设备标识
  public type = 0; // 类型
  public content = ''; // 内容
  public tenantId = ''; // 租户ID
  
  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
{
  fieldName: 'msgType',
  component: 'Select',
  label: '日志类型',
  componentProps:{
    placeholder: '请选择类型',
    onUpdateValue: (e: any) => {
      console.log(e);
    },

    options: getDictOptions(DictEnum.DEVICE_LOG_MSG_TYPE),
  } 
},
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      placeholder: ['开始时间', '结束时间'],
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      clearable: false,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '时间',
    field: 'ts',
    align: 'center',
    width: 200,
 },
  {
    title: '类型',
    field: 'type',
    align: 'center',
    width: 200,
    slots: {
      default: ({ row }) => {
        if(row.contentObj && row.contentObj.isUp){
          return h('div', {class: 'flex items-center justify-center'}, [renderDict(row.type, DictEnum.DEVICE_LOG_MSG_TYPE) , 
            h(IconifyIcon, {icon: 'ant-design:arrow-up-outlined', style: {color: '#409eff', width: '18px', height: '18px'}}),
          ]);
        }else if(row.contentObj && row.contentObj.isDown){
          return h('div', {class: 'flex items-center justify-center'}, [renderDict(row.type, DictEnum.DEVICE_LOG_MSG_TYPE) , 
            h(IconifyIcon, {icon: 'ant-design:arrow-down-outlined', style: {color: '#409eff', width: '18px', height: '18px'}})]);
        }
        return renderDict(row.type, DictEnum.DEVICE_LOG_MSG_TYPE);
      },
    },
 },
 {
  title: '内容',
  field: 'content',
  align: 'left',
  width: -1,
  // 内容过长时，显示省略号
  slots: {
    default: ({ row }) => {
      if(row.contentObj){
        return row.contentObj.data||'';
      }
      return '';
    },
  },
},
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
    visible: false,
 },

  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  ts: string;
  device: string;
  type: number;
  content: string;
  tenantId: string;
  contentObj: any;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'contentObj.msgId',  label: '消息ID', 
    labelMinWidth: 50,
    render: (_, data) => {
    return data.contentObj.msgId||'';
  }},
  {  field: 'ts',  label: '消息时间'},
  {  field: 'type',  label: '消息类型', 
    render(_, data) {
      return renderDict(data.type, DictEnum.DEVICE_LOG_MSG_TYPE);
    },
  },
  {  field: 'isUp',  label: '消息方向', 
    render(_, data) {
      if(data.contentObj && data.contentObj.isUp){
        return '上行';
      }else if(data.contentObj && data.contentObj.isDown){
        return '下行';
      }else{
        return '';
      }
    },
  },
  {  field: 'contentObj.code',  label: '消息状态', render: (_, data) => {
    if(data.contentObj.code == 0){
      return h(Tag, { color: 'green' }, '成功');
    }else{
      return h(Tag, { color: 'red' }, '失败('+data.contentObj.code+')');
    }
  }},
  {  field: 'contentObj.transport',  label: '通讯协议', render: (_, data) => {
    if(!data.contentObj.transport){
      return ''; 
    }
    return renderDict(data.contentObj.transport, DictEnum.TRANSPORT);
  } },
  {  field: 'contentObj.channelType',  label: '通道类型', render: (_, data) => {
    if(!data.contentObj.channelType){
      return ''; 
    }
    return renderDict(data.contentObj.channelType, DictEnum.CHANNEL_TYPE);
  } },
  {  field: 'contentObj.topic',  label: '消息主题', render: (_, data) => {
    return data.contentObj.topic||'';
  } },
  {  field: 'contentObj.data',  label: '消息内容', render: (_, data) => {
    return data.contentObj.data||'';
  } },

];
