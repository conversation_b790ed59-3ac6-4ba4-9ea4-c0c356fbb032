import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

export class State {
  public relationId = 0; // 关系ID
  public productKey = ''; // 子设备产品标识
  public deviceKey = ''; // 子设备标识
  public parentProductKey = ''; // 网关产品标识
  public parentDeviceKey = ''; // 网关设备标识
  public subDeviceAddr = null; // 子设备地址
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 更新时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'parentDeviceKey',
    component: 'Input',
    label: '网关设备标识',
    componentProps: {
      placeholder: '请输入网关设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '',
    field: 'relationId',
    align: 'left',
    width: 50,
    type: 'checkbox',
    slots: {
      default: ({ row }) => {
        return "";
      },
    },
  },
  {
    title: '子设备产品标识',
    field: 'productKey',
    align: 'left',
    width: -1,
 },
  {
    title: '子设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
 },
  {
    title: '子设备地址',
    field: 'subDeviceAddr',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 300, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  relationId: number;
  productKey: string;
  deviceKey: string;
  parentProductKey: string;
  parentDeviceKey: string;
  subDeviceAddr: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'relationId',  label: '关系ID'},
  {  field: 'productKey',  label: '子设备产品标识'},
  {  field: 'deviceKey',  label: '子设备标识'},
  {  field: 'parentProductKey',  label: '网关产品标识'},
  {  field: 'parentDeviceKey',  label: '网关设备标识'},
  {  field: 'subDeviceAddr',  label: '子设备地址'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdAt',  label: '创建时间'},
  {  field: 'updatedAt',  label: '更新时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'relationId',
    component: 'Input',
    label: '关系ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '子设备产品标识',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '请输入子设备产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '子设备标识',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '请输入子设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'parentProductKey',
    component: 'Input',
    label: '网关产品标识',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '请输入网关产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'parentDeviceKey',
    component: 'Input',
    label: '网关设备标识',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '请输入网关设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'subDeviceAddr',
    component: 'Input',
    label: '子设备地址',
    componentProps: {
      placeholder: '请输入子设备地址',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
}
];