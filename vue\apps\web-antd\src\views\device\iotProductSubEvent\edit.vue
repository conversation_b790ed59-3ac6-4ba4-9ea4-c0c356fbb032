<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <SceneModal :style="{ width: '50vw', height: '80vh', maxWidth: 'none', maxHeight: 'none' }"
        class="!w-[50vw] !h-[80vh]">
        <SceneGrid />
      </SceneModal>
      <template #sceneId>
        <Input v-model="sceneName" placeholder="请输入场景" style="width: 80%; margin-right: 8px;" readonly />
        <Button type="primary" @click="handleOpenSceneModal" style="width: 20%;">选择场景</Button>
      </template>
      <template #alarmConfigId>
        <Input v-model="alarmConfigName" placeholder="请输入报警配置" style="width: 80%; margin-right: 8px;" readonly />
        <Button type="primary" @click="handleOpenAlarmConfigModal" style="width: 20%;">选择报警配置</Button>
      </template>
    </BasicForm>
    <AlarmConfigModal :style="{ width: '50vw', height: '80vh', maxWidth: 'none', maxHeight: 'none' }"
      class="!w-[50vw] !h-[80vh]">
      <AlarmGrid />
    </AlarmConfigModal>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, ref, nextTick } from 'vue';
import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';
import type { DeepPartial } from '@vben/types';
import { useVbenForm } from '#/adapter/form';
import { Button, Input, message } from 'ant-design-vue';
import { Edit, View } from '#/api/device/iotProductSubEvent';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import { editSchema, sceneGridColumns, alarmConfigGridColumns, type RowType, type AlarmRowType } from './model';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AlarmConfigListByScene, SceneListByProduct } from '#/api/ruleengine/iotScene';

// 报警配置需要的接口 /api/v1/ruleengine/iotAlarmConfig/alarmConfigListByScene

const sceneName = ref('');
const alarmConfigName = ref('');

const emit = defineEmits<{ reload: []; openSceneModal: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
  productKey?: string;
}

const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

// ====================选择场景弹窗=================
const [SceneModal, sceneModalApi] = useVbenModal({
  title: '选择场景',
  onCancel: handleSceneCancel,
  onConfirm: handleSceneConfirm,
});

function handleSceneCancel() {
  sceneModalApi.close();
}

const selectedRecord = ref<RowType>({} as RowType); // 当前选中的场景记录
const selectedAlarmRecord = ref<AlarmRowType>({} as AlarmRowType); // 当前选中的报警配置记录

async function handleSceneConfirm() {
  const selectedRow = sceneGridApi.grid.getRadioRecord() as RowType;
  if (!selectedRow) {
    message.warning('请选择场景');
    return;
  }
  selectedRecord.value = selectedRow;
  console.log('选中的场景数据:', selectedRecord.value);

  // 同步显示值和表单值
  const { sceneName: newSceneName, sceneId } = selectedRecord.value;
  sceneName.value = newSceneName || '未知场景';
  await formApi.setValues({ sceneId });

  selectedAlarmRecord.value = {} as AlarmRowType; // 清空选中记录
  alarmConfigName.value = ''; // 清空显示值
  await formApi.setValues({ alarmConfigId: '' }); // 清空表单中的报警配置ID

  // 强制DOM更新
  await nextTick();
  const inputEl = document.querySelector('input[placeholder="请输入场景"]') as HTMLInputElement;
  if (inputEl) {
    inputEl.value = sceneName.value;
    inputEl.dispatchEvent(new Event('input', { bubbles: true }));
  }
  const inputEl2 = document.querySelector('input[placeholder="请输入报警配置"]') as HTMLInputElement;
  if (inputEl2) {
    inputEl2.value = ''; // 强制设置输入框值
    inputEl2.dispatchEvent(new Event('input', { bubbles: true }));
  }
  sceneModalApi.close();
}

async function handleOpenSceneModal() {
  const values = await formApi.getValues();
  const productKey = values.productKey;
  console.log('productKey', productKey)
  if (!productKey) {
    message.warning('请先填写产品标识');
    return;
  }
  sceneModalApi.open();
}

//==================选择报警配置弹窗====================

const [AlarmConfigModal, alarmConfigModalApi] = useVbenModal({
  title: '选择报警配置',
  onCancel: handleAlarmConfigCancel,
  onConfirm: handleAlarmConfigConfirm,
});

function handleAlarmConfigCancel() {
  alarmConfigModalApi.close();
}

async function handleAlarmConfigConfirm() {
  const selectedRow = alarmGridApi.grid.getRadioRecord() as AlarmRowType;
  // 更新选中记录
  selectedAlarmRecord.value = selectedRow;
  const { alarmName: newAlarmName, alarmConfigId: newAlarmId } = selectedAlarmRecord.value;

  // 同步显示值和表单值
  alarmConfigName.value = newAlarmName || '未知配置';
  await formApi.setValues({ alarmConfigId: newAlarmId });

  // 强制DOM更新
  await nextTick();
  const inputEl = document.querySelector('input[placeholder="请输入报警配置"]') as HTMLInputElement;
  if (inputEl) {
    inputEl.value = alarmConfigName.value;
    inputEl.dispatchEvent(new Event('input', { bubbles: true }));
  }

  alarmConfigModalApi.close();
}

function handleOpenAlarmConfigModal() {
  console.log('打开selectedRecord', selectedRecord.value)
  if (!selectedRecord.value.sceneId) {
    message.warning('请先选择场景');
    return;
  }
  alarmConfigModalApi.open();
}

// 场景
const sceneGridOptions: VxeTableGridOptions<RowType> = {
  radioConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'id',
  },
  columns: sceneGridColumns,
  exportConfig: {},
  height: 800,
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues: Record<string, any>) => {
        const mainFormValues = await formApi.getValues();
        return await SceneListByProduct({
          productKey: mainFormValues.productKey,
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const sceneGridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = sceneGridApi.grid.getCheckboxRecords().length > 0;
}
const [SceneGrid, sceneGridApi] = useVbenVxeGrid({
  gridOptions: sceneGridOptions,
  gridEvents: sceneGridEvents,
});

// 报警配置
const alarmGridOptions: VxeTableGridOptions<AlarmRowType> = {
  radioConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'alarmConfigId',
  },
  columns: alarmConfigGridColumns,
  exportConfig: {},
  height: 800,
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues: Record<string, any>) => {
        return await AlarmConfigListByScene({
          sceneId: selectedRecord.value.sceneId,
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const CheckboxChecked2 = ref(false);
// 单选无需监听复选框变化，移除 checkboxChange 事件（可选）
const alarmGridEvents: DeepPartial<VxeGridListeners> = {
  radioChange: (params: any) => {
    const selectedRow = params.row; // 获取当前选中行
    CheckboxChecked2.value = !!selectedRow; // 更新选中状态
  },
};
const [AlarmGrid, alarmGridApi] = useVbenVxeGrid({
  gridOptions: alarmGridOptions,
  gridEvents: alarmGridEvents,
});

//===================整个页面==========================
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, update, view, productKey } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;

    // 如果是新增且传入了productKey，设置产品标识并设为只读
    if (!update && !view && productKey) {
      await formApi.setValues({ productKey });
      // 设置产品字段为只读
      formApi.updateSchema([
        {
          fieldName: 'productKey',
          componentProps: {
            readonly: true,
            placeholder: '产品标识（由父组件传入）',
          },
        },
      ]);
    }

    if (isUpdate.value || isView.value) {
      const record = await View({ seId: id });
      await formApi.setValues(record);

      console.log('record', record)
      sceneName.value = record.sceneName
      alarmConfigName.value = record.alarmConfigName

      selectedRecord.value = {
        sceneId: record.sceneId,
        sceneName: record.sceneName,
      } as RowType;
      selectedAlarmRecord.value = {
        alarmConfigId: record.alarmConfigId,
        alarmName: record.alarmConfigName,
      } as AlarmRowType;

      await nextTick();
      const inputEl1 = document.querySelector('input[placeholder="请输入场景"]') as HTMLInputElement;
      if (inputEl1) {
        inputEl1.value = sceneName.value; // 强制设置输入框值
        inputEl1.dispatchEvent(new Event('input', { bubbles: true }));
      }

      const inputEl2 = document.querySelector('input[placeholder="请输入报警配置"]') as HTMLInputElement;
      if (inputEl2) {
        inputEl2.value = alarmConfigName.value; // 强制设置输入框值
        inputEl2.dispatchEvent(new Event('input', { bubbles: true }));
      }
    }
    drawerApi.setState({ confirmLoading: false, loading: false })

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    data.sceneId = selectedRecord.value.sceneId
    data.alarmConfigId = selectedAlarmRecord.value.alarmConfigId
    console.log('添加的数据', data)
    await (Edit(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  drawerApi.close();
  // 重置状态
  selectedRecord.value = {} as RowType;
  selectedAlarmRecord.value = {} as AlarmRowType;
  sceneName.value = '';
  alarmConfigName.value = '';

  // 重置产品字段为可编辑状态
  formApi.updateSchema([
    {
      fieldName: 'productKey',
      componentProps: {
        readonly: false,
        placeholder: '请输入产品标识',
      },
    },
  ]);

  await formApi.resetForm();
}

// watch(
//   async () => {
//     if (!formApi?.getValues) return '';
//     const values = await formApi.getValues();
//     return values.productKey;
//   },
//   (productKey) => {
//     sceneButtonDisabled.value = !productKey;
//   },
//   { immediate: true }
// );

</script>
