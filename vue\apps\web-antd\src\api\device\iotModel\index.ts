import { requestClient } from '#/api/request';

// 获取通用物模型列表
export function List(params:any) {
  return requestClient.get<any>('device/iotModel/list', { params });
}

// 删除/批量删除通用物模型
export function Delete(params:any) {
  return requestClient.post<any>('device/iotModel/delete', { ...params });
}

// 添加/编辑通用物模型
export function Edit(params:any) {
  return requestClient.post<any>('device/iotModel/edit', { ...params });
}

// 获取通用物模型指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotModel/view', { params });
}

// 导出通用物模型
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotModel/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}