import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import type { VbenFormSchema } from '@vben/common-ui';


export const modbusRtuFormSchema: VbenFormSchema[] = [
  {
    fieldName: 'subAddr',
    label: '从机地址',
    component: 'Select',
    componentProps: {
      options: Array.from({ length: 255 }, (_, i) => ({
        label: `${i + 1}-0x${(i + 1).toString(16).padStart(2, '0').toUpperCase()}`,
        value: i + 1,
      })),
      placeholder: '请选择从机地址',
      style: { width: '360px' },
      showSearch: true,      // 开启搜索
      allowClear: true,      // 可清空（可选）
      filterOption: (input: string, option: any) => {
        // 支持输入内容与label模糊匹配
        return option.label?.toLowerCase().includes(input.toLowerCase());
      },
    },
  },
  {
    fieldName: 'modbusFunction',
    label: '功能码',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.MODBUS_FUNCTION),
      placeholder: '请选择功能码',
      style: { width: '360px' },
      showSearch: true,      // 开启搜索
      allowClear: true,      // 可清空（可选）
      filterOption: (input: string, option: any) => {
        // 支持输入内容与label模糊匹配
        return option.label?.toLowerCase().includes(input.toLowerCase());
      },
    },
  },
  {
    fieldName: 'useSubAddr',
    label: '使用子设备地址',
    component: 'Switch',
    formItemClass: 'col-span-4', // 独占一行
    componentProps: {
      checkedChildren: '是',
      unCheckedChildren: '否',
      checkedValue: 1,      // 对应字典的“是”
      unCheckedValue: 0,    // 对应字典的“否”
      style: { marginLeft: '4px' , width: '30px' },
    },
  },
  {
    fieldName: 'regAddr',
    label: '起始寄存器地址',
    component: 'Input',
    //限制一下这个输入框，只能输入数字
    componentProps: {
      placeholder: '请输入起始寄存器地址',
      style: { width: '360px' },
      type: 'number',
    },
    renderComponentContent: (param: any) => {
      // 十进制 转十六进制
      let hexAddr = "0000";
      //只有这个值是数字，并且是在 0-65535之间，才进行转换
      if (param.regAddr && !isNaN(param.regAddr) && param.regAddr >= 0 && param.regAddr <= 65535) {
        hexAddr = param.regAddr ? parseInt(param.regAddr, 10).toString(16).toUpperCase() : '0000';
      }
      // 在转换完之后补齐这个六位数，让格式始终是六位数字
      hexAddr = '0x' + hexAddr.padStart(4, '0').toUpperCase();
      return {
        suffix: () => (
          <span style="
          background: #f5f6fa;
          color: #bfbfbf;
          border-radius: 0 4px 4px 0;
          margin-right: -1px;
          display: flex;
          align-items: center;
          height: 100%;
          padding: 0 12px 0 8px;
        ">
            {hexAddr}
          </span>
        ),
      };
    },
  },
  {
    fieldName: 'regCount',
    label: '寄存器个数',
    component: 'InputNumber',
    componentProps: {
      min: 1,
      max: 125,
      placeholder: '请输入寄存器个数',
      style: { width: '360px' },
    },
    dependencies: {
      if(values) {
        // 只有功能码为'3'或'4'和16时显示
        return values.modbusFunction === '3' || values.modbusFunction === '4' || values.modbusFunction === '16';
      },
      componentProps: (values: any, formApi: any) => {
        if (values.modbusFunction == 16) {
          return {
            min: 1,
            onChange: (e: any) => {
              values.regValueList = getRegValueList(e, values.regValueList);
            },
          }
        }
        return {
          min: 1,
          placeholder: '请输入寄存器数量',
          onChange: (e: any) => {
            console.log("regCount updateValue", e);
          },
        }
      },

      triggerFields: ['modbusFunction'],
    },
  },
  {
    fieldName: 'coilCount',
    label: '线圈个数',
    component: 'InputNumber',
    dependencies: {
      show: (values) => {
        // 只有功能码为'1'、'2'或'15'时显示
        return values.modbusFunction === '1' || values.modbusFunction === '2' || values.modbusFunction === '15';
      },
      componentProps: (values, formApi: any) => {
        if (values.modbusFunction == '15') {
          return {
            min: 1,
            max: 200,
            placeholder: '请输入线圈个数',
            style: { width: '360px' },
            onChange: (e: any) => {
              values.coilStatusList = getCoilStatusList(e, values.coilStatusList);
            },
          };
        }
        return {
          min: 1,
          max: 200,
          placeholder: '请输入线圈个数',
          style: { width: '360px' },
          onChange: (e: any) => {
            console.log("coilCount updateValue", e);
          },
        }
      },
      // 只有modbusFunction字段变化时才触发
      triggerFields: ['modbusFunction', 'coilStatusList'],
    },
  },
  {
    fieldName: 'coilValue',
    label: '线圈值',
    component: 'Input',
    componentProps: {
      placeholder: '请输入线圈值',
      style: { width: '360px' },
      type: 'number',
    },
    dependencies: {
      if(values) {
        // 功能码为5时显示
        return values.modbusFunction === '5';
      },
      triggerFields: ['modbusFunction'],
    },
    renderComponentContent: (param: any) => {
      // 十进制 转十六进制
      let hexAddr = "0000";
      //只有这个值是数字，并且是在 0-65535之间，才进行转换
      if (param.regAddr && !isNaN(param.regAddr) && param.regAddr >= 0 && param.regAddr <= 65535) {
        hexAddr = param.regAddr ? parseInt(param.regAddr, 10).toString(16).toUpperCase() : '0000';
      }
      // 在转换完之后补齐这个六位数，让格式始终是六位数字
      hexAddr = '0x' + hexAddr.padStart(4, '0').toUpperCase();
      return {
        suffix: () => (
          <span style="
          background: #f5f6fa;
          color: #bfbfbf;
          border-radius: 0 4px 4px 0;
          margin-right: -1px;
          display: flex;
          align-items: center;
          height: 100%;
          padding: 0 12px 0 8px;
        ">
            {hexAddr}
          </span>
        ),
      };
    },
  },
  {
    fieldName: 'regValue',
    label: '寄存器值',
    component: 'Input',
    componentProps: {
      placeholder: '请输入寄存器值',
      style: { width: '360px' },
      type: 'number',
    },
    dependencies: {
      if(values) {
        // 功能码为6时显示
        return values.modbusFunction === '6';
      },
      triggerFields: ['modbusFunction'],
    },
    renderComponentContent: (param: any) => {
      // 十进制 转十六进制
      let hexAddr = "0000";
      //只有这个值是数字，并且是在 0-65535之间，才进行转换
      if (param.regAddr && !isNaN(param.regAddr) && param.regAddr >= 0 && param.regAddr <= 65535) {
        hexAddr = param.regAddr ? parseInt(param.regAddr, 10).toString(16).toUpperCase() : '0000';
      }
      // 在转换完之后补齐这个六位数，让格式始终是六位数字
      hexAddr = '0x' + hexAddr.padStart(4, '0').toUpperCase();
      return {
        suffix: () => (
          <span style="
          background: #f5f6fa;
          color: #bfbfbf;
          border-radius: 0 4px 4px 0;
          margin-right: -1px;
          display: flex;
          align-items: center;
          height: 100%;
          padding: 0 12px 0 8px;
        ">
            {hexAddr}
          </span>
        ),
      };
    },
  },
  {
    fieldName: 'coilStatusList',
    label: '线圈状态列表',
    component: 'Input',
    formItemClass: 'col-span-4',
    dependencies: {
      show: (values) => {
        // 仅功能码为15时显示
        return values.modbusFunction === '15';
      },
      triggerFields: ['modbusFunction', 'coilCount'],
      componentProps: (values) => {
        // 如果功能码为15，线圈个数不能超过2000
        return {
          placeholder: '',
          onChange: (value: any, formApi: any) => {
            console.log('线圈状态列表变化:', value);
          }
        };
      },
    },
  },
  {
    fieldName: 'regValueList',
    label: '寄存器值列表',
    component: 'Input',
    formItemClass: 'col-span-4',
    dependencies: {
      show: (values) => {
        // 仅功能码为16时显示
        return values.modbusFunction === '16';
      },
      triggerFields: ['modbusFunction', 'regCount'],
      componentProps: (values) => {
        // 如果功能码为16，寄存器个数不能超过125
        return {
          placeholder: '',
          onChange: (value: any, formApi: any) => {
            console.log('寄存器列表变化:', value);
          }
        };
      },
    },
  },
  {
    fieldName: 'dataCheckType',
    label: '数据校验',
    formItemClass: 'col-span-4', // 独占一行
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.DATA_CHECK_TYPE),
      placeholder: '请选择数据校验方式',
      style: { width: '780px' },
    },
  },
  // 预览 Modbus 指令 的组件
  {
    fieldName: 'dataPreview',
    label: '',
    hideLabel: true, // 隐藏标签
    component: 'Input',
    formItemClass: 'col-span-4', // 独占一行
    //想要让这个组件不显示，只是起到一个插槽的作用
  },
  {
    fieldName: 'dataMsg',
    label: 'Modbus 指令预览',
    component: 'Input',
    formItemClass: 'col-span-4',
    componentProps: {
      rows: 2,
      readonly: true,
      style: { background: '#222', color: '#fff', fontFamily: 'monospace', fontSize: '16px' },
    },
  },
];

function getCoilStatusList(coilCount: number, oldStatusList: []) {
  let addedCount = coilCount - oldStatusList.length;
  let updated = [];
  if (addedCount > 0) {
    updated = [...oldStatusList, ...Array(addedCount).fill(false)];
  } else {
    updated = oldStatusList.slice(0, coilCount);
  }
  return updated;
}

function getRegValueList(regCount: number, oldValueList: any[]) {
  let addedCount = regCount - oldValueList.length;
  let updated = [];
  if (addedCount > 0) {
    updated = [...oldValueList, ...Array(addedCount).fill(0)];
  } else {
    updated = oldValueList.slice(0, regCount);
  }
  return updated;
}
