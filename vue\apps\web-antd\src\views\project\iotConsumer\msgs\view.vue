<template>
  <BasicDrawer :close-on-click-modal="false" title="详情" class="w-[600px]">
    <BasicForm />
    <div v-if="isAlarmType" class="p-4">
      <label style="font-size: 18px;">报警信息</label>
      <Divider />
      <AlarmForm :form-model="formModel" />
    </div>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { editSchema, type RowType } from './model';
import { getDictOptions } from '#/utils/dict';
import AlarmForm from './alarmForm.vue';
import { Divider } from 'ant-design-vue';
import { View } from '#/api/project/iotConsumerMsgs';

const isAlarmType = ref(false);
const formModel = ref({});

const getMsgTypeLabel = (value: string | number) => {
  const options = getDictOptions('consumer_msg_type');
  return options.find(item => item.dictValue.toString() === String(value))?.dictLabel || value;
};

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2', 
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4', 
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  async onOpenChange(isOpen) {
    if (!isOpen) return;
    drawerApi.setState({ showConfirmButton: false, loading: true });
    const info = drawerApi.getData();
    console.log('info.msgId', info.msgId)
    const record = await View({ msgId: info.msgId });
    console.log('页面info', record)

    if (record) {
      const processedInfo = {
        ...record,
        msgType: getMsgTypeLabel(record.msgType),
      };
      isAlarmType.value = record.msgType === 101;

      await formApi.setValues(processedInfo);
      // 保存数据到 formModel，供报警表单使用
      formModel.value = processedInfo;
    }
    drawerApi.setState({ loading: false });
  },
});

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
  isAlarmType.value = false;
}
</script>