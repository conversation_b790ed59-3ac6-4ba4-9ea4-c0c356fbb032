import type { VbenFormSchema } from '#/adapter/form';
import type { VxeGridPropTypes } from '#/adapter/vxe-table';

// 设备数据类型定义
export interface RowType {
  deviceId: number;
  deviceKey: string;
  deviceName: string;
  productKey: string;
  productName: string;
  categoryId: number;
  categoryName: string;
  status: string;
  isOnline: string;
  serialNumber: string;
  firmwareVersion: string;
  deviceSecret: string;
  longitude: number;
  latitude: number;
  address: string;
  activeTime: string;
  createTime: string;
  remark: string;
}

// 表格列配置
export const columns: VxeGridPropTypes.Columns<RowType> = [
  {
    type: 'radio',
    width: 50,
    align: 'center',
  },
  {
    title: '设备名称',
    field: 'deviceName',
    minWidth: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    minWidth: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '产品名称',
    field: 'productName',
    minWidth: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '产品分类',
    field: 'categoryName',
    minWidth: 100,
    showOverflow: 'tooltip',
  },
  {
    title: '设备状态',
    field: 'status',
    width: 100,
    align: 'center',
    slots: { default: 'status' },
  },
  {
    title: '在线状态',
    field: 'isOnline',
    width: 100,
    align: 'center',
    cellRender: ({ row }) => {
      const isOnline = row.isOnline === '1';
      return (
        <span class={`inline-block w-2 h-2 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`}></span>
      );
    },
  },
  {
    title: '序列号',
    field: 'serialNumber',
    minWidth: 120,
    showOverflow: 'tooltip',
  },
  {
    title: '固件版本',
    field: 'firmwareVersion',
    minWidth: 100,
    showOverflow: 'tooltip',
  },
  {
    title: '设备地址',
    field: 'address',
    minWidth: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '激活时间',
    field: 'activeTime',
    minWidth: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '创建时间',
    field: 'createTime',
    minWidth: 150,
    showOverflow: 'tooltip',
  },
  {
    title: '操作',
    field: 'action',
    width: 80,
    align: 'center',
    fixed: 'right',
    slots: { default: 'action' },
  },
];

// 搜索表单配置
export const formSchema: VbenFormSchema[] = [
  {
    fieldName: 'deviceName',
    component: 'Input',
    label: '设备名称',
    componentProps: {
      placeholder: '请输入设备名称',
      clearable: true,
    },
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      clearable: true,
    },
  },
  {
    fieldName: 'serialNumber',
    component: 'Input',
    label: '序列号',
    componentProps: {
      placeholder: '请输入序列号',
      clearable: true,
    },
  },
  {
    fieldName: 'status',
    component: 'Select',
    label: '设备状态',
    componentProps: {
      placeholder: '请选择设备状态',
      clearable: true,
      options: [
        { label: '启用', value: '0' },
        { label: '停用', value: '1' },
      ],
    },
  },
];

// 设备状态枚举
export enum DeviceStatus {
  ENABLED = '0',
  DISABLED = '1',
}

// 在线状态枚举
export enum OnlineStatus {
  ONLINE = '1',
  OFFLINE = '0',
}
