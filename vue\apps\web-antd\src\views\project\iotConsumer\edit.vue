<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm></BasicForm>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { md5 } from 'js-md5';
import { useVbenDrawer, z } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Edit, View } from '#/api/project/iotConsumer';
import { editSchema } from './model';
import { List as ListProject } from '#/api/project/iotProject';

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
}

const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

type Option = {
  label: string;
  value: string;
  deviceType?: number | string; // 添加deviceType字段
};
const projectOptions = ref<Option[]>([]);
async function loadProductOptions() {
  const res = await ListProject({
    page: 1,
    pageSize: 1000,
  });
  if (!res || !res.items) {
    projectOptions.value = [];
  } else {
    projectOptions.value = res.items.map((item: any) => ({
      label: item.projectName,
      value: item.projectId,
      deviceType: item.deviceType, // 保存deviceType信息
    }));
  }
  console.log('产品选项加载完成，包含deviceType:', projectOptions.value);
  formApi.updateSchema([
    {
      fieldName: 'projectId',
      component: 'Select',
      label: '项目',
      componentProps: {
        placeholder: '请选择状态',
        onUpdateValue: (e: any) => {
          console.log(e);
        },
        options: projectOptions.value,
        showSearch: true,
        filterOption: (input: any, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
      },
    },
  ]);
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-1',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }

    drawerApi.setState({ confirmLoading: true, loading: true });
    const { id, update, view } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;

    await formApi.resetForm(); // 重置表单所有状态
    //await initFormFields(); // 重新初始化表单字段配置

    if (isUpdate.value || isView.value) {
      const record = await View({ consumerId: id });
      record.status = record.status === '0' ? true : false;
      await formApi.setValues(record);
    }

    if (isUpdate.value) {
      const record = await View({ consumerId: id });
      record.userPassword = '';
      await formApi.setValues(record);

      // 根据模式动态调整密码字段
      formApi.updateSchema([
        {
          component: 'InputPassword',
          fieldName: 'userPassword',
          componentProps: {
            disabled: isUpdate.value || isView.value, // 修改或查看模式下禁用
            placeholder: isUpdate.value ? '修改时不可编辑密码' : '请输入登录密码',
          },
        },
        {
          component: 'InputPassword',
          componentProps: {
            disabled: isUpdate.value || isView.value, // 修改或查看模式下禁用
            placeholder: isUpdate.value ? '修改时不可编辑密码' : '请再次输入密码',
          },
          fieldName: 'confirmPassword',
        },
      ]);
    } else {
      // 根据模式动态调整密码字段
      formApi.updateSchema([
        {
          component: 'InputPassword',
          fieldName: 'userPassword',
          componentProps: {
            disabled: isUpdate.value || isView.value, // 修改或查看模式下禁用
            placeholder: isUpdate.value ? '修改时不可编辑密码' : '请输入登录密码',
          },
        },
        {
          component: 'InputPassword',
          componentProps: {
            disabled: isUpdate.value || isView.value, // 修改或查看模式下禁用
            placeholder: isUpdate.value ? '修改时不可编辑密码' : '请再次输入密码',
          },
          dependencies: {
            rules(values) {
              return z
                .string({ message: '请输入确认密码' })
                .min(5, '密码长度不能少于5个字符')
                .max(20, '密码长度不能超过20个字符')
                .refine(
                  (value: any) => value === values.userPassword,
                  '新密码和确认密码不一致',
                );
            },
            triggerFields: ['confirmPassword'],
          },
          fieldName: 'confirmPassword',
        },
      ]);
    }
    drawerApi.setState({ confirmLoading: false, loading: false });

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          },
        },
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          },
        },
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true });
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }

    const data = cloneDeep(await formApi.getValues());
    data.status = data.status === true ? '0' : '1';

    // 新增逻辑：只在新增模式下加密密码，修改模式不处理密码
    if (!isUpdate.value && data.userPassword) {
      data.userPassword = md5(data.userPassword); // 对密码进行MD5加密
    }
    await Edit(data);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false });
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

onMounted(() => {
  loadProductOptions();
});
</script>