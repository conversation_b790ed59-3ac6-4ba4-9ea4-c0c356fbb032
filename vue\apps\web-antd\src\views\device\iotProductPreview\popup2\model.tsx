import { h, ref } from 'vue';
import { Tag, SelectOption } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public channelId = 0; // ID
  public channelName = ''; // 连接器名称
  public direction = 0; // 桥接方向
  public type = 0; // 通道类型
  public script = ''; // 接口信息
  public status = 0; // 状态
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'channelName',
    component: 'Input',
    label: '连接器名称',
    componentProps: {
      placeholder: '请输入连接器名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: 'ID',
    field: 'channelId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '连接器名称',
    field: 'channelName',
    align: 'left',
    width: -1,
  },
  {
    title: '是否生效',
    field: 'status',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
      },
    },
  },
  {
    title: '状态',
    field: 'connectStatus',
    align: 'left',
    width: -1,
    slots: { default: 'status' },
    // slots: {
    //   default: ({ row }) => {
    //     return renderDict(row.connectStatus, DictEnum.CHANNEL_CONNECT);
    //   },
    // },
  },
  {
    title: '通道类型',
    field: 'type',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.type, DictEnum.CHANNEL_TYPE);
      },
    },
  },
  {
    title: '桥接方向',
    field: 'direction',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.direction, DictEnum.CHANNEL_DIRECTION);
      },
    },
  },
  {
    title: '备注',
    field: 'remark',
    align: 'left',
    width: -1,
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  channelId: number;
  channelName: string;
  direction: number;
  type: number;
  script: string;
  status: string;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
}

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'channelId', label: 'ID' },
  { field: 'channelName', label: '连接器名称' },
  {
    field: 'direction',
    label: '桥接方向',
    render: (_, row: any) => {
      return renderDict(row.direction, DictEnum.CHANNEL_DIRECTION);
    },
  },
  {
    field: 'type',
    label: '通道类型',
    render(_, row: any) {
      return renderDict(row.type, DictEnum.CHANNEL_TYPE);
    },
  },

  {
    field: 'status',
    label: '是否生效',
    render(_, row: any) {
      return renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
    },
  },
  // {
  //   field: 'connectStatus',
  //   label: '状态',
  //   render(_, row: any) {
  //     return renderDict(row.connectStatus, DictEnum.CHANNEL_CONNECT);
  //   },
  // },
  { field: 'createdAt', label: '创建时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'channelId',
    component: 'Input',
    label: 'ID',
    dependencies: { show: () => false, triggerFields: [''] },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'channelName',
    component: 'Input',
    label: '连接器名称',
    componentProps: {
      placeholder: '请输入连接器名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
  },
  {
    fieldName: 'direction',
    component: 'Select',
    label: '桥接方向',
    defaultValue: '2',
    componentProps: {
      placeholder: '请输入桥接方向',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: getDictOptions(DictEnum.CHANNEL_DIRECTION),
    },
    rules: 'required',
  },
  {
    fieldName: 'type',
    component: 'Select',
    label: '通道类型',
    defaultValue: '6',
    componentProps: {
      placeholder: '请输入通道类型',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: getDictOptions(DictEnum.CHANNEL_TYPE),
    },
    rules: 'required',
  },
  {
    fieldName: 'status',
    component: 'Switch',
    label: '状态',
    defaultValue: '1',
    componentProps: {
      style: {
        width: '20px',
      },
      placeholder: '请选择状态',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired',
  },
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
  //Http桥接入口参数
  {
    fieldName: 'httpInput.api',
    component: 'Input',
    label: '桥接入口',
    rules: 'required',
    dependencies: {
      show: (data) => data.type == '4' && data.direction == '1',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '请输入HTTP输入',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  //Http桥接出口参数
  {
    fieldName: 'Option01',
    component: 'Input',
    label: '请求地址',
    // rules: 'required',
    dependencies: {
      show: (data) => data.type == '4' && data.direction == '2',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },

  {
    fieldName: 'httpOutput.method',
    component: 'Select',
    label: '请求方法',
    rules: 'required',
    defaultValue: '1',
    dependencies: {
      show: (data) => data.type == '4' && data.direction == '2',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      // allowClear: true,
      // filterOption: true,
      options: [
        {
          label: 'POST',
          value: 'POST',
        },
        {
          label: 'PUT',
          value: 'PUT',
        },
        {
          label: 'GET',
          value: 'GET',
        },
      ],
      // placeholder: '请选择',
    },
  },

  {
    fieldName: 'httpOutput01',
    component: 'Input',
    label: '请求头',
    defaultValue: [],
    dependencies: {
      show: (data) => data.type == '4' && data.direction == '2',
      triggerFields: ['type', 'direction'],
    },
  },
  {
    fieldName: 'httpOutput02',
    component: 'Input',
    label: '请求参数',
    defaultValue: [],
    dependencies: {
      show: (data) => data.type == '4' && data.direction == '2',
      triggerFields: ['type', 'direction'],
    },
  },
  {
    fieldName: 'httpOutput03',
    component: 'Input',
    label: '请求配置',
    defaultValue: [],
    dependencies: {
      show: (data) => data.type == '4' && data.direction == '2',
      triggerFields: ['type', 'direction'],
    },
  },

  {
    fieldName: 'httpOutput.body',
    component: 'Textarea',
    label: '请求体',
    rules: 'required',
    dependencies: {
      show: (data) => data.type == '4' && data.direction == '2',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '请输入请求体',
    },
  },

  //mqtt桥接参数
  {
    fieldName: 'mqtt.ip',
    component: 'Input',
    label: 'MQTT服务地址',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '1',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '输入例：mqtt.example.com:1883',
      addonBefore: 'tcp://',
    },
  },

  {
    fieldName: 'mqtt.port',
    component: 'Input',
    label: '连接端口',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '1',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '输入例连接端口',
    },
  },

  {
    fieldName: 'mqtt.clientId',
    component: 'Input',
    label: '客户端id',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '1',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '请输入客户端ID',
    },
  },
  {
    fieldName: 'mqtt.user',
    component: 'Input',
    label: '用户名',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '1',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '请输入用户名',
    },
  },
  {
    fieldName: 'mqtt.pwd',
    component: 'Input',
    label: '密码',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '1',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '请输入密码',
    },
  },
  {
    fieldName: 'mqtt.topic',
    component: 'Input',
    label: '数据输入主题',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '1',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '请输入数据输入主题',
    },
  },

  //tcp或udp桥接参数
  {
    fieldName: 'tcpUdp.ip',
    component: 'Input',
    label: '连接地址',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '2' || data.type == '3',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '输入例连接地址',
      // addonBefore: 'tcp://',
    },
  },

  {
    fieldName: 'tcpUdp.port',
    component: 'Input',
    label: '连接端口',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '2' || data.type == '3',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '输入例连接端口',
    },
  },

  {
    fieldName: 'tcpUdp.user',
    component: 'Input',
    label: '用户名',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '2' || data.type == '3',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '请输入用户名',
    },
  },

  {
    fieldName: 'tcpUdp.pwd',
    component: 'Input',
    label: '密码',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '2' || data.type == '3',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '请输入密码',
    },
  },

  //Websocket桥接参数

  {
    fieldName: 'ws.url',
    component: 'Input',
    label: '连接地址',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '5',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '输入例连接地址',
    },
  },
  {
    fieldName: 'ws.token',
    component: 'Input',
    label: '认证Token',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '5',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '输入认证Token',
    },
  },
  {
    fieldName: 'ws.tag',
    component: 'Input',
    label: '订阅标签',
    dependencies: {
      if: (data) => data.type == '5',
      triggerFields: ['type', 'direction'],
    },
    componentProps: {
      placeholder: '输入订阅标签',
    },
  },

  //数据库桥接参数
  {
    fieldName: 'database.type',
    component: 'Select',
    label: '数据库类型',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '6',
      triggerFields: ['type'],
    },
    componentProps: {
      options: [{ label: '关系型数据库', value: 'rdb' }],

      placeholder: '请选择数据库类型',
    },
  },

  {
    fieldName: 'database.source',
    component: 'Select',
    label: '数据源',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '6',
      triggerFields: ['type'],
    },
    componentProps: {
      options: [{ label: 'mysql', value: 'mysql' }],

      placeholder: '请选择数据源',
    },
  },

  {
    fieldName: 'database.address',
    component: 'Input',
    label: '数据库地址',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '6',
      triggerFields: ['type'],
    },
    componentProps: {
      placeholder: '请输入数据库地址',
    },
  },
  {
    fieldName: 'database.user',
    component: 'Input',
    label: '用户名',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '6',
      triggerFields: ['type'],
    },
    componentProps: {
      placeholder: '请输入用户名',
    },
  },
  {
    fieldName: 'database.pwd',
    component: 'Input',
    label: '密码',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '6',
      triggerFields: ['type'],
    },
    componentProps: {
      placeholder: '请输入密码',
    },
  },
  {
    fieldName: 'database.schema',
    component: 'Input',
    label: '数据库名称',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '6',
      triggerFields: ['type'],
    },
    componentProps: {
      placeholder: '请输入数据库名称',
    },
  },
  {
    fieldName: 'database.sql',
    component: 'Textarea',
    label: 'SQL',
    rules: 'required',
    dependencies: {
      if: (data) => data.type == '6',
      triggerFields: ['type'],
    },
    componentProps: {
      placeholder: '请输入SQL语句',
    },
  },
];
