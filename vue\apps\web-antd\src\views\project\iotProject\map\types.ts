// 天地图相关类型定义

export interface TiandituMapProps {
  width?: string;
  height?: string;
  center?: [number, number]; // [经度, 纬度]
  zoom?: number;
  mapType?: 'vec' | 'img' | 'ter'; // 矢量、影像、地形
  showControls?: boolean;
  apiKey?: string;
}

export interface TiandituMapEmits {
  mapReady: [map: any];
  mapClick: [event: any];
  mapMoveEnd: [event: any];
  zoomEnd: [event: any];
}

// 位置选择结果接口
export interface LocationResult {
  lng: number;
  lat: number;
  address?: string;
}

export interface TiandituMapInstance {
  getMapInstance: () => any;
  getTiandituAPI: () => any;
  setMapType: (type: 'vec' | 'img' | 'ter') => void;
  zoomIn: () => void;
  zoomOut: () => void;
  resetView: () => void;
}

// 天地图API全局类型声明
declare global {
  interface Window {
    T: any;
  }
}

// 地图点位信息
export interface MapMarker {
  id: string | number;
  lng: number;
  lat: number;
  title?: string;
  content?: string;
  icon?: string;
  data?: any;
}

// 地图事件类型
export interface MapClickEvent {
  lnglat: {
    lng: number;
    lat: number;
  };
  point: {
    x: number;
    y: number;
  };
  target: any;
}

export interface MapMoveEvent {
  target: any;
  type: string;
}

export interface MapZoomEvent {
  target: any;
  type: string;
  zoom: number;
}

// 设备位置信息
export interface DeviceLocation {
  deviceId: string | number;
  deviceName: string;
  lng: number;
  lat: number;
  status?: 'online' | 'offline' | 'alarm' | 'inactive';
  lastUpdateTime?: string;
  data?: any;
}
