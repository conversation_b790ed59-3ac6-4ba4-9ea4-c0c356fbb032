<script setup lang="ts">
import { ref, onMounted, watch, h } from 'vue';
import { useVbenForm } from '#/adapter/form';
import { View } from '#/api/project/iotProject';
import { message, Button, Input, Divider } from 'ant-design-vue';
import { IconifyIcon } from '@vben/icons';

interface Props {
  projectId: any;
}

const props = defineProps<Props>();

// 项目详情数据
const projectData = ref<any>({});

// 复制到剪贴板功能
const copyToClipboard = async (value: string, fieldName: string) => {
  try {
    if (!value) {
      message.warning('没有可复制的内容');
      return;
    }

    // 使用现代浏览器的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(value);
      message.success(`${fieldName}已复制到剪贴板`);
    } else {
      // 降级方案：使用传统的 document.execCommand
      const textArea = document.createElement('textarea');
      textArea.value = value;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        message.success(`${fieldName}已复制到剪贴板`);
      } catch (err) {
        message.error('复制失败，请手动复制');
      } finally {
        document.body.removeChild(textArea);
      }
    }
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败');
  }
};

// 复制Access Key
const copyAccessKey = async () => {
  const values = await basicFormApi.getValues();
  await copyToClipboard(values.accessKey, 'Access Key');
};

// 复制Secret Key
const copySecretKey = async () => {
  const values = await basicFormApi.getValues();
  await copyToClipboard(values.secretKey, 'Secret Key');
};

// 基本信息表单配置（包含所有信息）
const basicInfoSchema = [
  {
    fieldName: 'projectId',
    label: '项目ID',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'projectName',
    label: '项目名称',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'regionName',
    label: '所属区域',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'contactUser',
    label: '联系人',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'contactPhone',
    label: '联系电话',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'longitude',
    label: '经度',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'latitude',
    label: '纬度',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'status',
    label: '项目状态',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'address',
    label: '项目地址',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'accessKey',
    label: 'Access Key',
    component: 'Input',
    componentProps: {
      type: 'password',
      readonly: true,
      disabled: false,
      placeholder: ' ',
      addonAfter: h(Button, {
        type: 'text',
        size: 'small',
        icon: h(IconifyIcon, {
          icon: 'mdi:content-copy',
          style: { fontSize: '14px' }
        }),
        onClick: () => copyAccessKey(),
        style: {
          border: 'none',
          boxShadow: 'none',
          padding: '0 8px',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }
      })
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'secretKey',
    label: 'Secret Key',
    component: 'Input',
    componentProps: {
      type: 'password',
      readonly: true,
      disabled: false,
      placeholder: ' ',
      addonAfter: h(Button, {
        type: 'text',
        size: 'small',
        icon: h(IconifyIcon, {
          icon: 'mdi:content-copy',
          style: { fontSize: '14px' }
        }),
        onClick: () => copySecretKey(),
        style: {
          border: 'none',
          boxShadow: 'none',
          padding: '0 8px',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }
      })
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'callbackUrl',
    label: '回调地址',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'createdAt',
    label: '创建时间',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'updatedAt',
    label: '更新时间',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'createdByName',
    label: '创建人',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'updatedByName',
    label: '更新人',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'remark',
    label: '备注',
    component: 'Input',
    componentProps: {
      readonly: true,
      disabled: false,
      placeholder: ' ',
    },
    formItemClass: 'col-span-2',
  },
];



// 创建基本信息表单实例
const [BasicForm, basicFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
  },
  layout: 'horizontal',
  schema: basicInfoSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-6 gap-x-4',
});

// 加载项目详情数据
const loadProjectDetail = async () => {
  if (!props.projectId) {
    console.warn('projectId为空，无法加载项目详情');
    return;
  }

  try {
    const response = await View({ projectId: props.projectId });
    if (response) {
      projectData.value = response;

      // 处理所有数据（合并基本信息和系统信息）
      const allData = {
        projectId: response.projectId,
        projectName: response.projectName,
        regionName: response.regionName,
        contactUser: response.contactUser,
        contactPhone: response.contactPhone,
        longitude: response.longitude ? response.longitude.toFixed(6) : '',
        latitude: response.latitude ? response.latitude.toFixed(6) : '',
        status: response.status === '0' ? '启用' : '禁用',
        address: response.address,
        accessKey: response.accessKey,
        secretKey: response.secretKey,
        callbackUrl: response.callbackUrl,
        createdAt: response.createdAt,
        updatedAt: response.updatedAt,
        createdByName: response.createdBySumma?.nickName || '',
        updatedByName: response.updatedBySumma?.nickName || '',
        remark: response.remark || '',
      };

      // 更新表单数据
      await basicFormApi.setValues(allData);

      console.log('项目详情加载成功:', response);
    } else {
      message.error('获取项目详情失败');
    }
  } catch (error) {
    console.error('加载项目详情失败:', error);
    message.error('加载项目详情失败');
  }
};

// 监听projectId变化
watch(
  () => props.projectId,
  (newProjectId) => {
    if (newProjectId) {
      loadProjectDetail();
    }
  },
  { immediate: true }
);

onMounted(() => {
  if (props.projectId) {
    loadProjectDetail();
  }
});

// 暴露方法给父组件调用
defineExpose({
  loadProjectDetail
});
</script>

<template>
  <div class="p-4">
    <!-- 基本信息 -->
    <label style="font-size: 18px;">基本信息</label>
    <Divider />
    <BasicForm class="gap-[8px]"></BasicForm>
  </div>
</template>

<style scoped>
.p-4 {
  padding: 16px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mb-4 {
  margin-bottom: 16px;
}

.text-lg {
  font-size: 18px;
}

.font-medium {
  font-weight: 500;
}
</style>