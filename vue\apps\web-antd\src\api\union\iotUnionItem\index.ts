import { requestClient } from '#/api/request';

// 获取组合子设备表列表
export function List(params:any) {
  return requestClient.get<any>('union/iotUnionItem/list', { params });
}

// 删除/批量删除组合子设备表
export function Delete(params:any) {
  return requestClient.post<any>('union/iotUnionItem/delete', { ...params });
}

// 添加/编辑组合子设备表
export function Edit(params:any) {
  return requestClient.post<any>('union/iotUnionItem/edit', { ...params });
}

// 获取组合子设备表指定详情
export function View(params:any) {
  return requestClient.get<any>('union/iotUnionItem/view', { params });
}

// 导出组合子设备表
export function Export(params:any) {
  return requestClient.post<Blob>('/union/iotUnionItem/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}

// 整体修改组合设备的子设备列表
export function EditList(params:any) {
  return requestClient.post<any>('/union/iotUnionItem/listEdit', { ...params });
}
