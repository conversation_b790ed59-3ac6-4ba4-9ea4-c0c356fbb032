<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { Page } from '@vben/common-ui';
import { Card, Tag, Tabs, TabPane, Button, Divider ,} from 'ant-design-vue';
import dayjs from 'dayjs';
import { View } from '#/api/manage/iotFirmware';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';

const route = useRoute();

// 固件数据
const firmwareData = ref<any>(null);
const loading = ref(false);
const currentTab = ref('a');

// 获取固件详情
async function loadFirmwareDetail() {
  try {
    loading.value = true;
    console.log(' view.vue 页面开始加载');
    console.log(' 当前路由信息:', route);
    console.log(' 路由查询参数:', route.query);

    const firmwareId = route.query.firmwareId;
    console.log(' 获取到的固件ID:', firmwareId);

    if (!firmwareId) {
      console.error(' 固件ID不存在，路由参数:', route.query);
      return;
    }

    console.log(' 准备调用API获取固件详情，参数:', { firmwareId });
    const data = await View({ firmwareId });
    console.log(' API返回的固件数据:', data);

    firmwareData.value = data;

    // 更新页面标题
    document.title = `${data.firmwareName || '固件详情'} - 固件管理`;
    console.log('固件详情加载成功');

  } catch (error) {
    console.error(' 获取固件详情失败:', error);
  } finally {
    loading.value = false;
  }
}

// 格式化日期
const formatDate = (date: string) => {
  return date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : '-';
};

// 获取文件名
const getFileName = (url: string) => {
  return url ? url.split('/').pop() || url : '';
};

onMounted(() => {
  console.log('🚀 view.vue 组件已挂载');
  console.log('🚀 当前完整路由:', window.location.href);
  loadFirmwareDetail();
});
</script>

<template>
  <Page auto-content-height>
    <!-- 顶部信息卡片 - 仿照 iotProductPreview 的样式 -->
    <Card class="mb-2" v-if="firmwareData">
      <!-- 固件基本信息一行显示 -->
      <div class="firmware-header-info">
        <label style="font-size: 24px;">固件：{{ firmwareData.firmwareName || '未命名固件' }}</label>
        <label class="ml-6">版本：{{ firmwareData.firmwareVersion || '-' }}</label>
        <label class="ml-6">类型：{{ renderDict(firmwareData.firmwareType, DictEnum.FIRMWARE_TYPE) || '-' }}</label>
        <label class="ml-6">状态：</label>
        <Tag :color="firmwareData.isLatest ? 'green' : 'default'" class="ml-1">
          {{ firmwareData.isLatest ? '最新版本' : '历史版本' }}
        </Tag>
      </div>

      <!-- 第二行信息 -->
      <div class="firmware-header-info mt-3">
        <label>产品：{{ firmwareData.productName || '-' }}</label>
        <label class="ml-6">产品标识：{{ firmwareData.productKey || '-' }}</label>
        <label class="ml-6">创建时间：{{ formatDate(firmwareData.createdAt) }}</label>
        <label class="ml-6">文件：</label>
        <a v-if="firmwareData.fileUrl" :href="firmwareData.fileUrl" target="_blank" class="file-link ml-1">
          {{ getFileName(firmwareData.fileUrl) }}
        </a>
        <span v-else class="ml-1">-</span>
      </div>

      <!-- 第三行信息 -->
      <div class="firmware-header-info mt-3" v-if="firmwareData.remark">
        <label>固件描述：{{ firmwareData.remark }}</label>
      </div>
    </Card>

    <!-- 主要内容区域 -->
    <Card class="mt-2">
      
    </Card>
  </Page>
</template>

<style lang="less" scoped>
.firmware-header-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  label {
    font-size: 16px;
    color: #262626;
  }
}

.file-link {
  color: #1890ff;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

.detail-info {
  .info-row {
    display: flex;
    margin-bottom: 16px;
    gap: 32px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-item {
    flex: 1;
    display: flex;
    align-items: center;

    &.full-width {
      flex: 3;
    }

    .label {
      color: #666;
      font-size: 14px;
      margin-right: 8px;
      white-space: nowrap;
      min-width: 80px;
    }

    .value {
      color: #262626;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.version-history,
.device-usage {
  color: #666;
  text-align: center;
  padding: 40px 0;
}
</style>