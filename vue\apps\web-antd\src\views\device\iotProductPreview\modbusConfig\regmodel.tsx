import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public modbusId = 0; // 寄存器ID
  public productKey = ''; // 产品标识
  public modelKey = ''; // 模型标识
  public address = 0; // 寄存器地址
  public subAddr = 1; // 从机地址
  public isReadonly = 1; // 是否只读
  public dataType = null; // 数据类型
  public byteOrder = null; // 字节序
  public ratio = 1; // 系数（最大支持4位小数）
  public offset = 0; // 偏移
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}


// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '',
    field: 'modbusId',
    align: 'left',
    width: 50,
    type: 'checkbox',
    slots: {
      default: ({ row }) => {
        return '';
      }
    },
  },
  {
    title: '模型标识',
    field: 'modelKey',
    align: 'left',
    width: -1,
  },
  {
    title: '寄存器地址',
    field: 'address',
    align: 'left',
    width: -1,
  },
  {
    title: '从机地址',
    field: 'subAddr',
    align: 'left',
    width: -1,
  },
  {
    title: '是否只读', field: 'isReadonly', align: 'left', width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.isReadonly, DictEnum.IOT_YES_NO);
      }
    },
  },
  {
    title: '数据类型', field: 'dataType', align: 'left', width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.dataType, DictEnum.REG_DATA_TYPE);
      }
    },
  },
  {
    title: '字节序', field: 'byteOrder', align: 'left', width: -1,
    slots: {
      default: ({ row }) => {
        if (['uint16', 'int16'].includes(row.dataType)) {
          return renderDict(row.byteOrder, DictEnum.REG_BYTE_ORDER_16);
        } else if (['uint32', 'int32', 'float32'].includes(row.dataType)) {
          return renderDict(row.byteOrder, DictEnum.REG_BYTE_ORDER_32);
        }
        return '';
      }
    },
  },
  {
    title: '系数（最大支持4位小数）',
    field: 'ratio',
    align: 'left',
    width: -1,
  },
  // {
  //   title: '偏移',
  //   field: 'offset',
  //   align: 'left',
  //   width: -1,
  // },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  modbusId: number;
  productKey: string;
  modelKey: string;
  address: number;
  subAddr: number;
  isReadonly: number;
  dataType: string;
  byteOrder: string;
  ratio: number;
  offset: number;
  tenantId: string;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'modbusId', label: '寄存器ID' },
  { field: 'productKey', label: '产品标识' },
  { field: 'modelKey', label: '模型标识' },
  { field: 'address', label: '寄存器地址' },
  { field: 'subAddr', label: '从机地址' },
  {
    field: 'isReadonly',
    label: '是否只读',
    render(row: any) {
      return renderDict(row.isReadonly, 'iot_yes_no');
    },
  },
  {
    field: 'dataType',
    label: '数据类型',
    render(row: any) {
      return renderDict(row.dataType, 'reg_data_type');
    },
  },
  {
    field: 'byteOrder',
    label: '字节序',
    render(row: any) {
      return renderDict(row.byteOrder, 'reg_byte_order_16');
    },
  },
  { field: 'ratio', label: '系数（最大支持4位小数）' },
  { field: 'offset', label: '偏移' },
  { field: 'tenantId', label: '租户ID' },
  { field: 'createdAt', label: '创建时间' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'modbusId',
    component: 'Input',
    label: '寄存器ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'modelKey',
    component: 'Input',
    label: '模型标识',
    componentProps: {
      placeholder: '请输入模型标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'address',
    component: 'InputNumber',
    label: '寄存器地址',
    componentProps: {
      placeholder: '请输入寄存器地址',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({ required_error: '请输入寄存器地址', invalid_type_error: '无效数字' })
  },
  {
    fieldName: 'subAddr',
    component: 'InputNumber',
    label: '从机地址',
    defaultValue: 0,
    componentProps: {
      placeholder: '请输入从机地址',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({ required_error: '请输入从机地址', invalid_type_error: '无效数字' })
  },
  {
    fieldName: 'isReadonly',
    component: 'Select',
    label: '是否只读',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择是否只读',
      options: getDictOptions('iot_yes_no'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired'
  },
  {
    fieldName: 'dataType',
    component: 'Select',
    label: '数据类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择数据类型',
      options: getDictOptions('reg_data_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired'
  },
  {
    fieldName: 'byteOrder',
    component: 'Select',
    label: '字节序',
    dependencies: { 
      componentProps: (values: any)=>{
        const dataType = values.dataType;
        return {
          placeholder: '请选择字节序',
          options: dataType == 'uint16' || dataType == 'int16' ? getDictOptions(DictEnum.REG_BYTE_ORDER_16) : getDictOptions(DictEnum.REG_BYTE_ORDER_32),
          defaultValue: dataType == 'uint16' || dataType == 'int16' ? 'AB' : 'ABCD',
        }
      }, 
      triggerFields: ['dataType'], 
    },
    rules: 'selectRequired'
  },
  {
    fieldName: 'ratio',
    component: 'InputNumber',
    label: '系数（最大支持4位小数）',
    defaultValue:1,
    componentProps: {
      placeholder: '请输入系数（最大支持4位小数）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({ required_error: '请输入系数（最大支持4位小数）', invalid_type_error: '无效数字'})
  },
  {
    fieldName: 'offset',
    component: 'InputNumber',
    label: '偏移',
    defaultValue: 0,
    componentProps: {
      placeholder: '请输入偏移',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
    rules: z.number({ required_error: '请输入偏移', invalid_type_error: '无效数字' })
  },
];