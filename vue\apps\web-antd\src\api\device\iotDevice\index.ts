import { requestClient } from '#/api/request';

// 获取设备表列表
export function List(params: any) {
  return requestClient.get<any>('device/iotDevice/list', { params });
}

// 删除/批量删除设备表
export function Delete(params: any) {
  return requestClient.post<any>('device/iotDevice/delete', { ...params });
}

// 添加/编辑设备表
export function Edit(params: any) {
  return requestClient.post<any>('device/iotDevice/edit', { ...params });
}

// 修改设备表状态
export function Status(params: any) {
  return requestClient.post<any>('device/iotDevice/status', { ...params });
}

// 获取设备表指定详情
export function View(params: any) {
  return requestClient.get<any>('device/iotDevice/view', { params });
}

// 导出设备表
export function Export(params: any) {
  return requestClient.post<Blob>('/device/iotDevice/export', { ...params }, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}

// 查询设备详情
export function Detail(params: any) {
  return requestClient.get<any>('device/iotDevice/detail', { params });
}

// 获取设备属性趋势
export function PropertyTrend(params: any) {
  return requestClient.get<any>('device/iotDevice/propertyTrend', { params });
}

// 设置设备属性
export function SetProperty(params: any) {
  return requestClient.post<any>('device/iotDevice/propertySet', { ...params });
}

// 获取设备属性
export function GetProperty(params: any) {
  return requestClient.post<any>('device/iotDevice/propertyGet', { ...params });
}

// 获取设备属性历史数据
export function PropertyHistPage(params: any) {
  return requestClient.get<any>('device/iotDevice/propertyHistory', { params });
}

// 获取设备列表-不分页
export function ListNoPage(params: any) {
  return requestClient.get<any>('device/iotDevice/listNoPage', { params });
}

// 获取设备属性最新数据
export function PropertyLatest(params: any) {
  return requestClient.get<any>('device/iotDevice/propertyLatest', { params });
}

// 导出设备属性历史数据
export function ExportPropertyHist(params: any) {
  return requestClient.post<Blob>('/device/iotDevice/exportPropertyHistory', { ...params }, {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}
// 设备功能调用
export function FunctionDo(params: any) {
  return requestClient.post<any>('device/iotDevice/functionDo', { ...params });
}

// 从监控服务器获取监控设备详情
export function GB28181DeviceInfo(params: any) {
  return requestClient.get<any>('device/iotDevice/gb28181DeviceInfo', { params });
}

//获取视频流播放地址
export function GB28181StartReview(params: any) {
  return requestClient.get<any>('device/iotDevice/gb28181StartReview', { params });
}

//获取监控设备绑定表列表
export function CameraBindList(params: any) {
  return requestClient.get<any>('device/iotCameraBind/list', { params });
}
//新增监控设备绑定
export function EditCameraBind(params: any) {
  return requestClient.post<any>('device/iotCameraBind/edit', { ...params });
}
//删除监控设备绑定
export function DeleteCameraBind(params: any) {
  return requestClient.post<any>('device/iotCameraBind/delete', { ...params });
}

//GB28181设备本地录像列表
export function GB28181LocalVideoList(params: any) {
  return requestClient.get<any>('device/iotDevice/gb28181LocalVideoList', { params });
}

//GB28181设备本地录像回放
export function GB28181LocalVideoPlayback(params: any) {
  return requestClient.get<any>('device/iotDevice/gb28181LocalVideoPlayback', { params });
}
//GB28181设备视频流停止 /api/v1/device/iotDevice/gb28181Stop
export function GB28181Stop(params: any) {
  return requestClient.get<any>('device/iotDevice/gb28181Stop', { params });
}

//GB28181设备云台控制
export function GB28181Ptz(params: any) {
  return requestClient.get<any>('device/iotDevice/gb28181Ptz', {
    params: { ...params },
  });
}
