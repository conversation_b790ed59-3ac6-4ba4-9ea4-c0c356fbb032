<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm></BasicForm>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, ref, reactive } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep, addFullName } from '@vben/utils';
import { getSysDeptTreeApi } from '#/api/system/dept';
import { useVbenForm } from '#/adapter/form';
import { Edit, List } from '#/api/device/iotBatchOperation';
import { List as ProductList } from '#/api/device/iotProduct';
import { List as ProjectList } from '#/api/project/iotProject';
import { editSchema } from './model';

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  view: boolean;
}

const isView = ref(false);

// 统一管理所有选项数据
const options = reactive({
  product: [] as Array<{ label: string; value: string }>,
  project: [] as Array<{ label: string; value: string }>,
});

// 加载产品选项
async function loadProductOptions() {
  try {
    const response = await ProductList({
      page: 1,
      pageSize: 1000,
    });
    if (response && response.items) {
      options.product = response.items.map((item: any) => ({
        label: `${item.productName}`,
        value: item.productKey,
      }));
    }
  } catch (error) {
    console.error('加载产品选项失败:', error);
  }
}

// 加载项目选项
async function loadProjectOptions() {
  try {
    const response = await ProjectList({ page: 1, pageSize: 1000 });
    if (response && response.items) {
      options.project = response.items.map((item: any) => ({
        label: item.projectName,
        value: item.projectId,
      }));
    }
  } catch (error) {
    console.error('加载项目选项失败:', error);
  }
}

async function getDeptTree() {
  const treeRes = await getSysDeptTreeApi();
  const treeData = treeRes.items;
  addFullName(treeData, 'deptName', ' / ');
  return treeData;
}
//加载机构选项
async function loadDeptOptions() {
  const treeData = await getDeptTree();
  formApi.updateSchema([
    {
      componentProps: {
        fieldNames: { label: 'deptName', value: 'deptId' },
        showSearch: true,
        treeData: treeData,
        treeDefaultExpandAll: true,
        treeLine: { showLeafIcon: false },
        treeNodeLabelProp: 'fullName',
        // 选中后显示在输入框的值
        displayRender: (label: any, selected: any, node: any) => {
          // node.props.dataRef 即为选中节点数据
          return node.props.dataRef.fullName || label;
        },
      },
      fieldName: 'targetDept',
    },
  ]);
}

// 加载所有选项数据（只加载产品和项目）
async function loadAllOptions() {
  try {
    await Promise.all([
      loadProductOptions(),
      loadProjectOptions(),
    ]);
  } catch (error) {
    console.error('加载选项数据时发生错误:', error);
    // 确保即使出错也不会阻塞页面
  }
}

// 更新表单schema中的下拉选项（去掉 targetDept，下拉由 TreeSelect 动态更新）
function updateSchemaOptions() {
  formApi.updateSchema([
    {
      fieldName: 'productKey',
      component: 'Select',
      label: '产品',
      componentProps: {
        placeholder: '请选择产品',
        options: options.product,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        onUpdateValue: (e: any) => {
          console.log('产品选择:', e);
        },
      },
    },
    {
      fieldName: 'projectId',
      component: 'Select',
      label: '接收项目',
      componentProps: {
        placeholder: '请选择接收项目',
        options: options.project,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        onUpdateValue: (e: any) => {
          console.log('接收项目选择:', e);
        },
      },
    }
  ]);
}

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true });

    const { view } = drawerApi.getData() as ModalProps;
    isView.value = view;
    await loadDeptOptions();
    // 加载所有下拉选项数据
    await loadAllOptions();

    // 更新表单schema中的下拉选项
    updateSchemaOptions();

    drawerApi.setState({ confirmLoading: false, loading: false });

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());

    // 处理设备标识字段：将多行文本转换为数组，并映射字段名
    if (data.deviceKeys && typeof data.deviceKeys === 'string') {
      // 按回车分割，过滤空行，去除首尾空格
      data.deviceKeyList = data.deviceKeys
        .split('\n')
        .map(item => item.trim())
        .filter(item => item.length > 0);

      // 删除原字段，因为后端期望的是 deviceKeyList
      delete data.deviceKeys;
    } else if (data.deviceKeys && Array.isArray(data.deviceKeys)) {
      // 如果已经是数组，直接映射字段名
      data.deviceKeyList = data.deviceKeys;
      delete data.deviceKeys;
    }

    // 只保留最终提交数据的打印
    console.log('提交的数据:', data);

    await (Edit(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

</script>
