<script lang="ts" setup>
import { h, ref, computed, onMounted } from 'vue';
import {
  Button,
  message,
  Modal,
  Popconfirm,
  Tag,
} from 'ant-design-vue';


import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import {
 
  Export,
  
  Status,
  TestConnect ,
  
  View
} from '#/api/ruleengine/iotChannel';
import { List,Edit,Delete,} from '#/api/device/iotProductEnter';

import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, Layoutss, querySchema, type RowType } from './model';
//import editModal from './edit.vue';
import viewModal from './popup2/view.vue';
import popupModal from './popup1/index.vue'; // 引入新组件
const props = defineProps({
  productKey: { type: String, required: true },
  productName: { type: String, required: true },
});

const formOptions: VbenFormProps = {
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  showCollapseButton: true,
  submitOnChange: true,
  submitOnEnter: false,
};

const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: { highlight: true, labelField: 'channelId' },
  rowConfig: { keyField: 'channelId' },
  columns: Layoutss, // 假设 Layoutss 中已定义状态列的渲染逻辑
  exportConfig: {},
  height: 600,
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          productKey: props.productKey,
        });
        
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
  
};

const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};

const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}

const [Grid, gridApi] = useVbenVxeGrid({
  //formOptions,
  gridClass: 'p-0 ',
  gridOptions,
  gridEvents,
});

const [ViewModal, modalApi] = useVbenModal({
  connectedComponent: viewModal,
});
const openViewModal = (channelId: string) => {
  modalApi.setData({ channelId }); // 传递channelId
  modalApi.open(); // 打开模态框
};
const handleTest = async (row: RowType) => {
  await TestConnect({ channelId: row.channelId });
  message.success('连接成功');
};


const productOptions = ref([
  { label: props.productName, value: props.productKey },
]);

onMounted(() => {
  gridApi.formApi.updateSchema([
    {
      fieldName: 'productKey',
      component: 'Select',
      label: '所属产品',
      componentProps: {
        placeholder: '请选择产品',
        options: productOptions.value,
        value: props.productKey,
        disabled: true,
      },
    },
  ]);
});

function handlePreview(record: RowType) {
  modalApi.setData({ record });
  modalApi.open();
}

const [PopupModal, popupModalApi] = useVbenModal({
  connectedComponent: popupModal,

});

function handleAdd() {
  popupModalApi.setData({ update: false, view: false });
  popupModalApi.open();
  const dataToPass = {
    message: '', 
  };
  popupModalApi.setData({ 
    update: false, 
    view: false,
    productKey: dataToPass 
  });
  popupModalApi.open();  
}

async function handleDelete(row: RowType) {
  await Delete({ channelId: [row.channelId] });
  message.success('删除成功');
  await handleRefresh();
}

async function handleRefresh() {
  await gridApi.query();
}

function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const ids = rows.map(row => row.channelId);
  if (ids.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await Delete({ channelId: ids });
      message.success('删除成功');
      await handleRefresh();
    },
  });
}

async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '资源通道', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success('导出成功');
}
async function handleStatusChange(row: RowType) {
  // await Status({ id: row.id, status: row.status });
  await Status({ scriptId: row.scriptId, status: row.status });
  await message.success('操作成功');
  await handleRefresh();
}
</script>

<template>
  <div class="p-0 m-0 ">
    <Grid table-title="资源通道">
      <template #toolbar-tools>
        <Button
          class="mr-2 flex items-center"
          type="primary"
          :icon="h(MdiPlus)"
          @click="handleAdd"
          v-access:code="'cpm:ruleengine:iotChannel:edit'"
        >
          新增
        </Button>
        <Button
          class="mr-2 flex items-center"
          type="primary"
          :disabled="!CheckboxChecked"
          :icon="h(MdiDelete)"
          @click="handleMultiDelete"
          v-access:code="'cpm:ruleengine:iotChannel:delete'"
        >
          删除
        </Button>
        <Button
          class="mr-2 flex items-center"
          type="primary"
          :icon="h(MdiExport)"
          @click="handleExport"
          v-access:code="'cpm:ruleengine:iotChannel:export'"
        >
          导出
        </Button>
      </template>

      <!-- 状态列纯展示逻辑 -->
      <template #status="{ row }">
        <span
          :class="row.status === '0'? 'text-green-500' : 'text-red-500'"
          style="white-space: nowrap;"
        >
          {{ row.status === '0'? '已链接' : '未链接' }}
        </span>
      </template>

      <template #action="{ row }">
        <div class="flex items-center">
          <Button
            class="mr-2 border-none p-0"
            type="link"
            @click="handlePreview(row)"
            v-access:code="'cpm:ruleengine:iotChannel:view'"
          >
            查看
          </Button>
          <AccessControl
            :codes="['cpm:ruleengine:iotChannel:delete']"
            type="code"
          >
            <Popconfirm
              title="确定删除吗？"
              :get-popup-container="getVxePopupContainer"
              placement="left"
              @confirm="handleDelete(row)"
            >
              <Button
                class="mr-2 border-none p-0"
                :block="false"
                type="link"
                danger
              >
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
    <PopupModal ref="refopupModal" :productKey="productKey" @reload="handleRefresh" />
    <ViewModal />
  </div>
</template>