<template>
  <div id="videoMonitoring" class="video-monitoring-container">
    <div class="video-monitoring-layout">
      <!-- 左侧：关联监控设备列表 -->
      <div class="device-list-section">
        <deviceList ref="deviceListRef" :device-key="deviceKey" @show-video="showVideoPlayer" />
      </div>

      <!-- 右侧：视频播放器 -->
      <div class="video-player-section">
        <!-- 设备切换加载状态 -->
        <div v-if="isDeviceSwitching" class="device-switching-overlay">
          <div class="switching-content">
            <div class="switching-spinner"></div>
            <h3>设备切换中...</h3>
            <p>正在停止当前流并切换到新设备</p>
          </div>
        </div>

        <videoPlayer v-else-if="selectedDeviceId !== null" ref="videoPlayerRef" :key="selectedDeviceId"
          :device-id="selectedDeviceId" :video-url="videoUrl" @back-to-list="backToDeviceList"
          @play-failed="handlePlayFailed" @play-success="handlePlaySuccess" @play-started="handlePlayStarted" />

        <!-- 未选择设备时的占位内容 -->
        <div v-else class="no-selection">
          <div class="no-selection-content">
            <div class="no-selection-icon">🎥</div>
            <h3>选择监控设备</h3>
            <p>请在左侧列表中选择要查看的监控设备</p>
            <div class="selection-hint">
              <p class="hint-text">💡 提示：</p>
              <ul class="hint-list">
                <li>支持FLV、HLS、RTMP等多种视频流格式</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import deviceList from './deviceList.vue';
import videoPlayer from './videoPlayer.vue';

// 接收父组件传递的设备标识
const props = defineProps<{
  deviceKey?: string; // 主设备的设备标识
}>();

const selectedDeviceId = ref<string | null>(null);
const videoUrl = ref<string>('');
const videoPlayerRef = ref(); // 视频播放器组件引用
const deviceListRef = ref(); // 设备列表组件引用
const isDeviceSwitching = ref(false); // 设备切换状态

// 保存上一个设备状态，用于失败时回滚
const previousDeviceState = ref<{
  deviceId: string | null;
  videoUrl: string;
}>({
  deviceId: null,
  videoUrl: ''
});

// 使用传入的 deviceKey 或默认值
const deviceKey = props.deviceKey || '';

// 处理播放失败事件
const handlePlayFailed = (deviceId: string, error: any) => {
  console.error(`❌ 收到播放失败事件 - 设备 ${deviceId} 播放失败:`, error);
  console.log('🔄 当前isDeviceSwitching状态:', isDeviceSwitching.value);

  // 隐藏切换状态loading
  if (isDeviceSwitching.value) {
    isDeviceSwitching.value = false;
    console.log('🔄 已隐藏切换状态loading');
  } else {
    console.log('🔄 切换状态loading已经是隐藏状态');
  }

  // 显示错误提示
  let errorMessage = '设备播放失败';
  if (error?.response?.data?.message) {
    errorMessage = error.response.data.message;
  } else if (error?.message) {
    errorMessage = error.message;
  } else if (typeof error === 'string') {
    errorMessage = error;
  }

  message.error(`设备切换失败: ${errorMessage}`);

  // 回滚到上一个设备状态
  console.log('🔄 回滚到上一个设备状态:', previousDeviceState.value);
  selectedDeviceId.value = previousDeviceState.value.deviceId;
  videoUrl.value = previousDeviceState.value.videoUrl;

  // 同步更新设备列表的选中状态
  if (deviceListRef.value && deviceListRef.value.updateSelectedDevice) {
    console.log('🔧 开始同步设备列表选中状态，目标设备:', previousDeviceState.value.deviceId);
    deviceListRef.value.updateSelectedDevice(previousDeviceState.value.deviceId);
    console.log('✅ 已同步设备列表选中状态至:', previousDeviceState.value.deviceId);
  } else {
    console.warn('⚠️ 无法同步设备列表选中状态，deviceListRef或updateSelectedDevice方法不存在');
    console.log('deviceListRef.value:', deviceListRef.value);
    if (deviceListRef.value) {
      console.log('updateSelectedDevice method:', deviceListRef.value.updateSelectedDevice);
    }
  }

  // 如果上一个设备也是空的，则显示选择提示
  if (selectedDeviceId.value === null) {
    console.log('📋 回滚到初始状态');
  }
};

// 处理播放成功事件
const handlePlaySuccess = (deviceId: string) => {
  console.log('✅ 收到播放成功事件，设备ID:', deviceId);
  console.log('🔄 当前isDeviceSwitching状态:', isDeviceSwitching.value);

  // 隐藏切换状态loading
  if (isDeviceSwitching.value) {
    isDeviceSwitching.value = false;
    console.log('🔄 播放成功，已隐藏切换状态loading');
  } else {
    console.log('🔄 播放成功，切换状态loading已经是隐藏状态');
  }

  // 更新上一个设备状态
  previousDeviceState.value = {
    deviceId: selectedDeviceId.value,
    videoUrl: videoUrl.value
  };
  console.log('📋 更新上一个设备状态:', previousDeviceState.value);
};

// 处理播放开始事件
const handlePlayStarted = (deviceId: string) => {
  console.log('🎬 收到播放开始事件，设备ID:', deviceId);
  console.log('🔄 当前isDeviceSwitching状态:', isDeviceSwitching.value);

  // 播放开始时立即隐藏切换loading
  if (isDeviceSwitching.value) {
    console.log('🔄 播放开始，立即隐藏切换loading');
    isDeviceSwitching.value = false;
  } else {
    console.log('🔄 播放开始，切换loading已经是隐藏状态');
  }
};

// 显示视频播放器
const showVideoPlayer = async (deviceId: string, streamUrl: string) => {
  console.log('🎥 显示视频播放器, deviceId:', deviceId, 'videoUrl:', streamUrl);
  console.log('🔄 当前选中的设备:', selectedDeviceId.value);
  console.log('🔄 当前isDeviceSwitching状态:', isDeviceSwitching.value);

  // 如果选择的是当前正在播放的设备，直接返回
  if (selectedDeviceId.value === deviceId) {
    console.log('🎥 选择的是当前正在播放的设备，无需切换');
    return;
  }

  // 保存当前状态，以便切换失败时恢复
  const currentDeviceId = selectedDeviceId.value;
  const currentVideoUrl = videoUrl.value;
  console.log('💾 保存当前状态:', { currentDeviceId, currentVideoUrl });

  // 更新上一个设备状态（用于成功播放后的回滚状态）
  previousDeviceState.value = {
    deviceId: currentDeviceId,
    videoUrl: currentVideoUrl
  };

  // 如果当前已有设备在播放，先停止播放
  if (selectedDeviceId.value !== null && selectedDeviceId.value !== deviceId) {
    console.log('🛑 检测到设备切换，先停止当前播放的设备:', selectedDeviceId.value);

    // 显示切换状态
    isDeviceSwitching.value = true;
    console.log('🔄 显示切换状态loading');

    try {
      await stopCurrentVideo();

      // 等待一小段时间确保停止完成
      await new Promise(resolve => setTimeout(resolve, 300));

    } catch (error) {
      console.error('❌ 停止当前视频失败:', error);
      // 切换失败，恢复之前的状态
      isDeviceSwitching.value = false;
      selectedDeviceId.value = currentDeviceId;
      videoUrl.value = currentVideoUrl;
      console.log('🔄 恢复之前的状态:', { currentDeviceId, currentVideoUrl });
      return;
    }
  } else {
    // 如果没有正在播放的设备，不显示切换状态，直接设置设备
    console.log('🔄 没有正在播放的设备，直接设置新设备');
  }

  // 设置新的设备和视频URL
  console.log('📝 设置新的设备和视频URL');
  selectedDeviceId.value = deviceId;
  videoUrl.value = streamUrl;
};

// 停止当前视频播放
const stopCurrentVideo = async () => {
  return new Promise((resolve) => {
    if (videoPlayerRef.value) {
      console.log('🛑 调用视频播放器停止方法');

      // 调用停止方法
      if (videoPlayerRef.value.stopVideoStream) {
        videoPlayerRef.value.stopVideoStream();
      }

      // 调用重置方法，确保彻底清理
      if (videoPlayerRef.value.resetState) {
        videoPlayerRef.value.resetState();
        console.log('� 重置播放器状态');
      }

      // 等待停止完成
      setTimeout(resolve, 500);
    } else {
      console.log('🛑 视频播放器引用不存在');
      resolve(void 0);
    }
  });
};

// 返回设备列表
const backToDeviceList = async () => {
  console.log('📋 返回设备列表');
  // 先停止当前播放
  await stopCurrentVideo();
  selectedDeviceId.value = null;
  videoUrl.value = '';
};

// 监听设备切换，当设备ID变化时停止当前播放
watch(() => selectedDeviceId.value, async (newDeviceId, oldDeviceId) => {
  if (oldDeviceId !== null && newDeviceId !== oldDeviceId) {
    console.log('🔄 设备切换检测:', { from: oldDeviceId, to: newDeviceId });
    // 如果从有设备切换到无设备，确保停止播放
    if (newDeviceId === null) {
      await stopCurrentVideo();
    }
  }
});
</script>

<style scoped>
.video-monitoring-container {
  height: 850px;
  width: 100%;
  overflow: hidden;

}

.video-monitoring-layout {
  display: flex;
  height: 100%;
  width: 100%;
}

.device-list-section {
  width: 320px;
  height: 100%;
  flex-shrink: 0;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
}

.video-player-section {
  flex: 1;
  height: 800px;
  background-color: #000;
  position: relative;
}

.no-selection {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.no-selection-content {
  text-align: center;
  color: #fff;
  max-width: 400px;
  padding: 40px;
}

.no-selection-icon {
  font-size: 80px;
  margin-bottom: 24px;
  opacity: 0.8;
}

.no-selection h3 {
  font-size: 24px;
  margin: 0 0 16px 0;
  font-weight: 500;
  color: #fff;
}

.no-selection p {
  font-size: 16px;
  margin: 0 0 24px 0;
  opacity: 0.8;
  line-height: 1.5;
}

.selection-hint {
  text-align: left;
  max-width: 400px;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 16px;
  border-radius: 8px;
  margin-top: 24px;
}

.hint-text {
  font-size: 14px;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.hint-list {
  margin: 0;
  padding-left: 16px;
  font-size: 13px;
  opacity: 0.9;
  line-height: 1.6;
}

.hint-list li {
  margin-bottom: 4px;
}

.device-switching-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.switching-content {
  text-align: center;
  color: #fff;
  padding: 40px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.8) 0%, rgba(24, 144, 255, 0.6) 100%);
  border-radius: 16px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 300px;
}

.switching-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.switching-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
  color: #fff;
}

.switching-content p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.4;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
