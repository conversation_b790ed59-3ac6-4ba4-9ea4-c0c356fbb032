<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <template #deviceKey="slotProps">
        <div style="display: flex; gap: 8px;">
          <Input placeholder="请选择设备" v-bind="slotProps" readonly style="width: 200%;" />
          <Button type="primary" @click="openSelectDevice">选择</Button>
        </div>
      </template>
      <template #tslFunctionsCheck>
        <TslFunctionsCheckboxGroup :functions="tslFunctions" :modelValue="selectedFunctions"
          @update:modelValue="handleFunctionChange" class="tsl-checkbox-group-wrap" />
      </template>
    </BasicForm>
    <SelectDevice ref="refSelectDevice" @deviceSelected="onDeviceSelected" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';
import { getSysUserListApi } from '#/api/system/user';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { Input, Button, } from 'ant-design-vue';
import { useVbenForm } from '#/adapter/form';
import { Edit, List, View as View2 } from '#/api/manage/iotCmdPerm';
import { editSchema } from './model';
import SelectDevice from './selectDevice/selectDevice/index.vue';
import dayjs from 'dayjs';
import TslFunctionsCheckboxGroup from './TslFunctionsCheckboxGroup.vue';
import { ListNoPage as ListProduct, View as DetailProduct } from '#/api/device/iotProduct';
import { onMounted } from 'vue';
onMounted(() => {
  console.log('edit.vue mounted');
});
const refSelectDevice = ref();
const tslFunctions = ref<any[]>([]);
const selectedFunctions = ref<string[]>([]);
async function fetchTslFunctions(productKey: string) {
  console.log('fetchTslFunctions called with:', productKey);
  if (!productKey) {
    tslFunctions.value = [];
    selectedFunctions.value = [];
    return;
  }
  const res = await DetailProduct({ productKey });
  console.log('DetailProduct返回：', res);
  tslFunctions.value = res?.tsl?.functions || [];
  console.log('tslFunctions:', tslFunctions.value);
  selectedFunctions.value = [];
  console.log('表格数据', res.value);
}
// 选择指令时同步到表单
const handleFunctionChange = (value: string[]) => {
  selectedFunctions.value = value;
  formApi.setFieldValue('tslFunctionsCheck', value);
};
function openSelectDevice() {
  refSelectDevice.value.openModal();
}
// 设备选择弹窗回调
async function onDeviceSelected(deviceKey: string, deviceName: string) {
  // deviceName 显示在输入框，deviceKey 作为实际提交值
  await formApi.setFieldValue('deviceKey', deviceName);
  // 如果你需要 deviceKey 作为隐藏字段提交，可以再加一个字段存 deviceKey
}
const emit = defineEmits<{ reload: [] }>();
interface UserItem {
  userId: string | number;
  userName: string;
}
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
}
const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) return;
    drawerApi.setState({ confirmLoading: true, loading: true });
    const { id, update, view } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;
    // 1. 用 getSysUserListApi 接口获取所有 userId
    const userRes = await getSysUserListApi({
      page: 1,
      pageSize: 1000,
    } as any);
    console.log('userRes:', userRes);
    // 假设 userRes 是用户数组，请根据实际API结构调整
    const userItems = Array.isArray((userRes as any).items) ? (userRes as any).items : [];
    console.log('userItems:', userItems);

    const userNameSet = new Set<string>();
    userItems.forEach((item: any) => {
      if (typeof item.userName === 'string') {
        userNameSet.add(item.userName);
      }
    });
    const options = userItems.map((item: any) => ({
      label: item.userName,
      value: item.userId,
    }));
    console.log('userName options:', options);

    formApi.updateSchema([
      {
        fieldName: 'userId',
        componentProps: {
          options,
          placeholder: '请选择用户名称',
        },
      },
    ]);
    const productRes = await ListProduct({});
    const productItems = Array.isArray(productRes.items) ? productRes.items : [];
    const productKeyOptions = productItems.map((item: any) => ({
      label: item.productName || item.productKey,
      value: item.productKey,
    }));
    formApi.updateSchema([
      {
        fieldName: 'productKey',
        componentProps: {
          options: productKeyOptions,
          placeholder: '请选择产品Key',
          showSearch: true,
          allowClear: true,
          // 添加筛选逻辑：支持按产品名称和产品Key搜索
          filterOption: (input: string, option: any) => {
            const label = option.label?.toLowerCase() || '';
            const value = option.value?.toLowerCase() || '';
            const searchText = input.toLowerCase();
            return label.includes(searchText) || value.includes(searchText);
          },
          onChange: (val: string) => {
            console.log('productKey onChange', val);
            fetchTslFunctions(val); // ← 这里加上
          },
        },
      },
    ]);
    // 3. 如果是编辑或查看，获取详情并设置表单初始值
    if (isUpdate.value || isView.value) {
      const record = await View2({ cmdPermId: id });
      // 日期字段转换
      if (record.startTime) {
        record.startTime = dayjs(record.startTime);
      }
      if (record.endTime) {
        record.endTime = dayjs(record.endTime);
      }
      const userItems = Array.isArray((userRes as any).items)
        ? ((userRes as any).items as UserItem[])
        : [];
      // 直接用 record 里的 userId、productKey、deviceKey、deviceName 等字段
      const user = userItems.find(u => String(u.userId) === String(record.userId));
      if (user) {
        record.user = user;
      }
      if (record.productKey) {
        await fetchTslFunctions(record.productKey);
      } else {
        tslFunctions.value = [];
      }
      if (record.functions) {
        selectedFunctions.value = record.functions.split(',').filter(Boolean);
        // 同步到表单隐藏字段（如果有）
        await formApi.setFieldValue('tslFunctionsCheck', selectedFunctions.value);
      } else {
        selectedFunctions.value = [];
        await formApi.setFieldValue('tslFunctionsCheck', []);
      }
      await formApi.setValues(record);
      const data = await formApi.getValues();
      computed
      console.log(data['userName']);
    }
    drawerApi.setState({ confirmLoading: false, loading: false });
    if (isView.value) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});
async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = await formApi.getValues();
    if (Array.isArray(data.tslFunctionsCheck)) {
      data.functions = data.tslFunctionsCheck.join(',');
      delete data.tslFunctionsCheck;
    }
    console.log('提交数据', data);
    await Edit(data);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}
async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

</script>
<style lang="less" scoped>
.tsl-checkbox-group-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 16px;
  max-height: 200px;
  overflow-y: auto;
}

.tsl-checkbox-group-wrap .ant-checkbox-group {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 8px 16px;
}
</style>