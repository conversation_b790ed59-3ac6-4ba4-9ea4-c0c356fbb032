import { h, ref } from 'vue';
import { Tag, Button } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public channelId = 0; // ID
  public scriptName = ''; // 脚本名称
  public execOrder = 0; // 执行顺序
  public productKey = ''; // 产品标识
  public inputType = 0; // 脚本事件（1=设备上报 2=平台下发 3=设备上线 4=设备离线 5=HTTP接入 6=MQTT接入）
  public outputType = 0; // 输出动作（1=消息重发 2=HTTP接入 6=MQTT接入）
public direction =0;
public type =0;
public channelName= '';
  public content = ''; // 脚本内容
  public status = 0; // 状态：0=正常，1=停用
  public tenantId = ''; // 租户ID
  public firmwareType = 0; 
  public deptId = 0;// 所属机构
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'scriptName',
    component: 'Input',
    label: '脚本名称',
    componentProps: {
      placeholder: '请输入脚本名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
    componentProps: {
      placeholder: '请输入所属产品',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: 'ID',
    field: 'scriptId',
    align: 'left',
    width: -1,
    // type: 'checkbox',
    type: 'checkbox',
  },
  {
    title: '脚本名称',
    field: 'scriptName',
    align: 'left',
    width: -1,
  },
  {
    title: '所属产品',
    field: 'productName', //productKey对应的名字
    align: 'left',
    width: -1,
  },

  //（1=设备上报 2=平台下发 3=设备上线 4=设备离线 5=HTTP接入 6=MQTT接入）
  {
    title: '脚本事件',
    field: 'inputType',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.inputType, DictEnum.SCRIPT_INPUT_TYPE);
      },
    },
  },

  {
    title: '输出动作',
    field: 'outputType',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.outputType, DictEnum.SCRIPT_OUTPUT_TYPE);
      },
    },
  },

  //0=正常，1=停用
  {
    title: '状态',
    field: 'status',
    align: 'left',
    width: -1,
    slots: { default: 'status' },
  },

  {
    title: '执行顺序',
    field: 'execOrder',
    align: 'left',
    width: -1,
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

//st的表格列调用
export const Layoutss: VxeGridProps['columns'] = [
  {
    title: 'ID',
    field: 'channel.channelId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '连接器名称',
    field: 'channel.channelName',
    align: 'left',
    width: -1,
  },
   {
      title: '固件类型', field: 'firmwareType', align: 'center', width: -1,
      slots: {
        default: ({ row }) => {
          return renderDict(row.firmwareType, DictEnum.FIRMWARE_TYPE);
        }
      },
      visible: false,
    },
  {
    title: '是否生效',
    field: 'channel.status',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.channel.status, DictEnum.SYS_NORMAL_DISABLE);
      },
    },
  },
  {
    title: '状态',
    field: 'channel.connectStatus',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.channel.connectStatus, DictEnum.CHANNEL_CONNECT);
      },
    },
    // slots: {
    //   default: ({ row }) => {
    //     return renderDict(row.connectStatus, DictEnum.CHANNEL_CONNECT);
    //   },
    // },
  },
  {
    title: '通道类型',
    field: 'channel.type',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.channel.type, DictEnum.CHANNEL_TYPE);
      },
    },
  },
  {
    title: '桥接方向',
    field: 'channel.direction',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.channel.direction, DictEnum.CHANNEL_DIRECTION);
      },
    },
  },
  {
    title: '备注',
    field: 'channel.remark',
    align: 'left',
    width: -1,
  },
  {
    title: '创建时间',
    field: 'channel.createdAt',
    align: 'left',
    width: -1,
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];
// 表格列接口
export interface RowType {
  scriptId: string;
  scriptName: string;
  channelId: number;
  channelName: string;
  direction: number;
  type: number;
  script: string;
  status: string;
  tenantId: string;
  createdDept: number;
  connectState: string;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  firmwareType: number;
  deletedAt: string;
  remark: string;
  publishStatus: string;
  ActionScript: string; //脚本语言
  execOrder: number;
  inputType: number;
  outputType: number;
  content: string;
  deptId: number;
}

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'scriptId', label: 'ID' },
  { field: 'scriptName', label: '脚本名称' },
  { field: 'productName', label: '所属产品' },
  { field: 'execOrder', label: '执行顺序' },

  //（1=设备上报 2=平台下发 3=设备上线 4=设备离线 5=HTTP接入 6=MQTT接入）
  {
    field: 'inputType',
    label: '脚本事件',
    render(_, row: any) {
      return renderDict(row.inputType, DictEnum.SCRIPT_INPUT_TYPE);
    },
  },
  //（1=消息重发 2=HTTP接入 6=MQTT接入）
  {
    field: 'outputType',
    label: '输出动作',
    // render(_, row: any) {
    //   return renderDict(String(row.inputType), DictEnum.SCRIPT_OUTPUT_TYPE);
    // },
  },
  {
    field: 'outputType',
    label: '输出动作',
    render(_, row: any) {
      return renderDict(String(row.outputType), DictEnum.SCRIPT_OUTPUT_TYPE);
    },
  },

  { field: 'content', label: '脚本内容' },
  //0=正常，1=停用
  {
    field: 'status',
    label: '状态',
    render(val, _) {
      return renderDict(String(val), DictEnum.CHANNEL_CONNECT);
    },
  },
  // { field: 'tenantId', label: '租户ID' },
  // { field: 'deptId', label: '所属机构' },
  // { field: 'createdDept', label: '创建部门' },
  // { field: 'createdBy', label: '创建者' },
  { field: 'createdAt', label: '创建时间' },
  // { field: 'updatedBy', label: '更新者' },
  { field: 'updatedAt', label: '更新时间' },
  // { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: DescItem[] = [
  {
    label: 'ID',
    field: 'channel.channelId',
    // type: 'checkbox',
  },
  {
    label: '连接器名称',
    field: 'channel.channelName',
  },
  {
    label: '状态',
    field: 'channel.connectStatus',
    render(_, row: any) {
      return renderDict(row.channel.connectStatus, DictEnum.CHANNEL_CONNECT);
    },
  },
  {
    field: 'channel.State',
    label: '是否生效',
    render(_, row: any) {
      return row.channel.Status === '0' ? '已发布' : '未发布';
    },
  },
  {
    label: '通道类型',
    field: 'channel.type',
    render(_, row: any) {
      return renderDict(row.channel.type, DictEnum.CHANNEL_TYPE);
    },
  },
  {
    label: '桥接方向',
    field: 'channel.direction',
    render(_, row: any) {
      return renderDict(row.channel.direction, DictEnum.CHANNEL_DIRECTION);
    },
  },
    { field: 'createdAt', label: '创建时间' },
    // { field: 'updatedBy', label: '更新者' },
    { field: 'updatedAt', label: '更新时间' },
  
];
