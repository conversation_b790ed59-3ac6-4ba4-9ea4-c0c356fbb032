import { requestClient } from '#/api/request';

// 获取报警关联通知模版列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotAlarmTemplate/list', { params });
}

// 删除/批量删除报警关联通知模版
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotAlarmTemplate/delete', { ...params });
}

// 添加/编辑报警关联通知模版
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotAlarmTemplate/edit', { ...params });
}

// 获取报警关联通知模版指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotAlarmTemplate/view', { params });
}

// 导出报警关联通知模版
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotAlarmTemplate/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}