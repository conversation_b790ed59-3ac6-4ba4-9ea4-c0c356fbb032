import { requestClient } from '#/api/request';

// 获取产品可订阅事件列表列表
export function List(params:any) {
  return requestClient.get<any>('device/iotProductSubEvent/list', { params });
}

// 删除/批量删除产品可订阅事件列表
export function Delete(params:any) {
  return requestClient.post<any>('device/iotProductSubEvent/delete', { ...params });
}

// 添加/编辑产品可订阅事件列表
export function Edit(params:any) {
  return requestClient.post<any>('device/iotProductSubEvent/edit', { ...params });
}

// 获取产品可订阅事件列表指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotProductSubEvent/view', { params });
}

// 导出产品可订阅事件列表
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotProductSubEvent/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}