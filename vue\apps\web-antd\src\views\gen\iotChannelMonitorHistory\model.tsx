import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public icmhId = 0; // 统计ID
  public channelType = null; // 通道类型
  public channelId = 0; // 资源ID
  public shardId = 0; // 分片ID
  public reportTime = ''; // 统计时间开始时间
  public reportInterval = 0; // 统计间隔秒默认60秒
  public dataType = null; // 数据类型
  public dataChgVal = 0; // 数据变化值
  public dataCurrentVal = 0; // 数据当前值
  public dataHistoryVal = 0; // 数据历史值
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {    
			fieldName: 'channelType',    
			component: 'Select',    
			label: '通道类型',    
			defaultValue: null,    
			componentProps: {    
				placeholder: '请选择通道类型',    
				options: getDictOptions('channel_type'),    
				onUpdateValue: (e: any) => {    
					console.log(e);    
				},  
			},
			rules:'selectRequired',
			formItemClass: 'col-span-1',
		},
		  {
				fieldName: 'channelId',
				component: 'InputNumber',
				label: '资源ID',
				componentProps: {
					placeholder: '请输入资源ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入资源ID', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'reportTime',
				component: 'DatePicker',
				label: '统计时间开始时间',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {    
			fieldName: 'dataType',    
			component: 'Select',    
			label: '数据类型',    
			defaultValue: null,    
			componentProps: {    
				placeholder: '请选择数据类型',    
				options: getDictOptions('channel_monitor_data_type'),    
				onUpdateValue: (e: any) => {    
					console.log(e);    
				},  
			},
			rules:'selectRequired',
			formItemClass: 'col-span-1',
		},
		];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '统计ID',
    field: 'icmhId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
 {    
				title: '通道类型',    field: 'channelType',    align: 'left',    width: -1, 
				slots: {
      				default: ({ row }) => {
						return renderDict(row.channelType, 'channel_type');
					}
				},
			},
			  {
    title: '资源ID',
    field: 'channelId',
    align: 'left',
    width: -1,
 },
  {
    title: '分片ID',
    field: 'shardId',
    align: 'left',
    width: -1,
 },
  {
    title: '统计时间开始时间',
    field: 'reportTime',
    align: 'left',
    width: -1,
 },
  {
    title: '统计间隔秒默认60秒',
    field: 'reportInterval',
    align: 'left',
    width: -1,
 },
 {    
				title: '数据类型',    field: 'dataType',    align: 'left',    width: -1, 
				slots: {
      				default: ({ row }) => {
						return renderDict(row.dataType, 'channel_monitor_data_type');
					}
				},
			},
			  {
    title: '数据变化值',
    field: 'dataChgVal',
    align: 'left',
    width: -1,
 },
  {
    title: '数据当前值',
    field: 'dataCurrentVal',
    align: 'left',
    width: -1,
 },
  {
    title: '数据历史值',
    field: 'dataHistoryVal',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  icmhId: number;
  channelType: number;
  channelId: number;
  shardId: number;
  reportTime: string;
  reportInterval: number;
  dataType: number;
  dataChgVal: number;
  dataCurrentVal: number;
  dataHistoryVal: number;
  tenantId: string;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'icmhId',  label: '统计ID'},
  {
				field: 'channelType',
				label: '通道类型',
				render(row: any) {
					return renderDict(row.channelType, 'channel_type');
				},
			},
			  {  field: 'channelId',  label: '资源ID'},
  {  field: 'shardId',  label: '分片ID'},
  {  field: 'reportTime',  label: '统计时间开始时间'},
  {  field: 'reportInterval',  label: '统计间隔秒默认60秒'},
  {
				field: 'dataType',
				label: '数据类型',
				render(row: any) {
					return renderDict(row.dataType, 'channel_monitor_data_type');
				},
			},
			  {  field: 'dataChgVal',  label: '数据变化值'},
  {  field: 'dataCurrentVal',  label: '数据当前值'},
  {  field: 'dataHistoryVal',  label: '数据历史值'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdAt',  label: '创建时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
 {
					fieldName: 'icmhId',
					component: 'Input',
					label: '统计ID',
					dependencies: {   show: () => false,    triggerFields: [''],   },
					componentProps: {
						placeholder: '',
						onUpdateValue: (e: any) => {
							console.log(e);
						},   
					},
					formItemClass: 'col-span-1',
				},  {    
			fieldName: 'channelType',    
			component: 'Select',    
			label: '通道类型',    
			defaultValue: null,    
			componentProps: {    
				placeholder: '请选择通道类型',    
				options: getDictOptions('channel_type'),    
				onUpdateValue: (e: any) => {    
					console.log(e);    
				},  
			},
			rules:'selectRequired',
			formItemClass: 'col-span-1',
		},
		  {
				fieldName: 'channelId',
				component: 'InputNumber',
				label: '资源ID',
				componentProps: {
					placeholder: '请输入资源ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入资源ID', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'shardId',
				component: 'InputNumber',
				label: '分片ID',
				componentProps: {
					placeholder: '请输入分片ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入分片ID', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'reportTime',
				component: 'DatePicker',
				label: '统计时间开始时间',
				componentProps: {
					type: 'datetime',
					clearable: true,
					showTime: true,
					shortcuts: 'FMTime',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:'selectRequired',
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'reportInterval',
				component: 'InputNumber',
				label: '统计间隔秒默认60秒',
				componentProps: {
					placeholder: '请输入统计间隔秒默认60秒',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入统计间隔秒默认60秒', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {    
			fieldName: 'dataType',    
			component: 'Select',    
			label: '数据类型',    
			defaultValue: null,    
			componentProps: {    
				placeholder: '请选择数据类型',    
				options: getDictOptions('channel_monitor_data_type'),    
				onUpdateValue: (e: any) => {    
					console.log(e);    
				},  
			},
			rules:'selectRequired',
			formItemClass: 'col-span-1',
		},
		  {
				fieldName: 'dataChgVal',
				component: 'InputNumber',
				label: '数据变化值',
				componentProps: {
					placeholder: '请输入数据变化值',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入数据变化值', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'dataCurrentVal',
				component: 'InputNumber',
				label: '数据当前值',
				componentProps: {
					placeholder: '请输入数据当前值',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入数据当前值', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'dataHistoryVal',
				component: 'InputNumber',
				label: '数据历史值',
				componentProps: {
					placeholder: '请输入数据历史值',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:z.number({required_error: '请输入数据历史值', invalid_type_error: '无效数字'}),
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'tenantId',
				component: 'Input',
				label: '租户ID',
				componentProps: {
					placeholder: '请输入租户ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:'required',
				formItemClass: 'col-span-1',
			},];