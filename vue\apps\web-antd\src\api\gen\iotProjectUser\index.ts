import { requestClient } from '#/api/request';

// 获取项目员工表列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotProjectUser/list', { params });
}

// 删除/批量删除项目员工表
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotProjectUser/delete', { ...params });
}

// 添加/编辑项目员工表
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotProjectUser/edit', { ...params });
}

// 获取项目员工表指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotProjectUser/view', { params });
}

// 导出项目员工表
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotProjectUser/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}