<script setup lang="ts">
import { onMounted, watch } from 'vue';
import { Page } from '@vben/common-ui';

import { Divider } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type {
    VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { useVbenForm } from '#/adapter/form';
import { columns, schema, type RowType } from './model';
import { View } from '#/api/project/iotConsumerOauth';

// 定义接收的属性类型
const props = defineProps({
    consumer: {
        type: Object,
        required: true,
        default: () => ({}),
    },
});

const [BasicForm, formApi] = useVbenForm({
    commonConfig: {
        componentProps: { class: 'w-full' },
        formItemClass: 'col-span-9',
    },
    layout: 'horizontal',
    schema: schema,
    showDefaultActions: false,
    wrapperClass: 'grid grid-cols-12 gap-x-4',
});


const gridOptions: VxeTableGridOptions<RowType> = {
    checkboxConfig: {
        highlight: true,
    },
    rowConfig: {
        keyField: 'consumerId',
    },
    columns: columns,
    exportConfig: {},
    height: '300px',
    keepSource: true,
    showOverflow: false,
    pagerConfig: {},
    proxyConfig: {
        ajax: {
            query: async ({ page }, formValues) => {
                const response = await View({
                    id: props.consumer.consumerId
                });
                return response;
            },
        },
    },
    toolbarConfig: {
        custom: false,
        export: false,
        refresh: false,
        resizable: true,
        search: false,
        zoom: false,
    },
};
const [Grid, gridApi] = useVbenVxeGrid({
    gridOptions,
});

// 1. 初始加载时同步
onMounted(() => {
    if (props.consumer) {
        formApi.setValues(props.consumer); // 将数据设置到表单
    }
});

// 2. 监听 props.consumer 变化（若父组件数据动态更新）
watch(
    () => props.consumer,
    (newVal: Record<string, any>) => {
        if (newVal) {
            formApi.setValues(newVal); // 数据变化时重新同步
        }
    },
    { deep: true } // 深度监听对象变化
);
</script>
<template class="p-0 m-0">
    <div class="p-0 m-0 h-[800px]">
        <!-- <h5 class="form-title">用户信息</h5> -->
        <label style="font-size: 18px;">基本信息</label>
        <Divider />
        <div class="user-info-container">
            <!-- 左侧头像区域 -->
            <div class="avatar-box">
                <!-- 示例头像 -->
                <div class="avatar">
                    <img src="https://img.keaitupian.cn/newupload/08/1628502171175140.jpg" alt="avatar">
                </div>
            </div>
            <!-- 右侧表单区域 -->
            <div class="form-box">
                <BasicForm class="gap-[8px] h-full" />
            </div>
        </div>
        <label style="font-size: 18px;">第三方授权信息</label>
        <Divider />
        <Grid class="p-0 m-0 h-[100px]">
        </Grid>
    </div>
</template>

<style scoped>
/* 容器：左侧头像 + 右侧表单 */
.user-info-container {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    padding: 24px;
}

/* 左侧头像区域 */
.avatar-box {
    width: 140px;
    /* 头像宽度 */
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar {
    width: 120px;
    height: 120px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 右侧表单区域 */
.form-box {
    flex: 1;
    /* 占满剩余空间 */
}
</style>
