import { h, ref } from 'vue';
import { Tag, Button } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public scriptId = 0; // ID
  public scriptName = ''; // 脚本名称
  public execOrder = 0; // 执行顺序
  public productKey = ''; // 产品标识
  public inputType = 0; // 脚本事件（1=设备上报 2=平台下发 3=设备上线 4=设备离线 5=HTTP接入 6=MQTT接入）
  public outputType = 0; // 输出动作（1=消息重发 2=HTTP接入 6=MQTT接入）
  public deptId = 0;
  public content = ''; // 脚本内容
  public status = 0; // 状态：0=正常，1=停用
  public tenantId = ''; // 租户ID
  public userId = 0; // 用户ID
  public alarmUserId = 0; // 所属机构
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
];

// 表格列
export const columns: VxeGridProps['columns'] = [
    {
    type: 'checkbox', // duoxuan按钮
    width: 60,
    align: 'center',
  },
  {
    title: 'Id',
    field: 'alarmUserId',
    width: 40,
    visible: false
  },
  {
    title: '用户编码',
    field: 'userId',
    align: 'center',
    width: 200,
  },
  {
    title: '用户名',
    field: 'user.userName',
    align: 'left',
    width: -1,
  },
  {
    title: '接警级别',
    field: 'level', //productKey对应的名字
    align: 'center',
    width: 200,
     slots: {
      default: ({ row }) => {
        return renderDict(row.level, DictEnum.ALARM_USER_LEVEL);
      },
    },
  },

  //（1=设备上报 2=平台下发 3=设备上线 4=设备离线 5=HTTP接入 6=MQTT接入）
  {
    title: '手机号',
    field: 'user.phonenumber',
    align: 'center',
    width: 200,

  },
  {
    title: '备注',
    field: 'remark',
    align: 'left',
    width: -1,
  },
  {
    title: '操作',
    width: 120,
    align: 'center',
    slots: { default: 'action' }
  },
];

// 表格列接口
export interface RowType {
  scriptId: number;
  scriptName: string;

  ActionScript: string; //脚本语言
  execOrder: number;
  inputType: number;
  outputType: number;
alarmUserId: number; // 接警用户ID
  userId: number; // 用户ID
  content: string;
  status: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  remark: string;
}

// 查看字段列表
export const viewSchema: DescItem[] = [

];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
    {
    fieldName: 'alarmUserId',
    component: 'Input',
    label: 'ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
     rules:'selectRequired'
  },
   {
  fieldName: 'productKey',
  component: 'Input',
  label: '产品标识',
  formItemClass: 'col-span-6',
   componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
       readonly: true,
    },
     rules:'selectRequired'
},
   {
  fieldName: 'deviceKey',
  component: 'Input',
  label: '设备标识',
  formItemClass: 'col-span-6',
  componentProps: {
    placeholder: '请选择设备',
    readonly: true, // 不允许手动输入
  }
  },
  {
    label: '用户id',
    fieldName: 'userId',
    component: 'Input',
    formItemClass: 'col-span-6',
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);

      },
       readonly: true,
    },
     rules:'selectRequired'
  },
  {
    label: '用户名',
    fieldName: 'user.userName',
    component: 'Input',
    formItemClass: 'col-span-6',
    componentProps: {
      placeholder: '',
      readonly: true, // 用户名字段始终只读，通过选择按钮填充
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
     rules:'selectRequired'
  },
  {
    label: '手机号',
    fieldName: 'user.phonenumber',
    component: 'Input',
    formItemClass: 'col-span-6',
     dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '手机号',
      readonly: true,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules:'selectRequired'
  },
  //  {
  //       fieldName: 'level',
  //       component: 'Select',
  //       label: '接警级别',
  //       defaultValue: null,
  //       formItemClass: 'col-span-6',
  //       componentProps: {
  //         placeholder: '请选择接警级别',
  //         options: getDictOptions('alarm_user_level'),
  //         onUpdateValue: (e: any) => {
  //           console.log(e);
  //         },
  //       },
  //       rules:'selectRequired'
  //     },
      {
          fieldName: 'level',
          component: 'Select',
          label: '接警级别',
          defaultValue: null,
         formItemClass: 'col-span-6',
          componentProps: {
            placeholder: '请选择',
            onUpdateValue: (e: any) => {
              console.log(e);
            },
            options: getDictOptions(DictEnum.ALARM_USER_LEVEL),
          },
          rules: 'selectRequired',
        },
        {
      fieldName: 'remark',
      component: 'Input',
      label: '备注',
      formItemClass: 'col-span-12',
      componentProps: {
        placeholder: '请输入备注',
        onUpdateValue: (e: any) => {
          console.log(e);
        },
      },
    rules:null
  },
];
