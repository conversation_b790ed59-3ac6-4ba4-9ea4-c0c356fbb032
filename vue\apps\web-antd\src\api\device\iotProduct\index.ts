import { requestClient } from '#/api/request';
import type { AxiosRequestConfig } from '@vben/request';

// 获取产品表列表
export function List(params:any) {
  return requestClient.get<any>('device/iotProduct/list', { params });
}

// 删除/批量删除产品表
export function Delete(params:any) {
  return requestClient.post<any>('device/iotProduct/delete', { ...params });
}

// 添加/编辑产品表
export function Edit(params:any) {
  return requestClient.post<any>('device/iotProduct/edit', { ...params });
}

// 获取产品表指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotProduct/view', { params });
}

// 导出产品表
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotProduct/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}

// 产品发布
export function Publish(params:any) {
  return requestClient.post<any>('/device/iotProduct/publish',  { ...params });
}

// 导出产品物模型
export function ExportModel(params:any) {
  return requestClient.post<Blob>('/device/iotProduct/exportModel',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}

/**
 * 导入产品物模型
 */
export function ImportModel(params:any) {
  return requestClient.post<any>('/device/iotProduct/importModel',  { ...params });
}

// 获取产品列表-不分页
export function ListNoPage(params:any) {
  return requestClient.get<any>('device/iotProduct/listNoPage', { params });
}

// 修改产品扩展配置
export function ExtraConfigEdit(params:any) {
  return requestClient.post<any>('/device/iotProduct/extraConfigEdit',  { ...params });
}

// 修改标签关联属性
export function TagLinkEdit(params:any) {
  return requestClient.post<any>('/device/iotProduct/tagLinkEdit',  { ...params });
}
