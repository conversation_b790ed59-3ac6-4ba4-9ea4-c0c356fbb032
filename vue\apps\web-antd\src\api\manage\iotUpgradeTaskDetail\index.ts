import { requestClient } from '#/api/request';

// 获取升级任务明细表列表
export function List(params:any) {
  return requestClient.get<any>('manage/iotUpgradeTaskDetail/list', { params });
}

// 删除/批量删除升级任务明细表
export function Delete(params:any) {
  return requestClient.post<any>('manage/iotUpgradeTaskDetail/delete', { ...params });
}

// 添加/编辑升级任务明细表
export function Edit(params:any) {
  return requestClient.post<any>('manage/iotUpgradeTaskDetail/edit', { ...params });
}

// 获取升级任务明细表指定详情
export function View(params:any) {
  return requestClient.get<any>('manage/iotUpgradeTaskDetail/view', { params });
}

// 导出升级任务明细表
export function Export(params:any) {
  return requestClient.post<Blob>('/manage/iotUpgradeTaskDetail/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}