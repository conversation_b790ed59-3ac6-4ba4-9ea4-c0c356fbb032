<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Page } from '@vben/common-ui';

import { Card, Switch, Tabs, TabPane, Button, message, Divider, Row, Col, Modal } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import { Statistic, View } from '#/api/project/iotConsumer';

import Status from './status/index.vue';
import Device from './device/index.vue';
import LoginRecords from './loginRecords/index.vue';
import Msgs from './msgs/index.vue';
import DeviceOperation from './deviceOperation/index.vue'

const route = useRoute();

const currentTab = ref('a');
const consumer = ref({
  consumerId: route.query.consumerId,
  userName: '',
  userPassword: '',
  userSalt: '',
  nickName: '',
  realName: '',
  sex: '',
  phone: '',
  email: '',
  ip: '',
  status: '',
  projectId: '',
  projectName: '',
  tenantId: '',
  deptId: 0,
  createdDept: 0,
  createdBy: 0,
  createdAt: '',
  updatedBy: 0,
  updatedAt: '',
  deletedBy: 0,
  deletedAt: '',
  remark: '',
})

const statistic = ref({
  deviceCount: '',
  groupCount: '',
  alarmCountToday: '',
  alarmCountUnread: '',
  notifyCountToday: '',
  notifyCountUnread: '',
  latestLoginTime: '',
})

async function reloadEditForm() {
  const record = await View({ consumerId: route.query.consumerId });
  if (record.sex !== undefined) {
    record.sex = record.sex === '0' ? '男' :
      record.sex === '1' ? '女' : '未知';
  }
  consumer.value = record;
}
async function reloadStatistic() {
  const record = await Statistic({ consumerId: route.query.consumerId });
  statistic.value = record;
}

onMounted(() => {
  reloadEditForm()
  reloadStatistic()
});
</script>

<template>
  <Page class="h-full overflow-y-auto">
    <Card class="h-200 mb-4">
      <!-- 主栅格行：控制整体布局 -->
      <Row align="middle">
        <Col span="3">
        <div class="user-info">
          <label style="font-size: 24px;">终端用户: &nbsp;&nbsp;{{ consumer.userName }}</label>
        </div>
        <label class="login-time-label">最近登录时间: {{ statistic.latestLoginTime }}</label>
        </Col>
        <!-- 中间：最近登录时间，占更大比例居中 -->
        <Col span="4" style="text-align: center;">
        
        </Col>
        <!-- 右侧：统计卡片区域，保持紧凑 -->
        <Col span="15">
        <Row :gutter="10" justify="end">
          <Col span="6">
          <div class="text-center p-0 m-0" style="text-align: center;">
            <div class="text-xl font-bold">{{ statistic.deviceCount }}</div>
            <div class="text-gray-500">设备总数</div>
          </div>
          </Col>
          <Col span="6">
          <div class="text-center p-0 m-0" style="text-align: center;">
            <div class="text-xl font-bold">{{ statistic.groupCount }}</div>
            <div class="text-gray-500">分组数</div>
          </div>
          </Col>
          <Col span="6">
          <div class="text-center p-0 m-0" style="text-align: center;">
            <div class="text-xl font-bold">{{ statistic.alarmCountUnread }} / {{ statistic.alarmCountToday }}</div>
            <div class="text-gray-500">未读报警/今日报警</div>
          </div>
          </Col>
          <Col span="6">
          <div class="text-center p-0 m-0" style="text-align: center;">
            <div class="text-xl font-bold">{{ statistic.notifyCountUnread }} / {{ statistic.notifyCountToday }}</div>
            <div class="text-gray-500">未读通知/今日通知</div>
          </div>
          </Col>
        </Row>
        </Col>
      </Row>
    </Card>
    <Card>
      <Tabs v-model:activeKey="currentTab">
        <TabPane key="a" tab="状态">
          <Status :consumer="consumer" class="p-0 m-0" />
        </TabPane>
        <TabPane key="b" tab="设备">
          <Device :consumer="consumer" class="p-0 m-0" />
        </TabPane>
        <TabPane key="c" tab="设备操作记录">
          <DeviceOperation :consumer="consumer" class="p-0 m-0" ></DeviceOperation>
        </TabPane>
        <TabPane key="d" tab="消息中心">
          <Msgs :consumer="consumer" class="p-0 m-0" ></Msgs>
        </TabPane>
        <TabPane key="e" tab="登录记录">
          <LoginRecords :consumer="consumer" class="p-0 m-0" />
        </TabPane>
      </Tabs>
    </Card>
  </Page>
</template>
