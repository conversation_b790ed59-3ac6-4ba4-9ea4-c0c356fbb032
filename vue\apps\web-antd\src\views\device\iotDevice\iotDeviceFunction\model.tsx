import type { VxeGridProps } from '#/adapter/vxe-table';
import { DictEnum } from '@vben/constants';
import { renderDict } from '#/utils';

// 表格列接口
export interface RowType {
  key: string;
  name: string;
  valueType: {
    type: string;
    max?: number;
    min?: string;
    decimals?: string;
    unit?: string;
    step?: string;
    trueText?: string;
    falseText?: string;
    maxLength?: string;
    enumList?: Array<{value: string, text: string}>;
  };
  desc?: string;
  value?: any;
}

// 基础列定义
export const functionParamsColumns: VxeGridProps['columns'] = [
  {
    title: '参数名称',
    field: 'name',
    align: 'left',
    width: -1,
  },
  {
    title: '输入类型',
    field: 'valueType.type',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.valueType?.type, DictEnum.DEVICE_MODEL_DATA_TYPE);
        if (found) {
          return found;
        }
        return row.valueType?.type;
      },
    },
  },
  {
    title: '参数值',
    field: 'value',
    align: 'left',
    width: -1,
    slots: {
      default: 'valueSlot',
    },
  },
];

// 获取参数默认值
export function getDefaultValue(param: any): any {
  if (!param || !param.valueType) return '';

  const type = param.valueType.type;

  switch (type) {
    case 'integer':
    case 'decimal':
      return param.valueType.min || 0;
    case 'bool':
      return false;
    case 'enum':
      return param.valueType.enumList && param.valueType.enumList.length > 0
        ? param.valueType.enumList[0].value
        : '';
    case 'array':
      return [];
    case 'object':
      return {};
    case 'string':
    default:
      return '';
  }
}

