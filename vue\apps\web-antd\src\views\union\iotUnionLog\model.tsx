import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

export class State {
  public unionLogId = 0; // 属性日志ID
  public unionKey = ''; // 组合设备标识
  public content = null; // 日志内容
  public modelKey = ''; // 属性标识符
  public fieldValue = ''; // 属性值
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'unionLogId',
    component: 'InputNumber',
    label: '属性日志ID',
    componentProps: {
      placeholder: '请输入属性日志ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '属性日志ID',
    field: 'unionLogId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '组合设备标识',
    field: 'unionKey',
    align: 'left',
    width: -1,
 },
  {
    title: '属性标识符',
    field: 'modelKey',
    align: 'left',
    width: -1,
 },
  {
    title: '属性值',
    field: 'fieldValue',
    align: 'left',
    width: -1,
 },
  {
    title: '租户ID',
    field: 'tenantId',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  unionLogId: number;
  unionKey: string;
  content: string;
  modelKey: string;
  fieldValue: string;
  tenantId: string;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'unionLogId',  label: '属性日志ID'},
  {  field: 'unionKey',  label: '组合设备标识'},
  {  field: 'content',  label: '日志内容'},
  {  field: 'modelKey',  label: '属性标识符'},
  {  field: 'fieldValue',  label: '属性值'},
  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdAt',  label: '创建时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'unionLogId',
    component: 'Input',
    label: '属性日志ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'unionKey',
    component: 'Input',
    label: '组合设备标识',
    componentProps: {
      placeholder: '请输入组合设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'content',
    component: 'Input',
    label: '日志内容',
    componentProps: {
      placeholder: '请输入日志内容',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:null
},
  {
    fieldName: 'modelKey',
    component: 'Input',
    label: '属性标识符',
    componentProps: {
      placeholder: '请输入属性标识符',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'fieldValue',
    component: 'Input',
    label: '属性值',
    componentProps: {
      placeholder: '请输入属性值',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'tenantId',
    component: 'Input',
    label: '租户ID',
    componentProps: {
      placeholder: '请输入租户ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
];