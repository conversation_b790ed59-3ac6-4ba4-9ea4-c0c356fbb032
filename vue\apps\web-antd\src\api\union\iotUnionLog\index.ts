import { requestClient } from '#/api/request';

// 获取组合设备日志表列表
export function List(params:any) {
  return requestClient.get<any>('union/iotUnionLog/list', { params });
}

// 删除/批量删除组合设备日志表
export function Delete(params:any) {
  return requestClient.post<any>('union/iotUnionLog/delete', { ...params });
}

// 添加/编辑组合设备日志表
export function Edit(params:any) {
  return requestClient.post<any>('union/iotUnionLog/edit', { ...params });
}

// 获取组合设备日志表指定详情
export function View(params:any) {
  return requestClient.get<any>('union/iotUnionLog/view', { params });
}

// 导出组合设备日志表
export function Export(params:any) {
  return requestClient.post<Blob>('/union/iotUnionLog/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}