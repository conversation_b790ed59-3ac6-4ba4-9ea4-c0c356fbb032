<script lang="ts" setup>
import { h, reactive, ref, computed, onMounted, watch } from 'vue';
import { Button, message, Tag, Modal, Popconfirm, Select} from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenForm, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { View, Delete, Edit } from '#/api/device/iotCjt188Config';
import { MdiPlus } from '@vben/icons';
import { columns, editSchema, type RowType, type FieldType } from './model';
import { cloneDeep } from '@vben/utils';
import { List } from '#/api/device/iotProductModel';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { DictTag } from '#/components/dict';

const emit = defineEmits(['reload']);
const props = defineProps({
  productKey: {
    type: String,
    required: true,
  },
  published: {
    type: Boolean,
    required: true,
  },
});

const isUpdate = ref(false);
const data = ref<RowType>({
  cjt188Id: 0,
  productKey: props.productKey,
  meterType: '',
  fieldsObj: [],
  tenantId: '',
  createdAt: '',
});

const gridOptions = {
  checkboxConfig: {
    highlight: true,
    labelField: 'cjt188Id',
  },
  rowConfig: {
    keyField: 'cjt188Id',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async () => {
        console.log("gridQuery", data.value?.fieldsObj);
        return {
          items: data.value?.fieldsObj || [],
          total: data.value?.fieldsObj?.length || 0,
        }
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: gridOptions as any,
  gridEvents,
});

async function handleDelete(row: FieldType) {
  data.value.fieldsObj = data.value.fieldsObj.filter((item: any) => item.key !== row.key);
  await gridApi.query();
}
async function handleRefresh() {
  gridApi.grid?.clearEdit();
  isUpdate.value = false;
  formApi.setState({
    submitButtonOptions: {
      content: '修改',
    },
  });
  const result = await View({
    productKey: props.productKey,
  });
  const models = await List({ productKey: props.productKey, page: 1, pageSize: 1000 });
  console.log("加载数据", result, models);
  if (!result) {
    data.value = {
      cjt188Id: 0,
      productKey: props.productKey,
      meterType: '',
      fieldsObj: [],
      tenantId: '',
      createdAt: '',
    };
  } else {
    data.value = {
      cjt188Id: result.cjt188Id,
      productKey: result.productKey,
      meterType: String(result.meterType),
      fieldsObj: result.fieldsObj.map((item: any) => ({
        ...item,
        dataType: String(item.dataType),
        modelName: models.items.find((model: any) => model.modelKey === item.key)?.modelName,
      })),
    } as RowType;
  }

  await formApi.setValues(data.value);
  await gridApi.query();
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
  },
  layout: 'horizontal',
  schema: editSchema,
  showDefaultActions: props.published ? false : true,
  wrapperClass: 'grid-cols-6 gap-x-4',
  actionButtonsReverse: true,
  submitButtonOptions: {
    content: '修改',
    type: 'primary',
  },
  resetButtonOptions: {
    content: '刷新',
  },
  handleReset: () => {
    handleRefresh();
  },
  handleSubmit: () => {
    if(isUpdate.value){
      handleConfirm();
    }else{
      isUpdate.value = true;
      formApi.setState({
        submitButtonOptions: {
          content: '保存',
        },
      });
      data.value.fieldsObj.forEach((item:any)=>{
        gridApi.grid?.setEditRow(item);
      });
    }
  },
});

const [AddModel, addApi] = useVbenModal({
  title: '添加属性',
  onConfirm: () => {
    handleAddConfirm();
  },
  onCancel: () => {
    addApi.close();
  },
  onOpenChange: async (open) => {
    if (!open) {
      addApi.close();
    }
    addApi.setState({
      confirmLoading: false,
      loading: true,
    });
    addFormApi.setValues({
      key: [],
    });

    const options = await getPropertyOptions(props.productKey);
    addFormApi.updateSchema([
      {
        fieldName: 'key',
        component: 'Select',
        label: '物模型字段',
        componentProps: {
          multiple: true,
          placeholder: '请选择物模型字段标识',
          options: options,
        },
        rules: 'required'
      }
    ]);
    addApi.setState({
      confirmLoading: false,
      loading: false,
    });
    addFormApi.setState({
      commonConfig: {
        componentProps: {
          readonly: false,
          "only-read": false,
        }
      }
    });
  },
});


// 表格搜索表单
const getPropertyOptions = async (productKey: string) => {
  const result = await List({ productKey: productKey, page: 1, pageSize: 1000 });
  console.log("产品物模型", result);
  const currentKeys = data.value.fieldsObj.map((item: any) => item.key);
  if (!!result.items) {
    return result.items.filter((item: any) => !currentKeys.includes(item.modelKey)).map((item: any) => ({
      label: item.modelName,
      value: item.modelKey,
    }));
  }
  return [] as any;
}

const [AddForm, addFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
  },
  layout: 'horizontal',
  showDefaultActions: false,
  schema: [
    {
      fieldName: 'key',
      component: 'Select',
      label: '物模型字段',
      componentProps: {
        mode: 'multiple',
        placeholder: '请选择物模型字段标识',
        options: [],
      },
      rules: 'required'
    },
  ]
});

async function handleAddConfirm() {
  try {
    const { valid } = await addFormApi.validate();
    if (!valid) {
      return;
    }
    const d = cloneDeep(await addFormApi.getValues());
    if (d.key.length === 0) {
      message.error("请至少选择一个物模型字段");
      return;
    }
    const modelList = await List({ productKey: props.productKey, page: 1, pageSize: 1000 });

    for (const k of d.key) {
      if (data.value.fieldsObj.find((item: any) => item.key === k)) {
        continue;
      }
      data.value.fieldsObj = [...data.value.fieldsObj, {
        key: k,
        modelName: modelList.items.find((item: any) => item.modelKey === k)?.modelName,
        field: 'SlaveId',
        dataType: data.value.meterType.startsWith('2') ? '2' : '1',
      }];
    }
    console.log(data.value);
    gridApi.query();
    addApi.close();
  } catch (error) {
    console.error(error);
  }
}

async function handleConfirm() {
  console.log("handleConfirm", isUpdate.value);
  try {
    //formApi.setState({ confirmLoading: true })
    if(!isUpdate.value){
      return;
    }
    const d = cloneDeep(await formApi.getValues());
    if(!d.meterType){
      message.error("请选择仪表类型");
      return;
    }
    data.value.meterType = d.meterType;
    await (Edit(data.value));
    message.success("保存成功");
    await handleRefresh();
  } catch (error) {
    console.error(error);
  }
}

async function handleAdd() {
  //打开对话框
  addApi.open();
}
onMounted(async () => {
  await handleRefresh();
});

watch(
  () => props.published,
  async (newVal) => {
    formApi.setState({
      showDefaultActions: !newVal,
    });
    await handleRefresh();
  }
);

</script>
<template>
  <Page auto-content-height>
    <BasicForm>
      <template #meterType="slotProps">
        <span v-if="!isUpdate && !slotProps.value">--</span>
        <Select v-if="isUpdate" :options="getDictOptions(DictEnum.CJT188_METER_TYPE)" v-bind="slotProps" class="w-[300px]" />
        <DictTag v-if="!isUpdate && slotProps.value" :dicts="getDictOptions(DictEnum.CJT188_METER_TYPE)" :value="slotProps.value" />
      </template>
    </BasicForm>  
    <Grid table-title="CJ/T 188数据与物模型绑定列表">
      <template #toolbar-tools>
        <Button class="mr-2 flex items-center " type="primary" :icon="h(MdiPlus)" @click="handleAdd"
          v-access:code="'cpm:device:iotCjt188Config:edit'" v-if="!props.published && isUpdate">
          添加物模型字段
        </Button>
      </template>
      <template #dataType_edit="{ row }">
        <Select v-if="isUpdate" :options="getDictOptions(DictEnum.CJT188_DATA_TYPE)" v-model:value="row.dataType" class="w-[300px]" />
        <DictTag v-else :dicts="getDictOptions(DictEnum.CJT188_DATA_TYPE)" :value="row.dataType" />
      </template>
      <template #field_edit="{ row }">
        <div v-if="isUpdate">
          <Select v-if="row.dataType == 1" :options="getDictOptions(DictEnum.CJT188_WATER_GAS_DATA1_KEYS)" v-model:value="row.field" class="w-[300px]" />
          <Select v-if="row.dataType == 2" :options="getDictOptions(DictEnum.CJT188_HEAT_DATA1_KEYS)" v-model:value="row.field" class="w-[300px]" />
          <Select v-if="row.dataType == 3" :options="getDictOptions(DictEnum.CJT188_WATER_GAS_DATA2_KEYS)" v-model:value="row.field" class="w-[300px]" />
          <Select v-if="row.dataType == 4" :options="getDictOptions(DictEnum.CJT188_HEAT_DATA2_KEYS)" v-model:value="row.field" class="w-[300px]" />
        </div>
        <div v-else>
          <DictTag  v-if="row.dataType == 1"  :dicts="getDictOptions(DictEnum.CJT188_WATER_GAS_DATA1_KEYS)" :value="row.field" />
          <DictTag  v-if="row.dataType == 2"  :dicts="getDictOptions(DictEnum.CJT188_HEAT_DATA1_KEYS)" :value="row.field" />
          <DictTag  v-if="row.dataType == 3"  :dicts="getDictOptions(DictEnum.CJT188_WATER_GAS_DATA2_KEYS)" :value="row.field" />
          <DictTag  v-if="row.dataType == 4"  :dicts="getDictOptions(DictEnum.CJT188_HEAT_DATA2_KEYS)" :value="row.field" />
        </div>
      </template>
      <template #action="{ row }">
        <div class="flex items-center">
          <AccessControl :codes="['cpm:device:iotCjt188Config:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)" v-if="!props.published && isUpdate">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
    <AddModel>
      <AddForm></AddForm>
    </AddModel>
  </Page>
</template>