<script lang="ts" setup>
import { h, reactive, ref, computed } from 'vue';
import { Button, message, Tag, Modal, Popconfirm, Switch } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, Delete } from '#/api/device/iotProductCategory';
import { MdiPlus, MdiExport,IconifyIcon } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import editDrawer from './edit.vue';
import viewDrawer from './view.vue';
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  align: 'center',
  rowConfig: {
    keyField: 'categoryName',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        console.log(page, formValues);
        let result = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
        let records = result.items
        if (records && records.length > 0) {
          let idMap = records.reduce((acc:any, cur:any) => {
            acc[cur.categoryId] = cur.categoryName ;
            return acc;
          }, {});
          result.items.forEach((item:any) => {
            if(item.parentId === 0){
              item.parentName = '根目录';
            }else{
              item.parentName = idMap[item.parentId];
            }
          });
        }

        return result;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
  treeConfig: {
    parentField: 'parentId',
    rowField: 'categoryId',
    transform: true,
    expandAll: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});
const [ViewDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: viewDrawer,
});
function handlePreview(record: RowType) {
  drawerApi.setData({ record });
  drawerApi.open();
}
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});
function handleAdd(parentId: number) {
  editDrawerApi.setData({ update: false, view: false, parentId:parentId});
  editDrawerApi.open();
}
function handleEdit(row: RowType) {
  editDrawerApi.setData({ id: row.categoryId, update: true, view: false });
  editDrawerApi.open();
}
async function handleDelete(row: RowType) {
  await Delete({ categoryId: [row.categoryId] });
  message.success("删除成功");
  await handleRefresh();
}
async function handleRefresh() {
  await gridApi.query();
}
async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '产品分类表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success("导出成功");
}
const expandAll = () => {
  gridApi.grid?.setAllTreeExpand(true);
};

const collapseAll = () => {
  gridApi.grid?.setAllTreeExpand(false);
};
</script>
<template>
  <Page auto-content-height>
    <Grid table-title="产品分类">
      <template #toolbar-tools>
        <Button class="mr-2 flex items-center " type="primary" :icon="h(MdiPlus)" @click="handleAdd(0)"
          v-access:code="'cpm:device:iotProductCategory:edit'">
          新增
        </Button>
        <Button class="mr-2 flex items-center"  @click="expandAll">展开</Button>
        <Button class="mr-2 flex items-center"  @click="collapseAll">折叠</Button>
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiExport)" @click="handleExport"
          v-access:code="'cpm:device:iotProductCategory:export'">
          导出
        </Button>
      </template>

      <template #action="{ row }">
        <div class="flex items-center">
   
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handlePreview(row)"
            v-access:code="'cpm:device:iotProductCategory:view'">
            查看
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleAdd(row.categoryId)"
            v-access:code="'cpm:device:iotProductCategory:edit'">
            添加子类
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row)"
            v-access:code="'cpm:device:iotProductCategory:edit'">
            修改
          </Button>
          <AccessControl :codes="['cpm:device:iotProductCategory:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
    <EditDrawer @reload="handleRefresh" />
    <ViewDrawer />
  </Page>
</template>