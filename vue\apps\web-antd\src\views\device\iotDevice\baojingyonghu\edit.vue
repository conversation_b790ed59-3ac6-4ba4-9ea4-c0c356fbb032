<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[500px]">
    <BasicForm>
      <template #user.userName="slotProps">
        <div style="display: flex; gap: 8px;">
          <Input placeholder="请选择用户" v-bind="slotProps" readonly style="width: 200%;" />
          <Button v-if="!isUpdate && !isView" type="primary" @click="openSelectUser">选择用户</Button>
        </div>
      </template>
    </BasicForm>
    <SelectDevice ref="refSelectDevice" @userSelected="onUserSelected" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';
import { Input, Button, } from 'ant-design-vue';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';
import SelectDevice from './selectDevice/selectDevice/index.vue';
import { useVbenForm } from '#/adapter/form';

import { Edit, View } from '#/api/device/iotDeviceAlarmUser';
import { editSchema } from './model';

const refSelectDevice = ref();
const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
  productKey?: string;
  deviceKey?: string;
}

const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});
const open = (params: any) => {
  console.log('打开编辑抽屉:', params);
 // visible.value = true;
  // 可以在这里初始化表单数据
};

// 关闭抽屉的方法
const close = () => {
//  visible.value = false;
};

// 通过 defineExpose 暴露方法给父组件
defineExpose({
  open,
  close
});
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});
async function onUserSelected(userIds: string, userNames: string, phonenumbers: string) {
  // 设置用户相关字段
  await formApi.setFieldValue('userId', userIds);
  await formApi.setFieldValue('user.userName', userNames);
  await formApi.setFieldValue('user.phonenumber', phonenumbers);
}
function openSelectUser() {
  refSelectDevice.value.openModal();
}
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, update, view, productKey, deviceKey } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;

    if (isUpdate.value || isView.value) {
      const record = await View({ alarmUserId: id });

      // 处理接警级别字典值的显示
      // 确保level字段使用正确的值格式，与字典选项的value类型一致
      if (record.level !== undefined && record.level !== null) {
        // 转换为字符串类型，确保与字典选项的value类型匹配
        record.level = String(record.level);
      }

      console.log('编辑模式 - 设置表单值:', record);
      await formApi.setValues(record);
    } else {
      // 新增模式，设置默认值和传递的参数
      await formApi.setValues({
        productKey: productKey,
        deviceKey: deviceKey,
        email: { sslEnable: false, authEnable: false }
      });
    }
    drawerApi.setState({ confirmLoading: false, loading: false })

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      // 编辑模式下不设置全局readonly，让选择按钮正常显示
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
  


    const data = cloneDeep(await formApi.getValues());

    // 确保包含必要的设备信息
    const drawerData = drawerApi.getData() as ModalProps;
    if (drawerData.productKey) {
      data.productKey = drawerData.productKey;
    }
    if (drawerData.deviceKey) {
      data.deviceKey = drawerData.deviceKey;
    }

    await Edit(data);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false });
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

</script>