<script lang="ts" setup>
import { h, ref } from 'vue';
import { Button, message } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export } from '#/api/project/iotConsumerOptLog';
import { MdiExport } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import editDrawer from './edit.vue';
import viewDrawer from './view.vue';
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'id',
  },
  rowConfig: {
    keyField: 'id',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});
const [ViewDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: viewDrawer,
});
function handlePreview(record: RowType) {
  drawerApi.setData({ record });
  drawerApi.open();
}
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});

async function handleRefresh() {
  await gridApi.query();
}
async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '终端用户设备操作记录表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success("导出成功");
}
</script>
<template>
  <Page auto-content-height>
    <Grid table-title="终端用户设备操作记录表">
      <template #toolbar-tools>
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiExport)" @click="handleExport"
          v-access:code="'cpm:project:iotConsumerOptLog:export'">
          导出
        </Button>
      </template>
      <template #action="{ row }">
        <div class="flex items-center justify-center">
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handlePreview(row)"
            v-access:code="'cpm:project:iotConsumerOptLog:view'">
            查看
          </Button>
        </div>
      </template>
    </Grid>
    <EditDrawer @reload="handleRefresh" />
    <ViewDrawer />
  </Page>
</template>