<script lang="ts" setup>
import { h, ref, onMounted } from 'vue';
import { router } from '#/router';
import { Button, message, Modal, Popconfirm, Switch } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, Delete, Status } from '#/api/project/iotConsumer';
import { List as ListProject } from '#/api/project/iotProject';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import editDrawer from './edit.vue';
import viewDrawer from './view.vue';
import resetPwdModal from './reset-pwd-modal.vue';

type Option = {
  label: string;
  value: string;
  deviceType?: number | string; // 添加deviceType字段
};
const projectOptions = ref<Option[]>([]);
async function loadProductOptions() {
  const res = await ListProject({
    page: 1,
    pageSize: 1000,
  });
  if (!res || !res.items) {
    projectOptions.value = [];
  } else {
    projectOptions.value = res.items.map((item: any) => ({
      label: item.projectName,
      value: item.projectId,
      deviceType: item.deviceType, // 保存deviceType信息
    }));
  }
  console.log('产品选项加载完成，包含deviceType:', projectOptions.value);
  gridApi.formApi.updateSchema([
    {
      fieldName: 'projectId',
      component: 'Select',
      label: '项目',
      componentProps: {
        placeholder: '请选择项目',
        onUpdateValue: (e: any) => {
          console.log(e);
        },
        options: projectOptions.value,
        showSearch: true,
        filterOption: (input: any, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
      },
    },
  ]);
}
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'consumerId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const response = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
        return response;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});
const [ViewDrawer] = useVbenDrawer({
  connectedComponent: viewDrawer,
});
function handlePreview(consumerId: number) {
  let url = `/project/consumerDetail?consumerId=${consumerId}`;
  router.push(url);
}
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});
function handleAdd() {
  editDrawerApi.setData({ update: false, view: false });
  editDrawerApi.open();
}
function handleEdit(row: RowType) {
  editDrawerApi.setData({ id: row.consumerId, update: true, view: false });
  editDrawerApi.open();
}
async function handleDelete(row: RowType) {
  await Delete({ consumerId: [row.consumerId] });
  message.success("删除成功");
  await handleRefresh();
}
async function handleRefresh() {
  await gridApi.query();
}
function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const ids: string[] = [];
  for (const row of rows) {
    ids.push(row.consumerId);
  }
  if (ids.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await Delete({ consumerId: ids });
      message.success("删除成功");
      await handleRefresh();
    },
  });
}
async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '终端用户表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success("导出成功");
}
async function handleStatusChange(row: RowType) {
  await Status({ consumerId: row.consumerId, status: row.status });
  await message.success("操作成功")
  await handleRefresh();
}

const [ResetPwdModal, resetPwdModalApi] = useVbenModal({
  connectedComponent: resetPwdModal,
});

function handleResetPassword(row: RowType) {
  resetPwdModalApi.setData({ record: row });
  resetPwdModalApi.open();
}

onMounted(() => {
  loadProductOptions();
  // // 1. 获取路由参数
  // const { productName, productKey } = deviceRoute.query;
  // if (productKey) {
  //   // 2. 设置表单字段
  //   gridApi.formApi.setFieldValue('productKey', productKey); // 你的下拉框字段名是 productKey
  //   // 3. 自动触发一次搜索
  //   console.log('自动触发搜索', productName);
  //   // gridApi.query();
  // }
});
</script>
<template>
  <Page auto-content-height>
    <Grid table-title="终端用户表">
      <template #toolbar-tools>
        <Button class="mr-2 flex items-center " type="primary" :icon="h(MdiPlus)" @click="handleAdd"
          v-access:code="'cpm:project:iotConsumer:edit'">
          新增
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :disabled="!CheckboxChecked" :icon="h(MdiDelete)"
          @click="handleMultiDelete" v-access:code="'cpm:project:iotConsumer:delete'">
          删除
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiExport)" @click="handleExport"
          v-access:code="'cpm:project:iotConsumer:export'">
          导出
        </Button>
      </template>
      <template #status="{ row }">
        <Switch v-model:checked="row.status" :checkedValue="'0'" :unCheckedValue="'1'" @change="handleStatusChange(row)"
          :disabled="!hasAccessByCodes(['cpm:project:iotConsumer:status'])" />
      </template>
      <template #avatar="{ row }">
        <!-- <img :src="processImageUrl(row.avatar)" alt="产品图片"
          style="width: 62px; height: 62px; border-radius: 8px; object-fit: cover; margin-right: 12px;"
          @error="handleImageError" /> -->
      </template>
      <template #action="{ row }">
        <div class="flex items-center">
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handlePreview(row.consumerId)"
            v-access:code="'cpm:project:iotConsumer:view'">
            查看
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row)"
            v-access:code="'cpm:project:iotConsumer:edit'">
            编辑
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleResetPassword(row)"
            v-access:code="'cpm:system:user:resetPwd'">重置密码</Button>
          <AccessControl :codes="['cpm:project:iotConsumer:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
    <EditDrawer @reload="handleRefresh" />
    <ViewDrawer />
    <ResetPwdModal />
  </Page>
</template>