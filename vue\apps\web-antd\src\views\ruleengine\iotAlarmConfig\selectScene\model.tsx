
import type { VxeGridProps } from '#/adapter/vxe-table';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import type { VbenFormSchema } from '@vben/common-ui';

// 表单验证规则
// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'SceneName',
    component: 'Input',
    label: '场景名称',
    componentProps: {
      placeholder: '请输入场景名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];
// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '',
    align: 'center',
    width: 50,
    type: 'radio',
  },
  {
    title: '编号',
    field: 'sceneId',
    align: 'center',
    width: 60,
  },
  {
    title: '场景名称',
    field: 'sceneName',
    align: 'center',
    width: -1,
  },
  {
    title: '状态',
    field: 'status',
    align: 'center',
    width: -1,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
        if (found) {
          return found;
        }
        return row.status;
      },
    },
  },
  {
    title: '触发类型',
    field: 'triggerType',
    align: 'center',
    width: -1,
    slots: {
          default: ({ row }) => {
            let found = renderDict(row.triggerType, DictEnum.SCENE_TRIGGER_TYPE);
            if (found) {
              return found;
            }
            return row.triggerType;
          },
        },
  },
  {
    title: '执行方式',
    field: 'executeMode',
    align: 'center',
    width: -1,
    slots: {
          default: ({ row }) => {
            let found = renderDict(row.executeMode, DictEnum.SCENE_EXECUTE_MODE);
            if (found) {
              return found;
            }
            return row.executeMode;
          },
        },
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: -1,
  },
];
// 表格列接口

export interface RowType {
  sceneId: number;
  sceneName: string;
  status: string;
  hasAlarm: number;
  triggerType: string;
  executeMode: number;
  silentMinute: number;
  executeDelay: number;
  createdAt: string;
};
