<template>
    <ScriptContentForm>
      <template #modbusParam.coilStatusList="slotProps">
        <div class="grid grid-cols-8 gap-x-4">
          <div class="col-span-8 mb-4">
            <div class="grid grid-cols-2 gap-x-4">
              <label class="text-sm font-medium col-span-2">全开或全关</label>
              <Switch v-model:checked="allCoilStatus" class="w-10" @update:checked="async (e:any)=> await changeAllCoilStatus(e)" />
            </div>
          </div>
          <div v-for="item, index in slotProps.value" :key="index" class="col-span-2 mb-4">
            <label class="text-sm font-medium">{{'#' + index + ' 线圈状态值'}}</label>
            <Switch :key="index" :checked="item" @update:checked="async (e)=> await editArray(scriptContentFormApi, 'modbusParam.coilStatusList', slotProps.value, index, e)" />
          </div>
        </div>
      </template>
      <template #modbusParam.regValueList="slotProps">
        <div class="grid grid-cols-8 gap-x-4">
          <div v-for="item, index in slotProps.value" :key="index" class="col-span-4 mb-4">
            <label class="text-sm font-medium">{{'#' + index + ' 寄存器值'}}</label>
            <InputGroup compact>
              <Input style="width: 70%;" type="number" :key="index" :value="item" @update:value="async (e:any)=> await editArray(scriptContentFormApi, 'modbusParam.regValueList', slotProps.value, index, e)" :min="0"/>
              <Input style="width: 30%;"  readonly :key="index" :value="hex(item)" class="w-10"  />
            </InputGroup>
          </div>
        </div>
      </template>
      <template #review>
        <div class="flex-horizontal w-full">
          <Button type="link" @click="getReviewData" class="p-0">
            生成预览
          </Button>
          <Textarea v-show="reviewData.length > 0" class="w-full bg-gray-800 text-white" readonly :value="reviewData" :rows="1" :autoSize="{minRows: 1, maxRows: 10}"/>
        </div> 
      </template>
    </ScriptContentForm>
</template>
<script setup lang="ts">
import { computed, nextTick, onMounted, ref, toRaw, unref, watch } from 'vue';
import { editArray } from '#/utils/bindArray';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Alert , Steps, Button , Switch, Textarea, message, Input, InputGroup} from 'ant-design-vue';
import { View as DetailProduct } from '#/api/device/iotProduct';
import { editStep3Schema, type Step3DataType } from './model';
import { GenModbusRTUMsg } from '#/api/device/iotPollTask';

const props = defineProps<{
    formData: Step3DataType;
    modelKeysOptions: any[]; 
}>();


watch(() => props.formData.pollType, async (newVal) => {
  console.log("watch props.formData", props.formData);
  const data = cloneDeep(props.formData);
  data.pollScriptObj = cloneDeep(props.formData.pollScriptObj);
  data.modbusParam = cloneDeep(props.formData.modbusParam);
  data.extendConfigObj = cloneDeep(props.formData.extendConfigObj);
  await scriptContentFormApi.setValues(data);
  const newFormData = await scriptContentFormApi.getValues();
  console.log("watch props.formData set and get", newFormData);
});

watch(() => props.modelKeysOptions, async (newVal) => {
  console.log("watch props.modelKeysOptions", newVal);
  scriptContentFormApi.updateSchema([
    {
      fieldName: 'pollScriptObj.modelKeys',
      component: 'Select',
      label: '物模型',
      componentProps: {
        options: props.modelKeysOptions,
        mode: 'multiple',
        valueFormat: 'string',
      },
      rules: 'required',
    },
  ]);
});


const reviewData = ref<any>({});

const allCoilStatus = ref(false);

watch(allCoilStatus, async (newVal) => {
  const data = await scriptContentFormApi.getValues();
  const updated = data.modbusParam.coilStatusList.map(() => {
    return newVal;
  });
  await scriptContentFormApi.setFieldValue("modbusParam.coilStatusList", updated);
});

async function getReviewData(){
  const data = await scriptContentFormApi.getValues();
  const res = await GenModbusRTUMsg(data.modbusParam)
  message.success("生成预览成功");
  reviewData.value = res.dataMsg||'';
  console.log("getReviewData", data);
}

async function validate(){
  let valid = await scriptContentFormApi.validate();
  return valid;
}

async function getFormData() {
  const formValues = await scriptContentFormApi.getValues();
  const data1 = cloneDeep(formValues);
  console.log("getFormData", data1, formValues);
  if(data1.pollType != '2'){
    data1.pollScriptObj.modelKeys = [];
  }
  if(data1.pollType != '3'){
    data1.pollScriptObj.dataType = 'hex';
    data1.pollScriptObj.dataMsg = '';
  }
  if(data1.pollType != '4'){
    data1.pollScriptObj.useSubAddr = '0';
    data1.modbusParam = {
      useSubAddr: '0',
      subAddr: 1,
      modbusFunction: '1',
      regAddr: 0,
      coilCount: 1,
      regCount: 1,
      coilValue: 0,
      regValue: 0,
      coilStatusList: [],
      regValueList: [],
      dataCheckType: '2',
    };
  }
  return data1;
}

async function changeAllCoilStatus(checked: boolean){
  const data = await scriptContentFormApi.getValues();
  const updated = data.modbusParam.coilStatusList.map(() => {
    return checked;
  });
  console.log("changeAllCoilStatus", data);
  await scriptContentFormApi.setValues({
    "modbusParam.coilStatusList": updated
  });
}

const [ScriptContentForm, scriptContentFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },  
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editStep3Schema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-6 gap-x-4',
});

function hex(item:any) {
  if(item){
    return '0x' + parseInt(item).toString(16).padStart(4, '0').toUpperCase();
  }
  return '0x0000';
}

onMounted(() => {
  console.log("onMounted props.modelKeysOptions", props.modelKeysOptions);
  const data = cloneDeep(props.formData);
  data.pollScriptObj = cloneDeep(props.formData.pollScriptObj);
  data.modbusParam = cloneDeep(props.formData.modbusParam);
  data.extendConfigObj = cloneDeep(props.formData.extendConfigObj);
  scriptContentFormApi.setValues(data);
});

defineExpose({
  validate,
  getFormData,
});

</script>
