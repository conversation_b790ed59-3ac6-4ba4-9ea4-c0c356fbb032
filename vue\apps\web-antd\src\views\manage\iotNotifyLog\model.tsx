import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { List } from '#/api/manage/iotNotifyLog'


export class State {
  public notifyLogId = 0; // 日志ID
  public serviceType = '';
  public id = 0;
  public templateId = 0;
  public templateName = '';
  public businessType = null;
  public channelType = '';
  public channelId = 0; // 通知模板ID
  public alarmLogId = 0; // 报警记录ID
  public userType = null; // 用户类型
  public userId = 0; // 接警人员ID
  public phoneNumber = ''; // 手机号
  public alarmConfigId = 0; // 报警配置ID
  public sceneId = 0; // 场景ID
  public alarmLevel = null; // 报警级别
  public notifyContent = null; // 通知内容
  public notifyResult = null; // 通知结果
  public tenantId = ''; // 租户ID
  public deptId = 0; // 所属机构
  public createdAt = ''; // 创建时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'userType',
    component: 'Select',
    label: '用户类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择用户类型',
      options: getDictOptions(DictEnum.NOTIFY_USER_TYPE),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'phoneNumber',
    component: 'Input',
    label: '发送账号',
    componentProps: {
      placeholder: '请输入发送账号',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '渠道名称',
    field: 'channelName',
    align: 'left',
    width: '8%',
    slots: {
      default: ({ row }) => {
        return row.template.channelName
      }
    }
  },
  {
    title: '渠道类型',
    field: 'channelType',
    align: 'left',
    width: '8%',
    slots: {
      default: ({ row }) => {
        return renderDict(row.template.channelType, DictEnum.NOTIFY_CHANNEL_TYPE);
      }
    }
  },
  {
    title: '服务商',
    field: 'template.serviceType',
    align: 'left',
    width: '8%',
    slots: {
      default: ({ row }) => {
        // 根据渠道类型动态选择服务商字典
        let dictType = DictEnum.NOTIFY_PROVIDER_SMS;
        if (row.template.channelType) {
          switch (row.template.channelType) {
            case 1: // 短信
            case 3: // 语音
              dictType = DictEnum.NOTIFY_PROVIDER_SMS;
              break;
            case 2: // 微信
              dictType = DictEnum.NOTIFY_PROVIDER_WECHAT;
              break;
            case 4: // 邮箱
              dictType = DictEnum.NOTIFY_PROVIDER_EMAIL;
              break;
            case 5: // 钉钉
              dictType = DictEnum.NOTIFY_PROVIDER_DING;
              break;
            case 6: // MQTT
              dictType = DictEnum.NOTIFY_PROVIDER_MQTT;
              break;
          }
        }
        return renderDict(row.template.serviceType, dictType);
      }
    },
  },

  {
    title: '模板名称',
    field: 'template.templateName',
    align: 'left',
    width: '8%',
  },
  {
    title: '业务类型', field: 'template.businessType', align: 'left', width: '8%',
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.template.businessType, DictEnum.NOTIFY_BUSINESS_TYPE);
        if (found) {
          return found;
        }
        return row.template.businessType;
      },
    },
  },
  {
    title: '用户类型',
    field: 'userType',
    align: 'left',
    width: '8%',
    slots: {
      default: ({ row }) => {
        return renderDict(row.userType, DictEnum.NOTIFY_USER_TYPE);
      }
    },
  },
  {
    title: '发送账号',
    field: 'phoneNumber',
    align: 'left',
    width: '8%',
  },

  {
    title: '报警记录ID',
    field: 'alarmLogId',
    align: 'left',
    width: '8%',
  },
  {
    title: '发送时间',
    field: 'createdAt',
    align: 'left',
    width: '8%',
  },
  {
    title: '通知结果',
    field: 'notifyResult',
    align: 'left',
    width: -1,
  },

  { title: '操作', width: 120, align: 'left', slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  status: any;
  notifyLogId: number;
  templateId: number;
  channelId: number;
  alarmLogId: number;
  businessType: number;
  userType: number;
  userId: number;
  id: number;
  serviceType: string;
  channelType: string;
  phoneNumber: string;
  alarmConfigId: number;
  sceneId: number;
  alarmLevel: number;
  notifyContent: string;
  notifyResult: string;
  tenantId: string;
  deptId: number;
  templateName: string;
  createdAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'phoneNumber', label: '发送账号' },
  { field: 'notifyContent', label: '通知内容' },
  { field: 'notifyResult', label: '通知结果' },

  { field: 'createdAt', label: '创建时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'notifyLogId',
    component: 'Input',
    label: '日志ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'templateId',
    component: 'InputNumber',
    label: '通知模板ID',
    componentProps: {
      placeholder: '请输入通知模板ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({ required_error: '请输入通知模板ID', invalid_type_error: '无效数字' })
  },
  {
    fieldName: 'alarmLogId',
    component: 'InputNumber',
    label: '报警记录ID',
    componentProps: {
      placeholder: '请输入报警记录ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({ required_error: '请输入报警记录ID', invalid_type_error: '无效数字' })
  },
  {
    fieldName: 'userType',
    component: 'Select',
    label: '用户类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择用户类型',
      options: getDictOptions('notify_user_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired'
  },
  {
    fieldName: 'userId',
    component: 'InputNumber',
    label: '接警人员ID',
    componentProps: {
      placeholder: '请输入接警人员ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({ required_error: '请输入接警人员ID', invalid_type_error: '无效数字' })
  },
  {
    fieldName: 'phoneNumber',
    component: 'Input',
    label: '手机号',
    componentProps: {
      placeholder: '请输入手机号',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'alarmConfigId',
    component: 'InputNumber',
    label: '报警配置ID',
    componentProps: {
      placeholder: '请输入报警配置ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({ required_error: '请输入报警配置ID', invalid_type_error: '无效数字' })
  },
  {
    fieldName: 'sceneId',
    component: 'InputNumber',
    label: '场景ID',
    componentProps: {
      placeholder: '请输入场景ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.number({ required_error: '请输入场景ID', invalid_type_error: '无效数字' })
  },
  {
    fieldName: 'alarmLevel',
    component: 'Select',
    label: '报警级别',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择报警级别',
      options: getDictOptions(DictEnum.ALARM_LEVEL),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired'
  },
  {
    fieldName: 'notifyContent',
    component: 'Input',
    label: '通知内容',
    componentProps: {
      placeholder: '请输入通知内容',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
  {
    fieldName: 'notifyResult',
    component: 'Input',
    label: '通知结果',
    componentProps: {
      placeholder: '请输入通知结果',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];