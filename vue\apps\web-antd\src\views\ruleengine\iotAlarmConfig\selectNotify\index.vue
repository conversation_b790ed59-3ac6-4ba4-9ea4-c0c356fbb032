<script lang="ts" setup>
import { ref, defineProps } from 'vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { List } from '#/api/manage/iotNotifyTemplate';
import { columns, querySchema, type RowType } from './model';
import { useVbenModal, } from '@vben/common-ui';

const props = defineProps<{
  getNotifyKey: (row: RowType) => void;
}>();
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};//
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    // labelField: 'id',
  },
  rowConfig: {
    keyField: 'id',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

//确认按钮回调函数
const onConfirm = () => {
  const row = gridApi.grid.getRadioRecord();
  if (row) {
    props.getNotifyKey(row); // 直接传递整行数据
  }
  modalApi.close();
};

function openModal() {
  modalApi.open();
}

defineExpose({
  openModal,
});
const [Modal, modalApi] = useVbenModal({
  onConfirm: onConfirm,
  onCancel: () => {
    modalApi.close();
  },
  onOpenChange: (isOpen) => {
    if (isOpen) {
      return null;
    }
    modalApi.close();
  },
});
</script>
<template>
  <div>
    <Modal class="h-[600px] w-[1000px]" title="通知模板场景">
      <div class="h-[500px]">
        <Grid table-title="通知模板表"> </Grid>
      </div>
    </Modal>
  </div>
</template>
