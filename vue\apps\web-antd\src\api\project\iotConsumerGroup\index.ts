import { requestClient } from '#/api/request';

// 获取终端用户设备分组列表
export function List(params:any) {
  return requestClient.get<any>('project/iotConsumerGroup/list', { params });
}

// 删除/批量删除终端用户设备分组
export function Delete(params:any) {
  return requestClient.post<any>('project/iotConsumerGroup/delete', { ...params });
}

// 添加/编辑终端用户设备分组
export function Edit(params:any) {
  return requestClient.post<any>('project/iotConsumerGroup/edit', { ...params });
}

// 获取终端用户设备分组指定详情
export function View(params:any) {
  return requestClient.get<any>('project/iotConsumerGroup/view', { params });
}

// 导出终端用户设备分组
export function Export(params:any) {
  return requestClient.post<Blob>('/project/iotConsumerGroup/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}

// 获取终端用户设备分组指定详情
// /api/v1/project/iotConsumerGroup/listWithDeviceCount
export function ListWithDeviceCount(params:any) {
  return requestClient.get<any>('project/iotConsumerGroup/listWithDeviceCount', { params });
}