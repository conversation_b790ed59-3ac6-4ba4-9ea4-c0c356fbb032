<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <template #Inputs="slotProps">
        <Divider orientation="left">输入参数</Divider>
      </template>
      <template #OutPuts="slotProps">
        <Divider orientation="left">输出参数</Divider>
      </template>
      <template #items="slotProps">
        <div>
          <div
            v-for="(item, index) in slotProps.value"
            style="display: flex; align-items: center"
          >
            <InputGroup compact>
              <Input
                type="text"
                :addon-before="'\ ' + item.modelOrder + '\ '"
                :value="item.modelKey"
                style="width: 40%"
                readonly
              />
              <Input
                type="text"
                :value="item.modelName"
                width="70%"
                style="width: 40%"
                readonly
              />
            </InputGroup>
            <Button @click="handleEditParam(index)">编辑</Button>
            <Button @click="handleDeleteParam(index)">删除</Button>
          </div>
          <Button @click="handleAddParamClick">添加参数</Button>
        </div>
      </template>
      <template #outPutItems="slotProps">
        <div>
          <div
            v-for="(item, index) in slotProps.value"
            style="display: flex; align-items: center"
          >
            <InputGroup compact>
              <Input
                type="text"
                :addon-before="'\ ' + item.modelOrder + '\ '"
                :value="item.modelKey"
                style="width: 40%"
                readonly
              />
              <Input
                type="text"
                :value="item.modelName"
                width="70%"
                style="width: 40%"
                readonly
              />
            </InputGroup>
            <Button @click="handleEditParam(index)">编辑</Button>
            <Button @click="handleDeleteParam(index)">删除</Button>
          </div>
          <Button @click="handleAddParamClick">添加参数</Button>
        </div>
      </template>
      <template #enumList="slotProps">
        <div>
          <div
            v-for="(item, index) in slotProps.value"
            style="display: flex; align-items: center"
          >
            <Input
              type="text"
              v-model:value="item.value"
              placeholder="枚举值 例如:0"
            />
            <Input
              type="text"
              v-model:value="item.text"
              placeholder="枚举描述"
            />
            <Button @click="handleDeleteEnum(index)">删除</Button>
          </div>
          <Button @click="handleAddEnum">添加枚举项</Button>
        </div>
      </template>
      <template #itemKeys="slotProps">
        <div style="display: flex; align-items: center">
          <Input v-for="(item, index) in slotProps.value" type="number" :value="item" @change="async (e:any)=>{await editArray(formApi, 'itemKeys', slotProps.value, index, e.target.value); }" />
        </div>
      </template>
    </BasicForm>
    <ObjectModal @confirm="handlerObjectSelected" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Alert, Button, Input, InputGroup, Row } from 'ant-design-vue';
import { Edit, View } from '#/api/device/iotModel';
import { editSchema } from './model';
import objectModal from './object.vue';
import { editArray } from '#/utils/bindArray';
const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
}

const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-4',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-4 gap-x-3',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true });
    const { id, update, view } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;
    if (isUpdate.value || isView.value) {
      const record = await View({ modelId: id });
      record.props = [];
      if (record.isChart === 1) {
        record.props.push('isChart');
      }
      if (record.isMonitor === 1) {
        record.props.push('isMonitor');
      }
      if (record.isReadonly === 1) {
        record.props.push('isReadonly');
      }
      if (record.isHistory === 1) {
        record.props.push('isHistory');
      }
      if (record.isSharePerm === 1) {
        record.props.push('isSharePerm');
      }
      console.log(record);

      await formApi.setValues(record);
    }

    drawerApi.setState({ confirmLoading: false, loading: false });

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            'only-read': true,
          },
        },
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            'only-read': false,
          },
        },
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true });
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    data.type = 'property';
    data.isChart =  data.props && data.props.includes('isChart') ? 1 : 0;
    data.isMonitor = data.props && data.props.includes('isMonitor') ? 1 : 0;
    data.isReadonly = data.props && data.props.includes('isReadonly') ? 1 : 0;
    data.isHistory = data.props && data.props.includes('isHistory') ? 1 : 0;
    data.isSharePerm = data.props && data.props.includes('isSharePerm') ? 1 : 0;

    console.log('handleConfirm', data);
    await Edit(data);
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false });
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

async function handleAddEnum() {
  let datas = await formApi.getValues();
  console.log('handleAddEnum', datas.enumList);
  let latestindex = 0;
  datas.enumList.forEach((item: any) => {
    if (item.index > latestindex) {
      latestindex = item.index;
    }
  });

  await formApi.setValues({
    enumList: [
      ...datas.enumList,
      { index: latestindex + 1, value: '', text: '' },
    ],
  });
}

async function handleDeleteEnum(index: any) {
  console.log('handleDeleteEnum', index);
  let datas = await formApi.getValues();
  datas.enumList = datas.enumList.filter(
    (item: any, i: any) => item.index !== index,
  );
  await formApi.setValues({ enumList: datas.enumList });
}

async function handleDeleteParam(index: any) {
  console.log('handleDeleteParam', index);
  let datas = await formApi.getValues();
  datas.items = datas.items.filter((item: any, i: any) => i !== index);
  await formApi.setValues({ items: datas.items });
}
async function handleEditParam(i: any) {
  let editData = cloneDeep(await formApi.getValues());
  await ObjectModalApi.setData({
    index: i,
    update: true,
    title: '修改参数',
    formData: editData.items[i],
  });
  ObjectModalApi.open();
}

const [ObjectModal, ObjectModalApi] = useVbenModal({
  connectedComponent: objectModal,
});
async function handleAddParamClick() {
  await ObjectModalApi.setData({ title: '添加参数' });
  ObjectModalApi.open();
}

async function handlerObjectSelected(index: any, update: boolean, datas: any) {
  console.log('handlerObjectSelected', datas);
  let editData = await formApi.getValues();
  let newItems = [];
  if (!update) {
    newItems = [...editData.items, datas];
  } else {
    newItems = editData.items.map((item: any, i: any) => {
      if (i === index) {
        return datas;
      }
      return item;
    });
  }
  await formApi.setValues({ items: newItems });
}
</script>
