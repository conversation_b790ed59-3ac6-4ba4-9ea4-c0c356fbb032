import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';
import { renderDict, renderPopoverMemberSumma, type MemberSumma } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public taskId = 0; // 任务ID
  public taskName = ''; // 任务名称
  public taskType = null; // 任务范围
  public upgradeType = 0; // 升级类型
  public firmwareId = 0; // 固件ID
  public firmwareVersion = ''; // 固件版本
  public hardwareVersion = ''; // 硬件版本
  public subPackageSize = 0; // 分包字节大小
  public fileUrl = ''; // 文件地址
  public taskTime = ''; // 预定执行时间
  public taskStatus = null; // 任务状态
  public productKey = ''; // 产品标识
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 更新时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'taskName',
    component: 'Input',
    label: '任务名称',
    componentProps: {
      placeholder: '请输入任务名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'firmwareId',
    component: 'InputNumber',
    label: '固件ID',
    componentProps: {
      placeholder: '请输入固件ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '任务ID',
    field: 'taskId',
    align: 'center',
    headerAlign: 'center',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '任务名称',
    field: 'taskName',
    align: 'center',
    headerAlign: 'center',
    width: -1,
  },
  {
    title: '任务类型',
    field: 'taskType',
    align: 'center',
    headerAlign: 'center',
    width: -1,
    slots: {
      default: ({ row }) => {
        console.log('🎯 任务类型渲染 - row.taskType:', row.taskType, '类型:', typeof row.taskType);
        const result = renderDict(row.taskType, 'upgrade_task_type');
        console.log('🎯 字典渲染结果:', result);
        return result;
      }
    },
  },

  {
    title: '设备数量',
    field: 'deviceCount',
    align: 'center',
    headerAlign: 'center',
    width: -1,
  },
  {
    title: '设备标识',
    field: 'deviceKeys',
    align: 'center',
    headerAlign: 'center',
    width: -1,
    visible: false,
  },
  {
    title: '设备名称',
    field: 'deviceName',
    align: 'center',
    headerAlign: 'center',
    width: -1,
    visible: false, // 隐藏，只做存储
  },
  {
    title: '预定执行时间',
    field: 'taskTime',
    align: 'center',
    headerAlign: 'center',
    width: 200,
  },
  {
    title: '添加时间',
    field: 'createdAt',
    align: 'center',
    headerAlign: 'center',
    width: 200,
  },
  {
    title: '任务描述',
    field: 'remark',
    align: 'center',
    headerAlign: 'center',
    width: -1,
  },
  {
    title: '操作',
    width: 180,
    align: 'center',
    headerAlign: 'center',
    slots: { default: 'action' }
  },
];

// 表格列接口
export interface RowType {
  taskId: number;
  taskName: string;
  taskType: number;
  upgradeType: number;
  firmwareId: number;
  firmwareVersion: string;
  hardwareVersion: string;
  subPackageSize: number;
  fileUrl: string;
  taskTime: string;
  taskStatus: number;
  productKey: string;
  deviceKeys: string; // 设备标识列表（逗号分隔）
  deviceName: string; // 设备名称列表（逗号分隔，隐藏存储）
  deviceCount: number; // 设备数量
  tenantId: string;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  
  { field: 'taskId', label: '任务ID' },
  { field: 'firmwareId', label: '固件id' },
  { field: 'productKey', label: '产品标识' },
  { field: 'productName', label: '产品名称' },
  { field: 'firmwareName', label: '固件名称' },
  { field: 'firmwareVersion', label: '固件版本号' },
  { field: 'deviceKey', label: '设备标识' },
  { field: 'deviceCount', label: '设备数量' },

  { field: 'taskName', label: '任务名称' },
  {
    field: 'taskType',
    label: '任务范围',
    render(row: any) {
      return renderDict(row.taskType, 'upgrade_task_type');
    },
  },
  { field: 'upgradeType', label: '升级类型' },
  { field: 'firmwareId', label: '固件ID' },
  { field: 'firmwareVersion', label: '固件版本' },
  { field: 'hardwareVersion', label: '硬件版本' },
  { field: 'subPackageSize', label: '分包字节大小' },
  { field: 'fileUrl', label: '文件地址' },
  { field: 'taskTime', label: '预定执行时间' },
  {
    field: 'taskStatus',
    label: '任务状态',
    render(row: any) {
      return renderDict(row.taskStatus, DictEnum.FIRMWARE_TYPE);
    },
  },
  { field: 'productKey', label: '产品标识' },
  { field: 'tenantId', label: '租户ID' },
  { field: 'createdDept', label: '创建部门' },
  { field: 'createdBy', label: '创建者' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'updatedAt', label: '更新时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'firmwareId',
    component: 'Input',
    label: '固件ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'taskId',
    component: 'Input',
    label: '任务ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
  {
    fieldName: 'productName',
    component: 'Input',
    label: '产品名称',
    componentProps: {
      placeholder: '请输入任务名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      readonly: true,
    },
    rules: 'required'
  },
  {
    fieldName: 'firmwareName',
    component: 'Input',
    label: '固件名称',
    componentProps: {
      placeholder: '请输入任务名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      readonly: true,
    },
    rules: 'required'
  },
  {
    fieldName: 'firmwareVersion',
    component: 'Input',
    label: '固件版本号',
    componentProps: {
      placeholder: '请输入任务名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      readonly: true,
    },
    rules: 'required'
  },
  {
    fieldName: 'taskName',
    component: 'Input',
    label: '任务名称',
    componentProps: {
      placeholder: '请输入任务名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },

    },
    rules: 'required'
  },
  {
    fieldName: 'taskType',
    component: 'Select',
    label: '任务范围',
    // 不设置默认值，让表单自己处理
    componentProps: {
      placeholder: '请选择任务范围',
      options: getDictOptions('upgrade_task_type'),
      fieldNames: {
        label: 'label',
        value: 'value',
      },
      onUpdateValue: (e: any) => {
        console.log('📝 任务范围选择值:', e, '类型:', typeof e);
        console.log('📝 可用选项:', getDictOptions('upgrade_task_type'));
      },
      onFocus: () => {
        const options = getDictOptions('upgrade_task_type');
        console.log('📝 任务范围下拉框聚焦，当前选项:', options);
        if (options.length === 0) {
          console.warn('📝 任务范围字典选项为空，可能还未加载');
        }
      },
    },
    rules: 'selectRequired'
  },
  {
    fieldName: 'deviceKeys',
    component: 'Input',
    label: '设备Keys',
    componentProps: {
      style: { display: 'none' }, // 隐藏字段，用于存储实际的设备Keys
    },
    formItemClass: 'hidden',
  },
  {
    fieldName: 'deviceName',
    component: 'Input',
    label: '设备名称',
    componentProps: {
      style: { display: 'none' }, // 隐藏字段，用于存储设备名称
    },
    formItemClass: 'hidden',
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备Key',
    componentProps: {
      style: { display: 'none' }, // 隐藏字段，兼容字段
    },
    formItemClass: 'hidden',
  },
  {
    fieldName: 'deviceCount',
    component: 'InputNumber',
    label: '设备数量',
    componentProps: {
      placeholder: '0',
      readonly: true,
      style: { display: 'none' }, // 隐藏数量输入框，只在按钮上显示
    },
    formItemClass: 'hidden',
  },
  {
    fieldName: 'selectDeviceButton',
    component: 'Input',
    label: '选择设备',
    componentProps: {
      style: { display: 'none' }, // 这个字段只用于占位，实际显示在模板中
    },
    formItemClass: 'w-full',
  },

  {
    fieldName: 'taskTime',
    component: 'DatePicker',
    label: '预定执行时间',
    componentProps: {
      type: 'datetime',
      clearable: true,
      shortcuts: 'FMTime',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: {
        format: 'HH:mm:ss',
        hourStep: 1,
        minuteStep: 1,
        secondStep: 1,
      },
      placeholder: '请选择预定执行时间',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },

  {
    fieldName: 'remark',
    component: 'Textarea',
    label: '升级描述',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];