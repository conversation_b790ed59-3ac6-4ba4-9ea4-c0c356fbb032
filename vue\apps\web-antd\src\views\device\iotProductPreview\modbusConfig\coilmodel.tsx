import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public modbusId = 0; // 寄存器ID
  public productKey = ''; // 产品标识
  public modelKey = ''; // 模型标识
  public address = 0; // 寄存器地址
  public subAddr = 1; // 从机地址
  public isReadonly = null; // 是否只读
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '',
    field: 'modbusId',
    align: 'left',
    width: 50,
    type: 'checkbox',
    slots: {
      default: ({ row }) => {
        return '';
      }
    },
  },
  {
    title: '模型标识',
    field: 'modelKey',
    align: 'left',
    width: -1,
 },
  {
    title: '寄存器地址',
    field: 'address',
    align: 'left',
    width: -1,
 },
  {
    title: '从机地址',
    field: 'subAddr',
    align: 'left',
    width: -1,
 },
 {    
				title: '是否只读',    field: 'isReadonly',    align: 'left',    width: -1, 
				slots: {
      				default: ({ row }) => {
						return renderDict(row.isReadonly, 'iot_yes_no');
					}
				},
			},
			  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  modbusId: number;
  productKey: string;
  modelKey: string;
  address: number;
  subAddr: number;
  isReadonly: number;
  tenantId: string;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'modbusId',  label: '寄存器ID'},
  {  field: 'productKey',  label: '产品标识'},
  {  field: 'modelKey',  label: '模型标识'},
  {  field: 'address',  label: '寄存器地址'},
  {  field: 'subAddr',  label: '从机地址'},
  {
				field: 'isReadonly',
				label: '是否只读',
				render(row: any) {
					return renderDict(row.isReadonly, 'iot_yes_no');
				},
			},
			  {  field: 'tenantId',  label: '租户ID'},
  {  field: 'createdAt',  label: '创建时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'modbusId',
    component: 'Input',
    label: '寄存器ID',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
    dependencies: {   show: () => false,    triggerFields: [''],   },
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'modelKey',
    component: 'Input',
    label: '模型标识',
    componentProps: {
      placeholder: '请输入模型标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:'required'
},
  {
    fieldName: 'address',
    component: 'InputNumber',
    label: '寄存器地址',
    componentProps: {
      placeholder: '请输入寄存器地址',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入寄存器地址', invalid_type_error: '无效数字'})
},
  {
    fieldName: 'subAddr',
    component: 'InputNumber',
    label: '从机地址',
    componentProps: {
      placeholder: '请输入从机地址',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  rules:z.number({required_error: '请输入从机地址', invalid_type_error: '无效数字'})
},
  {    
			fieldName: 'isReadonly',    
			component: 'Select',    
			label: '是否只读',    
			defaultValue: null,    
			componentProps: {    
				placeholder: '请选择是否只读',    
				options: getDictOptions('iot_yes_no'),    
				onUpdateValue: (e: any) => {    
					console.log(e);    
				},  
			},
			rules:'selectRequired'
		},
];