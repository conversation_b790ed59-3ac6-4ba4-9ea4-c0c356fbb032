import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

export class State {
	public groupId = 0; // 分组ID
	public groupName = ''; // 分组名称
	public groupOrder = 0; // 排序，值越大，排序越靠前
	public consumerId = 0; // 终端用户ID
	public tenantId = ''; // 租户ID
	public createdAt = ''; // 创建时间

	constructor(state?: Partial<State>) {
		if (state) {
			Object.assign(this, state);
		}
	}
}

export function newState(state: State | Record<string, any> | null): State {
	if (state !== null) {
		if (state instanceof State) {
			return cloneDeep(state);
		}
		return new State(state);
	}
	return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
	{
		fieldName: 'groupName',
		component: 'Input',
		label: '分组名称',
		componentProps: {
			placeholder: '请输入分组名称',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'consumerId',
		component: 'InputNumber',
		label: '终端用户ID',
		componentProps: {
			placeholder: '请输入终端用户ID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: z.number({ required_error: '请输入终端用户ID', invalid_type_error: '无效数字' }),
		formItemClass: 'col-span-1',
	},];

// 表格列
export const columns: VxeGridProps['columns'] = [
	{
		title: '分组ID',
		field: 'groupId',
		align: 'left',
		width: -1,
		type: 'checkbox',
	},
	{
		title: '分组名称',
		field: 'groupName',
		align: 'left',
		width: -1,
	},
	{
		title: '排序，值越大，排序越靠前',
		field: 'groupOrder',
		align: 'left',
		width: -1,
	},
	{
		title: '终端用户ID',
		field: 'consumerId',
		align: 'left',
		width: -1,
	},
	{
		title: '租户ID',
		field: 'tenantId',
		align: 'left',
		width: -1,
	},
	{
		title: '创建时间',
		field: 'createdAt',
		align: 'left',
		width: -1,
	},
	{ title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
	groupId: number;
	groupName: string;
	groupOrder: number;
	consumerId: number;
	tenantId: string;
	createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
	{ field: 'groupId', label: '分组ID' },
	{ field: 'groupName', label: '分组名称' },
	{ field: 'groupOrder', label: '排序，值越大，排序越靠前' },
	{ field: 'consumerId', label: '终端用户ID' },
	{ field: 'tenantId', label: '租户ID' },
	{ field: 'createdAt', label: '创建时间' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
	{
		fieldName: 'groupId',
		component: 'Input',
		label: '分组ID',
		dependencies: { show: () => false, triggerFields: [''], },
		componentProps: {
			placeholder: '',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'groupName',
		component: 'Input',
		label: '分组名称',
		componentProps: {
			placeholder: '请输入分组名称',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
		rules: 'required',
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'groupOrder',
		component: 'InputNumber',
		label: '排序，值越大，排序越靠前',
		componentProps: {
			placeholder: '请输入排序，值越大，排序越靠前',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: null,
		formItemClass: 'col-span-1',
	}, {
		fieldName: 'consumerId',
		component: 'InputNumber',
		label: '终端用户ID',
		componentProps: {
			placeholder: '请输入终端用户ID',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		}, rules: z.number({ required_error: '请输入终端用户ID', invalid_type_error: '无效数字' }),
		formItemClass: 'col-span-1',
	},];