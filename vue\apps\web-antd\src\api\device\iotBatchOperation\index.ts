import { requestClient } from '#/api/request';

// 获取批量操作任务列表
export function List(params:any) {
  return requestClient.get<any>('device/iotBatchOperation/list', { params });
}

// 删除/批量删除批量操作任务
export function Delete(params:any) {
  return requestClient.post<any>('device/iotBatchOperation/delete', { ...params });
}

// 添加/编辑批量操作任务
export function Edit(params:any) {
  return requestClient.post<any>('device/iotBatchOperation/edit', { ...params });
}

// 获取批量操作任务指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotBatchOperation/view', { params });
}

// 导出批量操作任务
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotBatchOperation/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}