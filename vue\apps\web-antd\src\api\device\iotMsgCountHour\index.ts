import { requestClient } from '#/api/request';

// 获取设备消息汇总列表
export function List(params:any) {
  return requestClient.get<any>('device/iotMsgCountHour/list', { params });
}

// 删除/批量删除设备消息汇总
export function Delete(params:any) {
  return requestClient.post<any>('device/iotMsgCountHour/delete', { ...params });
}

// 添加/编辑设备消息汇总
export function Edit(params:any) {
  return requestClient.post<any>('device/iotMsgCountHour/edit', { ...params });
}

// 获取设备消息汇总指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotMsgCountHour/view', { params });
}

// 导出设备消息汇总
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotMsgCountHour/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}