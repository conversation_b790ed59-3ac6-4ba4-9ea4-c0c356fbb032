<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <template #fileUrl="slotProps">
        <div style="display: flex; flex-direction: column; gap: 8px;">
          <Upload
            :file-list="fileList"
            :before-upload="beforeUpload"
            :custom-request="customRequest"
            accept=".bin,.hex,.fw,.zip"
            :multiple="false"
            list-type="text"
            @remove="handleRemove"
          >
            <Button type="primary">
              <template #icon>
                <svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor">
                  <path d="M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 0 0-12.6 0l-112 141.8c-4.1 5.2-.4 12.9 6.3 12.9z"/>
                  <path d="M878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"/>
                </svg>
              </template>
              上传固件文件
            </Button>
          </Upload>
          <div style="color: #666; font-size: 12px; margin-top: 4px;">
            请上传不超过 <span style="color: #1890ff; font-weight: bold;">100MB</span> 的
            <span style="color: #1890ff; font-weight: bold;">bin/hex/fw/zip</span> 格式文件
          </div>
          <div v-if="slotProps.value" style="color: #666; font-size: 12px;">
            当前文件: {{ slotProps.value }}
          </div>
        </div>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { Button, Input, Upload, message } from 'ant-design-vue';
import type { UploadFile } from 'ant-design-vue';
import { uploadApi } from '#/api';
import { Edit, View } from '#/api/manage/iotFirmware';
import { editSchema } from './model';

//import SelectProduct from './src/views/manage/iotFirmware/popup1/index.vue';
import { getDictOptions } from '#/utils/dict';
import { DictEnum } from '@vben/constants';

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
  productKey?: string;
  productName?: string;
  firmwareType?: number;
}

const isUpdate = ref(false);
const isView = ref(false);

// 产品选择相关
const selectProductRef = ref();

// 存储原始的固件类型key值
const originalFirmwareTypeKey = ref<string | null>(null);

// 文件上传相关
const fileList = ref<UploadFile[]>([]);

// 打开产品选择弹窗
function openSelectProduct() {
  selectProductRef.value?.openModal();
}

// 文件上传前的验证
const beforeUpload = (file: File) => {
  const isValidType = ['bin', 'hex', 'fw', 'zip'].some(ext =>
    file.name.toLowerCase().endsWith(`.${ext}`)
  );
  if (!isValidType) {
    message.error('只能上传 bin/hex/fw/zip 格式的文件!');
    return false;
  }
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    message.error('文件大小不能超过 100MB!');
    return false;
  }

  // 清空之前的文件列表（因为只允许一个文件）
  fileList.value = [];

  // 添加新文件到列表
  const uploadFile: UploadFile = {
    uid: file.name + Date.now(),
    name: file.name,
    status: 'uploading',
    originFileObj: file as any,
  };
  fileList.value.push(uploadFile);

  return true;
};

// 自定义上传请求
const customRequest = async (options: any) => {
  const { file, onProgress, onSuccess, onError } = options;

  try {
    // 进度回调
    const progressEvent = (e: any) => {
      const percent = Math.round((e.loaded / e.total) * 100);
      onProgress({ percent });
    };

    // 调用上传API
    const response = await uploadApi(file, progressEvent);

    // 上传成功
    onSuccess(response);
    message.success('文件上传成功!');

    // 更新文件列表状态
    const currentFile = fileList.value.find(f => f.originFileObj === file);
    if (currentFile) {
      currentFile.status = 'done';
      currentFile.response = response;
      currentFile.url = response.url || response.data?.url;
    }

    // 更新表单值
    const fileUrl = response.url || response.data?.url;
    if (fileUrl) {
      formApi.setFieldValue('fileUrl', fileUrl);
    }

  } catch (error) {
    console.error('文件上传失败:', error);

    // 更新文件列表状态
    const currentFile = fileList.value.find(f => f.originFileObj === file);
    if (currentFile) {
      currentFile.status = 'error';
    }

    onError(error);
    message.error('文件上传失败!');
  }
};

// 移除文件
const handleRemove = (file: UploadFile) => {
  const index = fileList.value.findIndex(item => item.uid === file.uid);
  if (index > -1) {
    fileList.value.splice(index, 1);
  }
  // 清空表单值
  formApi.setFieldValue('fileUrl', '');
  return true;
};

// 处理产品选择回调
async function handleProductSelected(productKey: string, productName: string, firmwareType: string) {
  console.log('选择的产品:', { productKey, productName, firmwareType });
  console.log('firmwareType 原始类型:', typeof firmwareType, 'firmwareType 原始值:', firmwareType);

  // 确保firmwareType是字符串类型，与字典选项的value类型一致
  const firmwareTypeValue = String(firmwareType);

  // 保存原始的key值
  originalFirmwareTypeKey.value = firmwareTypeValue;

  console.log('设置固件类型值:', firmwareTypeValue, '类型:', typeof firmwareTypeValue);

  // 获取字典选项并找到对应的标签
  const dictOptions = getDictOptions(DictEnum.FIRMWARE_TYPE);
  const matchedOption = dictOptions.find(opt => opt.value === firmwareTypeValue);
  const firmwareTypeLabel = matchedOption ? matchedOption.label : firmwareTypeValue;

  console.log('设置固件类型值:', firmwareTypeValue, '显示标签:', firmwareTypeLabel);
  console.log('保存原始key值:', originalFirmwareTypeKey.value);

  try {
    // 动态设置固件类型下拉选项
    const firmwareTypeOptions = [{ label: firmwareTypeLabel, value: firmwareTypeLabel }];
    formApi.updateSchema([
      {
        fieldName: 'firmwareType',
        componentProps: {
          options: firmwareTypeOptions,
          disabled: true, // 保持禁用状态
        },
      },
    ]);

    // 批量设置表单字段值
    await formApi.setValues({
      productKey: productKey,
      productName: productName,
      firmwareType: firmwareTypeLabel, // 设置为字典标签而不是key
    });

    console.log('✅ 表单字段设置成功');

  } catch (error) {
    console.error('❌ 设置表单字段失败:', error);
  }
}

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    console.log('🔍 edit抽屉打开');
    drawerApi.setState({ confirmLoading: true, loading: true })
    const drawerData = drawerApi.getData() as ModalProps;
    console.log('📋 edit抽屉接收到的完整数据:', drawerData);

    const { id, update, view, productKey, productName, firmwareType } = drawerData;
    isUpdate.value = update;
    isView.value = view;

    console.log('📋 抽屉模式:', { update, view });
    console.log('📋 父组件传递的产品信息:', { productKey, productName, firmwareType });

    // 清空文件列表和原始固件类型key值
    fileList.value = [];
    originalFirmwareTypeKey.value = null;

    // 如果是新增模式且父组件传递了产品信息，自动填入
    if (!update && !view && productKey && productName) {
      console.log('🔄 新增模式 - 准备自动填入父组件产品信息:', { productKey, productName, firmwareType });

      // 延迟设置，确保表单已经初始化
      setTimeout(async () => {
        try {
          console.log('🔄 开始设置表单值...');

          // 处理固件类型字段
          if (firmwareType !== undefined && firmwareType !== null) {
            console.log('🔍 处理父组件传递的固件类型:', firmwareType, '类型:', typeof firmwareType);

            const dictOptions = getDictOptions(DictEnum.FIRMWARE_TYPE);
            const firmwareTypeKey = String(firmwareType);
            // 保存原始key值
            originalFirmwareTypeKey.value = firmwareTypeKey;

            const matchedOption = dictOptions.find(opt => opt.value === firmwareTypeKey);
            const firmwareTypeLabel = matchedOption ? matchedOption.label : firmwareTypeKey;

            console.log('固件类型转换: key', firmwareTypeKey, '-> 标签', firmwareTypeLabel);
            console.log('保存原始key值:', originalFirmwareTypeKey.value);

            // 动态设置固件类型下拉选项
            const firmwareTypeOptions = [{ label: firmwareTypeLabel, value: firmwareTypeLabel }];
            formApi.updateSchema([
              {
                fieldName: 'firmwareType',
                componentProps: {
                  options: firmwareTypeOptions,
                  disabled: true, // 保持禁用状态
                },
              },
            ]);

            // 设置表单值，包含固件类型
            await formApi.setValues({
              productKey: productKey,
              productName: productName,
              firmwareType: firmwareTypeLabel, // 设置为字典标签
            });
          } else {
            // 没有固件类型时，只设置产品信息
            await formApi.setValues({
              productKey: productKey,
              productName: productName,
            });
          }

          console.log('✅ 父组件产品信息自动填入成功');

          // 验证设置结果
          const currentValues = await formApi.getValues();
          console.log('📋 设置后的表单值:', currentValues);
        } catch (error) {
          console.error('❌ 自动填入产品信息失败:', error);
        }
      }, 200);
    } else {
      console.log('⚠️ 不满足自动填入条件:', {
        isNewMode: !update && !view,
        hasProductKey: !!productKey,
        hasProductName: !!productName
      });
    }

    if (isUpdate.value || isView.value) {
      const record = await View({ firmwareId: id });

      // 处理isLatest字段的数据类型转换
      if (record.isLatest !== undefined && record.isLatest !== null) {
        // 将数字类型转换为布尔类型，Switch组件需要布尔值
        record.isLatest = record.isLatest === 1 || record.isLatest === true;
      }

      // 处理固件类型字段，将key转换为字典标签显示
      if (record.firmwareType !== undefined && record.firmwareType !== null) {
        console.log('🔍 处理固件类型字段');
        console.log('原始固件类型值:', record.firmwareType, '类型:', typeof record.firmwareType);

        const dictOptions = getDictOptions(DictEnum.FIRMWARE_TYPE);
        console.log('固件类型字典选项:', dictOptions);

        const firmwareTypeKey = String(record.firmwareType);
        // 保存原始key值
        originalFirmwareTypeKey.value = firmwareTypeKey;

        const matchedOption = dictOptions.find(opt => opt.value === firmwareTypeKey);
        console.log('查找key:', firmwareTypeKey, '匹配结果:', matchedOption);
        console.log('保存原始key值:', originalFirmwareTypeKey.value);

        if (matchedOption) {
          const firmwareTypeLabel = matchedOption.label;
          console.log('✅ 找到匹配选项，标签:', firmwareTypeLabel);
          record.firmwareType = firmwareTypeLabel; // 显示字典标签

          // 动态设置固件类型下拉选项
          const firmwareTypeOptions = [{ label: firmwareTypeLabel, value: firmwareTypeLabel }];
          formApi.updateSchema([
            {
              fieldName: 'firmwareType',
              componentProps: {
                options: firmwareTypeOptions,
                disabled: true, // 保持禁用状态
              },
            },
          ]);
        } else {
          console.log('❌ 未找到匹配的字典选项');
          // 如果没找到匹配项，保持原值
          const firmwareTypeOptions = [{ label: String(record.firmwareType), value: String(record.firmwareType) }];
          formApi.updateSchema([
            {
              fieldName: 'firmwareType',
              componentProps: {
                options: firmwareTypeOptions,
                disabled: true,
              },
            },
          ]);
        }
      }

      console.log('编辑模式 - 设置表单值:', record);
      console.log('isLatest字段值:', record.isLatest, '类型:', typeof record.isLatest);
      console.log('固件类型字段值:', record.firmwareType);

      // 如果有文件URL，初始化文件列表
      if (record.fileUrl) {
        const fileName = record.fileUrl.split('/').pop() || '固件文件';
        fileList.value = [{
          uid: 'existing-file',
          name: fileName,
          status: 'done',
          url: record.fileUrl,
        }];
      } else {
        fileList.value = [];
      }

      await formApi.setValues(record);
    }
    drawerApi.setState({ confirmLoading: false, loading: false })

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());

    // 处理isLatest字段的数据类型转换，后端可能需要数字类型
    if (data.isLatest !== undefined && data.isLatest !== null) {
      data.isLatest = data.isLatest ? 1 : 0;
    }

    // 处理固件类型字段，使用保存的原始key值
    if (data.firmwareType !== undefined && data.firmwareType !== null) {
      if (originalFirmwareTypeKey.value) {
        data.firmwareType = originalFirmwareTypeKey.value; // 使用保存的原始key值
        console.log('固件类型使用原始key值:', originalFirmwareTypeKey.value);
      } else {
        // 如果没有保存的key值，尝试从字典中查找
        const dictOptions = getDictOptions(DictEnum.FIRMWARE_TYPE);
        const matchedOption = dictOptions.find(opt => opt.label === data.firmwareType);
        if (matchedOption) {
          data.firmwareType = matchedOption.value; // 转换回key值
          console.log('固件类型转换: 标签', data.firmwareType, '-> key', matchedOption.value);
        } else {
          console.log('⚠️ 未找到固件类型标签对应的key值:', data.firmwareType);
        }
      }
    }

    console.log('提交数据:', data);
    console.log('isLatest提交值:', data.isLatest, '类型:', typeof data.isLatest);
    console.log('firmwareType提交值:', data.firmwareType, '类型:', typeof data.firmwareType);

    await (Edit(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();

  // 清空文件列表和相关状态
  fileList.value = [];
  originalFirmwareTypeKey.value = null;
}

</script>