import { requestClient } from '#/api/request';

// 获取七牛云回调日志表列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotQiniuLog/list', { params });
}

// 删除/批量删除七牛云回调日志表
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotQiniuLog/delete', { ...params });
}

// 添加/编辑七牛云回调日志表
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotQiniuLog/edit', { ...params });
}

// 获取七牛云回调日志表指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotQiniuLog/view', { params });
}

// 导出七牛云回调日志表
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotQiniuLog/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}