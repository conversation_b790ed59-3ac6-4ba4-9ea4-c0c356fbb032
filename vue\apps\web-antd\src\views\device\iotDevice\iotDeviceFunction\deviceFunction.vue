<template>
  <Card>
    <div class="device-function-container">
      <!-- 调试信息 -->
      <div v-if="loading" class="loading-info">
        <Spin size="large" />
        <p>正在加载功能列表...</p>
      </div>

      <!-- 左侧Tab -->
      <div v-if="!loading">
        <Tabs tab-position="left" v-model:activeKey="activeTabKey">
          <TabPane v-for="func in functionList" :key="func.key" :tab="func.name">
            <!-- Tab内容可以为空，因为详细内容会在右侧显示 -->
          </TabPane>
        </Tabs>

        <!-- 如果没有功能，显示提示 -->
        <div v-if="functionList.length === 0" class="no-functions">
          <Empty description="该产品暂无可执行功能" />
        </div>
      </div>

      <!-- 中间和右侧区域容器 -->
      <div class="content-container">
        <!-- 中间表格区域 -->
        <div class="middle-table">
          <Card :bordered="false" class="table-card">
            <!-- 当选中功能时总是显示表格，无论是否有参数 -->
            <div v-if="currentFunction" class="grid-container">
              <!-- 总是显示表格，即使没有参数 -->
              <Grid>
                <template #value="{ row }">
                  <!-- 根据参数类型显示不同的输入控件 -->
                  <Input v-if="row.valueType?.type === 'string'" v-model:value="row.value" placeholder="请输入参数值" />
                  <div v-else-if="row.valueType?.type === 'integer' || row.valueType?.type === 'decimal'"
                    class="input-with-unit">
                    <InputNumber v-model:value="row.value" placeholder="请输入数值" style="width: 100%" />
                    <span v-if="row.valueType?.unit" class="unit-suffix">{{ row.valueType.unit }}</span>
                  </div>
                  <Switch v-else-if="row.valueType?.type === 'bool'" v-model:checked="row.value" />
                  <Select v-else-if="row.valueType?.type === 'enum'" v-model:value="row.value" style="width: 100%"
                    placeholder="请选择">
                    <Select.Option v-for="option in row.valueType?.enumList" :key="option.value" :value="option.value">
                      {{ option.text }}
                    </Select.Option>
                  </Select>
                  <span v-else>{{ row.value }}</span>
                </template>
              </Grid>
            </div>
            <div v-else class="no-function-selected">
              <Empty description="请选择左侧功能" />
            </div>

            <!-- 当选中功能时显示执行按钮，无论是否有参数 -->
            <div class="action-buttons" v-if="currentFunction">
              <Button type="primary" @click="executeFunction" :loading="executing">执行</Button>
              <Button v-if="currentFunctionParams.length > 0" @click="resetParams">清空</Button>
            </div>
          </Card>
        </div>

        <!-- 右侧结果区域 -->
        <div class="right-result">
          <Card :bordered="false" title="执行结果">
            <Spin :spinning="executing">
              <div class="result-content">
                <template v-if="executionResult">
                  <div class="result-item">
                    <div class="result-label">执行状态:</div>
                    <div class="result-value">
                      <Tag :color="executionResult.success ? 'success' : 'error'">
                        {{ executionResult.success ? '成功' : '失败' }}
                      </Tag>
                    </div>
                  </div>

                  <div class="result-item">
                    <div class="result-label">执行时间:</div>
                    <div class="result-value">{{ executionResult.time }}</div>
                  </div>

                  <div class="result-item">
                    <div class="result-label">返回结果:</div>
                    <div class="result-value">
                      <pre
                        v-if="executionResult.data !== null">{{ JSON.stringify(executionResult.data, null, 2) }}</pre>
                      <span v-else class="null-result">null</span>
                    </div>
                  </div>

                  <div v-if="!executionResult.success" class="result-item">
                    <div class="result-label">错误信息:</div>
                    <div class="result-value error-message">
                      {{ executionResult.message }}

                      <!-- 针对设备不在线的特殊提示 -->
                      <div v-if="executionResult.originalMessage === 'device not online'" class="error-hint">
                        系统显示设备在线，但实际连接可能已断开。建议：
                        <ul>
                          <li>刷新页面获取最新设备状态</li>
                          <li>检查设备电源和网络连接</li>
                          <li>等待几分钟后再尝试</li>
                          <li>如果问题持续存在，可能需要重启设备</li>
                        </ul>
                      </div>

                      <!-- 针对超时的特殊提示 -->
                      <div v-else-if="executionResult.message === 'call timeout'" class="error-hint">
                        设备响应超时，请检查设备是否正常工作或网络连接是否稳定
                      </div>
                    </div>
                  </div>

                  <!-- 显示错误码 -->
                  <div v-if="executionResult.errorCode" class="result-item">
                    <div class="result-label">错误码:</div>
                    <div class="result-value">{{ executionResult.errorCode }}</div>
                  </div>
                </template>

                <Empty v-else description="暂无执行结果" class="empty-result" />
              </div>
            </Spin>
          </Card>
        </div>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { Card, Switch, Tabs, TabPane, Button, message, Input, InputNumber, Select, Tag, Spin, Empty } from 'ant-design-vue';
import { View as getProductDetail } from '#/api/device/iotProduct';
// 取消注释并导入FunctionDo接口
import { FunctionDo } from '#/api/device/iotDevice';
import { getDefaultValue } from './model';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeGridProps, VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';

const props = defineProps<{
  productKey: string;
  deviceKey: string;
}>();

// 状态变量
interface FunctionItem {
  key: string;
  name: string;
  inputs?: any[];
  [key: string]: any;
}
const functionList = ref<FunctionItem[]>([]);
const activeTabKey = ref('');
const loading = ref(false);
const executing = ref(false);
interface ExecutionResult {
  success: boolean;
  time: string;
  data: any;
  message: any;
  errorCode?: number;
  originalMessage?: string;
}
const executionResult = ref<ExecutionResult | null>(null);
const currentFunctionParams = ref<any[]>([]);

// Grid 表格列定义
const columns: VxeGridProps['columns'] = [
  {
    title: '',
    field: 'key',
    align: 'center',
    width: '8%',
    type: 'checkbox',
  },
  {
    title: '参数名称',
    field: 'name',
    align: 'left',
    width: '20%',
  },
  {
    title: '输入类型',
    field: 'inputType',
    align: 'center',
    width: '20%',
    formatter: ({ row }) => {
      return getTypeName(row.valueType?.type);
    },
  },
  {
    title: '参数值',
    field: 'value',
    align: 'left',
    width: '52%',
    slots: { default: 'value' },
  },
];

// Grid 配置选项
const gridOptions: VxeTableGridOptions<any> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'key',
  },
  columns: columns,
  height: 650, // 设置固定最大高度，超出部分滚动
  keepSource: true,
  showOverflow: false,
  pagerConfig: { enabled: false, },
  data: [],
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};

const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
});

const CheckboxChecked = ref(false);

function handleCheckboxChange() {
  if (gridApi?.grid) {
    CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
    // 更新选中的行键
    selectedRowKeys.value = gridApi.grid.getCheckboxRecords().map(record => record.key);
  }
}

// 添加选中状态管理
const selectedRowKeys = ref<string[]>([]);

// 监听参数列表变化，更新Grid数据并自动全选
watch(currentFunctionParams, async (newParams) => {

  // 如果gridApi还未初始化，等待一下
  if (!gridApi?.grid) {
    await nextTick();
    // 如果仍然没有初始化，再等待一点时间
    if (!gridApi?.grid) {
      setTimeout(() => {
        if (gridApi?.grid && newParams) {
          loadGridData(newParams);
        }
      }, 100);
      return;
    }
  }

  await loadGridData(newParams);
}, { immediate: true });

// 提取Grid数据加载逻辑
async function loadGridData(params: any[]) {
  if (!gridApi?.grid) return;

  try {
    // 直接通过设置Grid的data来更新数据
    gridApi.setGridOptions({
      data: params || []
    });

    if (params && params.length > 0) {
      // 使用 nextTick 确保数据加载完成后再设置选中状态
      await nextTick();
      gridApi.grid.setAllCheckboxRow(true);
      selectedRowKeys.value = params.map(param => param.key);
    } else {
      selectedRowKeys.value = [];
    }
  } catch (error) {
    console.error('Grid数据加载失败:', error);
  }
}

// 获取当前选中的功能
const currentFunction = computed(() => {
  return functionList.value.find(func => func.key === activeTabKey.value) || null;
});

// 获取类型名称
function getTypeName(type: string): string {
  return type || '未知';
}

// 监听选中的功能变化，更新参数列表
watch(currentFunction, (newFunc) => {
  if (newFunc) {
    // 深拷贝参数，避免修改原始数据
    // 注意：根据数据结构，函数的输入参数可能在inputs字段中
    const inputParams = newFunc.inputs || [];

    currentFunctionParams.value = inputParams.map(param => {
      return {
        ...param,
        value: getDefaultValue(param) // 设置默认值
      };
    });
  } else {
    currentFunctionParams.value = [];
  }
});

// 根据参数类型获取默认值
function getDefaultValueLocal(param: any) {
  if (!param || !param.valueType) return '';

  const type = param.valueType.type;

  if (type === 'bool') return false;
  if (type === 'integer' || type === 'decimal') return 0;
  if (type === 'enum' && param.valueType.enumList && param.valueType.enumList.length > 0) {
    return param.valueType.enumList[0].value;
  }
  return '';
}

// 获取产品功能列表
async function fetchProductFunction() {
  loading.value = true;
  try {
    // 使用View接口获取产品详情
    const res = await getProductDetail({ productKey: props.productKey });

    // 根据接口数据结构获取functions
    if (res && res.tsl && res.tsl.functions) {
      functionList.value = res.tsl.functions;

      // 如果有功能，默认选中第一个
      if (functionList.value.length > 0) {
        activeTabKey.value = functionList.value[0]?.key ?? '';

        // 手动触发一次功能变化，确保参数加载
        await nextTick();
        const firstFunc = functionList.value[0];
        if (firstFunc) {
          const inputParams = firstFunc.inputs || [];
          currentFunctionParams.value = inputParams.map(param => {
            return {
              ...param,
              value: getDefaultValue(param)
            };
          });
        }
      }
    } else {
      functionList.value = [];
    }
  } catch (error) {
    message.error('获取产品功能失败');
  } finally {
    loading.value = false;
  }
}

// 执行功能
async function executeFunction() {
  if (!currentFunction.value) {
    message.warning('请选择要执行的功能');
    return;
  }

  let inputParams: Record<string, any> = {};

  // 如果有参数，则获取选中的记录
  if (currentFunctionParams.value.length > 0) {
    const selectedRecords = gridApi?.grid ? gridApi.grid.getCheckboxRecords() : [];
    if (selectedRecords.length === 0) {
      message.warning('请选择要执行的参数');
      return;
    }

    // 构建参数对象
    selectedRecords.forEach((param: any) => {
      inputParams[param.key] = param.value;
    });
  }
  // 如果没有参数，inputParams 保持为空对象

  executing.value = true;
  try {
    // 调用设备功能执行API
    const result = await FunctionDo({
      productKey: props.productKey,
      deviceKey: props.deviceKey,
      functionKey: currentFunction.value.key,
      input: inputParams,
      isAsync: false,
      syncTimeout: 10
    });

    console.log('执行结果:', result);
    // 处理特定错误码和消息
    if (result.code === 50 && result.message === 'device not online') {
      // 设备不在线的特殊处理
      executionResult.value = {
        success: false,
        time: new Date().toLocaleString(),
        data: result.data || null,
        message: '设备不在线，请确认设备实际连接状态',
        errorCode: result.code,
        originalMessage: result.message
      };

      // 显示更友好的错误提示
      message.error('设备不在线，无法执行功能。请刷新页面获取最新设备状态或检查设备连接。');
    } else {
      // 其他情况的处理
      executionResult.value = {
        success: result.code === 0,
        time: new Date().toLocaleString(),
        data: result || inputParams,
        message: result.message || '操作成功',
        errorCode: result.code !== 0 ? result.code : undefined
      };

      if (result.code === 0) {
        message.success('功能执行成功');
      } else {
        message.error(`功能执行失败: ${result.message || `错误码: ${result.code}`}`);
      }
    }
  } catch (error) {
    let errorMessage = '未知错误';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'object' && error !== null) {
      errorMessage = JSON.stringify(error);
    } else if (error !== null && error !== undefined) {
      errorMessage = String(error);
    }

    message.error(`执行功能失败: ${errorMessage}`);
    executionResult.value = {
      success: false,
      time: new Date().toLocaleString(),
      data: null,
      message: errorMessage
    };
  } finally {
    executing.value = false;
  }
}

// 重置参数
function resetParams() {
  if (currentFunction.value) {
    const inputParams = currentFunction.value.inputs || [];
    currentFunctionParams.value = inputParams.map((param: any) => ({
      ...param,
      value: getDefaultValueLocal(param)
    }));
  }
  message.success('参数已重置');
}

onMounted(async () => {

  // 先获取产品功能列表
  await fetchProductFunction();

  // 确保Grid已经初始化
  await nextTick();

  // 如果有默认选中的功能且有参数，再次确保数据加载
  if (activeTabKey.value && currentFunctionParams.value.length > 0) {
    setTimeout(() => {
      if (gridApi?.grid && currentFunctionParams.value.length > 0) {
        loadGridData(currentFunctionParams.value);
      }
    }, 200);
  }
});
</script>

<style scoped>
.device-function-container {
  display: flex;
  height: 100%;
  min-height: 400px;
  min-width: 0;
  gap: 12px;
}

.content-container {
  display: flex;
  flex: 1;
  gap: 12px;
  height: 100%;
  min-width: 0;
  overflow-x: auto;
}

.left-tabs {
  width: 160px;
  flex-shrink: 0;
  background-color: #fff;
  border-radius: 4px;
  overflow-y: auto;
}

.middle-table,
.right-result {
  flex: 1 1 0;
  min-width: 0;
  /* 防止内容溢出撑破布局 */
  max-width: 100%;
  display: flex;
  flex-direction: column;
}

.table-card,
.right-result>.ant-card {
  flex: 1 1 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.right-result {
  background-color: #fff;
  border-radius: 4px;
  overflow-y: auto;
}

.middle-table {
  flex: 0 1 60%;
  min-width: 0;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  margin-right: 8px;
}

.table-card {
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
}

/* 针对表格Card的特殊处理 */
.table-card :deep(.ant-card-body) {
  padding: 0;
  /* 表格Card完全无内边距 */
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* Grid容器占据剩余空间 */
.grid-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  overflow: hidden;
}

.action-buttons {
  margin-top: 0;
  margin-bottom: 0;
  padding: 12px 0;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.result-content {
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.empty-result {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  margin-top: 60px;
  /* 调整空结果的垂直位置 */
}

/* 覆盖Ant Design的Empty组件样式 */
:deep(.ant-empty) {
  margin: 0 auto;
}

:deep(.ant-empty-image) {
  height: auto;
  margin-bottom: 16px;
}

:deep(.ant-empty-description) {
  color: #8c8c8c;
}

.result-item {
  margin-bottom: 16px;
}

.result-label {
  font-weight: 500;
  margin-bottom: 4px;
}

.result-value {
  word-break: break-all;
}

.result-value pre {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  max-height: 350px;
  overflow: auto;
}

.error-message {
  color: #ff4d4f;
}

.null-result {
  color: #999;
  font-style: italic;
}

.error-hint {
  font-size: 12px;
  color: #ff7875;
  margin-top: 8px;
  background-color: #fff2f0;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ffccc7;
}

.error-hint ul {
  margin: 4px 0 0 0;
  padding-left: 20px;
}

.error-hint li {
  margin-bottom: 4px;
}

/* 隐藏Tab内容区域，去掉蓝色间隙 */
:deep(.ant-tabs-left > .ant-tabs-content-holder) {
  display: none;
  /* 完全隐藏Tab内容区域 */
}

/* 适配小屏幕 */
@media (max-width: 1200px) {
  .device-function-container {
    flex-direction: column;
    height: auto;
  }

  .left-tabs {
    width: 100%;
    height: auto;
    max-height: 200px;
  }

  .middle-table,
  .right-result {
    width: 100%;
  }
}

@media (max-width: 900px) {
  .content-container {
    flex-direction: column;
    overflow-x: visible;
  }

  .middle-table,
  .right-result {
    min-width: 0;
    width: 100%;
    max-width: 100%;
  }
}

/* 添加单位样式 */
.input-with-unit {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.unit-suffix {
  position: absolute;
  right: 10px;
  color: rgba(0, 0, 0, 0.45);
  pointer-events: none;
}

/* 确保InputNumber的内容不会与单位重叠 */
:deep(.ant-input-number-input) {
  padding-right: 25px;
}

/* 调试样式 */
.loading-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
}

.no-functions {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.no-function-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Grid表格容器样式已整合到上面的.grid-container中 */
</style>
