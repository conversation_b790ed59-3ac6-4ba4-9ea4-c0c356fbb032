import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public infoId = 0; // 访问ID
  public consumerId = 0; // 终端用户ID
  public loginName = ''; // 登录账号
  public ipaddr = ''; // 登录IP地址
  public loginLocation = ''; // 登录地点
  public loginMethod = 0; // 登录类型
  public browser = ''; // 浏览器类型
  public os = ''; // 操作系统
  public status = 0; // 登录状态
  public msg = ''; // 提示消息
  public logModule = ''; // 登录模块
  public createdAt = ''; // 创建时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
				fieldName: 'consumerId',
				component: 'InputNumber',
				label: '终端用户ID',
				componentProps: {
					placeholder: '请输入终端用户ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:null,
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'loginName',
				component: 'Input',
				label: '登录账号',
				componentProps: {
					placeholder: '请输入登录账号',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'loginMethod',
				component: 'InputNumber',
				label: '登录类型',
				componentProps: {
					placeholder: '请输入登录类型',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:null,
				formItemClass: 'col-span-1',
			},  {    
			fieldName: 'status',    
			component: 'Select',    
			label: '登录状态',    
			defaultValue: null,    
			componentProps: {    
				placeholder: '请选择登录状态',    
				options: getDictOptions('consumer_login_statue'),    
				onUpdateValue: (e: any) => {    
					console.log(e);    
				},  
			},
			rules:null,
			formItemClass: 'col-span-1',
		},
		  {
				fieldName: 'createdAt',
				component: 'RangePicker',
				label: '创建时间',
				componentProps: {
					type: 'datetimerange',
					clearable: true,
					valueFormat: 'FMDateRange',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:null,
				formItemClass: 'col-span-1',
			},];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '访问ID',
    field: 'infoId',
    align: 'left',
    width: -1,
    type: 'checkbox',
  },
  {
    title: '终端用户ID',
    field: 'consumerId',
    align: 'left',
    width: -1,
 },
  {
    title: '登录账号',
    field: 'loginName',
    align: 'left',
    width: -1,
 },
  {
    title: '登录IP地址',
    field: 'ipaddr',
    align: 'left',
    width: -1,
 },
  {
    title: '登录地点',
    field: 'loginLocation',
    align: 'left',
    width: -1,
 },
  {
    title: '登录类型',
    field: 'loginMethod',
    align: 'left',
    width: -1,
 },
  {
    title: '浏览器类型',
    field: 'browser',
    align: 'left',
    width: -1,
 },
  {
    title: '操作系统',
    field: 'os',
    align: 'left',
    width: -1,
 },
 {    
				title: '登录状态',    field: 'status',    align: 'left',    width: -1, 
				slots: {
      				default: ({ row }) => {
						return renderDict(row.status, 'consumer_login_statue');
					}
				},
			},
			  {
    title: '提示消息',
    field: 'msg',
    align: 'left',
    width: -1,
 },
  {
    title: '登录模块',
    field: 'logModule',
    align: 'left',
    width: -1,
 },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
 },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  infoId: number;
  consumerId: number;
  loginName: string;
  ipaddr: string;
  loginLocation: string;
  loginMethod: number;
  browser: string;
  os: string;
  status: number;
  msg: string;
  logModule: string;
  createdAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  {  field: 'infoId',  label: '访问ID'},
  {  field: 'consumerId',  label: '终端用户ID'},
  {  field: 'loginName',  label: '登录账号'},
  {  field: 'ipaddr',  label: '登录IP地址'},
  {  field: 'loginLocation',  label: '登录地点'},
  {  field: 'loginMethod',  label: '登录类型'},
  {  field: 'browser',  label: '浏览器类型'},
  {  field: 'os',  label: '操作系统'},
  {
				field: 'status',
				label: '登录状态',
				render(row: any) {
					return renderDict(row.status, 'consumer_login_statue');
				},
			},
			  {  field: 'msg',  label: '提示消息'},
  {  field: 'logModule',  label: '登录模块'},
  {  field: 'createdAt',  label: '创建时间'},
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
 {
					fieldName: 'infoId',
					component: 'Input',
					label: '访问ID',
					dependencies: {   show: () => false,    triggerFields: [''],   },
					componentProps: {
						placeholder: '',
						onUpdateValue: (e: any) => {
							console.log(e);
						},   
					},
					formItemClass: 'col-span-1',
				},  {
				fieldName: 'consumerId',
				component: 'InputNumber',
				label: '终端用户ID',
				componentProps: {
					placeholder: '请输入终端用户ID',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:null,
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'loginName',
				component: 'Input',
				label: '登录账号',
				componentProps: {
					placeholder: '请输入登录账号',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'ipaddr',
				component: 'Input',
				label: '登录IP地址',
				componentProps: {
					placeholder: '请输入登录IP地址',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'loginLocation',
				component: 'Input',
				label: '登录地点',
				componentProps: {
					placeholder: '请输入登录地点',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'loginMethod',
				component: 'InputNumber',
				label: '登录类型',
				componentProps: {
					placeholder: '请输入登录类型',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  rules:null,
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'browser',
				component: 'Input',
				label: '浏览器类型',
				componentProps: {
					placeholder: '请输入浏览器类型',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'os',
				component: 'Input',
				label: '操作系统',
				componentProps: {
					placeholder: '请输入操作系统',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},  {    
			fieldName: 'status',    
			component: 'Select',    
			label: '登录状态',    
			defaultValue: null,    
			componentProps: {    
				placeholder: '请选择登录状态',    
				options: getDictOptions('consumer_login_statue'),    
				onUpdateValue: (e: any) => {    
					console.log(e);    
				},  
			},
			rules:null,
			formItemClass: 'col-span-1',
		},
		  {
				fieldName: 'msg',
				component: 'Input',
				label: '提示消息',
				componentProps: {
					placeholder: '请输入提示消息',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},  {
				fieldName: 'logModule',
				component: 'Input',
				label: '登录模块',
				componentProps: {
					placeholder: '请输入登录模块',
					onUpdateValue: (e: any) => {
						console.log(e);
					},   
				},  
				rules:null,
				formItemClass: 'col-span-1',
			},];