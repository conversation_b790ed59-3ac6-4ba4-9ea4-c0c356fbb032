import { requestClient } from '#/api/request';

// 获取项目设备关联员工列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotProjectUseUser/list', { params });
}

// 删除/批量删除项目设备关联员工
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotProjectUseUser/delete', { ...params });
}

// 添加/编辑项目设备关联员工
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotProjectUseUser/edit', { ...params });
}

// 获取项目设备关联员工指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotProjectUseUser/view', { params });
}

// 导出项目设备关联员工
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotProjectUseUser/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}