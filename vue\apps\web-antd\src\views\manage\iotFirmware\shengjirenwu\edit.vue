<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <!-- 自定义选择设备按钮 -->
      <template #selectDeviceButton="{ field }">
        <div class="relative inline-block">
          <Button
            type="primary"
            @click="openSelectDevice"
          >
            选择设备
          </Button>
          <!-- 设备数量徽章 -->
          <span
            v-if="selectedDeviceCount > 0"
            class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
          >
            {{ selectedDeviceCount }}
          </span>
        </div>
      </template>
    </BasicForm>

    <!-- 设备选择组件 -->
    <SelectDevice
      ref="refSelectDevice"
      @device-selected="onDeviceSelected"
    />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, nextTick, ref, h } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';
import { Button, message, Modal, Popconfirm,Input } from 'ant-design-vue';
import { useVbenForm } from '#/adapter/form';
import { Alert } from 'ant-design-vue';
import { Edit, View } from '#/api/manage/iotUpgradeTask';
import { List as FirmwareList } from '#/api/manage/iotFirmware';
import { editSchema } from './model';
import SelectDevice from './selectDevice/index.vue';
import { getDictOptions } from '#/utils/dict';

const emit = defineEmits<{ reload: [] }>();
const refSelectDevice = ref();
const selectedDeviceCount = ref(0); // 选中设备数量
const originalTaskTypeKey = ref<string | null>(null); // 保存原始的任务范围key值
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
  firmwareParams?: {
    firmwareId?: string;
    firmwareName?: string;
    productKey?: string;
    productName?: string;
    firmwareVersion?: string;
  };
}

const isUpdate = ref(false);
const isView = ref(false);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

// 获取固件信息的函数
async function getFirmwareInfo(firmwareId: string) {
  try {
    console.log('🔍 开始获取固件信息，firmwareId:', firmwareId);

    // 调用固件列表接口获取固件信息
    const firmwareResponse = await FirmwareList({
      page: 1,
      pageSize: 1000, // 获取所有固件
    });

    console.log('🔍 固件列表响应:', firmwareResponse);

    if (firmwareResponse && firmwareResponse.items) {
      // 查找匹配的固件
      const firmware = firmwareResponse.items.find((item: any) =>
        item.firmwareId?.toString() === firmwareId?.toString()
      );

      console.log('🔍 查找到的固件信息:', firmware);

      if (firmware) {
        return {
          firmwareId: firmware.firmwareId,
          firmwareName: firmware.firmwareName,
          firmwareVersion: firmware.firmwareVersion,
          productKey: firmware.productKey,
          productName: firmware.productName,
        };
      } else {
        console.warn('⚠️ 未找到匹配的固件，firmwareId:', firmwareId);
        console.warn('⚠️ 可用的固件列表:', firmwareResponse.items.map((item: any) => ({
          firmwareId: item.firmwareId,
          firmwareName: item.firmwareName,
        })));
      }
    }

    return null;
  } catch (error) {
    console.error('❌ 获取固件信息失败:', error);
    return null;
  }
}

// 设备选择回调函数
async function onDeviceSelected(deviceKeys: string, deviceNames: string, deviceCount: number) {
  console.log('设备选择回调:', { deviceKeys, deviceNames, deviceCount });

  // 设置表单字段值（同时设置deviceKeys和deviceName）
  await formApi.setFieldValue('deviceKey', deviceKeys);
  await formApi.setFieldValue('deviceKeys', deviceKeys);
  await formApi.setFieldValue('deviceName', deviceNames); // 新增：设置设备名称
  await formApi.setFieldValue('deviceCount', deviceCount);

  console.log('设备选择后表单字段设置:', {
    deviceKey: deviceKeys,
    deviceKeys: deviceKeys,
    deviceName: deviceNames, // 新增：显示设备名称
    deviceCount: deviceCount
  });

  // 更新按钮上的数量显示
  selectedDeviceCount.value = deviceCount;
}

// 打开设备选择弹窗
async function openSelectDevice() {
  try {
    // 获取当前表单的产品Key，用于过滤设备
    const formData = await formApi.getValues();
    const productKey = formData.productKey;
    console.log('打开设备选择弹窗，产品Key:', productKey);

    // 传递产品Key给设备选择组件
    if (refSelectDevice.value) {
      refSelectDevice.value.setProductKey(productKey);
      refSelectDevice.value.openModal();
    }
  } catch (error) {
    console.error('获取表单数据失败:', error);
    // 即使没有productKey也可以打开设备选择
    refSelectDevice.value?.openModal();
  }
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }
    drawerApi.setState({ confirmLoading: true, loading: true })
    const { id, update, view, firmwareParams } = drawerApi.getData() as ModalProps;
    console.log('📝 编辑弹窗打开，接收到的数据:', { id, update, view, firmwareParams });

    isUpdate.value = update;
    isView.value = view;

    // 清空表单，避免之前的数据影响
    console.log('📝 清空表单数据...');
    await formApi.resetForm();

    if (isUpdate.value || isView.value) {
      console.log('🔧 编辑/查看模式，任务ID:', id);
      const record = await View({ taskId: id });
      console.log('🔧 获取到的任务记录:', record);

      // 保存原始任务范围key值（不修改显示值）
      if (record.taskType !== undefined && record.taskType !== null) {
        console.log('🔍 保存原始任务范围key值');
        console.log('原始任务范围值:', record.taskType, '类型:', typeof record.taskType);

        // 保存原始key值，但不修改显示值
        originalTaskTypeKey.value = String(record.taskType);
        console.log('保存原始key值:', originalTaskTypeKey.value);
      }

      // 合并任务记录和固件参数，优先使用固件参数中的值
      const formData = {
        ...record,
        firmwareId: record.firmwareId ? Number(record.firmwareId) : null,
        // 优先使用固件参数中的值，确保固件信息是最新的
        firmwareName: (firmwareParams ? firmwareParams.firmwareName : '') || record.firmwareName || '',
        productName: (firmwareParams ? firmwareParams.productName : '') || record.productName || '',
        productKey: (firmwareParams ? firmwareParams.productKey : '') || record.productKey || '',
        firmwareVersion: (firmwareParams ? firmwareParams.firmwareVersion : '') || record.firmwareVersion || '',
        // 确保必要的字段有默认值
        upgradeType: record.upgradeType || 2, // 默认值应该是2
        hardwareVersion: record.hardwareVersion || '',
        subPackageSize: record.subPackageSize || 0,
        fileUrl: record.fileUrl || '',
        taskStatus: record.taskStatus || 1,
        // 设备选择字段处理
        deviceKeys: record.deviceKeys || record.deviceKey || '',
        deviceKey: record.deviceKey || record.deviceKeys || '', // 兼容字段
        deviceName: record.deviceName || '', // 新增：设备名称
        deviceCount: record.deviceCount || 0,
      };

      console.log('🔧 固件参数使用情况:', {
        '固件参数中的firmwareName': firmwareParams ? firmwareParams.firmwareName : 'undefined',
        '记录中的firmwareName': record.firmwareName,
        '最终使用的firmwareName': formData.firmwareName,
        '固件参数中的productName': firmwareParams ? firmwareParams.productName : 'undefined',
        '记录中的productName': record.productName,
        '最终使用的productName': formData.productName,
      });

      console.log('🔧 设置表单数据:', formData);
      console.log('🔧 任务范围字段值:', {
        原始值: record.taskType,
        转换后: formData.taskType,
        类型: typeof formData.taskType
      });
      console.log('🔧 固件信息补充:', {
        固件名称: formData.firmwareName,
        产品名称: formData.productName,
        产品标识: formData.productKey,
        固件版本: formData.firmwareVersion,
      });

      await formApi.setValues(formData);

      // 验证设置是否成功
      const verifyValues = await formApi.getValues();
      console.log('🔧 设置后验证 - taskType:', {
        设置前: formData.taskType,
        设置后: verifyValues.taskType,
        类型: typeof verifyValues.taskType
      });

      // 如果是编辑模式，设置设备数量显示
      selectedDeviceCount.value = record.deviceCount || 0;
    } else {
      // 新增时，重置设备数量
      selectedDeviceCount.value = 0;
      // 新增时，设置固件相关的默认值
      if (firmwareParams) {
        console.log('🆕 新增模式 - 接收到的固件参数:', firmwareParams);
        console.log('🆕 固件参数详细信息:', {
          productKey: firmwareParams.productKey,
          productName: firmwareParams.productName,
          firmwareName: firmwareParams.firmwareName,
          firmwareVersion: firmwareParams.firmwareVersion,
          firmwareId: firmwareParams.firmwareId,
        });

        const formValues = {
          productKey: firmwareParams.productKey || '',
          productName: firmwareParams.productName || '',
          firmwareName: firmwareParams.firmwareName || '',
          firmwareVersion: firmwareParams.firmwareVersion || '',
          firmwareId: firmwareParams.firmwareId ? Number(firmwareParams.firmwareId) : undefined,
          // 不设置 taskType 的默认值，让用户自己选择
        };

        console.log('🆕 准备设置的表单值:', formValues);
        await formApi.setValues(formValues);
        console.log('🆕 表单值设置完成');

        // 验证设置是否成功
        const currentValues = await formApi.getValues();
        console.log('🆕 设置后的表单当前值:', {
          productKey: currentValues.productKey,
          productName: currentValues.productName,
          firmwareName: currentValues.firmwareName,
          firmwareVersion: currentValues.firmwareVersion,
          firmwareId: currentValues.firmwareId,
        });
      } else {
        console.warn('🆕 新增模式 - 没有接收到固件参数');
      }
    }
    drawerApi.setState({ confirmLoading: false, loading: false })

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    } else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});

async function handleConfirm() {
  try {
    console.log('📝 开始提交表单...');
    drawerApi.setState({ confirmLoading: true, loading: true })

    const { valid } = await formApi.validate();
    if (!valid) {
      console.warn('⚠️ 表单验证失败');
      return;
    }

    // 验证关键字段是否存在
    const currentValues = await formApi.getValues();
    console.log('📝 验证前的表单当前值:', currentValues);

    if (!currentValues.taskName) {
      console.error('❌ 任务名称不能为空');
      throw new Error('任务名称不能为空');
    }

    if (!currentValues.taskType) {
      console.error('❌ 任务范围不能为空');
      throw new Error('任务范围不能为空');
    }

    if (!currentValues.firmwareId) {
      console.error('❌ 固件ID不能为空');
      throw new Error('固件ID不能为空');
    }

    const data = cloneDeep(await formApi.getValues());
    console.log('📝 表单数据:', data);

    // 详细检查关键字段
    console.log('📝 关键字段检查:', {
      firmwareId: data.firmwareId,
      firmwareName: data.firmwareName,
      firmwareVersion: data.firmwareVersion,
      productKey: data.productKey,
      productName: data.productName,
      taskName: data.taskName,
      taskType: data.taskType,
      deviceKeys: data.deviceKeys,
      deviceName: data.deviceName, // 新增：设备名称
      deviceCount: data.deviceCount,
      upgradeType: data.upgradeType,
      taskTime: data.taskTime,
    });

    // 检查是否缺少必需字段
    const missingFields = [];
    if (!data.taskName) missingFields.push('taskName');
    if (!data.taskType) missingFields.push('taskType');
    if (!data.firmwareId) missingFields.push('firmwareId');
    if (!data.productKey) missingFields.push('productKey');

    if (missingFields.length > 0) {
      console.error('❌ 缺少必需字段:', missingFields);
      throw new Error(`缺少必需字段: ${missingFields.join(', ')}`);
    }

    // 验证必要字段
    if (!data.firmwareId) {
      console.error('❌ 固件ID为空');
      throw new Error('固件ID不能为空');
    }

    if (!data.firmwareName) {
      console.error('❌ 固件名称为空');
      throw new Error('固件名称不能为空');
    }

    // 确保 firmwareId 是数字类型
    if (data.firmwareId && typeof data.firmwareId === 'string') {
      console.log('🔄 转换 firmwareId 从字符串到数字');
      data.firmwareId = parseInt(data.firmwareId, 10);
      console.log('🔄 转换后的 firmwareId:', data.firmwareId, typeof data.firmwareId);
    }

    // 处理任务范围字段，使用保存的原始key值
    if (data.taskType !== undefined && data.taskType !== null) {
      if (originalTaskTypeKey.value) {
        data.taskType = originalTaskTypeKey.value; // 使用保存的原始key值
        console.log('任务范围使用原始key值:', originalTaskTypeKey.value);
      } else {
        // 如果没有保存的key值，尝试从字典中查找
        const dictOptions = getDictOptions('upgrade_task_type');
        const matchedOption = dictOptions.find((opt: any) => opt.label === data.taskType);
        if (matchedOption) {
          data.taskType = matchedOption.value; // 转换回key值
          console.log('任务范围转换: 标签', data.taskType, '-> key', matchedOption.value);
        } else {
          console.log('⚠️ 未找到任务范围标签对应的key值:', data.taskType);
        }
      }

      // 确保 taskType 是数字类型
      if (typeof data.taskType === 'string') {
        console.log('🔄 转换 taskType 从字符串到数字');
        data.taskType = parseInt(data.taskType, 10);
        console.log('🔄 转换后的 taskType:', data.taskType, typeof data.taskType);
      }
    }

    // 处理设备选择字段
    let deviceKeysValue = '';
    let deviceNameValue = '';

    if (data.deviceKeys) {
      deviceKeysValue = data.deviceKeys;
    } else if (data.deviceKey) {
      deviceKeysValue = data.deviceKey;
    }

    if (data.deviceName) {
      deviceNameValue = data.deviceName;
    }

    console.log('🔍 设备选择字段处理:', {
      'data.deviceKeys': data.deviceKeys,
      'data.deviceKey': data.deviceKey,
      'data.deviceName': data.deviceName,
      '最终使用deviceKeys': deviceKeysValue,
      '最终使用deviceName': deviceNameValue
    });

    // 清理不需要的字段，只保留后端需要的字段
    const submitData = {
      taskId: data.taskId,
      taskName: data.taskName,
      taskType: data.taskType,
      upgradeType: data.upgradeType || 2, // 确保有默认值
      firmwareId: data.firmwareId,
      firmwareVersion: data.firmwareVersion,
      hardwareVersion: data.hardwareVersion || '',
      subPackageSize: data.subPackageSize || 0,
      fileUrl: data.fileUrl || '',
      taskTime: data.taskTime,
      taskStatus: data.taskStatus || 1, // 确保有默认值
      productKey: data.productKey,
      deviceKeys: deviceKeysValue, // 使用处理后的设备选择
      deviceName: deviceNameValue, // 新增：设备名称（隐藏存储）
      remark: data.remark || '',
    };

    // 最终数据检查
    console.log('📤 原始表单数据:', JSON.stringify(data, null, 2));
    console.log('📤 即将发送到后端的清理后数据:', JSON.stringify(submitData, null, 2));
    console.log('📤 数据类型检查:', {
      taskId: typeof submitData.taskId,
      taskName: typeof submitData.taskName,
      taskType: typeof submitData.taskType,
      firmwareId: typeof submitData.firmwareId,
      deviceKeys: typeof submitData.deviceKeys,
    });

    console.log('📤 发送请求到后端...');
    const result = await Edit(submitData);
    console.log('✅ 后端响应:', result);

    // 检查编辑后的关键字段是否发生变化
    console.log('🔍 编辑前后关键字段对比:');
    console.log('编辑前 firmwareId:', data.firmwareId, '编辑后 firmwareId:', submitData.firmwareId);
    console.log('编辑前 productKey:', data.productKey, '编辑后 productKey:', submitData.productKey);

    if (data.firmwareId !== submitData.firmwareId) {
      console.warn('⚠️ firmwareId 发生了变化！这可能导致查询过滤失效');
    }

    if (data.productKey !== submitData.productKey) {
      console.warn('⚠️ productKey 发生了变化！这可能导致查询过滤失效');
    }

    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error('❌ 提交失败:', error);
    throw error; // 重新抛出错误，让用户看到错误信息
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();

  // 清空保存的原始key值
  originalTaskTypeKey.value = null;
}

</script>