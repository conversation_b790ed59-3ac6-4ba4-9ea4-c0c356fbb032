<script setup lang="ts">
  import type { SysClient } from '#/api/system/client';
  import { computed, nextTick, onMounted, ref } from 'vue';
  import { useVbenDrawer } from '@vben/common-ui';
  import { $t } from '@vben/locales';
  import { addFullName, cloneDeep } from '@vben/utils';
  import { useVbenForm } from '#/adapter/form';
  import { Description, useDescription } from '#/components/description';
  import { viewSchema, type RowType } from './model';
  import { View } from '#/api/manage/iotCmdPerm';
import { getSysUserListApi } from '#/api/system';

  interface UserItem {
  userId: string | number;
  userName: string;
  // 其他字段...
}
function getUserName(userId: string | number) {
  const user = userItems.value.find(u => String(u.userId) === String(userId));
  return user ? user.userName : '';
}
const userItems = ref<UserItem[]>([]);

  const [BasicDrawer, drawerApi] = useVbenDrawer({
  onOpenChange: handleOpenChange,
  });
  const [registerDescription, { setDescProps }] = useDescription({
  column: 1,
  schema: viewSchema,
  });
 async function handleOpenChange(open: boolean) {
  if (!open) return null;
  const { record } = drawerApi.getData() as { record: RowType };
  const record2 = await View({ cmdPermId: record.cmdPermId });

  // 假设 userItems 是你已获取的用户列表
if (!record2.user && record2.userId && Array.isArray(userItems.value)) {
  const user = userItems.value.find(u => String(u.userId) === String(record2.userId));
  if (user) record2.user = user;
}
 // setDescProps({data: record2 }, true);
 if (record2.user && record2.user.userName) {
  record2.userName = record2.user.userName;
}
 console.log('详情数据', record2);
 setDescProps({ data: record2 }, true);
 
}
</script>
<template>
  <BasicDrawer :footer="false" class="w-[600px]" title="查看">
    <Description @register="registerDescription">
      
    </Description>
  </BasicDrawer>
</template>