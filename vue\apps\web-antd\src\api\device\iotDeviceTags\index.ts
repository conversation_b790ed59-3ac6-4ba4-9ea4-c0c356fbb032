import { requestClient } from '#/api/request';

// 获取设备标签列表
export function List(params:any) {
  return requestClient.get<any>('device/iotDeviceTags/list', { params });
}

// 删除/批量删除设备标签
export function Delete(params:any) {
  return requestClient.post<any>('device/iotDeviceTags/delete', { ...params });
}

// 添加/编辑设备标签
export function Edit(params:any) {
  return requestClient.post<any>('device/iotDeviceTags/edit', { ...params });
}

// 获取设备标签指定详情
export function View(params:any) {
  return requestClient.get<any>('device/iotDeviceTags/view', { params });
}

// 导出设备标签
export function Export(params:any) {
  return requestClient.post<Blob>('/device/iotDeviceTags/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}