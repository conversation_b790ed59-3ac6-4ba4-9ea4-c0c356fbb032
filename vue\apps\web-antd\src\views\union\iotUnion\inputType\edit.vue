<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
    </BasicForm>
  </BasicDrawer>
</template>

<script setup lang='ts' name=''>
import { useVbenDrawer, useVbenForm } from '@vben/common-ui';
import { computed, ref } from 'vue'
import { editSchema } from './model';
import { Edit } from '#/api/union/iotUnionField';
import { useRoute } from 'vue-router';
import { cloneDeep } from '@vben/utils';

const emit = defineEmits<{ reload: [] }>();
/** 路由实例 */
const route = useRoute();
/** 编辑/新增flag */
const isUpdate = ref(false);
const isView = ref(false);

/** 标题 */
const title = computed(() => {
  return isUpdate.value ? "编辑" : "新增";
});

/** 表单实例 */
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

/** 抽屉实例 */
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    /** 打开状态,不做行为 */
    if (!isOpen) {
      return null;
    }

    /** 打开加载状态 */
    drawerApi.setState({ confirmLoading: true, loading: true })
    /** 获取状态数据 */
    const { data, update, view, } = drawerApi.getData();
    isUpdate.value = update;
    isView.value = view;
    await formApi.setValues(data);
    // }

    /** 加载完成 */
    drawerApi.setState({ confirmLoading: false, loading: false })
    /** 预览状态 */
    if (view) {
      drawerApi.setState({ showConfirmButton: false });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: true,
            "only-read": true,
          }
        }
      });
    }
    /** 编辑/增加状态 */
    else {
      drawerApi.setState({ showConfirmButton: true });
      formApi.setState({
        commonConfig: {
          componentProps: {
            readonly: false,
            "only-read": false,
          }
        }
      });
    }
  },
});


/** 关闭抽屉 */
async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}
/** 提交数据 */
async function handleConfirm() {
  // formApi.submitForm()
  try {
    /** 打开加载状态 */
    drawerApi.setState({ confirmLoading: true, loading: true })
    /** 获取校验 */
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    /** 深度克隆数据 */
    const data = cloneDeep(await formApi.getValues());

    /** 载入组合设备标识 */
    data.unionKey = route.query.unionId
    /** 新建情况 */
    if (isUpdate.value == false) {
      data.fieldType = 2
    }
    // console.log("待上传数据:")
    // console.log(data)
    await (Edit(data));
    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}




</script>

<style scoped></style>
