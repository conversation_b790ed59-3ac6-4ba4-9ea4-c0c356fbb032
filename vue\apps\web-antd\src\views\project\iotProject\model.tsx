import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict, renderPopoverMemberSumma, type MemberSumma } from '#/utils';
import { DictEnum } from '@vben/constants';

export class State {
  public projectId = 0; // 项目ID
  public projectName = ''; // 项目名称
  public longitude = 0; // 中心点坐标（经度）
  public latitude = 0; // 中心点坐标（纬度）
  public region = null; // 行政区划编码
  public regionName = null;
  public address = null; // 详细地址
  public contactUser = null; // 项目负责人
  public contactPhone = null; // 负责人电话
  public status = 0; // 状态：0=正常，1=停用
  public tenantId = ''; // 租户ID
  public deptId = 0; // 所属机构
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedBySumma?: null | MemberSumma = null; // 删除人摘要信息
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注
  resetPassword = 0; // 是否重置密码

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'projectName',
    component: 'Input',
    label: '项目名称',
    componentProps: {
      placeholder: '请输入项目名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
  {
    fieldName: 'contactUser',
    component: 'Input',
    label: '项目责任人',
    componentProps: {
      placeholder: '请输入责任人姓名',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '编号',
    field: 'projectId',
    align: 'center',
    width: 60,
  },
  {
    title: '项目名称',
    field: 'projectName',
    align: 'left',
    width: -1,
  },
  // {
  //   title: '中心点坐标（经度）',
  //   field: 'longitude',
  //   align: 'left',
  //   width: -1,
  // },
  // {
  //   title: '中心点坐标（纬度）',
  //   field: 'latitude',
  //   align: 'left',
  //   width: -1,
  // },
  {
    title: '中心点坐标',
    // 自定义字段，这里可以任意命名，因为会用 customRender 覆盖
    field: 'coordinate',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => `${row.longitude || ''}, ${row.latitude || ''}`
    }
  },
  {
    title: '所属省市县',
    field: 'regionName',
    align: 'left',
    width: -1,
  },
  {
    title: '项目负责人',
    field: 'contactUser',
    align: 'left',
    width: -1,
  },
  {
    title: '责任人电话',
    field: 'contactPhone',
    align: 'left',
    width: -1,
  },
  {
    title: '所属机构',
    field: 'deptName',
    align: 'left',
    width: -1,
  },
  {
    title: '状态',
    field: 'status',
    align: 'left',
    width: -1,
    slots: { default: 'status' },
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  projectId: number;
  projectName: string;
  longitude: number;
  latitude: number;
  region: string;
  regionName: string;
  address: string;
  contactUser: string;
  contactPhone: string;
  status: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'projectId', label: '项目ID' },
  { field: 'projectName', label: '项目名称' },
  { field: 'longitude', label: '中心点坐标（经度）' },
  { field: 'latitude', label: '中心点坐标（纬度）' },
  { field: 'region', label: '行政区划编码' },
  { field: 'regionName', label: '所属省市县' },
  { field: 'address', label: '详细地址' },
  { field: 'contactUser', label: '项目负责人' },
  { field: 'contactPhone', label: '负责人电话' },
  { field: 'status', label: '状态：0=正常，1=停用' },
  { field: 'tenantId', label: '租户ID' },
  { field: 'deptId', label: '所属机构' },
  { field: 'createdDept', label: '创建部门' },
  { field: 'createdBy', label: '创建者' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'updatedBy', label: '更新者' },
  { field: 'updatedAt', label: '更新时间' },
  { field: 'deletedBy', label: '删除人' },
  { field: 'deletedAt', label: '删除时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'projectName',
    component: 'Input',
    label: '项目名称',
    componentProps: {
      placeholder: '请输入项目名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'region',
    component: 'TreeSelect',
    label: '项目所属省市县',
    componentProps: {
      placeholder: '请输入行政区划编码',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    disabled: false,
    rules: 'selectRequired',
  },
  {
    component: 'TreeSelect',
    componentProps: {
      getPopupContainer,
    },
    dependencies: {
      show: (model) => model.parentId !== 0,
      triggerFields: ['parentId'],
    },
    fieldName: 'deptId',
    label: '项目所属机构',
    rules: 'selectRequired',
  },

  {
    fieldName: 'contactUser',
    component: 'Input',
    label: '项目责任人',
    componentProps: {
      placeholder: '项目责任人',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    rules: null
    
  },
  {
    fieldName: 'contactPhone',
    component: 'Input',
    label: '负责人电话',
    componentProps: {
      placeholder: '请输入负责人电话',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z
      .string()
      .regex(/^1[3,4578]\d{9}$/, { message: '请输入正确的手机号' })
      .optional()
      .or(z.literal('')),
      formItemClass: 'col-span-4',
  },
  {
    fieldName: 'longitude',
    component: 'InputNumber',
    label: '设备坐标（经度）',
    componentProps: {
      placeholder: '请输入设备坐标（经度）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    disabled: false,
    rules: null,
  },
  {
    fieldName: 'latitude',
    component: 'InputNumber',
    label: '设备坐标（纬度）',
    componentProps: {
      placeholder: '请输入设备坐标（纬度）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    disabled: false,
    rules: null,
  },
  {
    fieldName: 'deviceLocationPicker',
    component: 'Input',
    label: '设备位置选择',
    componentProps: {
      placeholder: '点击按钮选择设备位置',
      readonly: true,
    },
    formItemClass: 'col-span-2',
    disabled: false,
    rules: null,
  },
  {
    fieldName: 'address',
    component: 'Input',
    label: '详细地址',
    componentProps: {
      placeholder: '请输入详细地址',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
    {
    fieldName: 'resetPassword',
    component: 'Checkbox',
    label: '是否重置密码',
    componentProps: {
      checkedValue: 1,
      uncheckedValue: 0,
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-2',
    defaultValue: 0,
    rules: null
  },
  {
    fieldName: 'status',
    component: 'Switch',
    label: '状态',
    defaultValue: false,
    componentProps: {
      style: {
        width: '20px',
      },
      placeholder: '请选择状态：0=正常，1=停用',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired',
    formItemClass: 'col-span-2',
  },
  {
    fieldName: 'callbackUrl',
    component: 'Input',
    label: '第三方回调地址',
    componentProps: {
      placeholder: '请输入第三方回调地址',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
  

  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];

// 详情页信息
export const detailEditSchema: VbenFormSchema[] = [
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      disabled: false,
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const detailColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '编号',
    field: 'deviceId',
    align: 'center',
    width: 60,
  },
  {
    title: '设备名称',
    field: 'deviceName',
    align: 'left',
    width: -1,
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
  },
  {
    title: '所属产品',
    field: 'productName',
    align: 'left',
    width: -1,
  },
  // {
  //   title: '设备坐标（经度）',
  //   field: 'longitude',
  //   align: 'left',
  //   width: -1,
  // },
  // {
  //   title: '设备坐标（纬度）',
  //   field: 'latitude',
  //   align: 'left',
  //   width: -1,
  // },
  {
    title: '版本',
    field: 'firmwareVersion',
    align: 'center',
    width: 100,
  },
  {
    title: '在线状态',
    field: 'deviceState',
    align: 'center',
    width: 100,
    slots: { default: 'deviceState' },
  },
  {
    title: '报警状态',
    field: 'alarmState',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.alarmState, DictEnum.ALARM_STATE);
        if (found) {
          return found;
        }
        return row.alarmState;
      },
    },
  },
  {
    title: '是否启用',
    field: 'status',
    align: 'center',
    width: 100,
    slots: { default: 'status' },
  },
  {
    title: '激活时间',
    field: 'activeTime',
    align: 'left',
    width: -1,
  },
  // {
  //   title: '在线超时设置',
  //   field: 'onlineTimeout',
  //   align: 'left',
  //   width: -1,
  // },
  // {
  //   title: '最后在线时间',
  //   field: 'lastOnlineTime',
  //   align: 'left',
  //   width: -1,
  // },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'left',
    width: -1,
  },
  { title: '操作', width: 150, slots: { default: 'action' } },
];
// 表格列接口
export interface DetailRowType {
  deviceId: number;
  resetPassword: number;
  productKey: string;
  deviceKey: string;
  deviceName: string;
  longitude: number;
  latitude: number;
  firmwareVersion: number;
  isShadow: number;
  imgUrl: string;
  deviceState: number;
  alarmState: number;
  rssi: number;
  thingsModelValue: string;
  networkAddress: string;
  networkIp: string;
  status: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
}