import { requestClient } from '#/api/request';

// 获取监控设备资源表列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotCameraFile/list', { params });
}

// 删除/批量删除监控设备资源表
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotCameraFile/delete', { ...params });
}

// 添加/编辑监控设备资源表
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotCameraFile/edit', { ...params });
}

// 获取监控设备资源表指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotCameraFile/view', { params });
}

// 导出监控设备资源表
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotCameraFile/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}