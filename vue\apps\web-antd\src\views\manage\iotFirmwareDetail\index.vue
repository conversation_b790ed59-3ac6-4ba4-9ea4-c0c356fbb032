<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { Page } from '@vben/common-ui';
import { Card, Button, message } from 'ant-design-vue';
import dayjs from 'dayjs';
import UpgradeTaskTable from '../iotFirmware/shengjirenwu/index.vue';
import { View } from '#/api/manage/iotFirmware';

const route = useRoute();

// 从路由参数中获取固件信息
const firmwareParams = ref({
  firmwareId: '',
  firmwareName: '',
  productKey: '',
  productName: '',
  firmwareVersion: '',
  createdAt: '', // 添加创建时间
  remark: '', // 添加固件描述
});

// 升级统计数据
const upgradeStats = ref({
  deviceCount: 0, // 固件升级设备总数
  upgradeSuccess: 0, // 升级成功数
  upgradeProgress: 0, // 正在升级数
  upgradeFailed: 0, // 升级失败数
});

// 刷新状态
const refreshLoading = ref(false);

// 计算属性，用于传递给子组件
const upgradeTaskProps = computed(() => ({
  firmwareId: firmwareParams.value.firmwareId,
  firmwareName: firmwareParams.value.firmwareName,
  productKey: firmwareParams.value.productKey,
  productName: firmwareParams.value.productName,
  firmwareVersion: firmwareParams.value.firmwareVersion,
}));

// 格式化日期函数
const formatDate = (date: string) => {
  if (!date) return '-';
  try {
    return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
  } catch (error) {
    console.error('日期格式化失败:', error);
    return date || '-';
  }
};

// 刷新升级统计数据
const refreshUpgradeStats = async () => {
  if (!firmwareParams.value.firmwareId) {
    message.warning('固件ID不存在，无法刷新统计数据');
    return;
  }

  refreshLoading.value = true;
  try {
    console.log('🔄 开始刷新升级统计数据...');
    const firmwareDetail = await View({ firmwareId: firmwareParams.value.firmwareId });
    console.log('🔄 获取到最新的固件详情:', firmwareDetail);

    // 更新升级统计数据
    upgradeStats.value = {
      deviceCount: firmwareDetail.deviceCount || 0,
      upgradeSuccess: firmwareDetail.upgradeSuccess || 0,
      upgradeProgress: firmwareDetail.upgradeProgress || 0,
      upgradeFailed: firmwareDetail.upgradeFailed || 0,
    };

    console.log('🔄 升级统计数据已更新:', upgradeStats.value);
    message.success('升级统计数据已刷新');
  } catch (error) {
    console.error('❌ 刷新升级统计数据失败:', error);
    message.error('刷新升级统计数据失败');
  } finally {
    refreshLoading.value = false;
  }
};

onMounted(() => {
  // 获取路由参数
  firmwareParams.value = {
    firmwareId: route.query.firmwareId as string || '',
    firmwareName: route.query.firmwareName as string || '',
    productKey: route.query.productKey as string || '',
    productName: route.query.productName as string || '',
    firmwareVersion: route.query.firmwareVersion as string || '',
    createdAt: route.query.createdAt as string || '',
    remark: route.query.remark as string || '',
  };

  // 获取升级统计数据
  upgradeStats.value = {
    deviceCount: parseInt(route.query.deviceCount as string) || 0,
    upgradeSuccess: parseInt(route.query.upgradeSuccess as string) || 0,
    upgradeProgress: parseInt(route.query.upgradeProgress as string) || 0,
    upgradeFailed: parseInt(route.query.upgradeFailed as string) || 0,
  };

  console.log('🚀 固件详情页面已加载');
  console.log('📄 接收到的固件参数:', firmwareParams.value);
  console.log('📊 接收到的升级统计数据:', upgradeStats.value);
});
</script>

<template>
  <Page auto-content-height>
    <!-- 上部模块 - 固件信息 -->
    <Card class="mb-4" title="固件信息">
      <div class="firmware-info">
        <div class="info-row">
          <div class="info-item">
            <span class="label">固件ID：</span>
            <span class="value">{{ firmwareParams.firmwareId || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">固件名称：</span>
            <span class="value">{{ firmwareParams.firmwareName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">固件版本：</span>
            <span class="value">{{ firmwareParams.firmwareVersion || '-' }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">产品标识：</span>
            <span class="value">{{ firmwareParams.productKey || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">产品名称：</span>
            <span class="value">{{ firmwareParams.productName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDate(firmwareParams.createdAt) }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item full-width">
            <span class="label">固件描述：</span>
            <span class="value">{{ firmwareParams.remark || '-' }}</span>
          </div>
        </div>
      </div>
    </Card>

    <!-- 中部模块 - 升级统计数据 -->
    <Card class="mb-4">
      <template #title>
        <div class="card-title-wrapper">
          <span>固件升级设备统计</span>
          <Button
            type="text"
            size="small"
            :loading="refreshLoading"
            @click="refreshUpgradeStats"
            class="refresh-btn"
          >
            🔄 刷新
          </Button>
        </div>
      </template>

      <div class="upgrade-stats">
        <div class="stats-row">
          <div class="stats-item">
            <div class="stats-label">固件升级设备总数</div>
            <div class="stats-value total">{{ upgradeStats.deviceCount }}</div>
          </div>
          <div class="stats-divider"></div>
          <div class="stats-item">
            <div class="stats-label">升级成功数</div>
            <div class="stats-value success">{{ upgradeStats.upgradeSuccess }}</div>
          </div>
          <div class="stats-divider"></div>
          <div class="stats-item">
            <div class="stats-label">正在升级数</div>
            <div class="stats-value progress">{{ upgradeStats.upgradeProgress }}</div>
          </div>
          <div class="stats-divider"></div>
          <div class="stats-item">
            <div class="stats-label">升级失败数</div>
            <div class="stats-value failed">{{ upgradeStats.upgradeFailed }}</div>
          </div>
        </div>
      </div>
    </Card>

    <!-- 下部模块 - 升级任务表 -->
    <Card class="mb-4" title="任务管理">
      <UpgradeTaskTable v-bind="upgradeTaskProps" />
    </Card>
  </Page>
</template>

<style lang="less" scoped>
.module-header {
  text-align: center;
  padding: 40px 20px;

  h2 {
    font-size: 24px;
    color: #262626;
    margin-bottom: 16px;
  }

  p {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

.firmware-info {
  .info-row {
    display: flex;
    margin-bottom: 16px;
    gap: 32px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-item {
    flex: 1;
    display: flex;
    align-items: center;

    &.full-width {
      flex: 3;
    }

    .label {
      color: #666;
      font-size: 14px;
      margin-right: 8px;
      white-space: nowrap;
      min-width: 80px;
    }

    .value {
      color: #262626;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

/* 卡片标题样式 */
.card-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .refresh-btn {
    color: #666;
    padding: 4px;

    &:hover {
      color: #1890ff;
      background-color: #f0f0f0;
    }

    &:focus {
      color: #1890ff;
    }
  }
}

/* 升级统计样式 */
.upgrade-stats {
  .stats-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
  }

  .stats-item {
    flex: 1;
    text-align: left;

    .stats-label {
      color: #666;
      font-size: 14px;
      margin-bottom: 8px;
      white-space: nowrap;
    }

    .stats-value {
      font-size: 24px;
      font-weight: 600;

      &.total {
        color: #1890ff; /* 蓝色 */
      }

      &.success {
        color: #52c41a; /* 绿色 */
      }

      &.progress {
        color: #fa8c16; /* 橙色 */
      }

      &.failed {
        color: #f5222d; /* 红色 */
      }
    }
  }

  .stats-divider {
    width: 1px;
    height: 40px;
    background-color: #000;
    margin: 0 20px;
  }
}
</style>
