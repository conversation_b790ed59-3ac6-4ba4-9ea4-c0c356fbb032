<template>
  <div class="jessibuca-player">
    <!-- Jessibuca播放器容器 (用于FLV/WebSocket流) -->
    <div v-if="shouldUseJessibuca" ref="playerContainer" class="player-container"></div>

    <!-- HTML5 Video播放器 (用于MP4/HLS等标准格式) -->
    <video v-else ref="videoElement" class="player-container video-element" :controls="true" :autoplay="false"
      :muted="true" :playsinline="true" :preload="'metadata'" @loadstart="handleVideoLoadStart"
      @loadeddata="handleVideoLoaded" @play="handleVideoPlay" @pause="handleVideoPause" @error="handleVideoError"
      @canplay="handleVideoCanPlay">
      <source :src="props.videoUrl" type="video/mp4" v-if="isMP4">
      <source :src="props.videoUrl" type="application/x-mpegURL" v-if="isHLS">
      您的浏览器不支持视频播放
    </video>

    <!-- 错误信息显示 -->
    <div v-if="error" class="error-message">
      {{ error }}
    </div>



    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载视频...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';

// 定义Props
interface Props {
  videoUrl: string;
  hasAudio?: boolean;
  fluent?: boolean;
  autoplay?: boolean;
  live?: boolean;
  error?: string;
  message?: string;
  visible?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  hasAudio: false,
  fluent: true,
  autoplay: true,
  live: true,
  visible: true
});

// 定义事件
const emit = defineEmits<{
  error: [error: any];
  play: [];
  pause: [];
  fullscreen: [isFullscreen: boolean];
}>();

// Refs
const playerContainer = ref<HTMLDivElement>();
const videoElement = ref<HTMLVideoElement>();
let jessibuca: any = null;

// 响应式状态
const error = ref<string>('');
const isLoading = ref(false);

// 计算属性 - 判断是否使用Jessibuca
const shouldUseJessibuca = computed(() => {
  if (!props.videoUrl) {
    // 如果没有URL，默认显示Jessibuca容器以便后续使用
    return true;
  }

  const url = props.videoUrl.toLowerCase();
  // 对于WebSocket流和FLV流，使用Jessibuca
  const useJessibuca = url.includes('.flv') || url.startsWith('ws://') || url.startsWith('wss://') || url.includes('rtmp://');

  return useJessibuca;
});

// 计算属性 - 判断是否为MP4
const isMP4 = computed(() => {
  return props.videoUrl.toLowerCase().includes('.mp4');
});

// 计算属性 - 判断是否为HLS
const isHLS = computed(() => {
  const url = props.videoUrl.toLowerCase();
  return url.includes('.m3u8') || url.includes('hls');
});

// HTML5 Video事件处理
const handleVideoLoadStart = () => {
  isLoading.value = true;
  // message.value = '开始加载视频...';
};

const handleVideoLoaded = () => {
  isLoading.value = false;
  // message.value = '视频加载完成';
};

const handleVideoPlay = () => {
  isLoading.value = false;
  // message.value = '';
  emit('play');
};

const handleVideoPause = () => {
  emit('pause');
};

const handleVideoError = (event: Event) => {
  isLoading.value = false;
  const target = event.target as HTMLVideoElement;
  const errorMsg = `视频播放错误: ${target.error?.message || '未知错误'}`;
  error.value = errorMsg;
  emit('error', errorMsg);
};

const handleVideoCanPlay = () => {
  isLoading.value = false;
  // message.value = '视频准备就绪';
};

// 检查Jessibuca是否可用
const isJessibucaAvailable = () => {
  const available = typeof window !== 'undefined' && (window as any).Jessibuca;
  return available;
};

// 等待Jessibuca库加载
const waitForJessibuca = (timeout = 5000): Promise<boolean> => {
  return new Promise((resolve) => {
    const startTime = Date.now();

    const checkJessibuca = () => {
      if (isJessibucaAvailable()) {
        resolve(true);
        return;
      }

      if (Date.now() - startTime > timeout) {
        console.error('❌ 等待Jessibuca库超时');
        resolve(false);
        return;
      }

      // 继续检查
      setTimeout(checkJessibuca, 100);
    };

    checkJessibuca();
  });
};

// 初始化播放器
const initPlayer = async () => {
  if (!playerContainer.value) {
    console.warn('⚠️ 播放器容器未准备好');
    error.value = '播放器容器未准备好';
    return;
  }

  // 等待Jessibuca库加载
  const isAvailable = await waitForJessibuca();
  if (!isAvailable) {
    console.error('❌ Jessibuca库加载失败或超时');
    error.value = 'Jessibuca播放器库加载失败，请刷新页面重试';
    return;
  }

  try {
    const JessibucaClass = (window as any).Jessibuca;

    // 使用官方API创建Jessibuca实例
    jessibuca = new JessibucaClass({
      container: playerContainer.value,
      videoBuffer: props.fluent ? 0.2 : 1.0, // 流畅模式使用更小的缓冲
      decoder: '/jessibuca/decoder.js', // 指定decoder路径（相对于public目录）
      isResize: true,
      hasAudio: props.hasAudio,
      debug: false,
      showBandwidth: true, // 显示带宽信息
      isNotMute: props.hasAudio, // 控制声音
      operateBtns: {
        fullscreen: true,
        screenshot: true,
        play: true,
        audio: props.hasAudio,
        record: false,
      },
      forceNoOffscreen: true,
      loadingText: '加载中...',
      timeout: 10, // 超时设置
      heartTimeout: 10,
      loadingTimeout: 10,
      supportDblclickFullscreen: true, // 支持双击全屏
      keepScreenOn: false,
      hotKey: true, // 启用快捷键
      autoWasm: true, // 自动降级到wasm模式
    });

    // 绑定事件 - 使用官方API
    jessibuca.on('load', () => {
      isLoading.value = false;
      // message.value = '播放器已就绪';
    });

    jessibuca.on('play', () => {
      isLoading.value = false;
      error.value = '';
      // message.value = '视频播放中';
      emit('play');
    });

    jessibuca.on('pause', () => {
      // message.value = '视频已暂停';
      emit('pause');
    });

    jessibuca.on('error', (errorData: any) => {
      console.error('❌ Jessibuca player error:', errorData);
      isLoading.value = false;
      error.value = `播放器错误: ${errorData}`;
      emit('error', errorData);
    });

    jessibuca.on('timeout', (type: string) => {
      console.warn('⏰ Jessibuca timeout:', type);
      isLoading.value = false;
      error.value = `连接超时: ${type}`;
      emit('error', `连接超时: ${type}`);
    });

    // 添加更多错误事件监听
    jessibuca.on('websocketError', (error: any) => {
      console.error('❌ WebSocket错误:', error);
      isLoading.value = false;
      error.value = `WebSocket连接错误: ${error}`;
      emit('error', error);
    });

    jessibuca.on('websocketClose', (event: any) => {
      console.warn('🔌 WebSocket连接关闭:', event);
      isLoading.value = false;
      error.value = 'WebSocket连接已关闭';
      emit('error', 'WebSocket连接已关闭');
    });

    jessibuca.on('fullscreen', (isFullscreen: boolean) => {
      emit('fullscreen', isFullscreen);
    });

    jessibuca.on('videoInfo', (info: any) => {
      // message.value = `视频分辨率: ${info.width}x${info.height}`;
    });

    jessibuca.on('audioInfo', (info: any) => {
    });

    // 如果有视频URL且设置了自动播放，开始播放
    if (props.videoUrl && props.autoplay) {
      await nextTick();
      play();
    }

  } catch (err) {
    console.error('❌ Failed to initialize Jessibuca player:', err);
    isLoading.value = false;
    error.value = `初始化播放器失败: ${String(err)}`;
    emit('error', err);
  }
};

// 播放视频
const play = (url?: string) => {
  const playUrl = url || props.videoUrl;
  if (!playUrl) {
    console.warn('⚠️ No video URL provided');
    error.value = '没有提供视频URL';
    return;
  }

  isLoading.value = true;
  error.value = '';
  // message.value = '正在连接...';

  if (shouldUseJessibuca.value) {
    // 检查Jessibuca播放器是否已初始化
    if (!jessibuca) {
      console.warn('⚠️ Jessibuca播放器未初始化，尝试重新初始化...');
      // 尝试重新初始化播放器
      initPlayer().then(() => {
        // 初始化完成后再次尝试播放
        if (jessibuca) {
          playWithJessibuca(playUrl);
        } else {
          isLoading.value = false;
          error.value = 'Jessibuca播放器初始化失败';
        }
      }).catch(initError => {
        console.error('❌ 重新初始化播放器失败:', initError);
        isLoading.value = false;
        error.value = '播放器初始化失败';
      });
      return;
    }

    playWithJessibuca(playUrl);
  } else if (videoElement.value) {
    playWithHTML5(playUrl);
  } else {
    console.warn('⚠️ 没有可用的播放器');
    isLoading.value = false;
    error.value = '播放器未初始化，请刷新页面重试';
  }
};

// 使用Jessibuca播放
const playWithJessibuca = (playUrl: string) => {
  try {

    // 检查播放器是否已经初始化
    if (!jessibuca) {
      console.error('❌ Jessibuca播放器未初始化');
      isLoading.value = false;
      error.value = 'Jessibuca播放器未初始化';
      emit('error', new Error('Jessibuca播放器未初始化'));
      return;
    }

    // 使用官方API播放
    const playPromise = jessibuca.play(playUrl);

    // 检查play方法是否返回Promise
    if (playPromise && typeof playPromise.then === 'function') {
      playPromise.then(() => {
        isLoading.value = false;
        // message.value = '播放成功';
      }).catch((err: any) => {
        console.error('❌ Jessibuca播放失败:', err);
        isLoading.value = false;
        error.value = `播放失败: ${String(err)}`;
        emit('error', err);
      });
    } else {
      // 如果不是Promise，添加延迟检查
      setTimeout(() => {
        // 检查播放器状态
        try {
          const state = jessibuca.getPlayerState ? jessibuca.getPlayerState() : null;
          if (state && state.isPlaying) {
            isLoading.value = false;
          } else {
            console.warn('⚠️ 播放器状态检查：未开始播放');
            // 可能需要更多时间，再等待一会
            setTimeout(() => {
              if (isLoading.value) {
                console.warn('⚠️ 播放器启动超时');
                isLoading.value = false;
                error.value = '播放器启动超时';
                emit('error', new Error('播放器启动超时'));
              }
            }, 3000);
          }
        } catch (stateError) {
          console.error('❌ 检查播放器状态失败:', stateError);
          // 状态检查失败，但不一定是播放失败，等待更多时间
          setTimeout(() => {
            if (isLoading.value) {
              console.warn('⚠️ 播放器状态检查失败且仍在加载中');
              isLoading.value = false;
              error.value = '播放器状态检查失败';
              emit('error', stateError);
            }
          }, 5000);
        }
      }, 1000);
    }
  } catch (err) {
    console.error('❌ Jessibuca播放调用失败:', err);
    isLoading.value = false;
    error.value = `播放调用失败: ${String(err)}`;
    emit('error', err);
  }
};

// 使用HTML5播放
const playWithHTML5 = (playUrl: string) => {
  try {
    // 清除之前的源
    videoElement.value!.src = '';
    videoElement.value!.load();

    // 设置新的源
    videoElement.value!.src = playUrl;

    // 尝试播放
    const playPromise = videoElement.value!.play();
    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          isLoading.value = false;
          // message.value = '播放成功';
        })
        .catch(err => {
          console.error('❌ HTML5播放失败:', err);
          isLoading.value = false;
          error.value = `视频播放失败: ${err.message}`;
          emit('error', err);
        });
    }
  } catch (err) {
    console.error('❌ HTML5播放器设置失败:', err);
    isLoading.value = false;
    error.value = `播放器设置失败: ${String(err)}`;
    emit('error', err);
  }
};

// 暂停视频
const pause = () => {

  if (shouldUseJessibuca.value && jessibuca) {
    try {
      // 使用官方API暂停
      jessibuca.pause().then(() => {
        // message.value = '视频已暂停';
      }).catch((err: any) => {
        console.error('❌ Jessibuca暂停失败:', err);
        error.value = `暂停失败: ${String(err)}`;
      });
    } catch (err) {
      console.error('❌ Jessibuca暂停调用失败:', err);
      error.value = `暂停调用失败: ${String(err)}`;
    }
  } else if (videoElement.value) {
    try {
      videoElement.value.pause();
      // message.value = '视频已暂停';
    } catch (err) {
      console.error('❌ HTML5视频暂停失败:', err);
    }
  }
};

// 停止视频
const stop = () => {

  if (jessibuca) {
    try {
      // 先清理画面
      jessibuca.clearView();

      // 关闭连接但不销毁实例（保留播放器以便下次使用）
      jessibuca.close();

      // 不调用destroy，避免销毁播放器实例
      // jessibuca.destroy();
      // jessibuca = null;

      // message.value = '播放器已停止';
    } catch (err) {
      console.error('❌ Jessibuca停止失败:', err);
    }
  }

  if (videoElement.value) {
    try {
      videoElement.value.pause();
      videoElement.value.src = '';
      videoElement.value.load();
    } catch (err) {
      console.error('❌ HTML5视频停止失败:', err);
    }
  }

  // 重置状态
  isLoading.value = false;
  error.value = '';
};

// 截图
const screenshot = () => {
  if (jessibuca) {
    try {
      // 使用官方API截图，返回base64格式
      const screenshotData = jessibuca.screenshot(
        `screenshot_${Date.now()}`, // 文件名
        'png', // 格式
        0.92, // 质量
        'base64' // 返回类型
      );
      // message.value = '截图成功';
      return screenshotData;
    } catch (err) {
      console.error('❌ Jessibuca截图失败:', err);
      error.value = `截图失败: ${String(err)}`;
      return null;
    }
  } else {
    console.warn('⚠️ Jessibuca播放器不可用，无法截图');
    error.value = 'Jessibuca播放器不可用，无法截图';
    return null;
  }
};

// 切换全屏
const toggleFullscreen = () => {
  if (jessibuca) {
    try {
      // 使用官方API切换全屏
      jessibuca.setFullscreen(!jessibuca.isFullscreen);
    } catch (err) {
      console.error('❌ 切换全屏失败:', err);
      error.value = `切换全屏失败: ${String(err)}`;
    }
  } else {
    console.warn('⚠️ Jessibuca播放器不可用，无法切换全屏');
    error.value = 'Jessibuca播放器不可用，无法切换全屏';
  }
};

// 设置音量 (0-1)
const setVolume = (volume: number) => {
  if (jessibuca) {
    try {
      jessibuca.setVolume(volume);
      // message.value = `音量已设置为 ${Math.round(volume * 100)}%`;
    } catch (err) {
      console.error('❌ 设置音量失败:', err);
      error.value = `设置音量失败: ${String(err)}`;
    }
  }
};

// 静音
const mute = () => {
  if (jessibuca) {
    try {
      jessibuca.mute();
      // message.value = '已静音';
    } catch (err) {
      console.error('❌ 静音失败:', err);
    }
  }
};

// 取消静音
const unmute = () => {
  if (jessibuca) {
    try {
      jessibuca.cancelMute();
      // message.value = '已取消静音';
    } catch (err) {
      console.error('❌ 取消静音失败:', err);
    }
  }
};

// 获取播放状态
const getPlayerState = () => {
  if (jessibuca) {
    try {
      return {
        isPlaying: jessibuca.isPlaying(),
        isMute: jessibuca.isMute(),
        hasLoaded: jessibuca.hasLoaded(),
        isRecording: jessibuca.isRecording()
      };
    } catch (err) {
      console.error('❌ 获取播放器状态失败:', err);
      return null;
    }
  }
  return null;
};

// 清理画布
const clearView = () => {
  if (jessibuca) {
    try {
      jessibuca.clearView();
      // message.value = '画面已清理';
    } catch (err) {
      console.error('❌ 清理画布失败:', err);
    }
  }
};

// 强制停止并销毁播放器（仅在组件销毁时使用）
const forceDestroy = () => {

  if (jessibuca) {
    try {
      // 清理画面
      jessibuca.clearView();
      // 关闭连接
      jessibuca.close();
      // 销毁实例
      jessibuca.destroy();
      jessibuca = null;
    } catch (err) {
      console.error('❌ 强制销毁播放器失败:', err);
    }
  }

  // 重置状态
  isLoading.value = false;
  error.value = '';
};

// 重新初始化播放器
const reinitializePlayer = async () => {

  // 先销毁现有播放器
  if (jessibuca) {
    try {
      jessibuca.close();
      jessibuca.destroy();
      jessibuca = null;
    } catch (err) {
      console.error('❌ 销毁旧播放器失败:', err);
    }
  }

  // 重新初始化
  await initPlayer();
};

// 监听videoUrl变化
watch(() => props.videoUrl, (newUrl, oldUrl) => {

  if (newUrl && newUrl !== oldUrl) {

    // 如果播放器已初始化且设置了自动播放，开始播放
    if (jessibuca && props.autoplay) {
      play(newUrl);
    } else if (!jessibuca) {
      // 播放器还未初始化，等待初始化完成
    } else {
    }
  }
}, { immediate: false });

// 监听visible变化
watch(() => props.visible, (visible) => {
  if (!visible && jessibuca) {
    pause();
  }
});

// 暴露方法给父组件
defineExpose({
  play,
  pause,
  stop,
  screenshot,
  toggleFullscreen,
  setVolume,
  mute,
  unmute,
  getPlayerState,
  clearView,
  forceDestroy,
  reinitializePlayer
});

onMounted(async () => {

  // 等待DOM渲染完成
  await nextTick();

  // 如果需要使用Jessibuca，优先初始化
  if (shouldUseJessibuca.value) {
    // message.value = '正在初始化播放器...';
    await initPlayer();
  }
  // 如果明确是HTML5格式，使用HTML5播放器
  else if (props.videoUrl && (isMP4.value || isHLS.value)) {
    // message.value = '使用HTML5播放器';

    // 对于HTML5播放器，确保视频源正确设置
    if (videoElement.value && props.videoUrl) {
      videoElement.value.src = props.videoUrl;
      if (props.autoplay) {
        try {
          await videoElement.value.play();
        } catch (err) {
          console.error('❌ HTML5自动播放失败:', err);
          error.value = '自动播放失败，请手动点击播放';
        }
      }
    }
  }
  // 默认情况下准备Jessibuca播放器
  else {
    // message.value = '正在准备播放器...';
    await initPlayer();
  }
});

onUnmounted(() => {
  // 组件销毁时彻底清理
  forceDestroy();
});
</script>

<style scoped>
.jessibuca-player {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #000;
  overflow: hidden;
}

.player-container {
  width: 100%;
  height: 100%;
  background-color: #000;
  overflow: hidden;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ff4d4f;
  text-align: center;
  padding: 24px;
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.1) 0%, rgba(255, 77, 79, 0.05) 100%);
  border: 1px solid rgba(255, 77, 79, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  font-size: 14px;
  font-weight: 500;
  max-width: 400px;
  z-index: 100;
}

.info-message {
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: #fff;
  font-size: 12px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  padding: 8px 12px;
  border-radius: 6px;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 50;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 200;
  backdrop-filter: blur(2px);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.video-element {
  object-fit: contain;
  border-radius: 8px;
}
</style>
