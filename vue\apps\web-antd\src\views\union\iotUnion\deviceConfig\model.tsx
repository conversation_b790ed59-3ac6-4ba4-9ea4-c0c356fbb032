import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';

// 子设备列表
export class ChildState {
  /** 子设备ID */
  public itemId = 0
  /** 组合设备标识 */
  public unionKey = 0
  /** 设备标识 */
  public deviceKey = ""
  /** 设备标识 */
  public fieldJson = ""
  /** 租户ID */
  public tenantId = ""
  /** 创建时间 */
  public createdAt = ""
  /** 创建人 */
  public fieldList = []
  /** 设备名称 */
  public deviceName = ""
  /** 产品标识 */
  public productKey = ""
  /** 产品名称 */
  public productName = ""
}
/** 子设备列表 表格列配置 */
export const childColumns: VxeGridProps['columns'] = [
  // 编号
  {
    title: '编号',
    field: 'itemId',
    align: 'center',
    width: 80,
  },
  //  产品名称
  {
    title: '产品名称',
    field: 'productName',
    align: 'center',
    width: -1,
  },
  // 设备名称
  {
    title: '设备名称',
    field: 'deviceName',
    align: 'center',
    width: -1,
  },
  // 操作
  { title: '操作', width: 120, slots: { default: 'action' } }
]
/** 子设备列表表格列类型 */
export interface ChildRowType {
  itemId: any;
  productName: any;
  deviceName: any;
}

// 属性列表
export class PropState {
  /** 模型ID*/
  public modelId = ""
  /** 模型名称 */
  public modelName = ""
  /** 模型标识 */
  public modelKey = ""
  /** 数据类型 */
  public dataType = ""
  /** 数据定义 */
  public specs = ""
}
/** 属性列表 表格列配置 */
export const propColumns: VxeGridProps['columns'] = [
  // 模型编号
  {
    title: '模型编号',
    field: 'modelId',
    align: 'center',
    width: -1,
  },
  // 模型名称
  {
    title: '模型名称',
    field: 'modelName',
    align: 'center',
    width: -1,
  },
  // 模型标识
  {
    title: '模型标识',
    field: 'modelKey',
    align: 'center',
    width: -1,
  },
  // 数据类型
  {
    title: '数据类型',
    field: 'dataType',
    align: 'center',
    width: -1,
  },

  // 操作
  { title: '是否启用', width: 120, slots: { default: 'action' } },

]
/** 属性列表表格列类型 */
export interface PropRowType {
  fieldId: number;
  fieldName: string;
  fieldKey: string;
  fieldType: string;
  fieldDefine: string;
  isEnable: number;
}


// 子设备新增设备表单
/** 子设备列表 新增*/
export const detailColumns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '设备名称',
    field: 'deviceName',
    align: 'left',
    width: -1,
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
  },
  {
    title: '所属产品',
    field: 'productName',
    align: 'left',
    width: -1,
  },
  {
    title: '在线状态',
    field: 'deviceState',
    align: 'center',
    width: 100,
    slots: { default: 'deviceState' },
  },
  {
    title: '是否启用',
    field: 'status',
    align: 'center',
    width: 100,
    slots: { default: 'status' },
  }
]
/** 子设备列表 新增列接口 */
export interface DetailRowType {
  deviceId: number;
  productKey: string;
  deviceKey: string;
  deviceName: string;
  longitude: number;
  latitude: number;
  firmwareVersion: number;
  isShadow: number;
  imgUrl: string;
  deviceState: number;
  alarmState: number;
  rssi: number;
  thingsModelValue: string;
  networkAddress: string;
  networkIp: string;
  status: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
}

/** 子设备列表 新增 表单 */
export const detailSchema: VbenFormSchema[] = [
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
  }, {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  }
]


/** 子设备类别 fieldList 类型 */
export class FieldListItemType {
  dataType = ""
  id = ""
  isEnable = 0
  modelId = 0
  modelKey = ""
  modelName = ""
  constructor(state?: Partial<FieldListItemType>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}
/** 整体修改组合设备的子设备列表类型 */
export class EditListItemType {
  createdAt = ""
  deviceKey = ""
  deviceName = ""
  fieldJson = ""
  fieldList = []
  itemId = 0
  productKey=""
  productName=""
  tenantId = ""
  unionKey = ""
  constructor(state?: Partial<EditListItemType>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

