import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { functionColumns } from '../iotProductModel/model';
export interface Step1DataType {
  taskId: number;
  taskName: string;
  status: string;
  remark: string;
  targetType: string;
  productKey: string;
  deviceKeyList: string[];
  disableDeviceKeyList: string[];
  groupId: number | null;
}

export interface Step2DataType {
  type: number;
  timeWeek: {
    time: string;
    week: [];
  };
  cron: string;
}

export interface Step3DataType {
  pollType: string;
  pollScriptObj: {
    modelKeys: string[];
    dataType: string;
    dataMsg: string;
    useSubAddr: string;
  };
  modbusParam: {
    useSubAddr: string;
    subAddr: number;
    modbusFunction: string;
    regAddr: number;
    coilCount: number;
    regCount: number;
    coilValue: number;
    regValue: number;
    coilStatusList: boolean[];
    regValueList: number[];
    dataCheckType: string;
  };
  extendConfigObj: {
    isParallel: boolean;
    isAsync: boolean;
    syncTimeout: number;
    cmdInterval: number;
    failRetry: number;
  };
}


export type FormData = Step1DataType & Step2DataType & Step3DataType;

export class State {
  public taskId = 0; // ID
  public taskName = ''; // 任务名称
  public targetType = null; // 目标类型
  public productKey = ''; // 目标产品
  public deviceKeys = null; // 目标设备
  public disableDeviceKeys = null; // 禁止运行设备
  public groupId = null; // 目标设备组
  public cron = ''; // 定时cron表达式
  public pollType = null; // 采集类型
  public pollScriptJson = null; // 指令脚本JSON
  public extendConfigJson = null; // 高级选项JSON
  public status = 0; // 数据状态
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间
  public updatedAt = ''; // 更新时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'targetType',
    component: 'Select',
    label: '目标',
    formItemClass: 'col-span-1',
    componentProps: {
      options: getDictOptions('poll_target_type'),
      onUpdateChecked: (e: any) => {
        console.log(e);
      },
    },
    defaultValue: '1',
  },
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '目标产品',
    formItemClass: 'col-span-1',

    hideLabel: true,
    componentProps: {
      placeholder: '请选择目标产品',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: {
      show: (values: any) => {
        return values.targetType == 2;
      },
      triggerFields: ['targetType'],
    },
    rules: null
  },
  {
    fieldName: 'deviceKeys',
    component: 'Input',
    label: '目标设备',
    hideLabel: true,
    formItemClass: 'col-span-1',

    componentProps: {
      mode: 'multiple',
      placeholder: '请输入设备标识符',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: {
      show: (values: any) => {
        return values.targetType == 1;
      },
      triggerFields: ['targetType'],
    },

    rules: null
  },
  {
    fieldName: 'groupId',
    component: 'Select',
    label: '设备组',
    hideLabel: true,
    formItemClass: 'col-span-1',
    componentProps: {
      placeholder: '请选择设备组',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: {
      show: (values: any) => {
        return values.targetType == 3;
      },
      triggerFields: ['targetType'],
    },
    rules: null
  },{
    fieldName: 'taskName',
    component: 'Input',
    label: '任务名称',
    componentProps: {
      placeholder: '请输入任务名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'status',
    component: 'Select',
    label: '状态',
    componentProps: {
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: getDictOptions(DictEnum.SYS_NORMAL_DISABLE),
    },
  },
];
export const columnsBase: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '编号',
    field: 'taskId',
    align: 'center',
    width: 100,
  },
  {
    title: '任务名称',
    field: 'taskName',
    align: 'left',
    width: -1,
  },
  {
    title: '采集类型', field: 'pollType', align: 'center', width: 200,
    slots: {
      default: ({ row }) => {
        return renderDict(row.pollType, 'poll_type');
      }
    },
  },
  {
    title: '目标类型', field: 'targetType', align: 'center', width: 200,
    slots: {
      default: ({ row }) => {
        return renderDict(row.targetType, 'poll_target_type');
      }
    },
  },
  {
    title: '采集目标',
    field: 'productKey',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        if (row.targetType == 1) {
          return row.deviceNames || row.deviceKeys;
        } else if (row.targetType == 2) {
          return row.productName || row.productKey;
        } else if (row.targetType == 3) {
          return row.groupName || row.groupId;
        }
        return '';
      }
    },
  },
  {
    title: '定时cron表达式',
    field: 'cron',
    align: 'left',
    width: -1,
  },
  {
    title: '状态',
    field: 'status',
    align: 'center',
    width: 200,
    slots: {
      default: 'status'
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  ...columnsBase,
  { title: '操作', width: 120, slots: { default: 'action' } },
];

//设备详情页面的表格列
export const deviceDetailColumns: VxeGridProps['columns'] = [...columnsBase,
  {
    title: '允许该设备',
    field: 'isEnable',
    width: 100,
    slots: {
      default: 'isEnableSlot',
    },
  },
  {
    title: '最近执行时间',
    field: 'lastExecAt',
    width: 200,
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  taskId: number;
  taskName: string;
  targetType: number;
  productKey: string;
  deviceKeys: string;
  disableDeviceKeys: string;
  groupId: number;
  cron: string;
  pollType: number;
  pollScriptJson: string;
  extendConfigJson: string;
  status: string;
  remark: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  isEnable: boolean;
  disableDeviceKeyList: string[];
};

// 查看字段列表
export const viewSchema = (data: any): DescItem[] => [
  { field: 'taskId', label: 'ID' },
  { field: 'taskName', label: '任务名称' },
  { field: 'status', label: '状态' ,
    render(val: any,row: any) {
      return renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
    }
  },
  {
    field: 'targetType',
    label: '目标类型',
    render(val: any, row: any) {
      return renderDict(row.targetType, 'poll_target_type');
    },
  },
  { field: 'productKey', label: '目标产品', 
    render(val: any,row: any) {
      if (row.targetType == 1) {
        return row.deviceNames || row.deviceKeys;
      } else if (row.targetType == 2) {
        return row.productName || row.productKey;
      } else if (row.targetType == 3) {
        return row.groupName || row.groupId;
      }
      return '';
    } 
  },
  { field: 'disableDeviceKeyList', label: '禁止运行设备' ,
    render(val: any,row: any) {
      return row.disableDeviceNames || row.disableDeviceKeys;
    }
  },
  { field: 'cron', label: '定时cron表达式' },
  {
    field: 'pollType',
    label: '采集类型',
    render(val: any,row: any) {
      return renderDict(row.pollType, 'poll_type');
    },
  },
  {
    field: 'pollScriptObj.modelKeys',
    label: '采集属性',
    show: () => {
      return data.pollType == 2;
    },
    render(val: any,row: any) {
      return row.pollScriptObj.modelKeys ? row.pollScriptObj.modelKeys.join(',') : '';
    }
  },
  {
    field: 'pollScriptObj.dataType',
    label: '指令格式',
    show: () => {
      return data.pollType == 3;
    },
    render(val: any,row: any) {
      return renderDict(row.pollScriptObj.dataType, 'topic_data_type');
    }
  },
  {
    field: 'pollScriptObj.dataMsg',
    label: '指令内容',
    show: () => {
      return data.pollType == 3;
    },
    render(val: any,row: any) {
      return row.pollScriptObj.dataMsg;
    }
  },
  {
    field: 'modbusParam.subAddr',
    label: '从机地址',
    show: () => {
      return data.pollType == 4;
    },
    render(val: any,row: any) {
      return row.modbusParam.subAddr;
    }
  },
  {
    field: 'modbusParam.modbusFunction',
    label: '功能码',
    show: () => {
      return data.pollType == 4;
    },
    render(val: any,row: any) {
      return renderDict(row.modbusParam.modbusFunction, 'modbus_function');
    }
  }, 
  {
    field: 'pollScriptObj.useSubAddr',
    label: '使用子设备地址',
    show: () => {
      return data.pollType == 4;
    },
    render(val: any,row: any) {
      return row.pollScriptObj.useSubAddr ? '是' : '否';
    }
  },
  {
    field: 'modbusParam.regAddr',
    label: '起始寄存器地址',
    show: () => {
      return data.pollType == 4;
    },
    render(val: any,row: any) {
      return row.modbusParam.regAddr;
    }
  },
  {
    field: 'modbusParam.coilCount',
    label: '线圈个数',
    show: () => {
      return data.pollType == 4 && (data.modbusParam.modbusFunction == 1 || data.modbusParam.modbusFunction == 2 || data.modbusParam.modbusFunction == 15);
    },
    render(val: any,row: any) {
      return row.modbusParam.coilCount;
    }
  },
  {
    field: 'modbusParam.regCount',
    label: '寄存器个数',
    show: () => {
      return data.pollType == 4 && (data.modbusParam.modbusFunction == 3 || data.modbusParam.modbusFunction == 4 || data.modbusParam.modbusFunction == 16);
    },
    render(val: any,row: any) {
      return row.modbusParam.regCount;
    }
  },
  {
    field: 'modbusParam.coilValue',
    label: '线圈值',
    show: () => {
      return data.pollType == 4 && data.modbusParam.modbusFunction == 5;
    },
    render(val: any,row: any) {
      return row.modbusParam.coilValue;
    }
  },
  {
    field: 'modbusParam.regValue',
    label: '寄存器值',
    show: () => {
      return data.pollType == 4 && data.modbusParam.modbusFunction == 6;
    },
    render(val: any,row: any) {
      return row.modbusParam.regValue;
    }
  },
  {
    field: 'modbusParam.coilStatusList',
    label: '线圈状态列表',
    show: () => {
      return data.pollType == 4 && data.modbusParam.modbusFunction == 15;
    },
    render(val: any,row: any) {
      return row.modbusParam.coilStatusList ? row.modbusParam.coilStatusList.map((item: any, index: number) => '#' + index + '：' + (item ? '开' : '关')).join(' , ') : '';
    }
  },
  {
    field: 'modbusParam.regValueList',
    label: '寄存器值列表',
    show: () => {
      return data.pollType == 4 && data.modbusParam.modbusFunction == 16;
    },
    render(val: any,row: any) {
      return row.modbusParam.regValueList ? row.modbusParam.regValueList.map((item: any, index: number) => '#' + index + '：' + item).join(' , ') : '';
    }
  },
  {
    field: 'modbusParam.dataCheckType',
    label: '数据校验类型',
    show: () => {
      return data.pollType == 4;
    },
    render(val: any,row: any) {
      return renderDict(row.modbusParam.dataCheckType, 'data_check_type');
    }
  },
  {
    field: 'extendConfigObj.isParallel',
    label: '是否并行',
    render(val: any,row: any) {
      return row.extendConfigObj.isParallel ? '是' : '否';
    }
  },
  {
    field: 'extendConfigObj.isAsync',
    label: '是否异步',
    render(val: any,row: any) {
      return row.extendConfigObj.isAsync ? '是' : '否';
    }
  },
  {
    field: 'extendConfigObj.syncTimeout',
    label: '同步超时时间',
    render(val: any,row: any) {
      return row.extendConfigObj.syncTimeout + ' 秒';
    }
  },
  {
    field: 'extendConfigObj.cmdInterval',
    label: '指令间隔时间',
    render(val: any,row: any) {
      return row.extendConfigObj.cmdInterval + ' 秒';
    }
  },
  {
    field: 'extendConfigObj.failRetry',
    label: '失败重试次数',
    render(val: any,row: any) {
      return row.extendConfigObj.failRetry + ' 次';
    }
  },

  { field: 'remark', label: '备注' },
];

// 第一步 编辑字段列表
export const editStep1Schema: VbenFormSchema[] = [
  {
    fieldName: 'taskId',
    component: 'Input',
    label: 'ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'taskName',
    component: 'Input',
    label: '任务名称',
    componentProps: {
      placeholder: '请输入任务名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  }, {
    fieldName: 'status',
    component: 'Switch',
    label: '状态',
    componentProps: {
      placeholder: '请输入数据状态',
      checkedValue: '0',
      unCheckedValue: '1',
      class: 'w-10',
      defaultValue: '0',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  }, {
    fieldName: 'remark',
    component: 'Textarea',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
    },
    rules: null
  },
  {
    fieldName: 'targetType',
    component: 'RadioGroup',
    label: '目标类型',
    componentProps: {
      options: getDictOptions('poll_target_type'),
      onUpdateChecked: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '目标产品',
    componentProps: {
      placeholder: '请输入目标产品',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: { show: (values: any) => values.targetType == 1 || values.targetType == 2, triggerFields: ['targetType'], },
    rules: 'required'
  },
  {
    fieldName: 'deviceKeyList',
    component: 'Select',
    label: '目标设备',
    componentProps: {
      placeholder: '请输入目标设备',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: { show: (values: any) => values.targetType == 1, triggerFields: ['targetType'], },
    rules: 'required'
  },
  {
    fieldName: 'disableDeviceKeyList',
    component: 'Input',
    label: '禁止运行设备',
    componentProps: {
      placeholder: '请输入禁止运行设备',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: { show: () => false, triggerFields: [''], },
    rules: null
  },
  {
    fieldName: 'groupId',
    component: 'Input',
    label: '目标设备组',
    componentProps: {
      placeholder: '请输入目标设备组',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: { show: (values: any) => values.targetType == 3, triggerFields: ['targetType'], },
    rules: 'required'
  },
];

function getCoilStatusList(coilCount: number, oldStatusList:[]){
  let addedCount = coilCount - oldStatusList.length;
  let updated = [];
  if (addedCount > 0) {
    updated = [...oldStatusList, ...Array(addedCount).fill(false)];
  } else {
      updated = oldStatusList.slice(0, coilCount);
  }
  return updated;
}

function getRegValueList(regCount: number, oldValueList:[]){
  let addedCount = regCount - oldValueList.length;
  let updated = [];
  if (addedCount > 0) {
    updated = [...oldValueList, ...Array(addedCount).fill(0)];
  } else {
      updated = oldValueList.slice(0, regCount);
  }
  return updated;
}

// 第二步 编辑字段列表
export const editStep2Schema: VbenFormSchema[] = [
  {
    fieldName: 'type',
    component: 'RadioGroup',
    label: '类型',
    componentProps: {
      options: [{label: '快捷设置', value: 0}, {label: '自定义表达式', value: 1}],
    },
    hideLabel: true,
  },
  {
    fieldName: 'timeWeek',
    component: 'WeekPicker',
    label: '时间星期',
    hideLabel: true,
    componentProps: {
      readOnly: true,
    },
    dependencies: { 
      disabled: (values)=>{
        return values.type == 1;
      },
      triggerFields: ['type']
    },
  },
  {
    fieldName: 'cron',
    component: 'Input',
    label: '定时cron表达式',
    componentProps: {
      placeholder: '请输入定时cron表达式',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    dependencies: { 
      disabled: (values)=>{
        return values.type == 0;
      },
      triggerFields: ['type']},
    hideLabel: true,
    rules: 'required'
  }];

// 第三步 编辑字段列表
export const editStep3Schema: VbenFormSchema[] = [
  {
    fieldName: 'pollType',
    component: 'RadioGroup',
    label: '采集类型',
    componentProps: {
      options: getDictOptions('poll_type'),
      onUpdateChecked: (e: any) => {
        console.log(e);
      },
    },
    dependencies: { show: (values: any) => false, triggerFields: [''], },
    rules: 'required'
  },
  {
    fieldName: 'pollScriptObj.modelKeys',
    component: 'Select',
    label: '采集属性',
    formItemClass: 'col-span-6',
    componentProps: {
      placeholder: '请选择采集属性',
    },
    rules: 'required',
    dependencies: { show: (values: any) => values.pollType == 2, triggerFields: ['pollType'], },
  },
  {
    fieldName: 'pollScriptObj.dataType',
    component: 'RadioGroup',
    label: '指令格式',
    formItemClass: 'col-span-6',
    componentProps:{
      placeholder: '请选择指令格式',
      options: getDictOptions('topic_data_type'),
    },
    dependencies: { 
      show: (values: any) => values.pollType == 3, 
      triggerFields: ['pollType','pollScriptObj'], 
    },
    rules: 'required'
  },
  {
    fieldName: 'pollScriptObj.dataMsg',
    component: 'Textarea',
    label: '指令内容',
    formItemClass: 'col-span-6',
    componentProps: {
      placeholder: '请输入指令内容',
    },
    dependencies: { show: (values: any) => values.pollType == 3, triggerFields: ['pollType','pollScriptObj'], },
    rules: 'required'
  },
  {
    fieldName: 'pollScriptObj.useSubAddr',
    component: 'Switch',
    label: '使用子设备地址',
    componentProps: {
      placeholder: '请输入指令内容',
      checkedValue: '1',
      unCheckedValue: '0',
      class: 'w-10',
    },
    dependencies: { show: () => false, triggerFields: ['pollScriptObj'], },
    rules: null
  },
  {
    fieldName: 'modbusParam.subAddr',
    component: 'Select',
    label: '从机地址',
    componentProps: {
      placeholder: '请输入从机地址',
      options: Array.from({ length: 255 }, (_, i) => ({ label: (i + 1).toString() + "-0x" + (i + 1).toString(16).padStart(2, '0'), value: i + 1 })),
    },
    dependencies: {
      show: (values: any) => {
        return values.pollType == 4;
      },
      triggerFields: ['pollType','modbusParam'],
    },
    defaultValue: 1,
    rules: null
  },
  {
    fieldName: 'modbusParam.modbusFunction',
    component: 'Select',
    label: '功能码',
    dependencies: {
      show: (values: any) => {
        return values.pollType == 4;
      },
      componentProps: (values: any) => {
        return {
          placeholder: '请输入功能码',
          options: getDictOptions('modbus_function'),
          onChange: (e: any) => {
            if(e == 15){
              values.modbusParam.coilStatusList = getCoilStatusList(values.modbusParam.coilCount, values.modbusParam.coilStatusList);
            }
            if(e == 16){
              values.modbusParam.regValueList = getRegValueList(values.modbusParam.regCount, values.modbusParam.regValueList);
            }
          },
        }
      },
      triggerFields: ['pollType', 'modbusParam'],
    },
    rules: null
  },
  {
    fieldName: 'modbusParam.useSubAddr',
    component: 'Switch',
    label: '使用子设备地址',
    defaultValue: '0',
    componentProps: {
      checkedValue: '1',
      unCheckedValue: '0',
      class: 'w-10',
    },
    formItemClass: 'col-span-6',
    dependencies: {
      show: (values: any) => {
        return values.pollType == 4;
      },
      triggerFields: ['pollType'],
    },
    rules: null
  },
  {
    fieldName: 'modbusParam.regAddr',
    component: 'InputNumber',
    label: '起始寄存器地址',
    componentProps: {
      placeholder: '请输入寄存器地址',
      min: 0,
    },

    dependencies: {
      show: (values: any) => {
        return values.pollType == 4;
      },
      triggerFields: ['pollType','modbusParam'],
    },
    rules: null
  },
  {
    fieldName: 'modbusParam.coilCount',
    component: 'InputNumber',
    label: '线圈个数',
    dependencies: {
      show: (values: any) => {
        if (values.pollType != 4) {
          return false;
        }
        return values.modbusParam.modbusFunction == 1 || values.modbusParam.modbusFunction == 2 || values.modbusParam.modbusFunction == 15;
      },
      componentProps: (values: any, formApi: any) => {
        if(values.modbusParam.modbusFunction == 15){
          return {
            min:1,
            onChange: (e: any) => {
              values.modbusParam.coilStatusList = getCoilStatusList(e, values.modbusParam.coilStatusList);
            },
          }
        }
        return {
          min:1,
          placeholder: '请输入线圈数量',
          onChange: (e: any) => {
            console.log("modbusParam.coilCount updateValue", e);
          },
        }
      },
      triggerFields: ['pollType','modbusParam'],
    },
    rules: null
  },
  {
    fieldName: 'modbusParam.regCount',
    component: 'InputNumber',
    label: '寄存器个数',
    componentProps: {
      placeholder: '请输入寄存器数量',
      min: 1,
    },
    dependencies: {
      show: (values: any) => {
        if (values.pollType != 4) {
          return false;
        }
        return values.modbusParam.modbusFunction == 3 || values.modbusParam.modbusFunction == 4 || values.modbusParam.modbusFunction == 16;
      },
      componentProps: (values: any, formApi: any) => {
        if(values.modbusParam.modbusFunction == 16){
          return {
            min:1,
            onChange: (e: any) => {
              values.modbusParam.regValueList = getRegValueList(e, values.modbusParam.regValueList);
            },
          }
        }
        return {
          min:1,
          placeholder: '请输入寄存器数量',
          onChange: (e: any) => {
            console.log("modbusParam.regCount updateValue", e);
          },
        }
      },
      triggerFields: ['pollType','modbusParam'],
    },
    rules: null
  },
  {
    fieldName: 'modbusParam.coilValue',
    component: 'InputNumber',
    label: '线圈值',
    componentProps: {
      placeholder: '请输入线圈值',
      min: 0,
    },
    dependencies: {
      show: (values: any) => {
        if (values.pollType != 4) {
          return false;
        }
        return values.modbusParam.modbusFunction == 5;
      },
      triggerFields: ['pollType','modbusParam'],
    },
    rules: null
  },
  {
    fieldName: 'modbusParam.regValue',
    component: 'InputNumber',
    label: '寄存器值',
    componentProps: {
      placeholder: '请输入寄存器值',
      min: 0,
    },
    dependencies: {
      show: (values: any) => {
        if (values.pollType != 4) {
          return false;
        }
        return values.modbusParam.modbusFunction == 6;
      },
      triggerFields: ['pollType','modbusParam'],
    },
    rules: null
  },
  {
    fieldName: 'modbusParam.coilStatusList',
    component: 'Input',
    label: '线圈状态列表',
    formItemClass: 'col-span-6',
    componentProps: {
      showLabel: false,
    },
    dependencies: {
      show: (values: any) => {
        if (values.pollType != 4) {
          return false;
        }
        return values.modbusParam.modbusFunction == 15;
      },
      triggerFields: ['pollType','modbusParam'],
    },
    rules: null
  },
  {
    fieldName: 'modbusParam.regValueList',
    component: 'Input',
    label: '寄存器值列表',
    formItemClass: 'col-span-6',
    componentProps: {
      showLabel: false,
    },
    dependencies: {
      show: (values: any) => {
        if (values.pollType != 4) {
          return false;
        }
        return values.modbusParam.modbusFunction == 16;
      },
      triggerFields: ['pollType','modbusParam'],
    },
    rules: null
  },
  {
    fieldName: 'modbusParam.dataCheckType',
    component: 'Select',
    label: '数据校验类型',
    defaultValue: '2',
    componentProps: {
      placeholder: '请选择数据校验类型',
      options: getDictOptions('data_check_type'),
    },
    dependencies: {
      show: (values: any) => {
        return values.pollType == 4;
      },
      triggerFields: ['pollType'],
    },
    rules: null
  },
  {
    fieldName: 'review',
    component: 'Input',
    label: '',
    formItemClass: 'col-span-6',
    dependencies: {
      show: (values: any) => {
        return values.pollType == 4;
      },
      triggerFields: ['pollType'],
    },
    rules: null
  },
];

export const extendConfigSchema: VbenFormSchema[] = [
  {
    fieldName: 'isParallel',
    component: 'Switch',
    label: '设备间是否并行',
    componentProps: {
      class: 'w-10',
    },
  },
  {
    fieldName: 'cmdInterval',
    component: 'InputNumber',
    label: '指令间隔时间',
    defaultValue: 1,
    componentProps: {
      min: 0,
    },
    suffix: '秒',
  },
  {
    fieldName: 'isAsync',
    component: 'Switch',
    label: '是否异步',
    componentProps: {
      class: 'w-10',
    },
  },
  {
    fieldName: 'syncTimeout',
    component: 'InputNumber',
    label: '同步超时时间',
    defaultValue: 5,
    componentProps: {
      placeholder: '请输入同步超时时间',
      min: 0,
    },
    suffix: '秒',
    dependencies: {
      disabled: (values: any) => {
        return values.isAsync == 0;
      },
      triggerFields: ['isAsync'],
    },
  },

  {
    fieldName: 'failRetry',
    component: 'InputNumber',
    label: '失败重试次数',
    defaultValue: 2,
    componentProps: {
      min: 0,
    },
    suffix: '次',
    dependencies: {
      disabled: (values: any) => {
        return values.isAsync == 0;
      },
      triggerFields: ['isAsync'],
    },
  },
];
