<script setup lang="ts">
import type { SysClient } from '#/api/system/client';
import { computed, nextTick, ref, h, reactive } from 'vue';
import { useVbenDrawer, useVbenModal, type VbenFormProps } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep, getVxePopupContainer } from '@vben/utils';
import { useVbenForm } from '#/adapter/form';
import { Description, useDescription } from '#/components/description';
import { viewSchema, type RowType, deviceColumns, deviceGroupQuerySchema } from './model';
import { useVbenVxeGrid, type VxeTableGridOptions, type VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { View } from '#/api/device/iotGroup';
import { List, Delete, Edit } from '#/api/device/iotDeviceGroup';
import { MdiPlus, MdiDelete } from '@vben/icons';
//import selectDevice from './selectDevice.vue';
import selectDevice from '../iotDevice/for-select.vue';
import { Button, message, Tag, Modal, Popconfirm, Switch } from 'ant-design-vue';
import { ListNoPage as ListProduct } from '#/api/device/iotProduct';
const currentGroup = ref<any>({
  groupId: 0,
});
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onOpenChange: handleOpenChange,
});
const [registerDescription, { setDescProps }] = useDescription({
  column: 2,
  schema: viewSchema,
});

async function getProductOptions() {
  const res = await ListProduct({});
  if(!res || !res.items){
    return [];
  }
  const options = res.items.map((item: any) => ({
    label: item.productName,
    value: item.productKey,
  }));
  return options;
}

async function handleOpenChange(open: boolean) {
  if (!open) {
    return null;
  }
  const { record } = drawerApi.getData() as { record: RowType };
  const record2 = await View({ groupId: record.groupId });
  currentGroup.value = record2 ? record2 : record;

  gridApi.formApi.updateSchema([
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
    componentProps: {
      placeholder: '请选择产品',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: await getProductOptions(),
      showSearch: true,
      filterOption: (input: any, option: any) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
    },
    formItemClass: 'col-span-1',
  }]);
  setDescProps({ data: record2 }, true);
  handleRefresh();
}

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: deviceGroupQuerySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  submitButtonOptions: {
    class: 'mr-4',
  },
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<any> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'id',
  },
  columns: deviceColumns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        if (!currentGroup.value.groupId) {
          return {
            data: [],
            total: 0,
          };
        }
        return await List({
          ...formValues,
          page: page.currentPage,
          pageSize: page.pageSize,
          groupId: currentGroup.value.groupId,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  gridClass: 'p-0',
  formOptions,
  gridOptions,
  gridEvents,
});

const [SelectDeviceModal, selectDeviceModalApi] = useVbenModal({
  connectedComponent: selectDevice,
});
function handleAdd() {
  selectDeviceModalApi.setData({ update: false, view: false});
  selectDeviceModalApi.open();
}

async function handleDelete(row: any) {
  await Delete({ id: [row.id] });
  message.success("删除成功");
  await handleRefresh();
}
async function handleRefresh() {
  await gridApi.query();
}
function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const ids: string[] = [];
  for (const row of rows) {
    ids.push(row.id);
  }
  if (ids.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await Delete({ id: ids });
      message.success("删除成功");
      await handleRefresh();
    },
  });
}

async function handleSelectDeviceConfirm(checkedDeviceKeys: any[]) {
  await Edit({
    groupId: currentGroup.value.groupId,
    deviceKeys: checkedDeviceKeys,
  });
  await handleRefresh();
  message.success('操作成功');
}
</script>
<template>
  <BasicDrawer :footer="false" class="w-[800px]" title="查看">
    <Description @register="registerDescription"></Description>
    <Card>
      <Grid class="p-0 border rounded-md mt-4" table-title="设备列表">
      <template #toolbar-tools>
        <Button class="mr-2 flex items-center " type="primary" :icon="h(MdiPlus)" @click="handleAdd"
          v-access:code="'cpm:device:iotDeviceGroup:edit'">
          新增
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :disabled="!CheckboxChecked" :icon="h(MdiDelete)"
          @click="handleMultiDelete" v-access:code="'cpm:device:iotDeviceGroup:delete'">
          删除
        </Button>
      </template>
      <template #action="{ row }">
        <div class="flex items-center justify-center">
          <AccessControl :codes="['cpm:device:iotDeviceGroup:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
    </Card>
  </BasicDrawer>
  <SelectDeviceModal @reload="handleRefresh" @confirm="handleSelectDeviceConfirm" />
</template>