import { requestClient } from '#/api/request';

// 获取建筑物列表
export function List(params:any) {
  return requestClient.get<any>('gen/iotBuild/list', { params });
}

// 删除/批量删除建筑物
export function Delete(params:any) {
  return requestClient.post<any>('gen/iotBuild/delete', { ...params });
}

// 添加/编辑建筑物
export function Edit(params:any) {
  return requestClient.post<any>('gen/iotBuild/edit', { ...params });
}

// 获取建筑物指定详情
export function View(params:any) {
  return requestClient.get<any>('gen/iotBuild/view', { params });
}

// 导出建筑物
export function Export(params:any) {
  return requestClient.post<Blob>('/gen/iotBuild/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}