
import { computed, h, onMounted, ref, watchEffect, type ComputedRef } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';
import { dictToOptions } from '#/store/dict';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';
import { List, Export, confirm } from '#/api/ruleengine/iotAlarmLog';


interface Column {
  title: string;
  width: number;
  // 修改visible类型
  visible?: boolean | ComputedRef<boolean>;
  slots?: {
    default: string;
  };
}
const confirmEnable = ref(true);

// 在模块加载时获取接口参数
onMounted(() => {
  fetchConfirmEnable();
});
async function fetchConfirmEnable() {
  try {
    const response = await List({ page: 1, pageSize: 1 }); // 获取第一页数据

    // 从响应中提取confirmEnable参数（根据实际接口结构调整）
    confirmEnable.value = response.data?.confirmEnable !== false;
    // 或从响应头中获取
    // confirmEnable.value = response.headers['x-confirm-enable'] !== 'false';
  } catch (error) {
    console.error('获取confirmEnable参数失败:', error);
  }
}
const getDefaultDateRange = () => {
  const now = dayjs();
  const currentWeekStart = now.startOf('week');
  const lastWeekStart = currentWeekStart.subtract(1, 'week');
  return [lastWeekStart.startOf('day'), currentWeekStart.endOf('day')];
};
const defaultDateRange = ref(getDefaultDateRange());

watchEffect(() => {
  const intervalId = setInterval(() => {
    defaultDateRange.value = getDefaultDateRange();
  }, 24 * 60 * 60 * 1000);
  return () => clearInterval(intervalId);
});
const rangePickerRequired = (value: any) => {
  return Array.isArray(value) && value.length === 2 && value[0] && value[1];
};

export class State {
  public alarmLogId = 0; // 报警记录ID
  public alarmConfigId = 0; // 报警配置ID
  public sceneId = 0; // 场景ID
  public productName = ''; // 报警产品
  public deviceName = ''; // 报警设备

  public key = '';
  public value = '';
  public alarmConfigName = '';
  public alarmLevel = 0; // 报警级别
  public startTime = ''; // 开始时间
  public endTime = ''; // 结束时间
  public confirmState = 0; // 处理状态（1=确认报警 2=躁扰报警 3=测试报警）
  public confirmContent = ''; // 处理结果
  public confirmTime = ''; // 处理时间
  public confirmUser = 0; // 处理用户ID
  public confirmUserType = ''; // 报警处理用户（1=设备报警用户 2=终端绑定用户 3=项目接警用户 4=后台用户）
  public sceneConditions = ''; // 场景条件JSON
  public tenantId = ''; // 租户ID（报警配置所属租户）
  public deptId = 0; // 所属机构
  public createdAt = ''; // 创建时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [

  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '报警时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY - MM - DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      // defaultValue: defaultDateRange
    },
    // rules: z.custom(rangePickerRequired, '请选择创建时间范围')
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: 'ID',
    field: 'alarmLogId',
    align: 'left',
    width: 100,
    type: 'checkbox',
  },

  {
    title: '报警名称',
    field: 'alarmConfigName',
    align: 'left',
    width: -1,
  },
  {
    title: '报警配置ID',
    field: 'alarmConfigId',
    align: 'left',
    width: 120,
  },
   {
     title: '报警级别',
     field: 'alarmLevel',
   align: 'left',
    width: -1,
     slots: {
       default: ({ row }) => {
       return renderDict(row.alarmLevel, DictEnum.ALARM_LEVEL);
       },
     },
   },
  {
    title: '开始时间',
    field: 'startTime',
    align: 'left',
    width: -1,
  },
  {
    title: '结束时间',
    field: 'endTime',
    align: 'left',
    width: -1,
  },
  {
    title: '处理状态',
    field: 'confirmState',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.confirmState, DictEnum.ALARM_CONFIRM_STATE);
      },
    },
  },
  {
    title: '处理结果',
    field: 'confirmContent',
    align: 'left',
    width: -1,
  },

  //   {
  //     title: '处理用户ID',
  //     field: 'confirmUser',
  //     align: 'left',
  //     width: -1,
  //  },
  {
    title: '处理用户',
    field: 'confirmUserType',
    align: 'left',
    width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.confirmUserType, DictEnum.ALARM_CONFIRM_USER_TYPE);
      },
    },
  },
  {
    title: '触发条件',
    field: 'matchFields',
    align: 'left',
    width: 300,
    slots: {
      default: ({ row }) => {
        const matchFields = Array.isArray(row.matchFields) ? row.matchFields : [];
        return matchFields.map((item: any) => {
          return item.key + ':' + item.value + '\n' + '满足条件:' + item.relOp + ' ' + item.condVal;
        }).join('\n');
      },
    },
  },
  {
    title: '操作',
    width: 160,
    //visible: computed(() => confirmEnable.value), 
    slots: { default: 'action' }
  }
];

// 表格列接口
export interface RowType {
  alarmLogId: number;
  alarmConfigId: number;
  sceneId: number;
  productName: string;
  alarmConfigName: string;
  deviceName: string;
  alarmLevel: number;
  startTime: string;
  endTime: string;
  confirmState: number;
  confirmContent: string;
  confirmTime: string;
  confirmUser: number;
  confirmUserType: string;
  sceneConditions: string;
  tenantId: string;
  deptId: number;
  key: string;
  value: string;
  createdAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'alarmLogId', label: '报警记录ID' },
  { field: 'items.alarmConfigId', label: '报警配置ID' },
  { field: 'sceneId', label: '场景ID' },
  { field: 'productName', label: '报警产品' },
  { field: 'deviceName', label: '报警设备' },
  { field: 'items.alarmConfigName', label: '报警名称' },

  { field: 'items.alarmLevel', label: '报警级别' },
  { field: 'startTime', label: '开始时间' },
  { field: 'endTime', label: '结束时间' },
  { field: 'confirmState', label: '处理状态' },
  { field: 'confirmContent', label: '处理结果' },
  { field: 'confirmTime', label: '处理时间' },
  { field: 'confirmUser', label: '处理用户ID' },
  { field: 'confirmUserType', label: '报警处理用户' },
  { field: 'sceneConditions', label: '场景条件JSON' },
  { field: 'deptId', label: '所属机构' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'alarmLogId',
    component: 'Input',
    label: '报警记录ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'confirmState',
    component: 'Select',
    label: '处理状态',
    componentProps: {
      placeholder: '请输入处理状态（1=确认报警 2=躁扰报警 3=测试报警）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: getDictOptions(DictEnum.ALARM_CONFIRM_STATE),

    },
    disabled: true,
  },
  {
    fieldName: 'confirmContent',
    component: 'Textarea',
    label: '处理结果',
    componentProps: {
      placeholder: '请输入处理结果',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
]
