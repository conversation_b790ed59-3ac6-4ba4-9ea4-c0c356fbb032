<script lang="ts" setup>
import type { NotificationItem } from '@vben/layouts';

import { computed, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationLoginExpiredModal, useVbenModal } from '@vben/common-ui';
import { VBEN_DOC_URL, VBEN_GITHUB_URL } from '@vben/constants';
import { useWatermark } from '@vben/hooks';
import { BookOpenText, CircleHelp, MdiGithub, UserOutlined } from '@vben/icons';
import {
  BasicLayout,
  LockScreen,
  Notification,
  UserDropdown,
} from '@vben/layouts';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';
import { openWindow } from '@vben/utils';

import { $t } from '#/locales';
import { useAuthStore } from '#/store';
import LoginForm from '#/views/_core/authentication/login.vue';
import { websocketGlobal } from '#/websocketutil';
import { List, Read,Delete } from '#/api/system/sysNoticeUser';

const messageList = ref<NotificationItem[]>([])
onMounted(async () => {
  const res = await List({ page: 1, pageSize: 10, isRead: 0 })
  if (res.items.length > 0) {
    res.items.forEach((item: any) => {
      const notice: NotificationItem = {
        id: item.id,
        avatar: item.createdBySumma.avatar,
        date: item.createdAt,
        isRead: item.isRead,
        message: item.noticeContent,
        title: item.noticeTitle
      }
      messageList.value.push(notice)
    });
  }
  console.log(messageList.value)
})
const [Modal, modalApi] = useVbenModal({
  showConfirmButton: false, cancelText: "确定"
});
const activeItem = ref<NotificationItem>()
const notifications = ref<NotificationItem[]>(messageList.value);
/** 清空 */
function noticeClear() {
  const ids:any[]=[]
  if (messageList.value.length > 0) {
    messageList.value.forEach(item => { item.isRead = true;ids.push(item.id) })
  }
  Delete({ ids })
}
/** 全部已读 */
function noticeMakeAll() {
  const ids:any[]=[]
  if (messageList.value.length > 0) {
    messageList.value.forEach(item => { item.isRead = true;ids.push(item.id) })
  }
  Read({ ids })
}
/** 阅读 */
function noticeRead(item: NotificationItem) {
  activeItem.value = item
  item.isRead = true
  Read({ ids: [item.id] })
  modalApi.open()
}
/** 查看全部 */
function noticeViewAll() {
router.push('/system/sysNoticeUser')
}


const userStore = useUserStore();
const authStore = useAuthStore();
const accessStore = useAccessStore();
const router = useRouter();
const { destroyWatermark, updateWatermark } = useWatermark();
const showDot = computed(() =>
  notifications.value.some((item) => !item.isRead),
);

const menus = computed(() => [
  {
    handler: () => {
      router.push('/profile');
    },
    icon: UserOutlined,
    text: $t('ui.widgets.profile'),
  },
  {
    handler: () => {
      openWindow("https://doc.xiujiezhilian.com/", {
        target: '_blank',
      });
    },
    icon: BookOpenText,
    text: $t('ui.widgets.document'),
  },
  {
    handler: () => {
      openWindow("https://github.com/xiujiecn/jie-iot", {
        target: '_blank',
      });
    },
    icon: MdiGithub,
    text: 'GitHub',
  },
  // {
  //   handler: () => {
  //     openWindow(`${VBEN_GITHUB_URL}/issues`, {
  //       target: '_blank',
  //     });
  //   },
  //   icon: CircleHelp,
  //   text: $t('ui.widgets.qa'),
  // },
]);

const avatar = computed(() => {
  return userStore.userInfo?.avatar ?? preferences.app.defaultAvatar;
});

async function handleLogout() {
  await authStore.logout(false);
  websocketGlobal.closeWebSocket();
}

function handleNoticeClear() {
  notifications.value = [];
}

function handleMakeAll() {
  notifications.value.forEach((item) => (item.isRead = true));
}
watch(
  () => preferences.app.watermark,
  async (enable) => {
    if (enable) {
      await updateWatermark({
        content: `${userStore.userInfo?.username}`,
      });
    } else {
      destroyWatermark();
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <BasicLayout @clear-preferences-and-logout="handleLogout">
    <template #user-dropdown>
      <UserDropdown :avatar :menus :text="userStore.userInfo?.realName" :description="userStore.userInfo?.email"
        tag-text="Pro" @logout="handleLogout" />
    </template>
    <template #notification>
      <Notification :dot="showDot" :notifications="notifications" @clear="noticeClear" @makeAll="noticeMakeAll"
        @read="noticeRead" @viewAll="noticeViewAll" />
    </template>
    <template #extra>
      <AuthenticationLoginExpiredModal v-model:open="accessStore.loginExpired" :avatar>
        <LoginForm />
      </AuthenticationLoginExpiredModal>
    </template>
    <template #lock-screen>
      <LockScreen :avatar @to-login="handleLogout" />
    </template>
  </BasicLayout>

  <Modal :centered="true" class="w-[600px]" :title="activeItem?.title">
    <span style="width: 100%;word-break:break-all;">{{ activeItem?.message }}</span>
  </Modal>
</template>
