import type { VbenFormSchema } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

// 设备选择按钮插槽
export const actionsFormSchema: VbenFormSchema[] = [
  {
    fieldName: 'id',
    label: 'id',
    component: 'Input',
    formItemClass: 'col-span-2', // 独占一行
    componentProps: {
      placeholder: '请输入动作名称',
      style: { width: '320px' }, // 设置宽度
    },
    dependencies: {
      show: () => false,
      triggerFields: ['id'],
    }
  },
  {
    fieldName: 'productName',
    label: 'productName',
    component: 'Input',
    formItemClass: 'col-span-2', // 独占一行
    componentProps: {
      style: { width: '320px' }, // 设置宽度
    },
    dependencies: {
      show: () => false,
      triggerFields: ['id'],
    }
  },
  {
    fieldName: 'deviceKeys',
    label: 'deviceKeys',
    component: 'Input',
    formItemClass: 'col-span-2', // 独占一行
    componentProps: {
      style: { width: '320px' }, // 设置宽度
    },
    dependencies: {
      show: () => false, // 只有id为空时显示
      triggerFields: ['id'],
    }
  },
  {
    fieldName: 'source',
    label: '动作范围',
    component: 'RadioGroup',
    formItemClass: 'col-span-2', // 独占一行
    componentProps: {
      buttonStyle: 'solid',
      optionType: 'button',
      options: getDictOptions(DictEnum.SCENE_SCRIPT_SOURCE), // 1=设备执行 2=产品执行
      style: { width: '320px' }, // 设置宽度
      // 默认选中产品执行
    },
    defaultValue: '2', // 默认选中产品执行
  },
  {
    fieldName: 'productKey',
    label: '产品名称',
    component: 'Select',
    formItemClass: 'col-span-1', // 占一半
    componentProps: {
      placeholder: '请选择产品',
      options: [],
      showSearch: true,
      allowClear: true,
      style: { width: '320px' }, // 设置宽度
    },
  },
  {
    fieldName: 'deviceNames',
    label: '设备名称',
    component: 'Input',
    formItemClass: 'col-span-1', // 占一半
    componentProps: {
      placeholder: '请选择设备',

      style: { width: '320px' }, // 设置宽度
    },
    dependencies: {
      show: (values) => values.source === '1', // 只有source为1时显示
      triggerFields: ['source'],
    }
  },

];
