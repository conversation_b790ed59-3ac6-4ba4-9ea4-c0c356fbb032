import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import dayjs from 'dayjs';
import { z } from '@vben/common-ui';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';

export class State {
  public detailId = 0; // ID
  public batchId = 0; // 批量任务
  public type = null; // 操作类型
  public productKey = ''; // 产品标识
  public deviceKey = ''; // 设备标识
  public sourceDept = 0; // 原机构
  public targetDept = 0; // 接收机构
  public projectId = 0; // 项目ID
  public result = null; // 处理结果
  public tenantId = ''; // 租户ID
  public createdAt = ''; // 创建时间
  public createdBy = ''; // 创建者
  public updatedAt = ''; // 更新时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'type',
    component: 'Select',
    label: '操作类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择操作类型',
      options: getDictOptions('batch_operation_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  },
  //加一个处理结果 result字段
  {
    fieldName: 'result',
    component: 'Select',
    label: '处理结果',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择处理结果',
      options: getDictOptions('batch_detail_result'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'batchId',
    component: 'Input',
    label: '任务ID',
    componentProps: {
      placeholder: '请输入任务ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  },
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'datetimerange',
      clearable: true,
      valueFormat: 'FMDateRange',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    }, rules: null,
    formItemClass: 'col-span-1',
  },];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    title: '编码',
    field: 'detailId',
    align: 'center',
    width: 80,
  },
  {
    title: '批量任务',
    field: 'batchId',
    align: 'center',
    width: 100,
  },
  {
    title: '操作类型', field: 'type', align: 'center', width: -1,
    slots: {
      default: ({ row }) => {
        return renderDict(row.type, 'batch_operation_type');
      }
    },
  },
  {
    title: '产品标识',
    field: 'productKey',
    align: 'left',
    width: -1,
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
  },
  {
    title: '原机构',
    field: 'sourceDept',
    align: 'left',
    width: -1,
  },
  {
    title: '接收机构/项目',
    field: 'targetDept',
    align: 'left',
    width: -1,
  },
  {
    title: '处理结果',
    field: 'result',
    align: 'left',
    width: -1,
    slots: { default: 'result' }
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: -1,
  },
];

// 表格列接口
export interface RowType {
  detailId: number;
  batchId: number;
  type: number;
  productKey: string;
  deviceKey: string;
  sourceDept: number;
  targetDept: number;
  projectId: number;
  result: number;
  tenantId: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'detailId', label: 'ID' },
  { field: 'batchId', label: '批量任务' },
  {
    field: 'type',
    label: '操作类型',
    render(row: any) {
      return renderDict(row.type, 'batch_operation_type');
    },
  },
  { field: 'productKey', label: '产品标识' },
  { field: 'deviceKey', label: '设备标识' },
  { field: 'sourceDept', label: '原机构' },
  { field: 'targetDept', label: '接收机构' },
  { field: 'projectId', label: '项目ID' },
  {
    field: 'result',
    label: '处理结果',
    render(row: any) {
      return renderDict(row.result, 'batch_detail_result');
    },
  },
  { field: 'tenantId', label: '租户ID' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'createdBy', label: '创建者' },
  { field: 'updatedAt', label: '更新时间' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'detailId',
    component: 'Input',
    label: 'ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'batchId',
    component: 'InputNumber',
    label: '批量任务',
    componentProps: {
      placeholder: '请输入批量任务',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    }, rules: z.number({ required_error: '请输入批量任务', invalid_type_error: '无效数字' }),
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'type',
    component: 'Select',
    label: '操作类型',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择操作类型',
      options: getDictOptions('batch_operation_type'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired',
    formItemClass: 'col-span-1',
  },
  {
    fieldName: 'productKey',
    component: 'Input',
    label: '产品标识',
    componentProps: {
      placeholder: '请输入产品标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required',
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'sourceDept',
    component: 'InputNumber',
    label: '原机构',
    componentProps: {
      placeholder: '请输入原机构',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    }, rules: z.number({ required_error: '请输入原机构', invalid_type_error: '无效数字' }),
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'targetDept',
    component: 'InputNumber',
    label: '接收机构',
    componentProps: {
      placeholder: '请输入接收机构',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    }, rules: null,
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'projectId',
    component: 'InputNumber',
    label: '项目ID',
    componentProps: {
      placeholder: '请输入项目ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    }, rules: null,
    formItemClass: 'col-span-1',
  }, {
    fieldName: 'result',
    component: 'Select',
    label: '处理结果',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择处理结果',
      options: getDictOptions('batch_detail_result'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    formItemClass: 'col-span-1',
  },
  {
    fieldName: 'tenantId',
    component: 'Input',
    label: '租户ID',
    componentProps: {
      placeholder: '请输入租户ID',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null,
    formItemClass: 'col-span-1',
  },];
