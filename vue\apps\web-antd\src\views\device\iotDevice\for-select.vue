<script lang="ts" setup>
import { h, reactive, ref, computed, onMounted } from 'vue';
import type { VbenFormProps } from '#/adapter/form';
import type {
    VxeTableGridOptions,
    VxeGridListeners,
} from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { List } from '#/api/device/iotDevice';
import { forSelectColumns, type RowType } from './model';
import { ListNoPage as ListProduct } from '#/api/device/iotProduct';


const emit = defineEmits(['confirm', 'reload']);
type Option = {
    label: string;
    value: string;
};

const productOptions = ref<Option[]>([]);
async function loadProductOptions() {
    const res = await ListProduct({
        page: 1,
        pageSize: 1000,
    });
    if(!res || !res.items){
        productOptions.value = [];
    }else{
        productOptions.value = res.items.map((item: any) => ({
            label: item.productName,
            value: item.productKey,
        }));
    }
    console.log(productOptions.value);
    gridApi.formApi.updateSchema([
        {
            fieldName: 'productKey',
            component: 'Select',
            label: '所属产品',
            componentProps: {
                placeholder: '请选择产品',
                onUpdateValue: (e: any) => {
                    console.log(e);
                },
                options: productOptions.value,
                showSearch: true,
                filterOption: (input: any, option: any) => {
                    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                },
            },
        },
    ]);
}

const [BasicModal, modalApi] = useVbenModal({
    onCancel: handleCancel,
    onConfirm: handleConfirm,
    async onOpenChange(isOpen) {
        if (!isOpen) {
            return null;
        }
        modalApi.setState({ confirmLoading: true, loading: true });
        await loadProductOptions();
        modalApi.setState({ confirmLoading: false, loading: false });
        modalApi.setState({ showConfirmButton: true });
    }
});

function handleCancel() {
    modalApi.close();
}

function handleConfirm() {
    const checkedDeviceKeys = gridApi.grid.getCheckboxRecords().map((item: any) => item.deviceKey);
    emit('confirm', checkedDeviceKeys);
    emit('reload');
    modalApi.close();
}

const formOptions: VbenFormProps = {
    // 默认展开
    collapsed: false,
    fieldMappingTime: [['date', ['start', 'end']]],
    schema: [
        {
            fieldName: 'productKey',
            component: 'Select',
            label: '所属产品',
        },
        {
            fieldName: 'deviceKey',
            component: 'Input',
            label: '设备标识',
            componentProps: {
                placeholder: '请输入设备标识',
                onUpdateValue: (e: any) => {
                    console.log(e);
                },
            },
        },],
    // 控制表单是否显示折叠按钮
    showCollapseButton: true,
    // 是否在字段值改变时提交表单
    submitOnChange: true,
    // 按下回车时是否提交表单
    submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
    checkboxConfig: {
        highlight: true,
        // labelField: 'deviceId',
    },
    rowConfig: {
        keyField: 'deviceId',
    },
    columns: forSelectColumns,
    exportConfig: {},
    height: 'auto',
    keepSource: true,
    showOverflow: false,
    pagerConfig: {},
    proxyConfig: {
        ajax: {
            query: async ({ page }, formValues) => {
                let res = await List({
                    page: page.currentPage,
                    pageSize: page.pageSize,
                    ...formValues,
                });
                res.items.forEach((item: any) => {
                    item.productName =
                        productOptions.value.find((i: any) => item.productKey === i.value)
                            ?.label || '';
                });
                return res;
            },
        },
    },
    toolbarConfig: {
        custom: false,
        export: false,
        refresh: false,
        resizable: false,
        search: false,
        zoom: false,
    },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
    checkboxChange: handleCheckboxChange,
    checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
    CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
    formOptions,
    gridOptions,
    gridEvents,
});

</script>
<template>
    <BasicModal :close-on-click-modal="false" title="选择设备" class="w-[800px] h-[800px]">
        <Grid>
            <template #toolbar-tools>
            </template>
        </Grid>
    </BasicModal>
</template>
