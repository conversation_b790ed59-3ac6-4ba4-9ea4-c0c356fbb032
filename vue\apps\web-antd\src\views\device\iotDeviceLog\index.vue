<script lang="ts" setup>
import { h, reactive, ref, computed, onMounted } from 'vue';
import { Button,} from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { IconifyIcon } from '@vben/icons';
import { commonDownloadExcel } from '#/utils/file/download';
import { List } from '#/api/device/iotDeviceLog';
import { columns, querySchema, type RowType } from './model';
import viewModal from './view.vue';
import dayjs from 'dayjs';
const Props = defineProps<{
  deviceKey: string;
  tenantId: string;
  fromCache: boolean;
}>();


const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['createdAt', ['startTime', 'endTime'], (value) => {
    return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
  }]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  handleReset: async () => {
    await gridApi.formApi.setValues({msgType: '', createdAt: [dayjs().subtract(1, 'hour'), dayjs()]});
    await gridApi.query();
  },
};
const gridOptions: VxeTableGridOptions<RowType> = {
  rowConfig: {
    keyField: 'deviceLogId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        console.log('grid Query', formValues);
        let res = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
          deviceKey: Props.deviceKey,
          fromCache: Props.fromCache,
          tenantId: Props.tenantId,
        });
        if(res.items.length > 0){
          res.items.forEach((item: any) => {
            try {
              const contentObj = JSON.parse(item.content);
              item.contentObj = contentObj;
            } catch (error) {
              item.contentObj = {
              };
            }
          });
        }
        return res;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
  //添加行点击触发事件
  cellClick({ row }: { row: RowType }) {
    handlePreview(row);
  },
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  gridClass: 'p-0 mt-0',
  formOptions: formOptions,
  gridOptions: gridOptions,
  gridEvents: gridEvents,
  hideSeparator: true,
});
const [ViewModal, modalApi] = useVbenModal({
  connectedComponent: viewModal,
});
function handlePreview(record: RowType) {
  modalApi.setData({ record });
  modalApi.open();
}

async function handleRefresh() {
  await gridApi.query();
}

onMounted(async () => {
  await gridApi.formApi.setValues({createdAt: [dayjs().subtract(1, 'day'), dayjs()]});
});

</script>
<template class="p-0 m-0">
  <div class="p-0 m-0 h-[800px]" >
    <Grid>
      <template #action="{ row }">
        <div class="flex items-center justify-center">
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handlePreview(row)"
            v-access:code="'cpm:device:iotDeviceLog:view'">
            详情
          </Button>
        </div>
      </template>
    </Grid>
    <ViewModal />
  </div>
</template>
