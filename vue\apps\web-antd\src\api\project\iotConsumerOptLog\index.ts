import { requestClient } from '#/api/request';

// 获取终端用户设备操作记录表列表
export function List(params:any) {
  return requestClient.get<any>('project/iotConsumerOptLog/list', { params });
}

// 删除/批量删除终端用户设备操作记录表
export function Delete(params:any) {
  return requestClient.post<any>('project/iotConsumerOptLog/delete', { ...params });
}

// 添加/编辑终端用户设备操作记录表
export function Edit(params:any) {
  return requestClient.post<any>('project/iotConsumerOptLog/edit', { ...params });
}

// 获取终端用户设备操作记录表指定详情
export function View(params:any) {
  return requestClient.get<any>('project/iotConsumerOptLog/view', { params });
}

// 导出终端用户设备操作记录表
export function Export(params:any) {
  return requestClient.post<Blob>('/project/iotConsumerOptLog/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}