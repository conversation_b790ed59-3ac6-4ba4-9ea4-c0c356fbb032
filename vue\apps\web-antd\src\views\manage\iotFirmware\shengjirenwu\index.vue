<script lang="ts" setup>
import { h, reactive, ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { Button, message, Tag, Modal, Popconfirm, Switch, Input } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenDrawer } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, Delete, View } from '#/api/manage/iotUpgradeTask';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import editDrawer from './edit.vue';
import ViewModal from './view.vue';



// 定义 props 接收父组件传递的固件信息
const props = defineProps<{
  firmwareId?: string;
  firmwareName?: string;
  productKey?: string;
  productName?: string;
  firmwareVersion?: string;
}>();

const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'taskId',
  },
  rowConfig: {
    keyField: 'taskId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        // 合并表单查询参数和固件相关参数
        const queryParams = {
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        };

        // 如果有搜索关键词，添加到查询条件中（模糊搜索）
        if (searchValue.value?.trim()) {
          queryParams.taskName = searchValue.value.trim();
          console.log('🔍 添加模糊搜索条件: taskName =', queryParams.taskName);
        }

        // 如果有固件相关参数，添加到查询条件中
        if (props.firmwareId) {
          queryParams.firmwareId = props.firmwareId;
        }
        if (props.productKey) {
          queryParams.productKey = props.productKey;
        }

        console.log('🔍 升级任务表查询参数:', queryParams);
        console.log('🔍 接收到的固件参数:', {
          firmwareId: props.firmwareId,
          firmwareName: props.firmwareName,
          productKey: props.productKey,
          productName: props.productName,
          firmwareVersion: props.firmwareVersion,
        });

        const result = await List(queryParams);
        console.log('🔍 查询结果:', result);
        console.log('🔍 查询到的记录数:', result?.items?.length || 0);

        if (result?.items?.length === 0) {
          console.warn('⚠️ 查询结果为空，可能的原因:');
          console.warn('1. 数据确实被删除了');
          console.warn('2. 查询条件不匹配');
          console.warn('3. 编辑后的数据 firmwareId 或 productKey 发生了变化');
        }

        return result;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: false, // 隐藏原搜索功能
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}

// 搜索框相关
const searchValue = ref('');

// 模糊搜索处理函数
function handleSearch() {
  const keyword = searchValue.value?.trim();
  console.log('🔍 执行模糊搜索，关键词:', keyword);

  if (keyword) {
    console.log('🔍 搜索条件: 任务名称包含 "' + keyword + '"');
  } else {
    console.log('🔍 搜索条件: 显示所有数据');
  }

  // 触发表格查询，搜索参数会在query函数中自动获取
  gridApi.query();
}

// 清空搜索
function handleClearSearch() {
  searchValue.value = '';
  console.log('🔍 清空搜索，显示所有数据');
  // 重新查询
  gridApi.query();
}

// 实时搜索 - 当输入内容变化时自动搜索
function handleInputChange() {
  // 防抖处理，避免频繁请求
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
  searchTimeout.value = setTimeout(() => {
    handleSearch();
  }, 500); // 500ms后执行搜索
}

// 搜索防抖定时器
const searchTimeout = ref<ReturnType<typeof setTimeout> | null>(null);
const [Grid, gridApi] = useVbenVxeGrid({
  //formOptions,
  gridClass: 'p-0 ',
  gridOptions,
  gridEvents,
});
// 查看弹窗引用
const refViewModal = ref();

function handlePreview(record: RowType) {
  console.log('🔍 点击查看按钮，任务记录:', record);

  if (refViewModal.value) {
    console.log('🔍 查看弹窗组件引用存在');
    try {
      // 直接传递记录并打开弹窗
      refViewModal.value.openModal(record);
      console.log('🔍 查看弹窗打开成功');
    } catch (error) {
      console.error('🔍 打开查看弹窗失败:', error);
    }
  } else {
    console.error('🔍 查看弹窗组件引用不存在');
  }
}
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});
function handleAdd() {
  console.log('🆕 点击新增按钮');
  console.log('🆕 父组件传递的固件参数:', {
    firmwareId: props.firmwareId,
    firmwareName: props.firmwareName,
    productKey: props.productKey,
    productName: props.productName,
    firmwareVersion: props.firmwareVersion,
  });

  // 传递固件相关参数到新增弹窗
  editDrawerApi.setData({
    update: false,
    view: false,
    firmwareParams: {
      firmwareId: props.firmwareId,
      firmwareName: props.firmwareName,
      productKey: props.productKey,
      productName: props.productName,
      firmwareVersion: props.firmwareVersion,
    }
  });

  console.log('🆕 传递给编辑弹窗的数据:', {
    update: false,
    view: false,
    firmwareParams: {
      firmwareId: props.firmwareId,
      firmwareName: props.firmwareName,
      productKey: props.productKey,
      productName: props.productName,
      firmwareVersion: props.firmwareVersion,
    }
  });

  editDrawerApi.open();
}
async function handleEdit(row: RowType) {
  console.log('🔧 编辑任务，行数据:', row);

  try {
    // 通过任务ID获取完整的任务信息
    console.log('🔍 通过任务ID获取完整信息:', row.taskId);
    const taskDetail = await View({ taskId: row.taskId });
    console.log('🔍 获取到的完整任务信息:', taskDetail);

    // 传递完整的任务信息到编辑弹窗
    editDrawerApi.setData({
      id: row.taskId,
      update: true,
      view: false,
      firmwareParams: {
        firmwareId: taskDetail.firmwareId || props.firmwareId,
        firmwareName: taskDetail.firmwareName || props.firmwareName,
        productKey: taskDetail.productKey || props.productKey,
        productName: taskDetail.productName || props.productName,
        firmwareVersion: taskDetail.firmwareVersion || props.firmwareVersion,
      }
    });

    console.log('🔧 传递给编辑弹窗的固件参数:', {
      firmwareId: taskDetail.firmwareId || props.firmwareId,
      firmwareName: taskDetail.firmwareName || props.firmwareName,
      productKey: taskDetail.productKey || props.productKey,
      productName: taskDetail.productName || props.productName,
      firmwareVersion: taskDetail.firmwareVersion || props.firmwareVersion,
    });

    editDrawerApi.open();
  } catch (error) {
    console.error('❌ 获取任务详情失败:', error);
    // 如果获取失败，使用原来的方式
    editDrawerApi.setData({
      id: row.taskId,
      update: true,
      view: false,
      firmwareParams: {
        firmwareId: props.firmwareId,
        firmwareName: props.firmwareName,
        productKey: props.productKey,
        productName: props.productName,
        firmwareVersion: props.firmwareVersion,
      }
    });
    editDrawerApi.open();
  }
}
async function handleDelete(row: RowType) {
  await Delete({ taskId: [row.taskId] });
  message.success("删除成功");
  await handleRefresh();
}
async function handleRefresh() {
  await gridApi.query();
}
function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const ids: string[] = [];
  for (const row of rows) {
    ids.push(row.taskId);
  }
  if (ids.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await Delete({ taskId: ids });
      message.success("删除成功");
      await handleRefresh();
    },
  });
}
async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '升级任务表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success("导出成功");
}

// 组件挂载时预加载字典
onMounted(async () => {
  console.log('🚀 升级任务表组件已挂载');
  console.log('🚀 接收到的固件参数:', {
    firmwareId: props.firmwareId,
    firmwareName: props.firmwareName,
    productKey: props.productKey,
    productName: props.productName,
    firmwareVersion: props.firmwareVersion,
  });

  // 检查参数是否为空
  if (!props.firmwareId) {
    console.warn('⚠️ 固件参数为空，可能是父组件还未加载完成');
    // 延迟检查
    setTimeout(() => {
      console.log('🚀 延迟检查固件参数:', {
        firmwareId: props.firmwareId,
        firmwareName: props.firmwareName,
        productKey: props.productKey,
        productName: props.productName,
        firmwareVersion: props.firmwareVersion,
      });
    }, 1000);
  }

  // 强制加载字典数据
  try {
    const { getDictOptions } = await import('#/utils/dict');
    console.log('🚀 开始预加载字典数据...');

    // 多次尝试获取字典数据
    for (let i = 0; i < 5; i++) {
      const dictOptions = getDictOptions('upgrade_task_type');
      console.log(`🚀 第${i + 1}次获取字典数据:`, dictOptions);

      if (dictOptions.length > 0) {
        console.log('🚀 字典数据加载成功:', dictOptions);
        break;
      }

      // 等待一段时间再重试
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  } catch (error) {
    console.error('🚀 预加载字典数据失败:', error);
  }
});

// 组件销毁时清理定时器
onUnmounted(() => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
    searchTimeout.value = null;
  }
});

</script>
<template>
  <div class="p-0 m-0 ">
    <Grid >
      <template #toolbar-tools>
        <!-- 搜索框 - 水平居左，支持模糊搜索 -->
        <div class="flex items-center mr-4">
          <Input.Search
            v-model:value="searchValue"
            placeholder="请输入任务名称"
            style="width: 280px"
            @search="handleSearch"
            @pressEnter="handleSearch"
            @input="handleInputChange"
            allowClear
            @clear="handleClearSearch"
            :loading="false"
          />
         
        </div>

        <!-- 操作按钮 -->
        <Button class="mr-2 flex items-center " type="primary" :icon="h(MdiPlus)" @click="handleAdd"
          v-access:code="'cpm:manage:iotUpgradeTask:edit'">
          新增
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :disabled="!CheckboxChecked" :icon="h(MdiDelete)"
          @click="handleMultiDelete" v-access:code="'cpm:manage:iotUpgradeTask:delete'">
          删除
        </Button>
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiExport)" @click="handleExport"
          v-access:code="'cpm:manage:iotUpgradeTask:export'">
          导出
        </Button>
      </template>
      <template #action="{ row }">
        <div class="flex items-center justify-center gap-2">
          <Button class="border-none p-0" :block="false" type="link" @click="handlePreview(row)"
            v-access:code="'cpm:manage:iotUpgradeTask:view'">
            查看
          </Button>
          <Button class="border-none p-0" :block="false" type="link" @click="handleEdit(row)"
            v-access:code="'cpm:manage:iotUpgradeTask:edit'">
            修改
          </Button>
          <AccessControl :codes="['cpm:manage:iotUpgradeTask:delete']" type="code">
            <Popconfirm title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
    <EditDrawer @reload="handleRefresh" />
    <ViewModal ref="refViewModal" />
  </div>
</template>