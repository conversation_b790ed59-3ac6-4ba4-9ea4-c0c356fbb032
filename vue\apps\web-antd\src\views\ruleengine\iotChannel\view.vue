<script setup lang="ts">
import type { SysClient } from '#/api/system/client';
import { computed, nextTick, ref } from 'vue';
import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { addFullName, cloneDeep } from '@vben/utils';
import { useVbenForm } from '#/adapter/form';
import { Description, useDescription } from '#/components/description';
import { viewSchema, type RowType } from './model';
import { View } from '#/api/ruleengine/iotChannel';
import type { DescItem } from '#/components/description';
import { DictEnum } from '@vben/constants';
import { renderDict } from '#/utils';
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onOpenChange: handleOpenChange,
});
const [registerDescription, { setDescProps }] = useDescription({
  column: 1,
  schema: viewSchema,
});
async function handleOpenChange(open: boolean) {
  if (!open) {
    return null;
  }
  const { record } = drawerApi.getData() as { record: RowType };
  const record2 = await View({ channelId: record.channelId });
  let currentSchema = cloneDeep(viewSchema);
  //Http桥接出口参数
  if (record2.type == '4' && record2.direction == '2') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'httpOutput.method',
        label: '输出方法',
        render(_, data) {
          return data.httpOutput.method;
        },
      },
      {
        field: 'httpOutput.url',
        label: '请求地址',
        render(_, data) {
          return data.httpOutput.url;
        },
      },
      {
        field: 'httpOutput.headers',
        label: '请求头',
        render(_, data) {
          return data.httpOutput.headers
            .map((item: any) => {
              return item.key + ': ' + item.value;
            })
            .join(', ');
        },
      },
      {
        field: 'httpOutput.params',
        label: '请求参数',
        render(_, data) {
          return data.httpOutput.params
            .map((item: any) => {
              return item.key + ': ' + item.value;
            })
            .join(', ');
        },
      },
      {
        field: 'httpOutput.config',
        label: '请求配置',
        render(_, data) {
          return data.httpOutput.config
            .map((item: any) => {
              return item.key + ': ' + item.value;
            })
            .join(', ');
        },
      },
      {
        field: 'httpOutput.body',
        label: '请求体',
        render(_, data) {
          return data.httpOutput.body;
        },
      },
    ];
  }

  //Http桥接入口参数
  if (record2.type == '4' && record2.direction == '1') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'httpInput.api',
        label: '输出方法',
        render(_, data) {
          return data.httpInput.api;
        },
      },
    ];
  }

  //MQTT服务器参数查看
  if (record2.type == '1') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'mqtt.ip',
        label: '服务器地址',
        render(_, data) {
          return data.mqtt.ip;
        },
      },
      {
        field: 'mqtt.port',
        label: '服务器端口',
        render(_, data) {
          return data.mqtt.port;
        },
      },
      {
        field: 'mqtt.clientId',
        label: '客户端ID',
        render(_, data) {
          return data.mqtt.clientId;
        },
      },
      {
        field: 'mqtt.user',
        label: '用户名',
        render(_, data) {
          return data.mqtt.user;
        },
      },
      {
        field: 'mqtt.pwd',
        label: '密码',
        render(_, data) {
          return data.mqtt.pwd;
        },
      },
      {
        field: 'mqtt.topic',
        label: '主题',
        render(_, data) {
          return data.mqtt.topic;
        },
      },
      {
        field: 'mqtt.deviceMatchType',
        label: '设备匹配类型',
        render(_, data) {
          return renderDict(data.mqtt.deviceMatchType, DictEnum.DEVICE_MATCH_TYPE);
        },
      },
    ];
  }

  //tcp或udp桥接参数查看
  if (record2.type == '2' || record2.type == '3') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'tcpUdp.ip',
        label: '连接地址',
        render(_, data) {
          return data.tcpUdp.ip;
        },
      },
      {
        field: 'tcpUdp.port',
        label: '连接端口',
        render(_, data) {
          return data.tcpUdp.port;
        },
      },
      {
        field: 'tcpUdp.user',
        label: '用户名',
        render(_, data) {
          return data.tcpUdp.user;
        },
      },
      {
        field: 'tcpUdp.pwd',
        label: '密码',
        render(_, data) {
          return data.tcpUdp.pwd;
        },
      },
    ];
  }

  //TCP/UDP服务器参数查看
  if (record2.type == '2' || record2.type == '3') {
    // 添加登录包类型和分包类型
    currentSchema = [
      ...currentSchema,
      {
        field: 'tcpUdp.loginType',
        label: '登录包类型',
        render(_, data) {
          const loginType = data.tcpUdp?.loginType;
          if (loginType === 0) return '系统内置';
          if (loginType === 1) return '云则空开';
          return loginType || '';
        },
      },
      {
        field: 'tcpUdp.frameType',
        label: '分包类型',
        render(_, data) {
          return renderDict(data.tcpUdp?.frameType, DictEnum.FRAME_TYPE);
        },
      },
    ];

    // 根据分包类型添加对应字段
    const frameType = record2.tcpUdp?.frameType;

    if (frameType === 'FixedLength') {
      currentSchema.push({
        field: 'tcpUdp.frameFrameLength',
        label: '固定长度',
        render(_, data) {
          return data.tcpUdp?.frameFrameLength || '';
        },
      });
    }

    if (frameType === 'DelimiterBased' || frameType === 'LengthFieldBased') {
      currentSchema.push({
        field: 'tcpUdp.frameMaxFrameLength',
        label: '最大长度',
        render(_, data) {
          return data.tcpUdp?.frameMaxFrameLength || '';
        },
      });
    }

    if (frameType === 'DelimiterBased') {
      currentSchema.push(
        {
          field: 'tcpUdp.frameStripDelimiter',
          label: '去掉分隔符本身',
          render(_, data) {
            const stripDelimiter = data.tcpUdp?.frameStripDelimiter;
            if (stripDelimiter === 0) return '否';
            if (stripDelimiter === 1) return '是';
            return stripDelimiter || '';
          },
        },
        {
          field: 'tcpUdp.frameDelimiters',
          label: '分隔符HEX编码',
          render(_, data) {
            return data.tcpUdp?.frameDelimiters || '';
          },
        }
      );
    }

    if (frameType === 'LengthFieldBased') {
      currentSchema.push(
        {
          field: 'tcpUdp.frameByteOrder',
          label: '长度字段的字节顺序',
          render(_, data) {
            return renderDict(data.tcpUdp?.frameByteOrder, DictEnum.FRAME_BYTE_ORDER);
          },
        },
        {
          field: 'tcpUdp.frameLengthFieldOffset',
          label: '长度字段偏移量',
          render(_, data) {
            return data.tcpUdp?.frameLengthFieldOffset || '';
          },
        },
        {
          field: 'tcpUdp.frameLengthFieldLength',
          label: '长度字段字节数',
          render(_, data) {
            return data.tcpUdp?.frameLengthFieldLength || '';
          },
        },
        {
          field: 'tcpUdp.frameLengthAdjustment',
          label: '长度调整值',
          render(_, data) {
            return data.tcpUdp?.frameLengthAdjustment || '';
          },
        },
        {
          field: 'tcpUdp.frameInitialBytesToStrip',
          label: '丢弃字节数',
          render(_, data) {
            return data.tcpUdp?.frameInitialBytesToStrip || '';
          },
        }
      );
    }
  }

  //Websocket桥接参数查看
  if (record2.type == '5') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'ws.url',
        label: '连接地址',
        render(_, data) {
          return data.ws.url;
        },
      },
      {
        field: 'ws.token',
        label: '认证Token',
        render(_, data) {
          return data.ws.token;
        },
      },
      {
        field: 'tcpUdp.tag',
        label: '订阅标签',
        render(_, data) {
          return data.ws.tag;
        },
      },
    ];
  }

  //数据库参数查看
  if (record2.type == '6') {
    currentSchema = [
      ...currentSchema,
      {
        field: 'database.type',
        label: '数据库类型',
        render(_, data) {
          return data.database.type;
          // return renderDict(data.database.type, DictEnum.CHANNEL_DIRECTION);
        },
      },
      {
        field: 'database.source',
        label: '数据源',
        render(_, data) {
          return data.database.source;
        },
      },
      {
        field: 'database.address',
        label: '数据库地址',
        render(_, data) {
          return data.database.address;
        },
      },
      {
        field: 'database.user',
        label: '用户名',
        render(_, data) {
          return data.database.user;
        },
      },
      {
        field: 'database.schema',
        label: '数据库名称',
        render(_, data) {
          return data.database.schema;
        },
      },
      {
        field: 'database.sql',
        label: 'SQL语句',
        render(_, data) {
          return data.database.sql;
        },
      },
    ];
  }
  // setDescProps({ data: record2 }, true);
  setDescProps({ data: record2, schema: currentSchema }, true);
}
</script>
<template>
  <BasicDrawer :footer="false" class="w-[600px]" title="查看">
    <Description @register="registerDescription"></Description>
  </BasicDrawer>
</template>
