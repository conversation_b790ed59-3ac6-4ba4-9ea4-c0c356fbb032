<script setup lang="ts">
import type { SysClient } from '#/api/system/client';
import { computed, h, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { useVbenVxeGrid, type VxeTableGridOptions } from '#/adapter/vxe-table';
import { useVbenModal, useVbenForm } from '@vben/common-ui';
import { Tabs, TabPane, RadioButton, RangePicker, RadioGroup, Button, message } from 'ant-design-vue';
import { useEcharts, EchartsUI } from '@vben/plugins/echarts';
import { PropertyTrend, PropertyHistPage ,ExportPropertyHist} from '#/api/device/iotDevice';
import { propertyTrendColumns } from './model';
import dayjs, { Dayjs } from 'dayjs';

import { getDictOptions } from '#/utils/dict';
import { DictEnum } from '@vben/constants';
import { IconifyIcon } from '@vben/icons';
import { commonDownloadExcel } from '#/utils/file/download';
import { emitter } from '#/views/_core/profile/mitt';

const chartRef = ref();
const renderEchartsRef = ref();
interface ModalProps {
  tenantId: string;
  deviceKey: string;
  key: string;
  name: string;
}

const State = reactive({
  tenantId: '',
  deviceKey: '',
  key: '',
  name: '',
  currentTab: 'line',
  currentTimeStep: '1',
  unit: '',
  max: 80_000,
  lineType: 'line',
  TabsList: [{
    key: 'line',
    label: '趋势图',
  }, {
    key: 'grid',
    label: '历史数据',
  },],
  timeQuery: [dayjs().subtract(30, 'minute'), dayjs()] as [Dayjs, Dayjs],
  queryFormValues: {
    timeType: 'hh1',
    time: [dayjs().subtract(1, 'hour'), dayjs()],
    aggregateWindow: '',
    aggregateFunction: 'AVG',
  },
  lineDatas: [],
  lineLabels: [],
  gridDatas: [],
  aggregateData: [] as any,
});

const [BasicModal, modalApi] = useVbenModal({
  onCancel() {
    State.key = '';
    State.lineDatas = []
    State.lineLabels = []
    emitter.off('wsDataProperty');
    modalApi.close();
  },
  async onOpenChange(isOpen: boolean) {
    if (!isOpen) {
      return null;
    }
    State.currentTab = 'line';
    modalApi.setState({ confirmLoading: true, loading: true });
    const { tenantId, deviceKey, key, name } = modalApi.getData() as ModalProps;
    const { renderEcharts } = useEcharts(chartRef);
    renderEchartsRef.value = renderEcharts;
    State.tenantId = tenantId;
    State.deviceKey = deviceKey;
    State.key = key;
    State.name = name;
    State.currentTimeStep = '1';
    State.unit = '';
    State.max = 80_000;
    await queryFormApi.setValues(State.queryFormValues);
    await refreshTabs();
    modalApi.setState({ confirmLoading: false, loading: false });
    emitter.on('wsDataProperty', handleWsDataProperty);
  }
});

const timeTypeOptions = [
  { label: '自定义时间段', value: 'custom' },
  { label: '最近5分钟', value: 'mm5' },
  { label: '最近15分钟', value: 'mm15' },
  { label: '最近30分钟', value: 'mm30' },
  { label: '最近1小时', value: 'hh1' },
  { label: '最近3小时', value: 'hh3' },
  { label: '最近6小时', value: 'hh6' },
  { label: '最近12小时', value: 'hh12' },
  { label: '最近24小时', value: 'hh24' },
  { label: '最近3天', value: 'dd3' },
  { label: '最近7天', value: 'dd7' },
  { label: '最近15天', value: 'dd15' },
  { label: '最近30天', value: 'dd30' },
  { label: '最近60天', value: 'dd60' },
  { label: '最近90天', value: 'dd90' },
  { label: '最近6个月', value: 'MM6' },
  { label: '最近1年', value: 'yy1' },
  { label: '今天', value: 'day1' },
  { label: '昨天', value: 'day2' },
  { label: '前天', value: 'day3' },
  { label: '上周今日', value: 'day4' },
  { label: '本周', value: 'week1' },
  { label: '上周', value: 'week2' },
  { label: '本月', value: 'month1' },
  { label: '上个月', value: 'month2' },
  { label: '今年', value: 'year1' },
  { label: '去年', value: 'year2' },
];

function getAggregateTimeWindowOptions(formValues: any) {
  const options = getDictOptions(DictEnum.AGGREGATE_TIME_WINDOW);
  let enableIndex = 0;
  if(!formValues.time || formValues.time.length < 2){
    return options;
  }
  const startTime = formValues.time[0];
  const endTime = formValues.time[1];
  if (endTime.diff(startTime, 'month') >= 6) {
    enableIndex = 13;
  } else if (endTime.diff(startTime, 'day') >= 60) {
    enableIndex = 11;
  } else if (endTime.diff(startTime, 'day') >= 3) {
    enableIndex = 9;
  } else if (endTime.diff(startTime, 'hour') >= 24) {
    enableIndex = 8;
  } else if (endTime.diff(startTime, 'hour') >= 3) {
    enableIndex = 5;
  }
  formValues.aggregateWindow = options[enableIndex]?.value || '';
  
  return options.map((item: any, index: number) => {
    if (index < enableIndex) {
      return {
        label: item.label,
        value: item.value,
        disabled: true,
      }
    }
    return {
      label: item.label,
      value: item.value,
    }
  });
}

// 时间范围变化
async function onTimeRangeChange(dates: any) {
  if (dates && dates.length === 2) {
    // 当用户手动修改时间范围时，自动切换到自定义时间段
    await queryFormApi.setFieldValue('timeType', 'custom');
  }
}

async function changeTimeType(e: any) {
  console.log("changeTimeType", e);
  //今天零点
  const todayZero = dayjs().startOf('day');
  //本周一零点
  const weekOneZero = dayjs().startOf('week');
  //本月1号零点
  const monthOneZero = dayjs().startOf('month');
  //今年1月1号零点
  const yearOneZero = dayjs().startOf('year');
  switch (e) {
    case 'custom':
      State.queryFormValues.time = [dayjs().subtract(30, 'minute'), dayjs()];
      break;
    case 'mm5':
      State.queryFormValues.time = [dayjs().subtract(5, 'minute'), dayjs()];
      break;
    case 'mm15':
      State.queryFormValues.time = [dayjs().subtract(15, 'minute'), dayjs()];
      break;
    case 'mm30':
      State.queryFormValues.time = [dayjs().subtract(30, 'minute'), dayjs()];
      break;
    case 'hh1':
      State.queryFormValues.time = [dayjs().subtract(1, 'hour'), dayjs()];
      break;
    case 'hh3':
      State.queryFormValues.time = [dayjs().subtract(3, 'hour'), dayjs()];
      break;
    case 'hh6':
      State.queryFormValues.time = [dayjs().subtract(6, 'hour'), dayjs()];
      break;
    case 'hh12':
      State.queryFormValues.time = [dayjs().subtract(12, 'hour'), dayjs()];
      break;
    case 'hh24':
      State.queryFormValues.time = [dayjs().subtract(24, 'hour'), dayjs()];
      break;
    case 'dd3':
      State.queryFormValues.time = [dayjs().subtract(3, 'day'), dayjs()];
      break;
    case 'dd7':
      State.queryFormValues.time = [dayjs().subtract(7, 'day'), dayjs()];
      break;
    case 'dd15':
      State.queryFormValues.time = [dayjs().subtract(15, 'day'), dayjs()];
      break;
    case 'dd30':
      State.queryFormValues.time = [dayjs().subtract(30, 'day'), dayjs()];
      break;
    case 'dd60':
      State.queryFormValues.time = [dayjs().subtract(60, 'day'), dayjs()];
      break;
    case 'dd90':
      State.queryFormValues.time = [dayjs().subtract(90, 'day'), dayjs()];
      break;
    case 'MM6':
      State.queryFormValues.time = [dayjs().subtract(6, 'month'), dayjs()];
      break;
    case 'yy1':
      State.queryFormValues.time = [dayjs().subtract(1, 'year'), dayjs()];
      break;
    case 'day1': //今天
      State.queryFormValues.time = [todayZero, dayjs()];
      break;
    case 'day2': //昨天
      State.queryFormValues.time = [todayZero.subtract(1, 'day'), todayZero];
      break;
    case 'day3': //前天
      State.queryFormValues.time = [todayZero.subtract(2, 'day'), todayZero.subtract(1, 'day')];
      break;
    case 'day4': //上周今日
      State.queryFormValues.time = [todayZero.subtract(7, 'day'), todayZero.subtract(6, 'day')];
      break;
    case 'week1': //本周
      State.queryFormValues.time = [weekOneZero, dayjs()];
      break;
    case 'week2': //上周
      State.queryFormValues.time = [weekOneZero.subtract(1, 'week'), weekOneZero];
      break;
    case 'month1': //本月
      State.queryFormValues.time = [monthOneZero, dayjs()];
      break;
    case 'month2': //上个月
      State.queryFormValues.time = [monthOneZero.subtract(1, 'month'), monthOneZero];
      break;
    case 'year1': //今年
      State.queryFormValues.time = [yearOneZero, dayjs()];
      break;
    case 'year2': //去年
      State.queryFormValues.time = [yearOneZero.subtract(1, 'year'), yearOneZero];
      break;
    default:
      State.queryFormValues.time = [dayjs().subtract(30, 'minute'), dayjs()];
      break;
  }
  await queryFormApi.setFieldValue('time', State.queryFormValues.time);
}

const [QueryForm, queryFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-1',
  },
  layout: 'horizontal',
  schema: [{
    fieldName: 'timeType',
    component: 'Select',
    label: '时间范围',
    labelWidth: 0,
    hideLabel: true,
    componentProps: {
      options: timeTypeOptions,
      onChange: changeTimeType,
    },
    formItemClass: 'col-span-2',
  }, {
    fieldName: 'time',
    component: 'RangePicker',
    label: '时间范围',
    labelWidth: 0,
    hideLabel: true,
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      placeholder: ['开始时间', '结束时间'],
      onChange: onTimeRangeChange,
    },
    formItemClass: 'col-span-4',
  },
  {
    fieldName: 'aggregateWindow',
    component: 'Select',
    label: '聚合窗口',
    labelWidth: 0,
    hideLabel: true,
    componentProps: {
      options: getDictOptions(DictEnum.AGGREGATE_TIME_WINDOW),
    },
    dependencies: {
      componentProps: async (formValues: any) => {
        return {
          options: getAggregateTimeWindowOptions(formValues),
        }
      },
      triggerFields: ['time'],
    },
    defaultValue: '',
  },
  {
    fieldName: 'aggregateFunction',
    component: 'Select',
    label: '聚合函数',
    labelWidth: 0,
    hideLabel: true,
    componentProps: {
      options: getDictOptions(DictEnum.AGGREGATE_FUNCTION),
    },
    dependencies: {
      show: (formValues: any) => {
        return formValues.aggregateWindow != '';
      },
      triggerFields: ['aggregateWindow'],
    },
  },

  ],
  resetButtonOptions: {
    show: false,
  },
  submitButtonOptions: {
    show: false,
  },
  wrapperClass: 'grid-cols-9 gap-x-1',
});


function handleTabChange(key: any) {
  State.currentTab = key;
}

async function refreshTabs() {
  if (State.currentTab == 'line') {
    await refreshLineCharts();
  } else if (State.currentTab == 'grid') {
    refreshGrid();
  }
}

async function refreshLineCharts() {
  console.log('refreshLineCharts');
  const formValues = await queryFormApi.getValues();
  State.queryFormValues.aggregateWindow = formValues.aggregateWindow;
  State.queryFormValues.aggregateFunction = formValues.aggregateFunction;
  State.queryFormValues.time = formValues.time;
  State.queryFormValues.timeType = formValues.timeType;
  if (!formValues.time || formValues.time.length < 2) {
    return;
  }
  const res = await PropertyTrend({
    deviceKey: State.deviceKey,
    propertyKey: State.key,
    size: 1000,
    aggregateWindow: formValues.aggregateWindow,
    aggregateFunction: formValues.aggregateFunction,
    startTime: formValues.time[0].format('YYYY-MM-DD HH:mm:ss'),
    endTime: formValues.time[1].format('YYYY-MM-DD HH:mm:ss'),
  });
  console.log(res);
  if (!res) {
    State.lineDatas = []
    State.lineLabels = []
    State.unit = '';
    State.max = 80_000;
    State.aggregateData = [];
  } else {
    State.lineDatas = res.values || [];
    State.lineLabels = res.labels || [];
    State.unit = res.unit || '';
    State.max = State.lineDatas.length > 0 ? Math.max(...State.lineDatas) : 80_000;
    State.aggregateData = res.aggregateData || [];
  }
  renderLineCharts();
  aggregateGridApi.query();
}

function renderLineCharts() {
  renderEchartsRef.value({
    grid: {
      bottom: 10,
      containLabel: true,
      left: '1%',
      right: '1%',
      top: '2%',
    },
    series: [
      {
        data: State.lineDatas,
        itemStyle: {
          color: '#5ab1ef',
        },
        smooth: true,
        type: State.lineType,
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
          ],
          symbolSize: 20,
          symbol: 'pin',
          symbolOffset: [0, -10],
        },
      },
    ],
    tooltip: {
      axisPointer: {
        lineStyle: {
          color: '#019680',
          width: 1,
        },
      },
      trigger: 'axis',
      formatter: '{b}' + State.name + ' {c}' + State.unit,
    },
    xAxis: {
      axisTick: {
        show: true,
      },
      boundaryGap: false,
      data: State.lineLabels,
      splitLine: {
        lineStyle: {
          type: 'solid',
          width: 1,
        },
        show: true,
      },
      type: 'category',
    },
    yAxis: [
      {
        axisTick: {
          show: false,
        },
        max: State.max + 10,
        splitArea: {
          show: true,
        },
        splitNumber: 4,
        type: 'value',
      },
    ],
  }, true);
}

function refreshGrid() {
  if (gridApi && gridApi.grid) {
    gridApi.query();
  }
}

type RowType = {
  ts: string;
  value: string;
  unit: string;
}

const aggregateGridOptions: VxeTableGridOptions<RowType> = {
  rowConfig: {
    keyField: 'time',
  },
  columns: propertyTrendColumns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return {
          items: State.aggregateData,
          total: State.aggregateData.length,
        }
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: true,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};


const [AggregateGrid, aggregateGridApi] = useVbenVxeGrid({
  gridOptions: aggregateGridOptions,
  gridEvents: {}, // 添加空的gridEvents
});


const gridOptions: VxeTableGridOptions<RowType> = {
  rowConfig: {
    keyField: 'time',
  },
  columns: propertyTrendColumns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        console.log(formValues);
        if (State.timeQuery.length < 2) {
          return;
        }
        return await PropertyHistPage({
          page: page.currentPage,
          pageSize: page.pageSize,
          deviceKey: State.deviceKey,
          propertyKey: State.key,
          startTime: State.timeQuery[0].format('YYYY-MM-DD HH:mm:ss'),
          endTime: State.timeQuery[1].format('YYYY-MM-DD HH:mm:ss')
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};


const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents: {}, // 添加空的gridEvents
});

async function exportGrid() {
  const formValues = {
    deviceKey: State.deviceKey,
    propertyKey: State.key,
    startTime: State.timeQuery[0].format('YYYY-MM-DD HH:mm:ss'),
    endTime: State.timeQuery[1].format('YYYY-MM-DD HH:mm:ss')
  };
  await commonDownloadExcel(ExportPropertyHist, '设备属性历史数据-' + State.name + '(' + State.key + ')', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success('导出成功');
}

function onTimeStepChange(value: any) {
  console.log("onTimeStepChange", value);
  if (value.target.value == '1') {
    State.timeQuery = [dayjs().subtract(30, 'minute'), dayjs()];
  } else if (value.target.value == '2') {
    State.timeQuery = [dayjs().subtract(1, 'hour'), dayjs()];
  } else if (value.target.value == '3') {
    State.timeQuery = [dayjs().subtract(12, 'hour'), dayjs()];
  }
  gridApi.query();
}


const lineType = ref('line');

function onLineTypeChange(value: any) {
  console.log("onLineTypeChange", value);
  if (value.target.value == 'line') {
    State.lineType = 'line';
    renderLineCharts();
  } else if (value.target.value == 'bar') {
    State.lineType = 'bar';
    renderLineCharts();
  }
}

function addLineDatas(value: any, time: string) {
    if (!value || State.currentTab != 'line') {
      return;
    }
    if (State.queryFormValues.aggregateWindow != "") {
      //可刷新页面数据
      return;
    }
    State.lineDatas = [...State.lineDatas, value] as any;
    State.lineLabels = [...State.lineLabels, time] as any;
    State.aggregateData = [ {
      ts: time,
      value: value,
      unit: State.unit,
    }, ...State.aggregateData] as any;
    renderLineCharts();
    aggregateGridApi.query();
}


function handleWsDataProperty(res: any) {
    console.log("接收到Websocket消息", res);  
    if(!res.e || !res.d){
        console.log("不是有效的设备属性消息", res);
        return;
    }

    if (res.e !== 'ws:device:properties:'+State.tenantId+':' + State.deviceKey || !res.d.propertys){
        console.log("不是有效的设备属性消息", res);
        return;
    }

    console.log("接收到设备属性变化", res.d);   
    
  if (res.d.propertys[State.key]) {
        let newp = res.d.propertys[State.key]
        if(!newp){
          return;
        }
        addLineDatas(newp.value, dayjs(newp.time).format('YYYY-MM-DD HH:mm:ss'));
      }
}

</script>
<template>
  <BasicModal :footer="false" class="w-[1000px]" :title="State.name + '(' + State.key + ')'">
    <Tabs :activeKey="State.currentTab" @change="handleTabChange">
      <TabPane tab="趋势图" key="line">
        <div class="w-full flex items-center p-2">
          <div class="h-[40px]">
            <QueryForm />
          </div>
          <div class="h-[40px] flex items-center justify-end mb-2">
            <RadioGroup class="flex items-center mr-2" v-model:value="lineType" @change="onLineTypeChange">
              <RadioButton value="line" type="button" class="m-0 w-8 h-8 p-2" title="折线图">
                <IconifyIcon icon="ant-design:line-chart-outlined" class="w-4 h-4" />
              </RadioButton>
              <RadioButton value="bar" type="button" class="m-0 w-8 h-8 p-2" title="柱状图">
                <IconifyIcon icon="ant-design:bar-chart-outlined" class="w-4 h-4" />
              </RadioButton>
            </RadioGroup>
            <Button class="w-8 h-8 p-1" @click="refreshLineCharts">
              <IconifyIcon icon="ant-design:search-outlined" class="w-4 h-4 m-0" />
            </Button>
          </div>
        </div>
        <EchartsUI ref="chartRef" />
        <AggregateGrid :data="State.aggregateData" class="h-[500px]" />
      </TabPane>
      <TabPane tab="历史数据" key="grid">
        <div class="h-[50px] flex items-center gap-x-4 flex-1">
          <RadioGroup v-model:value="State.currentTimeStep" @change="onTimeStepChange">
            <RadioButton value="1">最近半小时</RadioButton>
            <RadioButton value="2">最近一小时</RadioButton>
            <RadioButton value="3">最近12小时</RadioButton>
          </RadioGroup>
          <RangePicker v-model:value="State.timeQuery" class="w-[200px] flex-1" :show-time="{ format: 'HH:mm:ss' }"
            format="YYYY-MM-DD HH:mm:ss" :placeholder="['开始时间', '结束时间']" />
          <div>
            <Button type="primary" @click="refreshGrid">查询</Button>
            <Button type="primary" @click="exportGrid">导出</Button>
          </div>
        </div>
        <Grid :data="State.gridDatas" class="h-[500px]" />
      </TabPane>
    </Tabs>
  </BasicModal>
</template>