<script lang="ts" setup>
import { h, onMounted, ref } from 'vue';
import { Button, message, Popconfirm } from 'ant-design-vue';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { useVbenDrawer, useVbenModal, Page } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl } from '@vben/access';
import { List, Delete, Edit } from '#/api/device/iotProductModel';
import { View, TagLinkEdit } from '#/api/device/iotProduct';
import { MdiPlus, } from '@vben/icons';
import { propertyColumns, tagColumns, functionColumns, type RowType } from './model';
import editDrawer from './edit.vue';
import functionDrawer from './function.vue';
import objectModal from '../iotModel/object.vue';
import linkPropertySelector from '../iotProductPreview/extraConfig/gpsFieldSelector.vue';

const Props = defineProps({
  title: {
    type: String,
    default: '属性定义',
  },
  productKey: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'property',
  },
  published: {
    type: Boolean,
    default: false,
  },
});

const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'modelId',
  },
  columns: Props.type == "property" ? propertyColumns : Props.type == "tag" ? tagColumns : functionColumns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          productKey: Props.productKey,
          type: Props.type,
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}

// 存储标签关联属性的Map
const tagLinkModelMap = ref<Map<string, any>>(new Map());
const [Grid, gridApi] = useVbenVxeGrid({
  gridClass: 'p-0',
  gridOptions,
  gridEvents,
});

const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});

const [FunctionDrawer, functionDrawerApi] = useVbenDrawer({
  connectedComponent: functionDrawer,
});

function handleAdd() {
  if (Props.type == 'function' || Props.type == 'event') {
    functionDrawerApi.setData({ productKey: Props.productKey, update: false, type: Props.type, view: false});
    functionDrawerApi.open();
    return;
  }
  editDrawerApi.setData({ productKey: Props.productKey, update: false, type: Props.type, view: false});
  editDrawerApi.open();
}
function handleEdit(row: RowType) {
  if (Props.type == 'function' || Props.type == 'event') {
    functionDrawerApi.setData({ id: row.modelId, productKey: Props.productKey, update: true, type: Props.type ,view: false});
    functionDrawerApi.open();
    return;
  }
  editDrawerApi.setData({ id: row.modelId, productKey: Props.productKey, update: true, type: Props.type ,view: false});
  editDrawerApi.open();
}
function handleView(row: RowType) {
  if (Props.type == 'function' || Props.type == 'event') {
    functionDrawerApi.setData({ id: row.modelId, productKey: Props.productKey, update: false, type: Props.type, view: true });
    functionDrawerApi.open();
    return;
  }
  editDrawerApi.setData({ id: row.modelId, productKey: Props.productKey, update: true, type: Props.type, view: true });
  editDrawerApi.open();
}
async function handleDelete(row: RowType) {
  await Delete({ modelId: [row.modelId] });
  message.success("删除成功");
  await handleRefresh();
}
async function handleRefresh() {
  await gridApi.query();
}

async function handleImportModel() {
  ObjectModalApi.setData({ object: true, type: Props.type, title: '导入通用物模型' });
  ObjectModalApi.open();
}

function reload() {
  gridApi.query();
}


const [ObjectModal, ObjectModalApi] = useVbenModal({
  connectedComponent: objectModal,
});

async function handlerImportConfirm(index: any, update: boolean, datas: any) {
  console.log("handlerImportConfirm", datas);
  datas.productKey = Props.productKey;
  datas.modelId = null;
  await Edit(datas);
  handleRefresh();
}
// 关联属性 弹窗相关状态
const [linkPropertyModal, linkPropertyModalApi] = useVbenModal({
  title: '选择物模型字段',
  connectedComponent: linkPropertySelector,
});

function linkPropertyOpen(row: RowType) {

  // 根据 row.modelKey 从 Map 中获取初始值
  const initialValue = tagLinkModelMap.value.get(row.modelKey) || {};

  const modalData = {
    key: row.modelKey, // 使用 modelKey 作为关键字
    productKey: Props.productKey,
    initialValue: initialValue,
    title: `关联属性 - ${row.modelName || row.modelKey}`,
    row: row
  };

  linkPropertyModalApi.setData(modalData);
  linkPropertyModalApi.open();
}
//关联属性的确认
async function handleLinkPropertyConfirm(field: any) {
  // 关联属性选择确认结果

  if (field && field.key && field.formData) {
    try {
      // 将选择的结果保存到 Map 中
      tagLinkModelMap.value.set(field.key, field.formData);

      // 调用后端接口保存数据
      // 根据接口文档构造数据结构
      const requestData = {
        productKey: Props.productKey,
        tagKey: field.key,  // 这个 tagKey 等于 row.modelKey
        modelType: field.formData.modelType,
        modelKey: field.formData.modelKey,
        outputKey: field.formData.outputKey || ''
      };

      // 调用 tagLinkEdit 接口
      await TagLinkEdit(requestData);

      message.success('关联属性设置成功');

      // 可选：重新加载数据以确保同步
      await loadTagLinkModel();

    } catch (error) {
      console.error('保存关联属性失败:', error);
      message.error('保存关联属性失败，请重试');

      // 如果保存失败，从 Map 中移除
      tagLinkModelMap.value.delete(field.key);
    }
  } else {
    message.error('请选择有效的字段');
  }
}

// 加载标签关联属性配置
async function loadTagLinkModel() {
  if (Props.type !== 'tag' || !Props.productKey) {
    return;
  }

  try {
    const res = await View({ productKey: Props.productKey });
    if (res?.extraConfig?.tagLinkModel) {
      // 将 tagLinkModel 对象转换为 Map
      const tagLinkModel = res.extraConfig?.tagLinkModel;
      if (!tagLinkModel) {
        return;
      }
      tagLinkModelMap.value.clear();

      Object.keys(tagLinkModel).forEach(key => {
        tagLinkModelMap.value.set(key, tagLinkModel[key]);
      });
    } else {
      console.log('没有找到 tagLinkModel 配置');
    }
  } catch (error) {
    console.error('加载标签关联属性配置失败:', error);
  }
}

defineExpose({ reload });

onMounted(async () => {
  gridApi.query();
  loadTagLinkModel();

});
</script>
<template :id="Props.type" auto-content-height>
    <Grid>
      <template #toolbar-tools>
        <Button class="mr-2 flex items-center " type="primary" :icon="h(MdiPlus)" @click="handleAdd"
          v-access:code="'cpm:device:iotProductModel:edit'" v-if="!Props.published">
          添加
        </Button>
        <Button class="mr-2 flex items-center " type="primary" :icon="h(MdiPlus)" @click="handleImportModel"
          v-access:code="'cpm:device:iotProductModel:edit'"
          v-if="!Props.published && ['property', 'tag'].includes(Props.type)">
          导入通用物模型
        </Button>
      </template>
      <template #action="{ row }">
        <div class="flex items-center justify-center">
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleView(row)"
            v-access:code="'cpm:device:iotProductModel:view'">
            查看
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row)"  v-if="!Props.published"
            v-access:code="'cpm:device:iotProductModel:edit'">
            修改
          </Button>
          <Button v-if="row.type == 'tag' && !Props.published" class="mr-2 border-none p-0" :block="false" type="link"
            @click="linkPropertyOpen(row)" v-access:code="'cpm:device:iotProductModel:edit'">
            关联属性
          </Button>
          <AccessControl :codes="['cpm:device:iotProductModel:delete']" type="code" v-if="!Props.published">
            <Popconfirm :id="row.modelId" title="确定删除吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </AccessControl>
        </div>
      </template>
    </Grid>
    <EditDrawer @reload="handleRefresh" />
    <FunctionDrawer @reload="handleRefresh" />
    <ObjectModal @confirm="handlerImportConfirm" />
    <linkPropertyModal @confirm="handleLinkPropertyConfirm" />
</template>
