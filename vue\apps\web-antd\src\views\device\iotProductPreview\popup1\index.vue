

<template>
  <div>
    <Modal class="h-[600px] w-[1000px]" title="选择产品">
      <div class="table-content">
        <Grid ref="gridRef" table-title="选择接入点"></Grid>
      </div>
      <template #footer>
        <Button @click="handleCancel">取消</Button>
        <Button 
          type="primary" 
          @click="handleConfirm" 
          :loading="loading"
        >
          确定
        </Button>
      </template>
    </Modal>
  </div>
</template>

<script lang="ts" setup>
import { useVbenModal, VbenButton } from '@vben/common-ui';

import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';

import { columns, type RowType } from './model';
import type { DeepPartial } from '@vben/types';
//import { List, Export, Delete } from '#/api/device/iotProduct';
//import { List as CategoryList } from '#/api/device/iotProductCategory';
import { Alert, Input, Select, SelectOption, Button, message } from 'ant-design-vue';
import {
  Export,
  Status,
  TestConnect ,
  View,
  listNotLinked
} from '#/api/ruleengine/iotChannel';
import { List,Edit,Delete,} from '#/api/device/iotProductEnter';
import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { ref, defineProps, computed, getCurrentInstance } from 'vue';

const props = defineProps({
  productKey: { type: String, required: true },
  edit: { type: Boolean, default: false },
});

const emit = defineEmits(['confirm', 'reload','success']);
const formRef = ref();
const [Modal, modalApi] = useVbenModal();
const gridRef = ref(null);
const loading = ref(false);

// 表格配置
const gridOptions: VxeTableGridOptions<RowType> = {
  radioConfig: {
    highlight: true,
    labelField: 'channelId',
  },
  rowConfig: {
    keyField: 'channelId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        try {
          return await listNotLinked({
            page: page.currentPage,
            pageSize: page.pageSize,
            productKey: props.productKey,
            status: 0,
          });
        } catch (error) {
          message.error('加载数据失败');
          return { items: [], total: 0 };
        }
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

// 处理确认按钮点击
const handleConfirm = async () => {
  const selectedRows = gridApi.grid.getRadioRecord();
  
  if (selectedRows.length === 0) {
    message.warning('请至少选择一个产品');
    return;
  }
  
  loading.value = true;
 
  try {
    // 准备提交数据
    const channelId = selectedRows.channelId;
    
    // 调用Edit接口
    await Edit({
      channelId,
      productKey: props.productKey,
    });
  
    message.success('提交成功');
    emit('confirm', channelId); // 通知父组件
    emit('reload');
    modalApi.close(); // 关闭模态框
  } catch (error) {
    message.error('提交失败');
  } finally {
    loading.value = false;
  }
};

// 处理取消按钮
const handleCancel = () => {
  modalApi.close();
};

// 暴露打开模态框的方法
defineExpose({
  openModal: () => {
    modalApi.open();
   
  },
});

function AddOrUpdateApi(data: any) {
  throw new Error('Function not implemented.');
}

const handleSubmit = async () => {
  await formRef.value?.validate();
  try {
    const values = formRef.value?.getFieldsValue();
    
    if (props.edit) {
      // 更新模式
      await Edit(values);
      message.success('更新成功');
    } else {
      // 新增模式
      await listNotLinked(values);
      message.success('新增成功');
    }
     const parentVm = getCurrentInstance()?.parent;
    if (parentVm) {
            // 使用类型断言
            (parentVm as any).handleRefresh && await (parentVm as any).handleRefresh(); 
        }
    emit('success'); // 触发成功事件通知父组件
    modalApi.close(); // 关闭模态框
  } catch (error) {
    message.error('操作失败');
  }
};
</script>

<style scoped>
.table-content {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>