<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm></BasicForm>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { message } from 'ant-design-vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { useVbenForm } from '#/adapter/form';
import { Edit, View } from '#/api/project/iotProjectProduct';
import { List as ListProduct } from '#/api/device/iotProduct';
import { editSchema } from './model';

const emit = defineEmits<{ reload: [] }>();

interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
  projectId?: number | string;
}

const isUpdate = ref(false);
const isView = ref(false);
const productOptions = ref<any[]>([]);

const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

// 加载产品选项
async function loadProductOptions() {
  try {
    const res = await ListProduct({
      page: 1,
      pageSize: 1000
    });
    if (res && res.items) {
      productOptions.value = res.items.map((item: any) => ({
        label: item.productName,
        value: item.productId,
        productKey: item.productKey,
      }));

      // 更新表单schema中的产品选项
      updateProductOptionsToForm();
    }
  } catch (error) {
    console.error('加载产品选项失败:', error);
    message.error('加载产品选项失败');
  }
}

// 更新表单中的产品选项
function updateProductOptionsToForm() {
  if (formApi && formApi.updateSchema) {
    formApi.updateSchema([
      {
        fieldName: 'productId',
        component: 'Select',
        label: '选择产品',
        componentProps: {
          placeholder: '请选择要关联的产品',
          showSearch: true,
          options: productOptions.value,
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().includes(input.toLowerCase());
          },
          onChange: (value: any) => {
            // 当选择产品时，自动填充产品标识
            const selectedProduct = productOptions.value.find(p => p.value === value);
            if (selectedProduct) {
              formApi.setFieldValue('productKey', selectedProduct.productKey);
            }
          },
        },
        rules: 'required',
      },
    ]);
  }
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-1',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 gap-x-4',
});

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }

    drawerApi.setState({ confirmLoading: true, loading: true });

    // 加载产品选项
    await loadProductOptions();

    const { id, update, view, projectId } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;

    if (isUpdate.value || isView.value) {
      try {
        const record = await View({ ppId: id });
        await formApi.setValues(record);
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取数据失败');
      }
    } else {
      // 新增时设置默认值
      if (projectId) {
        await formApi.setValues({ projectId });
      }
    }

    drawerApi.setState({ confirmLoading: false, loading: false });

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
    } else {
      drawerApi.setState({ showConfirmButton: true });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true });

    // 验证表单
    const valid = await formApi.validate();
    if (!valid) {
      message.error('请检查表单填写是否正确');
      return;
    }

    const values = await formApi.getValues();
    console.log('提交的表单数据:', values);

    // 获取抽屉传递的数据
    const drawerData = drawerApi.getData() as ModalProps;

    // 处理数据格式
    const submitData = {
      ...values,
      // 确保项目ID存在
      projectId: values.projectId || drawerData.projectId,
    };

    console.log('发送到API的数据:', submitData);

    // 调用编辑接口
    const response = await Edit(submitData);
    console.log('API响应:', response);

    // 显示成功消息
    if (isUpdate.value) {
      message.success('编辑关联产品成功');
    } else {
      message.success('新增关联产品成功');
    }

    // 触发父组件刷新
    emit('reload');

    // 关闭抽屉
    await handleCancel();
  } catch (error: any) {
    console.error('保存失败:', error);

    // 显示错误消息
    const errorMessage = error?.message || error?.response?.data?.message || '操作失败，请重试';
    message.error(errorMessage);
  } finally {
    drawerApi.setState({ confirmLoading: false });
  }
}

async function handleCancel() {
  drawerApi.close();
  await formApi.resetForm();
}

defineExpose({
  open: drawerApi.open,
  close: drawerApi.close,
  setData: drawerApi.setData,
});
</script>