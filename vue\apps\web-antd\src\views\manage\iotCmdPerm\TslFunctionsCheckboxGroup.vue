<template>
  <div>
    <Checkbox
      :checked="isAllChecked"
      :indeterminate="isIndeterminate"
      @change="onCheckAll"
      style="margin-bottom: 8px;"
    >
      全选
    </Checkbox>
    <Checkbox
      v-for="func in functions"
      :key="func.key"
      :checked="modelValue.includes(func.key)"
      @change="e => onCheck(func.key, e.target.checked)"
    >
      {{ func.name }}
    </Checkbox>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';
import { Checkbox } from 'ant-design-vue';
import type { CheckboxChangeEvent } from 'ant-design-vue/es/checkbox/interface';
const props = defineProps<{
  functions: { key: string; name: string }[];
  modelValue: string[];
}>();

const isAllChecked = computed(() =>
  props.functions.length > 0 &&
  props.functions.every(fn => props.modelValue.includes(fn.key))
);

const isIndeterminate = computed(() =>
  props.modelValue.length > 0 &&
  !isAllChecked.value
);
const emit = defineEmits(['update:modelValue']);
function onCheckAll(e: CheckboxChangeEvent) {
  const checked = e.target.checked;
  const newValue = checked ? props.functions.map(fn => fn.key) : [];
  emit('update:modelValue', newValue);
}
function onCheck(key: string, checked: boolean) {
  let newValue = props.modelValue.slice();
  if (checked) {
    if (!newValue.includes(key)) newValue.push(key);
  } else {
    newValue = newValue.filter(k => k !== key);
  }
  emit('update:modelValue', newValue);
}
</script>